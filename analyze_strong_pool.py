#!/usr/bin/env python3
"""
分析当前强势池状况，了解为什么这么多板块在强势池，以及它们如何才会退场
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from src.domain.analyze.aggregate.analysis.zh_blank import AnalyzeZhBlankFundFlowAggregate
from src.infra.app import app
from loguru import logger

def analyze_strong_pool_situation():
    """分析强势池现状"""
    logger.info("=" * 70)
    logger.info("分析强势池现状：为什么这么多板块，如何才会退场")
    logger.info("=" * 70)
    
    # 初始化分析器
    analyzer = AnalyzeZhBlankFundFlowAggregate(is_concept=False)
    
    # 获取当前状态
    watchlist = analyzer.get_watchlist()
    
    strong_sectors = watchlist.get("strong_trend", [])
    consolidation_sectors = watchlist.get("consolidation", [])
    cooldown_sectors = watchlist.get("cooldown", [])
    
    logger.info(f"📊 当前状况:")
    logger.info(f"  强势池: {len(strong_sectors)}个板块")
    logger.info(f"  整固池: {len(consolidation_sectors)}个板块")
    logger.info(f"  冷却池: {len(cooldown_sectors)}个板块")
    
    # 分析强势池板块的状态
    if strong_sectors:
        logger.info(f"\n🔍 强势池详细分析:")
        
        # 按强势天数分组
        short_term = []  # 1-5天
        medium_term = []  # 6-10天
        long_term = []  # 11天以上
        
        for sector in strong_sectors:
            strong_days = sector.get('strong_days', 0)
            if strong_days <= 5:
                short_term.append(sector)
            elif strong_days <= 10:
                medium_term.append(sector)
            else:
                long_term.append(sector)
        
        logger.info(f"  短期强势(1-5天): {len(short_term)}个")
        logger.info(f"  中期强势(6-10天): {len(medium_term)}个")
        logger.info(f"  长期强势(11天+): {len(long_term)}个")
        
        # 分析回撤情况
        high_drawdown = []  # 回撤>2%
        medium_drawdown = []  # 回撤1-2%
        low_drawdown = []  # 回撤<1%
        
        for sector in strong_sectors:
            drawdown = abs(sector.get('drawdown_pct', 0))
            if drawdown > 0.02:
                high_drawdown.append(sector)
            elif drawdown > 0.01:
                medium_drawdown.append(sector)
            else:
                low_drawdown.append(sector)
        
        logger.info(f"\n📉 回撤分析:")
        logger.info(f"  高回撤(>2%): {len(high_drawdown)}个")
        logger.info(f"  中回撤(1-2%): {len(medium_drawdown)}个")
        logger.info(f"  低回撤(<1%): {len(low_drawdown)}个")
        
        # 显示一些具体例子
        logger.info(f"\n📋 具体例子:")
        
        if long_term:
            logger.info(f"  长期强势板块(可能接近退场):")
            for sector in sorted(long_term, key=lambda x: x.get('strong_days', 0), reverse=True)[:5]:
                logger.info(f"    {sector['name']}: {sector.get('strong_days', 0)}天, 回撤{sector.get('drawdown_pct', 0):.2%}")
        
        if high_drawdown:
            logger.info(f"  高回撤板块(接近退场条件):")
            for sector in sorted(high_drawdown, key=lambda x: abs(x.get('drawdown_pct', 0)), reverse=True)[:5]:
                logger.info(f"    {sector['name']}: 回撤{sector.get('drawdown_pct', 0):.2%}, {sector.get('strong_days', 0)}天")

def explain_exit_conditions():
    """解释退场条件"""
    logger.info(f"\n" + "=" * 70)
    logger.info("强势池板块的退场条件分析")
    logger.info("=" * 70)
    
    logger.info(f"\n🚪 从strong_trend退场的条件:")
    logger.info(f"  1️⃣ 直接退场到cooldown:")
    logger.info(f"     • 回撤≥15% (严重回撤止盈)")
    logger.info(f"     • 连续5日净流出 + FlowSlope<-0.5 + F5<0 (资金严重转弱)")
    logger.info(f"     • 跌破MA15 且 (大量资金流出>阈值80% 或 大幅下跌≤-5%)")
    
    logger.info(f"\n  2️⃣ 进入consolidation观察:")
    logger.info(f"     • 跌破MA5但未破MA15，且成交量萎缩")
    logger.info(f"     • MA5上方大跌但缩量(强势洗盘)")
    logger.info(f"     • FlowSlope转负但F5仍为正，且在MA5-MA15之间")
    logger.info(f"     • 连续小跌但资金流出不大，且在MA15上方")
    
    logger.info(f"\n🔄 从consolidation的出路:")
    logger.info(f"  ✅ 回到strong_trend (评分≥5分):")
    logger.info(f"     • 重新站上MA5: +3分")
    logger.info(f"     • 资金动能恢复: +2分")
    logger.info(f"     • 资金全面回流: +2分")
    logger.info(f"     • 突破整固区间: +3分")
    
    logger.info(f"\n  ❌ 进入cooldown (评分≤-5分或观察期10天):")
    logger.info(f"     • 跌破MA15: -3分")
    logger.info(f"     • 资金全面流出: -3分")
    logger.info(f"     • 连续3日净流出: -3分")

def analyze_why_so_many():
    """分析为什么强势池这么多板块"""
    logger.info(f"\n" + "=" * 70)
    logger.info("为什么强势池有这么多板块？")
    logger.info("=" * 70)
    
    logger.info(f"\n🤔 可能的原因:")
    logger.info(f"  1. 退场条件相对宽松:")
    logger.info(f"     • 回撤止盈从12%放宽到15%")
    logger.info(f"     • 连续流出从4天放宽到5天")
    logger.info(f"     • 增加了consolidation缓冲机制")
    
    logger.info(f"\n  2. 市场环境因素:")
    logger.info(f"     • 如果市场整体处于震荡上行，很多板块容易保持强势")
    logger.info(f"     • 板块轮动较快，资金在不同板块间切换")
    logger.info(f"     • 没有出现系统性的大幅下跌")
    
    logger.info(f"\n  3. 技术指标特点:")
    logger.info(f"     • MA5/MA15体系相对敏感，容易维持强势判断")
    logger.info(f"     • 资金流出阈值可能设置得较高")
    logger.info(f"     • FlowSlope等指标在震荡市中不容易持续转负")
    
    logger.info(f"\n💡 建议的优化方向:")
    logger.info(f"  1. 动态调整参数:")
    logger.info(f"     • 根据市场环境动态调整退场阈值")
    logger.info(f"     • 在强势板块过多时，适当收紧条件")
    
    logger.info(f"\n  2. 增加额外过滤:")
    logger.info(f"     • 考虑相对强度：与大盘或行业平均的比较")
    logger.info(f"     • 增加成交量确认：避免无量空涨")
    logger.info(f"     • 考虑时间衰减：强势时间过长的板块降权")
    
    logger.info(f"\n  3. 分层管理:")
    logger.info(f"     • 核心强势池：最强的10-15个板块")
    logger.info(f"     • 次级强势池：其他强势板块")
    logger.info(f"     • 观察池：consolidation板块")

if __name__ == "__main__":
    try:
        analyze_strong_pool_situation()
        explain_exit_conditions()
        analyze_why_so_many()
        
        logger.info(f"\n🎯 总结:")
        logger.info(f"强势池板块多的主要原因是退场条件相对宽松，")
        logger.info(f"这是为了避免在洗盘中被误杀而设计的。")
        logger.info(f"如果需要更精准的筛选，可以考虑增加额外的过滤条件。")
        
    except Exception as e:
        logger.error(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
