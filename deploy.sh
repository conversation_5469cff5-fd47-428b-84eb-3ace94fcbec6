#!/bin/bash

version="0.0.1" # 完成 zh 的数据同步


branch="main"
# git checkout
git checkout $branch
git pull

# delete container
container_id=$(docker ps -a|grep guanfu | awk '{print $1}')
docker rm -f $container_id > /dev/null 2>&1

# delete image
image_id=$(docker images |grep guanfu | awk '{print $3}')
docker rmi -f $image_id > /dev/null 2>&1



# docker build
docker build -t guanfu-trader:$version .

# docker run
docker run -d -p 9999:9999 --memory=1500m --memory-swap=3g --memory-reservation=512m --cpus=1  --restart=always --name guanfu-trader guanfu-trader:$version

docker images
docker ps


