"""
每日发送限制器使用示例

展示如何在不同的发送装置中使用 DailyRateLimiter 来控制告警发送频率
"""

from src.domain.monitor.aggregate.utils import daily_rate_limiter


class PriceAlertSender:
    """价格告警发送器示例"""
    
    def __init__(self):
        self.alert_type = "price_alert"
        # 设置价格告警每日最多发送50次
        daily_rate_limiter.set_daily_limit(self.alert_type, 50)
    
    def send_price_alert(self, code: str, price: float, message: str) -> bool:
        """
        发送价格告警
        
        Returns:
            bool: True表示发送成功，False表示被限制
        """
        # 检查是否可以发送
        if not daily_rate_limiter.can_send(self.alert_type):
            print(f"价格告警已达到每日发送限制，无法发送: {code} - {message}")
            return False
        
        # 记录发送
        if daily_rate_limiter.record_send(self.alert_type):
            # 实际发送逻辑
            print(f"发送价格告警: {code} 价格 {price} - {message}")
            
            # 显示当前状态
            status = daily_rate_limiter.get_send_status(self.alert_type)
            print(f"今日已发送: {status['sent_count']}/{status['daily_limit']}, "
                  f"剩余: {status['remaining']}")
            return True
        
        return False


class VolumeAlertSender:
    """成交量告警发送器示例"""
    
    def __init__(self):
        self.alert_type = "volume_alert"
        # 设置成交量告警每日最多发送30次
        daily_rate_limiter.set_daily_limit(self.alert_type, 30)
    
    def send_volume_alert(self, code: str, volume: int, message: str) -> bool:
        """
        发送成交量告警
        
        Returns:
            bool: True表示发送成功，False表示被限制
        """
        # 检查是否可以发送
        if not daily_rate_limiter.can_send(self.alert_type):
            print(f"成交量告警已达到每日发送限制，无法发送: {code} - {message}")
            return False
        
        # 记录发送
        if daily_rate_limiter.record_send(self.alert_type):
            # 实际发送逻辑
            print(f"发送成交量告警: {code} 成交量 {volume} - {message}")
            
            # 显示当前状态
            status = daily_rate_limiter.get_send_status(self.alert_type)
            print(f"今日已发送: {status['sent_count']}/{status['daily_limit']}, "
                  f"剩余: {status['remaining']}")
            return True
        
        return False


class SystemAlertSender:
    """系统告警发送器示例"""
    
    def __init__(self):
        self.alert_type = "system_alert"
        # 设置系统告警每日最多发送100次
        daily_rate_limiter.set_daily_limit(self.alert_type, 100)
    
    def send_system_alert(self, level: str, message: str) -> bool:
        """
        发送系统告警
        
        Returns:
            bool: True表示发送成功，False表示被限制
        """
        # 检查是否可以发送
        if not daily_rate_limiter.can_send(self.alert_type):
            print(f"系统告警已达到每日发送限制，无法发送: [{level}] {message}")
            return False
        
        # 记录发送
        if daily_rate_limiter.record_send(self.alert_type):
            # 实际发送逻辑
            print(f"发送系统告警: [{level}] {message}")
            
            # 显示当前状态
            status = daily_rate_limiter.get_send_status(self.alert_type)
            print(f"今日已发送: {status['sent_count']}/{status['daily_limit']}, "
                  f"剩余: {status['remaining']}")
            return True
        
        return False


def demo_usage():
    """演示使用方法"""
    print("=== 每日发送限制器使用演示 ===\n")
    
    # 创建不同的发送器
    price_sender = PriceAlertSender()
    volume_sender = VolumeAlertSender()
    system_sender = SystemAlertSender()
    
    print("1. 发送一些告警:")
    price_sender.send_price_alert("000001", 15.50, "价格突破阻力位")
    volume_sender.send_volume_alert("000002", 1000000, "成交量异常放大")
    system_sender.send_system_alert("ERROR", "数据库连接失败")
    
    print("\n2. 查看所有告警类型的状态:")
    all_status = daily_rate_limiter.get_all_status()
    for alert_type, status in all_status.items():
        print(f"{alert_type}: {status['sent_count']}/{status['daily_limit']} "
              f"(剩余: {status['remaining']})")
    
    print("\n3. 模拟达到限制:")
    # 快速发送多次价格告警直到达到限制
    for i in range(52):  # 超过50次限制
        success = price_sender.send_price_alert(f"00000{i%10}", 10.0 + i*0.1, f"测试告警 {i+1}")
        if not success:
            print(f"第 {i+1} 次发送被限制")
            break
    
    print("\n4. 手动重置某个类型的计数:")
    daily_rate_limiter.reset_daily_count("price_alert")
    print("价格告警计数已重置")
    
    # 重置后可以再次发送
    price_sender.send_price_alert("000001", 16.00, "重置后的第一条告警")
    
    print("\n5. 查看重置后的状态:")
    status = daily_rate_limiter.get_send_status("price_alert")
    print(f"价格告警状态: {status['sent_count']}/{status['daily_limit']} "
          f"(剩余: {status['remaining']})")


if __name__ == "__main__":
    demo_usage()
