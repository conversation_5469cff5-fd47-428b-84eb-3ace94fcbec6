# Test your FastAPI endpoints

GET http://127.0.0.1:8000/
Accept: application/json

###

GET http://127.0.0.1:8000/hello/User
Accept: application/json

###

http://127.0.0.1:9999/version

### 获取 zh stock price monitor
http://127.0.0.1:9999/api/v1/price/zh/stock/list?market_tag=zh_stock


###
# curl -X 'POST'
#  'http://127.0.0.1:9999/api/v1/store/zh/stock/kline/all/sync'
#  -H 'accept: application/json'
#  -H 'Content-Type: application/json'
#  -d '{
#  "start_date": "2025-05-26 09:00:00",
#  "end_date": "2025-05-26 15:00:00"
#}'
POST http://127.0.0.1:9999/api/v1/store/zh/stock/kline/all/sync
accept: application/json
Content-Type: application/json

{
  "start_date": "2025-05-26 09:00:00",
  "end_date": "2025-05-26 15:00:00"
}

###



