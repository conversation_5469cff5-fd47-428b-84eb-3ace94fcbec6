"""添加date,datetime索引

Revision ID: 7612a3769dec
Revises: c1f55a84c0c3
Create Date: 2025-05-26 19:17:22.319563

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "7612a3769dec"
down_revision: Union[str, None] = "c1f55a84c0c3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # op.create_index(op.f('ix_guanfu_k_line_30_min_datetime'), 'guanfu_k_line_30_min', ['datetime'], unique=False)
    # op.create_index(op.f('ix_guanfu_k_line_60_min_datetime'), 'guanfu_k_line_60_min', ['datetime'], unique=False)
    op.create_index(
        op.f("ix_guanfu_k_line_daily_date"),
        "guanfu_k_line_daily",
        ["date"],
        unique=False,
    )
    op.create_index(
        op.f("ix_guanfu_monitor_object_monitor_type"),
        "guanfu_monitor_object",
        ["monitor_type"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_guanfu_monitor_object_monitor_type"),
        table_name="guanfu_monitor_object",
    )
    op.drop_index(op.f("ix_guanfu_k_line_daily_date"), table_name="guanfu_k_line_daily")
    op.drop_index(
        op.f("ix_guanfu_k_line_60_min_datetime"), table_name="guanfu_k_line_60_min"
    )
    op.drop_index(
        op.f("ix_guanfu_k_line_30_min_datetime"), table_name="guanfu_k_line_30_min"
    )
    # ### end Alembic commands ###
