"""add_consolidation_fields_to_blank_state

Revision ID: 253adc548310
Revises: add_is_concept_blank_state
Create Date: 2025-07-11 22:38:39.062594

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '253adc548310'
down_revision: Union[str, None] = 'add_is_concept_blank_state'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # MySQL中修改ENUM类型需要重新定义整个列
    # 首先添加consolidation相关字段
    op.add_column('guanfu_zh_blank_analyze_fund_flow_state',
                  sa.Column('consolidation_days', sa.Integer(), nullable=False, server_default='0', comment='整固观察天数'))
    op.add_column('guanfu_zh_blank_analyze_fund_flow_state',
                  sa.Column('consolidation_high', sa.Float(), nullable=False, server_default='0.0', comment='整固期间最高点'))
    op.add_column('guanfu_zh_blank_analyze_fund_flow_state',
                  sa.Column('consolidation_low', sa.Float(), nullable=False, server_default='0.0', comment='整固期间最低点'))

    # 修改phase列的ENUM类型，添加consolidation值
    op.execute("ALTER TABLE guanfu_zh_blank_analyze_fund_flow_state MODIFY COLUMN phase ENUM('strong_trend', 'pre_burst', 'early_accum', 'consolidation', 'cooldown', 'none') NOT NULL DEFAULT 'none' COMMENT '当前阶段'")


def downgrade() -> None:
    """Downgrade schema."""
    # 恢复phase列的ENUM类型，移除consolidation值
    op.execute("ALTER TABLE guanfu_zh_blank_analyze_fund_flow_state MODIFY COLUMN phase ENUM('strong_trend', 'pre_burst', 'early_accum', 'cooldown', 'none') NOT NULL DEFAULT 'none' COMMENT '当前阶段'")

    # 删除consolidation相关字段
    op.drop_column('guanfu_zh_blank_analyze_fund_flow_state', 'consolidation_low')
    op.drop_column('guanfu_zh_blank_analyze_fund_flow_state', 'consolidation_high')
    op.drop_column('guanfu_zh_blank_analyze_fund_flow_state', 'consolidation_days')
