"""添加is_concept字段到板块分析状态表

Revision ID: add_is_concept_blank_state
Revises: 636501406913
Create Date: 2025-07-01 23:40:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_is_concept_blank_state'
down_revision: Union[str, None] = '636501406913'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 添加is_concept字段
    op.add_column('guanfu_zh_blank_analyze_fund_flow_state', 
                  sa.Column('is_concept', sa.<PERSON>(), nullable=False, default=False, comment='是否概念板块'))
    
    # 修改唯一约束，包含is_concept字段
    op.drop_constraint('name', 'guanfu_zh_blank_analyze_fund_flow_state', type_='unique')
    op.create_unique_constraint('uq_name_is_concept', 'guanfu_zh_blank_analyze_fund_flow_state', ['name', 'is_concept'])


def downgrade() -> None:
    """Downgrade schema."""
    # 恢复原来的唯一约束
    op.drop_constraint('uq_name_is_concept', 'guanfu_zh_blank_analyze_fund_flow_state', type_='unique')
    op.create_unique_constraint('name', 'guanfu_zh_blank_analyze_fund_flow_state', ['name'])
    
    # 删除is_concept字段
    op.drop_column('guanfu_zh_blank_analyze_fund_flow_state', 'is_concept')
