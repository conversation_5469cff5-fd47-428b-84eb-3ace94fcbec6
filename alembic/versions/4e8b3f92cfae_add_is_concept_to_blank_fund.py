"""add_is_concept_to_blank_fund

Revision ID: 4e8b3f92cfae
Revises: 253adc548310
Create Date: 2025-07-22 22:32:02.911402

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '4e8b3f92cfae'
down_revision: Union[str, None] = '253adc548310'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('guanfu_zh_blank_analyze_fund_flow_state', 'consolidation_days',
               existing_type=mysql.INTEGER(),
               nullable=True,
               existing_comment='整固观察天数',
               existing_server_default=sa.text("'0'"))
    op.alter_column('guanfu_zh_blank_analyze_fund_flow_state', 'consolidation_high',
               existing_type=mysql.FLOAT(),
               nullable=True,
               existing_comment='整固期间最高点',
               existing_server_default=sa.text("'0'"))
    op.alter_column('guanfu_zh_blank_analyze_fund_flow_state', 'consolidation_low',
               existing_type=mysql.FLOAT(),
               nullable=True,
               existing_comment='整固期间最低点',
               existing_server_default=sa.text("'0'"))
    op.drop_index('uq_name_is_concept', table_name='guanfu_zh_blank_analyze_fund_flow_state')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('uq_name_is_concept', 'guanfu_zh_blank_analyze_fund_flow_state', ['name', 'is_concept'], unique=True)
    op.alter_column('guanfu_zh_blank_analyze_fund_flow_state', 'consolidation_low',
               existing_type=mysql.FLOAT(),
               nullable=False,
               existing_comment='整固期间最低点',
               existing_server_default=sa.text("'0'"))
    op.alter_column('guanfu_zh_blank_analyze_fund_flow_state', 'consolidation_high',
               existing_type=mysql.FLOAT(),
               nullable=False,
               existing_comment='整固期间最高点',
               existing_server_default=sa.text("'0'"))
    op.alter_column('guanfu_zh_blank_analyze_fund_flow_state', 'consolidation_days',
               existing_type=mysql.INTEGER(),
               nullable=False,
               existing_comment='整固观察天数',
               existing_server_default=sa.text("'0'"))
    # ### end Alembic commands ###
