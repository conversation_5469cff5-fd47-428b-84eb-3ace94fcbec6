"""添加板块资金流分析状态表

Revision ID: 636501406913
Revises: 7612a3769dec
Create Date: 2025-06-22 17:33:54.148814

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '636501406913'
down_revision: Union[str, None] = '7612a3769dec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('guanfu_zh_blank_analyze_fund_flow_state',
    sa.Column('name', sa.VARCHAR(length=64), nullable=False, comment='blank name'),
    sa.Column('phase', sa.Enum('strong_trend', 'pre_burst', 'early_accum', 'cooldown', 'none', name='phaseenum'), nullable=False, comment='当前阶段'),
    sa.Column('score', sa.Integer(), nullable=True, comment='5日累计得分'),
    sa.Column('days_cnt', sa.Integer(), nullable=True, comment='评分周期天数(0-5)'),
    sa.Column('cooldown_days', sa.Integer(), nullable=True, comment='冷却倒计时'),
    sa.Column('high_since_strong', sa.Float(), nullable=True, comment='强势期以来最高点'),
    sa.Column('drawdown_pct', sa.Float(), nullable=True, comment='回撤百分比(-0.08表示-8%)'),
    sa.Column('strong_days', sa.Integer(), nullable=True, comment='强势天数'),
    sa.Column('outflow_streak', sa.Integer(), nullable=True, comment='资金流出连续天数'),
    sa.Column('last_date', sa.Date(), nullable=True, comment='最近交易日'),
    sa.Column('id', mysql.BIGINT(unsigned=True), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建日期'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新日期'),
    sa.CheckConstraint('drawdown_pct <= 0', name='ck_drawdown_le_zero'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('guanfu_zh_blank_analyze_fund_flow_state')
    # ### end Alembic commands ###
