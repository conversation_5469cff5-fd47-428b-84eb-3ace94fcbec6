#!/usr/bin/env python3
"""
测试实际运行分析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from src.domain.analyze.aggregate.analysis.zh_blank import AnalyzeZhBlankFundFlowAggregate
from src.infra.app import app
from loguru import logger

def test_analysis_run():
    """测试实际运行分析"""
    logger.info("开始测试实际分析运行...")
    
    try:
        # 初始化分析器（行业板块）
        analyzer = AnalyzeZhBlankFundFlowAggregate(is_concept=False)
        
        # 使用较少的天数进行测试，避免API调用过多
        end_date = "2025-07-10"  # 使用昨天的日期
        before_days = 30  # 只使用30天数据进行测试
        
        logger.info(f"开始运行分析: end_date={end_date}, before_days={before_days}")
        
        # 运行完整分析（跳过形态过滤以加快测试）
        result_df = analyzer.run_full_analysis(
            before_days=before_days,
            end_date=end_date,
            skip_pattern_filter=True
        )
        
        logger.info(f"分析完成，结果包含 {len(result_df)} 个板块状态")
        
        # 检查结果中是否有各种状态
        if not result_df.empty:
            phase_counts = result_df["phase"].value_counts()
            logger.info(f"各阶段板块数量: {phase_counts.to_dict()}")
            
            # 检查是否有consolidation状态的板块
            consolidation_count = (result_df["phase"] == "consolidation").sum()
            logger.info(f"consolidation状态板块数量: {consolidation_count}")
            
            # 显示一些样例数据
            if len(result_df) > 0:
                logger.info("前5个板块的状态:")
                sample_df = result_df.head()
                for idx, row in sample_df.iterrows():
                    logger.info(f"  {idx}: {row['phase']}, score={row['score']}, days={row['days_cnt']}")
        
        # 测试获取强势池摘要
        stage4 = analyzer._stage4
        summary = stage4.get_strong_pool_summary(result_df)
        logger.info(f"强势池摘要: {summary}")
        
        logger.info("✓ 实际分析运行测试成功")
        return True
        
    except Exception as e:
        logger.error(f"分析运行测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_consolidation_logic():
    """测试consolidation逻辑"""
    logger.info("开始测试consolidation逻辑...")
    
    try:
        from src.domain.analyze.aggregate.analysis.zh_blank import _Stage4MigrateByScoreAggregate
        import pandas as pd
        
        stage4 = _Stage4MigrateByScoreAggregate(is_concept=False)
        
        # 创建模拟的状态和今日数据
        state = pd.Series({
            "phase": "strong_trend",
            "strong_days": 10,
            "high_since_strong": 100.0,
            "outflow_streak": 0,
            "consolidation_days": 0,
            "consolidation_high": 0.0,
            "consolidation_low": 0.0,
        }, name="测试板块")
        
        # 模拟今日数据（洗盘特征）
        today_row = pd.Series({
            "close": 95.0,  # 5%回调
            "pct_chg": -3.5,  # 大幅下跌
            "vol_ratio_5": 0.7,  # 成交量萎缩
            "FlowSlope": -0.2,  # 资金动能转负但不严重
            "F5": 10.0,  # 5日资金流仍为正
            "F1": -5.0,  # 今日流出
            "MA20": 90.0,
        })
        
        # 模拟前一日数据
        prev_row = pd.Series({
            "pct_chg": -1.0,
            "F1": -2.0,
        })
        
        # 测试是否应该进入consolidation
        should_enter = stage4._should_enter_consolidation(
            state, today_row, prev_row, 15e9
        )
        
        logger.info(f"是否应该进入consolidation: {should_enter}")
        
        if should_enter:
            logger.info("✓ consolidation触发逻辑正常工作")
            
            # 测试consolidation状态更新
            state["phase"] = "consolidation"
            state["consolidation_high"] = 95.0
            state["consolidation_low"] = 95.0
            
            updated_state = stage4._update_consolidation_state(
                state, today_row, date.today()
            )
            
            logger.info(f"更新后的状态: phase={updated_state['phase']}")
            logger.info("✓ consolidation状态更新逻辑正常工作")
        else:
            logger.info("当前条件未触发consolidation，这也是正常的")
        
        logger.info("consolidation逻辑测试完成")
        return True
        
    except Exception as e:
        logger.error(f"consolidation逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("开始consolidation功能实际运行测试")
    logger.info("=" * 60)
    
    # 测试consolidation逻辑
    test1_result = test_consolidation_logic()
    
    # 测试实际分析运行（可能需要较长时间）
    logger.info("\n注意：以下测试可能需要较长时间，因为需要获取真实数据...")
    test2_result = test_analysis_run()
    
    if test1_result and test2_result:
        logger.info("🎉 所有实际运行测试通过！consolidation功能完全正常")
    else:
        logger.error("❌ 部分测试失败，请检查实现")
        sys.exit(1)
