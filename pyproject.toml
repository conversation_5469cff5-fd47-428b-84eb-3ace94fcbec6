[project]
name = "guanfu-trader"
version = "0.1.0"
description = ""
authors = [
    { name = "shadow-yd" }
]
readme = "README.md"
requires-python = "<4.0,>=3.12"
dependencies = [
    "pydantic-settings (>=2.9.1,<3.0.0)",
    "fastapi (>=0.115.12,<0.116.0)",
    "loguru (>=0.7.3,<0.8.0)",
    "apscheduler (>=3.11.0,<4.0.0)",
    "sqlalchemy (>=2.0.40,<3.0.0)",
    "pymysql (>=1.1.1,<2.0.0)",
    "akshare (>=1.16.83,<2.0.0)",
    "ipykernel (>=6.29.5,<7.0.0)",
    "tenacity (>=9.1.2,<10.0.0)",
    "alembic (>=1.15.2,<2.0.0)",
    "tushare (>=1.4.21,<2.0.0)",
    "lark-oapi (>=1.4.15,<2.0.0)",
    "python-socks (>=2.7.1,<3.0.0)",
    "langgraph (>=0.4.3,<0.5.0)",
    "uvicorn (>=0.34.2,<0.35.0)",
    "poetry (>=2.1.3,<3.0.0)",
    "black (>=25.1.0,<26.0.0)",
    "mplfinance (>=0.12.10b0,<0.13.0)",
    "pyecharts (>=2.0.8,<3.0.0)",
    "pyarrow (>=20.0.0,<21.0.0)",
    "scikit-learn (>=1.7.0,<2.0.0)",
]

[tool.poetry]
package-mode = false

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
jupyter = "^1.1.1"
pyecharts = "^2.0.8"
matplotlib = "^3.10.1"
pyppeteer = "^2.0.0"


[[tool.poetry.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
priority = "primary"

