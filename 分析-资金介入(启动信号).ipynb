#%%
# 该方法可能能识别主升浪的前夕
#%%
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Kline, Bar, Line, Scatter, Grid

code = "603899"
kline_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_daily_data.csv"
funds_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_individual_fund_flow.csv"

df_kline = pd.read_csv(kline_path)
df_funds = pd.read_csv(funds_path)

# 数据预处理
df_kline["日期"] = pd.to_datetime(df_kline["trade_date"], format="%Y%m%d")
df_funds["日期"] = pd.to_datetime(df_funds["日期"])
df = (
    pd.merge(df_kline, df_funds, on="日期", how="inner")
    .sort_values("日期")
    .reset_index(drop=True)
)

# 2) 数值化
num_cols = ["pct_chg", "vol", "主力净流入-净占比", "主力净流入-净额"]
df[num_cols] = df[num_cols].apply(pd.to_numeric, errors="coerce")

# ---- 信号计算（保持原公式即可） ---- #
threshold_75 = df["主力净流入-净占比"].quantile(0.75)
df["强势介入"] = (df["主力净流入-净额"] > 0) & (df["主力净流入-净占比"] > threshold_75)
df["异动不涨"]  = df["强势介入"] & (df["pct_chg"].abs() < 1.2)
threshold_drop = 2.0
df["下跌放缓"]  = df["强势介入"] & (df["pct_chg"] < 0) & (df["pct_chg"] > -threshold_drop)
df["信号"] = (df["异动不涨"] | df["下跌放缓"]).astype(int)

N = 2
df["连续信号数"] = df["信号"].rolling(window=N, min_periods=N).sum().fillna(0).astype(int)
df["主力入场信号"] = df["连续信号数"] >= N 
df_sorted = df.sort_values(by="日期", ascending=True)


# 准备图表数据
kline_data = [
    [
        float(df_sorted["open"].iloc[i]),
        float(df_sorted["close"].iloc[i]),
        float(df_sorted["low"].iloc[i]),
        float(df_sorted["high"].iloc[i]),
    ]
    for i in range(len(df_sorted))
]
date_list = df_sorted["日期"].dt.strftime("%Y-%m-%d").tolist()
bar_data = df_sorted["vol"].tolist()
line_data = df_sorted["主力净流入-净占比"].tolist()

# 计算价格范围，用于合理定位信号标记
price_range = df_sorted["high"].max() - df_sorted["low"].min()
signal_offset = price_range * 0.08  # 信号标记距离K线的偏移量（价格范围的8%）

# 分类信号数据 - 调整位置避免遮挡
abnormal_no_rise_data = [
    (
        float(df_sorted["low"].iloc[i]) - signal_offset
        if (df_sorted["主力入场信号"].iloc[i] and df_sorted["异动不涨"].iloc[i])
        else None
    )
    for i in range(len(df_sorted))
]

decline_slow_data = [
    (
        float(df_sorted["low"].iloc[i]) - signal_offset * 1.5
        if (
            df_sorted["主力入场信号"].iloc[i]
            and df_sorted["下跌放缓"].iloc[i]
            and not df_sorted["异动不涨"].iloc[i]
        )
        else None
    )
    for i in range(len(df_sorted))
]

mixed_signal_data = [
    (
        float(df_sorted["low"].iloc[i]) - signal_offset * 2.0
        if (
            df_sorted["主力入场信号"].iloc[i]
            and df_sorted["异动不涨"].iloc[i]
            and df_sorted["下跌放缓"].iloc[i]
        )
        else None
    )
    for i in range(len(df_sorted))
]

# 统计信号数量
abnormal_count = sum(1 for x in abnormal_no_rise_data if x is not None)
decline_count = sum(1 for x in decline_slow_data if x is not None)
mixed_count = sum(1 for x in mixed_signal_data if x is not None)

print(f"信号统计:")
print(f"  异动不涨信号: {abnormal_count} 个")
print(f"  下跌放缓信号: {decline_count} 个")
print(f"  混合信号: {mixed_count} 个")
print(f"  总信号数: {abnormal_count + decline_count + mixed_count} 个")

# K线图
kline = (
    Kline(init_opts=opts.InitOpts(width="1000px", height="500px"))
    .add_xaxis(date_list)
    .add_yaxis(
        "K 线",
        kline_data,
        itemstyle_opts=opts.ItemStyleOpts(
            color="#ef232a",  # 阳线颜色
            color0="#14b143",  # 阴线颜色
            border_color="#ef232a",
            border_color0="#14b143",
        ),
    )
    .set_global_opts(
        xaxis_opts=opts.AxisOpts(
            type_="category", boundary_gap=False, grid_index=0
        ),
        yaxis_opts=opts.AxisOpts(
            is_scale=True,
            splitarea_opts=opts.SplitAreaOpts(
                is_show=True, areastyle_opts=opts.AreaStyleOpts(opacity=1)
            ),
        ),
        title_opts=opts.TitleOpts(title=f"{code} 主力入场信号 K 线图（优化版）"),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
        datazoom_opts=[
            opts.DataZoomOpts(
                is_show=True,
                type_="inside",
                xaxis_index=[0, 1],
                range_start=50,
                range_end=100,
            ),
            opts.DataZoomOpts(
                is_show=True, xaxis_index=[0, 1], range_start=50, range_end=100
            ),
        ],
    )
)

# 成交量柱状图
bar = (
    Bar()
    .add_xaxis(date_list)
    .add_yaxis(
        "成交量",
        bar_data,
        yaxis_index=1,
        xaxis_index=0,
        itemstyle_opts=opts.ItemStyleOpts(color="#7f7f7f"),
        label_opts=opts.LabelOpts(is_show=False),
    )
    .set_global_opts(
        xaxis_opts=opts.AxisOpts(is_show=False),
        yaxis_opts=opts.AxisOpts(
            is_scale=True,
            split_number=2,
            axislabel_opts=opts.LabelOpts(is_show=False),
        ),
        legend_opts=opts.LegendOpts(is_show=False),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
    )
)

# 主力净占比折线图
line = (
    Line()
    .add_xaxis(date_list)
    .add_yaxis(
        "主力净占比",
        line_data,
        yaxis_index=2,
        xaxis_index=0,
        linestyle_opts=opts.LineStyleOpts(color="#5470C6", width=2),
        label_opts=opts.LabelOpts(is_show=False),
    )
    .set_global_opts(
        xaxis_opts=opts.AxisOpts(is_show=False),
        yaxis_opts=opts.AxisOpts(
            is_scale=True,
            position="right",
            splitline_opts=opts.SplitLineOpts(is_show=False),
        ),
        legend_opts=opts.LegendOpts(is_show=False),
        tooltip_opts=opts.TooltipOpts(trigger="axis"),
    )
)

# 分类信号散点图 - 优化视觉效果
scatter_abnormal = (
    Scatter()
    .add_xaxis(date_list)
    .add_yaxis(
        "异动不涨信号",
        abnormal_no_rise_data,
        symbol="triangle",
        symbol_size=16,  # 增大尺寸
        itemstyle_opts=opts.ItemStyleOpts(
            color="#FF3333", border_color="#990000", border_width=3, opacity=0.9
        ),
    )
)

scatter_decline = (
    Scatter()
    .add_xaxis(date_list)
    .add_yaxis(
        "下跌放缓信号",
        decline_slow_data,
        symbol="roundRect",  # 使用更明显的形状
        symbol_size=16,
        itemstyle_opts=opts.ItemStyleOpts(
            color="#00CC44", border_color="#004422", border_width=3, opacity=0.9
        ),
    )
)

scatter_mixed = (
    Scatter()
    .add_xaxis(date_list)
    .add_yaxis(
        "混合信号",
        mixed_signal_data,
        symbol="diamond",
        symbol_size=16,  # 混合信号最大
        itemstyle_opts=opts.ItemStyleOpts(
            color="#BB33FF", border_color="#660099", border_width=3, opacity=0.95
        ),
    )
)

# 叠加散点图到K线图
kline = (
    kline.overlap(scatter_abnormal).overlap(scatter_decline).overlap(scatter_mixed)
)

# 组合图表
grid = (
    Grid(init_opts=opts.InitOpts(width="100%", height="700px"))
    .add(
        kline,
        grid_index=0,
        grid_opts=opts.GridOpts(
            pos_left="10%", pos_right="8%", pos_top="5%", height="40%"
        ),
    )
    .add(
        bar,
        grid_index=1,
        grid_opts=opts.GridOpts(
            pos_left="10%", pos_right="8%", pos_top="50%", height="15%"
        ),
    )
    .add(
        line,
        grid_index=2,
        grid_opts=opts.GridOpts(
            pos_left="10%", pos_right="8%", pos_top="70%", height="15%"
        ),
    )
)

# 导出HTML文件
html_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_build_signal.html"
grid.render(html_path)

#%%

#%%
