[app]
name = "GuanFu-Trader"
host = "0.0.0.0"
log_level = "DEBUG"
thread_pool_size = 100
http_port = 9999
http_openapi_url = "/openapi.json"
http_docs_url = "/docs"


[lark]
[lark.trading]
app_id = "cli_a68538e79e78500d"
app_secret = "dicjzzlsfZHg0lF2eQEUobBYjjbDNnf3"
encrypt_key = "guanfu"
verification_token = "1twWwGya6ElyG93FUbhCrfQ3mKXe6c1L"
user_id = "23b3ebg1"
system_alert_tmpl_id = "AAq3y5s2FkctM"
notice_tmpl_id = "AAq4JSpThrcrC"
notice_success_tmpl_id = "AAq4asTwu6gyN"
notice_fail_tmpl_id = "AAq4asQYXaKil"
monitor_price_tmpl_id = "AAq4lV0SFr7VL"
monitor_5_min_volume_tmpl_id = "AAqdyK7vVVK4S"
monitor_1_min_volume_tmpl_id = "AAqIUF5plWMYW"
monitor_price_abnormal_tmpl_id = "AAqdRbsjUXOgk"

[lark.tt]
app_id = "cli_a3abf73e30b91013"
app_secret = "bFlLpTgvyyJVGsmKi0ZSodDxW2JuWV2z"
encrypt_key = ""
verification_token = ""
user_id = "166488913"
system_alert_tmpl_id = "AAq3y5s2FkctM"
notice_tmpl_id = "AAq4JSpThrcrC"
notice_success_tmpl_id = "AAq4JSpThrcrC"
notice_fail_tmpl_id = "AAq4JSpThrcrC"
monitor_price_tmpl_id = "AAq4lV0SFr7VL"
monitor_5_min_volume_tmpl_id = "AAqdyK7vVVK4S"
monitor_1_min_volume_tmpl_id = "AAqIUF5plWMYW"
monitor_price_abnormal_tmpl_id = "AAqdRbsjUXOgk"



[mysql]
host = "rm-2ze0h3019sv6opc15xo.mysql.rds.aliyuncs.com"
port = 3306
user = "guanfu"
password = "Shadow81608067"
db = "guanfu"
charset = "utf8mb4"
conn_timeout = 10
debug = false

# 连接池配置 - 为2核2G MySQL优化
pool_size = 10
max_overflow = 20
pool_recycle = 1800
pool_timeout = 30
pool_pre_ping = true