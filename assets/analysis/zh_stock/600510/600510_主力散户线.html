<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="861b95b5b13a4a5db631360c0c176131" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_861b95b5b13a4a5db631360c0c176131 = echarts.init(
            document.getElementById('861b95b5b13a4a5db631360c0c176131'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_861b95b5b13a4a5db631360c0c176131 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    4.65,
                    4.64,
                    4.64,
                    4.75
                ],
                [
                    4.68,
                    4.72,
                    4.63,
                    4.74
                ],
                [
                    4.71,
                    4.72,
                    4.65,
                    4.72
                ],
                [
                    4.79,
                    4.78,
                    4.72,
                    4.82
                ],
                [
                    4.79,
                    4.8,
                    4.77,
                    4.84
                ],
                [
                    4.83,
                    4.75,
                    4.71,
                    4.84
                ],
                [
                    4.78,
                    4.82,
                    4.74,
                    4.83
                ],
                [
                    4.82,
                    4.83,
                    4.81,
                    4.89
                ],
                [
                    4.83,
                    4.73,
                    4.7,
                    4.87
                ],
                [
                    4.75,
                    4.78,
                    4.7,
                    4.83
                ],
                [
                    4.8,
                    4.64,
                    4.62,
                    4.8
                ],
                [
                    4.64,
                    4.65,
                    4.61,
                    4.66
                ],
                [
                    4.65,
                    4.66,
                    4.6,
                    4.69
                ],
                [
                    4.69,
                    4.68,
                    4.59,
                    4.71
                ],
                [
                    4.67,
                    4.84,
                    4.67,
                    4.85
                ],
                [
                    4.86,
                    4.94,
                    4.79,
                    5.04
                ],
                [
                    4.96,
                    5.03,
                    4.95,
                    5.09
                ],
                [
                    5.05,
                    5.02,
                    4.95,
                    5.1
                ],
                [
                    5.0,
                    4.96,
                    4.95,
                    5.06
                ],
                [
                    4.98,
                    4.96,
                    4.94,
                    5.06
                ],
                [
                    4.97,
                    5.12,
                    4.97,
                    5.14
                ],
                [
                    5.12,
                    5.1,
                    5.06,
                    5.18
                ],
                [
                    5.11,
                    5.22,
                    5.07,
                    5.25
                ],
                [
                    5.19,
                    5.24,
                    5.14,
                    5.35
                ],
                [
                    5.28,
                    5.27,
                    5.19,
                    5.33
                ],
                [
                    5.22,
                    5.28,
                    5.17,
                    5.3
                ],
                [
                    5.28,
                    5.34,
                    5.28,
                    5.47
                ],
                [
                    5.34,
                    5.28,
                    5.19,
                    5.35
                ],
                [
                    5.3,
                    5.34,
                    5.23,
                    5.38
                ],
                [
                    5.36,
                    5.47,
                    5.34,
                    5.59
                ],
                [
                    5.46,
                    5.46,
                    5.41,
                    5.51
                ],
                [
                    5.47,
                    5.38,
                    5.35,
                    5.47
                ],
                [
                    5.39,
                    5.35,
                    5.33,
                    5.43
                ],
                [
                    5.34,
                    5.28,
                    5.23,
                    5.36
                ],
                [
                    5.25,
                    5.18,
                    5.06,
                    5.28
                ],
                [
                    5.17,
                    5.18,
                    5.12,
                    5.22
                ],
                [
                    5.22,
                    5.25,
                    5.16,
                    5.29
                ],
                [
                    5.25,
                    5.19,
                    5.17,
                    5.29
                ],
                [
                    5.19,
                    5.14,
                    5.12,
                    5.21
                ],
                [
                    5.15,
                    5.12,
                    5.04,
                    5.16
                ],
                [
                    5.13,
                    5.14,
                    5.1,
                    5.22
                ],
                [
                    5.14,
                    5.11,
                    5.09,
                    5.17
                ],
                [
                    5.11,
                    5.18,
                    5.07,
                    5.22
                ],
                [
                    5.0,
                    4.66,
                    4.66,
                    5.0
                ],
                [
                    4.65,
                    4.42,
                    4.34,
                    4.65
                ],
                [
                    4.35,
                    4.55,
                    4.16,
                    4.59
                ],
                [
                    4.57,
                    4.74,
                    4.57,
                    4.8
                ],
                [
                    4.7,
                    4.75,
                    4.68,
                    4.79
                ],
                [
                    4.78,
                    4.79,
                    4.71,
                    4.91
                ],
                [
                    4.79,
                    4.82,
                    4.72,
                    4.88
                ],
                [
                    4.75,
                    4.75,
                    4.73,
                    5.01
                ],
                [
                    4.77,
                    4.84,
                    4.73,
                    4.92
                ],
                [
                    4.91,
                    5.32,
                    4.84,
                    5.32
                ],
                [
                    5.4,
                    5.14,
                    5.1,
                    5.41
                ],
                [
                    5.1,
                    5.32,
                    5.08,
                    5.62
                ],
                [
                    5.33,
                    5.23,
                    5.22,
                    5.45
                ],
                [
                    5.19,
                    5.31,
                    5.19,
                    5.42
                ],
                [
                    5.37,
                    5.32,
                    5.3,
                    5.57
                ],
                [
                    5.29,
                    5.2,
                    5.14,
                    5.36
                ],
                [
                    5.18,
                    5.23,
                    5.16,
                    5.25
                ],
                [
                    5.22,
                    5.21,
                    5.15,
                    5.28
                ],
                [
                    5.2,
                    5.26,
                    5.17,
                    5.28
                ],
                [
                    5.32,
                    5.27,
                    5.24,
                    5.39
                ],
                [
                    5.26,
                    5.28,
                    5.22,
                    5.29
                ],
                [
                    5.3,
                    5.23,
                    5.2,
                    5.3
                ],
                [
                    5.28,
                    5.06,
                    5.04,
                    5.29
                ],
                [
                    5.1,
                    5.02,
                    4.99,
                    5.16
                ],
                [
                    5.07,
                    5.02,
                    5.0,
                    5.07
                ],
                [
                    5.04,
                    4.97,
                    4.96,
                    5.04
                ],
                [
                    5.01,
                    4.96,
                    4.95,
                    5.01
                ],
                [
                    4.96,
                    5.05,
                    4.94,
                    5.07
                ],
                [
                    5.05,
                    5.03,
                    4.99,
                    5.08
                ],
                [
                    5.03,
                    4.99,
                    4.97,
                    5.05
                ],
                [
                    5.01,
                    4.92,
                    4.91,
                    5.03
                ],
                [
                    4.91,
                    4.88,
                    4.86,
                    4.96
                ],
                [
                    4.89,
                    4.9,
                    4.86,
                    4.93
                ],
                [
                    4.9,
                    4.91,
                    4.84,
                    4.93
                ],
                [
                    4.91,
                    4.89,
                    4.87,
                    4.95
                ],
                [
                    4.89,
                    4.9,
                    4.87,
                    4.93
                ],
                [
                    4.9,
                    4.92,
                    4.86,
                    4.92
                ],
                [
                    4.88,
                    4.84,
                    4.84,
                    4.92
                ],
                [
                    4.86,
                    4.97,
                    4.84,
                    4.97
                ],
                [
                    4.95,
                    4.96,
                    4.92,
                    5.07
                ],
                [
                    4.94,
                    4.98,
                    4.94,
                    5.0
                ],
                [
                    4.98,
                    5.3,
                    4.98,
                    5.41
                ],
                [
                    5.28,
                    5.25,
                    5.18,
                    5.32
                ],
                [
                    5.24,
                    5.32,
                    5.21,
                    5.34
                ],
                [
                    5.33,
                    5.25,
                    5.23,
                    5.33
                ],
                [
                    5.25,
                    5.04,
                    5.02,
                    5.27
                ],
                [
                    5.07,
                    5.11,
                    5.07,
                    5.2
                ],
                [
                    5.12,
                    5.14,
                    5.1,
                    5.19
                ],
                [
                    5.12,
                    5.05,
                    4.99,
                    5.14
                ],
                [
                    5.04,
                    4.88,
                    4.86,
                    5.07
                ],
                [
                    4.89,
                    4.88,
                    4.88,
                    4.94
                ],
                [
                    4.86,
                    4.88,
                    4.83,
                    4.9
                ],
                [
                    4.9,
                    4.97,
                    4.87,
                    4.99
                ],
                [
                    4.99,
                    5.06,
                    4.97,
                    5.06
                ],
                [
                    5.05,
                    5.06,
                    5.0,
                    5.08
                ],
                [
                    5.05,
                    5.07,
                    5.03,
                    5.14
                ],
                [
                    5.09,
                    5.09,
                    5.05,
                    5.12
                ],
                [
                    5.1,
                    5.03,
                    4.99,
                    5.1
                ],
                [
                    5.02,
                    5.03,
                    4.99,
                    5.06
                ],
                [
                    5.03,
                    5.02,
                    5.0,
                    5.07
                ],
                [
                    5.05,
                    5.03,
                    4.99,
                    5.05
                ],
                [
                    5.0,
                    5.07,
                    4.99,
                    5.1
                ],
                [
                    5.08,
                    5.13,
                    5.06,
                    5.13
                ],
                [
                    5.13,
                    5.12,
                    5.1,
                    5.15
                ],
                [
                    5.13,
                    5.25,
                    5.12,
                    5.28
                ],
                [
                    5.3,
                    5.32,
                    5.25,
                    5.4
                ],
                [
                    5.32,
                    5.26,
                    5.22,
                    5.39
                ],
                [
                    5.23,
                    5.2,
                    5.13,
                    5.26
                ],
                [
                    5.19,
                    5.2,
                    5.15,
                    5.22
                ],
                [
                    5.2,
                    5.15,
                    5.13,
                    5.22
                ],
                [
                    5.18,
                    5.14,
                    5.12,
                    5.19
                ],
                [
                    5.13,
                    5.22,
                    5.13,
                    5.22
                ],
                [
                    5.23,
                    5.26,
                    5.16,
                    5.27
                ],
                [
                    5.26,
                    5.24,
                    5.22,
                    5.32
                ],
                [
                    5.25,
                    5.37,
                    5.22,
                    5.4
                ],
                [
                    5.38,
                    5.45,
                    5.33,
                    5.5
                ],
                [
                    5.46,
                    5.49,
                    5.41,
                    5.53
                ],
                [
                    5.49,
                    5.48,
                    5.43,
                    5.59
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -5.86
                ],
                [
                    "2025-02-05",
                    -17.46
                ],
                [
                    "2025-02-06",
                    -2.36
                ],
                [
                    "2025-02-07",
                    -6.77
                ],
                [
                    "2025-02-10",
                    -18.82
                ],
                [
                    "2025-02-11",
                    -9.13
                ],
                [
                    "2025-02-12",
                    2.32
                ],
                [
                    "2025-02-13",
                    -10.58
                ],
                [
                    "2025-02-14",
                    -19.40
                ],
                [
                    "2025-02-17",
                    -7.58
                ],
                [
                    "2025-02-18",
                    -8.69
                ],
                [
                    "2025-02-19",
                    -7.06
                ],
                [
                    "2025-02-20",
                    -0.16
                ],
                [
                    "2025-02-21",
                    -3.60
                ],
                [
                    "2025-02-24",
                    1.91
                ],
                [
                    "2025-02-25",
                    10.52
                ],
                [
                    "2025-02-26",
                    7.20
                ],
                [
                    "2025-02-27",
                    -8.06
                ],
                [
                    "2025-02-28",
                    4.52
                ],
                [
                    "2025-03-03",
                    8.10
                ],
                [
                    "2025-03-04",
                    12.90
                ],
                [
                    "2025-03-05",
                    14.83
                ],
                [
                    "2025-03-06",
                    -12.83
                ],
                [
                    "2025-03-07",
                    12.63
                ],
                [
                    "2025-03-10",
                    4.74
                ],
                [
                    "2025-03-11",
                    7.10
                ],
                [
                    "2025-03-12",
                    0.64
                ],
                [
                    "2025-03-13",
                    7.72
                ],
                [
                    "2025-03-14",
                    7.78
                ],
                [
                    "2025-03-17",
                    -0.83
                ],
                [
                    "2025-03-18",
                    5.15
                ],
                [
                    "2025-03-19",
                    -18.55
                ],
                [
                    "2025-03-20",
                    -8.02
                ],
                [
                    "2025-03-21",
                    -16.22
                ],
                [
                    "2025-03-24",
                    -8.86
                ],
                [
                    "2025-03-25",
                    5.11
                ],
                [
                    "2025-03-26",
                    -6.40
                ],
                [
                    "2025-03-27",
                    3.74
                ],
                [
                    "2025-03-28",
                    -6.73
                ],
                [
                    "2025-03-31",
                    -7.92
                ],
                [
                    "2025-04-01",
                    -4.51
                ],
                [
                    "2025-04-02",
                    -12.70
                ],
                [
                    "2025-04-03",
                    4.08
                ],
                [
                    "2025-04-07",
                    -3.44
                ],
                [
                    "2025-04-08",
                    -1.41
                ],
                [
                    "2025-04-09",
                    -5.81
                ],
                [
                    "2025-04-10",
                    -0.86
                ],
                [
                    "2025-04-11",
                    -6.71
                ],
                [
                    "2025-04-14",
                    10.87
                ],
                [
                    "2025-04-15",
                    7.86
                ],
                [
                    "2025-04-16",
                    4.70
                ],
                [
                    "2025-04-17",
                    -0.67
                ],
                [
                    "2025-04-18",
                    18.92
                ],
                [
                    "2025-04-21",
                    -11.18
                ],
                [
                    "2025-04-22",
                    -1.24
                ],
                [
                    "2025-04-23",
                    -11.25
                ],
                [
                    "2025-04-24",
                    6.04
                ],
                [
                    "2025-04-25",
                    -5.32
                ],
                [
                    "2025-04-28",
                    3.93
                ],
                [
                    "2025-04-29",
                    -4.65
                ],
                [
                    "2025-04-30",
                    7.90
                ],
                [
                    "2025-05-06",
                    5.49
                ],
                [
                    "2025-05-07",
                    -10.05
                ],
                [
                    "2025-05-08",
                    -10.16
                ],
                [
                    "2025-05-09",
                    -17.64
                ],
                [
                    "2025-05-12",
                    -33.22
                ],
                [
                    "2025-05-13",
                    -20.44
                ],
                [
                    "2025-05-14",
                    -15.73
                ],
                [
                    "2025-05-15",
                    -4.26
                ],
                [
                    "2025-05-16",
                    9.97
                ],
                [
                    "2025-05-19",
                    -6.00
                ],
                [
                    "2025-05-20",
                    -2.76
                ],
                [
                    "2025-05-21",
                    -2.91
                ],
                [
                    "2025-05-22",
                    2.33
                ],
                [
                    "2025-05-23",
                    1.04
                ],
                [
                    "2025-05-26",
                    -3.82
                ],
                [
                    "2025-05-27",
                    -2.90
                ],
                [
                    "2025-05-28",
                    -1.46
                ],
                [
                    "2025-05-29",
                    0.86
                ],
                [
                    "2025-05-30",
                    -11.99
                ],
                [
                    "2025-06-03",
                    -20.21
                ],
                [
                    "2025-06-04",
                    9.51
                ],
                [
                    "2025-06-05",
                    1.08
                ],
                [
                    "2025-06-06",
                    -0.10
                ],
                [
                    "2025-06-09",
                    5.20
                ],
                [
                    "2025-06-10",
                    -12.27
                ],
                [
                    "2025-06-11",
                    -16.35
                ],
                [
                    "2025-06-12",
                    -7.09
                ],
                [
                    "2025-06-13",
                    -17.97
                ],
                [
                    "2025-06-16",
                    -1.22
                ],
                [
                    "2025-06-17",
                    -1.63
                ],
                [
                    "2025-06-18",
                    -2.86
                ],
                [
                    "2025-06-19",
                    1.97
                ],
                [
                    "2025-06-20",
                    9.22
                ],
                [
                    "2025-06-23",
                    -14.46
                ],
                [
                    "2025-06-24",
                    3.23
                ],
                [
                    "2025-06-25",
                    -10.34
                ],
                [
                    "2025-06-26",
                    -7.80
                ],
                [
                    "2025-06-27",
                    -10.06
                ],
                [
                    "2025-06-30",
                    3.25
                ],
                [
                    "2025-07-01",
                    -3.70
                ],
                [
                    "2025-07-02",
                    -26.38
                ],
                [
                    "2025-07-03",
                    -14.24
                ],
                [
                    "2025-07-04",
                    9.81
                ],
                [
                    "2025-07-07",
                    -12.48
                ],
                [
                    "2025-07-08",
                    -23.90
                ],
                [
                    "2025-07-09",
                    -10.17
                ],
                [
                    "2025-07-10",
                    -17.62
                ],
                [
                    "2025-07-11",
                    -18.27
                ],
                [
                    "2025-07-14",
                    -9.41
                ],
                [
                    "2025-07-15",
                    -17.70
                ],
                [
                    "2025-07-16",
                    -11.05
                ],
                [
                    "2025-07-17",
                    8.57
                ],
                [
                    "2025-07-18",
                    -2.19
                ],
                [
                    "2025-07-21",
                    -2.79
                ],
                [
                    "2025-07-22",
                    -2.53
                ],
                [
                    "2025-07-23",
                    -3.83
                ],
                [
                    "2025-07-24",
                    -0.12
                ],
                [
                    "2025-07-25",
                    5.33
                ],
                [
                    "2025-07-28",
                    -0.94
                ],
                [
                    "2025-07-29",
                    -8.08
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -5.12
                ],
                [
                    "2025-02-05",
                    -6.44
                ],
                [
                    "2025-02-06",
                    -0.03
                ],
                [
                    "2025-02-07",
                    3.12
                ],
                [
                    "2025-02-10",
                    -5.69
                ],
                [
                    "2025-02-11",
                    0.31
                ],
                [
                    "2025-02-12",
                    -7.55
                ],
                [
                    "2025-02-13",
                    -2.72
                ],
                [
                    "2025-02-14",
                    11.66
                ],
                [
                    "2025-02-17",
                    -12.13
                ],
                [
                    "2025-02-18",
                    6.24
                ],
                [
                    "2025-02-19",
                    12.06
                ],
                [
                    "2025-02-20",
                    -9.39
                ],
                [
                    "2025-02-21",
                    -0.61
                ],
                [
                    "2025-02-24",
                    -5.60
                ],
                [
                    "2025-02-25",
                    3.45
                ],
                [
                    "2025-02-26",
                    -8.58
                ],
                [
                    "2025-02-27",
                    -0.05
                ],
                [
                    "2025-02-28",
                    -4.09
                ],
                [
                    "2025-03-03",
                    5.67
                ],
                [
                    "2025-03-04",
                    -4.60
                ],
                [
                    "2025-03-05",
                    -1.78
                ],
                [
                    "2025-03-06",
                    5.72
                ],
                [
                    "2025-03-07",
                    -4.56
                ],
                [
                    "2025-03-10",
                    0.33
                ],
                [
                    "2025-03-11",
                    -6.52
                ],
                [
                    "2025-03-12",
                    1.96
                ],
                [
                    "2025-03-13",
                    -5.24
                ],
                [
                    "2025-03-14",
                    -7.84
                ],
                [
                    "2025-03-17",
                    2.24
                ],
                [
                    "2025-03-18",
                    2.00
                ],
                [
                    "2025-03-19",
                    8.27
                ],
                [
                    "2025-03-20",
                    0.30
                ],
                [
                    "2025-03-21",
                    -4.31
                ],
                [
                    "2025-03-24",
                    -0.23
                ],
                [
                    "2025-03-25",
                    -1.86
                ],
                [
                    "2025-03-26",
                    0.75
                ],
                [
                    "2025-03-27",
                    5.56
                ],
                [
                    "2025-03-28",
                    -6.43
                ],
                [
                    "2025-03-31",
                    7.41
                ],
                [
                    "2025-04-01",
                    12.38
                ],
                [
                    "2025-04-02",
                    3.50
                ],
                [
                    "2025-04-03",
                    -5.98
                ],
                [
                    "2025-04-07",
                    0.96
                ],
                [
                    "2025-04-08",
                    -4.66
                ],
                [
                    "2025-04-09",
                    0.24
                ],
                [
                    "2025-04-10",
                    -2.03
                ],
                [
                    "2025-04-11",
                    0.95
                ],
                [
                    "2025-04-14",
                    0.98
                ],
                [
                    "2025-04-15",
                    0.91
                ],
                [
                    "2025-04-16",
                    11.83
                ],
                [
                    "2025-04-17",
                    -2.29
                ],
                [
                    "2025-04-18",
                    -9.71
                ],
                [
                    "2025-04-21",
                    18.15
                ],
                [
                    "2025-04-22",
                    2.11
                ],
                [
                    "2025-04-23",
                    4.34
                ],
                [
                    "2025-04-24",
                    -7.07
                ],
                [
                    "2025-04-25",
                    -0.11
                ],
                [
                    "2025-04-28",
                    -7.17
                ],
                [
                    "2025-04-29",
                    -0.02
                ],
                [
                    "2025-04-30",
                    -0.30
                ],
                [
                    "2025-05-06",
                    2.55
                ],
                [
                    "2025-05-07",
                    1.84
                ],
                [
                    "2025-05-08",
                    2.35
                ],
                [
                    "2025-05-09",
                    5.29
                ],
                [
                    "2025-05-12",
                    15.37
                ],
                [
                    "2025-05-13",
                    6.38
                ],
                [
                    "2025-05-14",
                    7.31
                ],
                [
                    "2025-05-15",
                    9.43
                ],
                [
                    "2025-05-16",
                    -2.93
                ],
                [
                    "2025-05-19",
                    4.04
                ],
                [
                    "2025-05-20",
                    -2.46
                ],
                [
                    "2025-05-21",
                    6.42
                ],
                [
                    "2025-05-22",
                    8.34
                ],
                [
                    "2025-05-23",
                    1.25
                ],
                [
                    "2025-05-26",
                    4.89
                ],
                [
                    "2025-05-27",
                    5.16
                ],
                [
                    "2025-05-28",
                    12.06
                ],
                [
                    "2025-05-29",
                    7.74
                ],
                [
                    "2025-05-30",
                    13.56
                ],
                [
                    "2025-06-03",
                    14.48
                ],
                [
                    "2025-06-04",
                    -3.04
                ],
                [
                    "2025-06-05",
                    9.68
                ],
                [
                    "2025-06-06",
                    -2.03
                ],
                [
                    "2025-06-09",
                    -1.44
                ],
                [
                    "2025-06-10",
                    -2.54
                ],
                [
                    "2025-06-11",
                    6.04
                ],
                [
                    "2025-06-12",
                    4.74
                ],
                [
                    "2025-06-13",
                    15.85
                ],
                [
                    "2025-06-16",
                    -1.96
                ],
                [
                    "2025-06-17",
                    1.50
                ],
                [
                    "2025-06-18",
                    8.00
                ],
                [
                    "2025-06-19",
                    7.84
                ],
                [
                    "2025-06-20",
                    4.96
                ],
                [
                    "2025-06-23",
                    19.07
                ],
                [
                    "2025-06-24",
                    2.20
                ],
                [
                    "2025-06-25",
                    5.45
                ],
                [
                    "2025-06-26",
                    6.38
                ],
                [
                    "2025-06-27",
                    8.71
                ],
                [
                    "2025-06-30",
                    -2.36
                ],
                [
                    "2025-07-01",
                    5.71
                ],
                [
                    "2025-07-02",
                    2.21
                ],
                [
                    "2025-07-03",
                    9.85
                ],
                [
                    "2025-07-04",
                    -5.73
                ],
                [
                    "2025-07-07",
                    -2.96
                ],
                [
                    "2025-07-08",
                    -1.38
                ],
                [
                    "2025-07-09",
                    -1.17
                ],
                [
                    "2025-07-10",
                    3.25
                ],
                [
                    "2025-07-11",
                    -1.51
                ],
                [
                    "2025-07-14",
                    -4.74
                ],
                [
                    "2025-07-15",
                    7.13
                ],
                [
                    "2025-07-16",
                    -3.91
                ],
                [
                    "2025-07-17",
                    11.74
                ],
                [
                    "2025-07-18",
                    12.16
                ],
                [
                    "2025-07-21",
                    -0.06
                ],
                [
                    "2025-07-22",
                    -1.31
                ],
                [
                    "2025-07-23",
                    -2.81
                ],
                [
                    "2025-07-24",
                    -1.80
                ],
                [
                    "2025-07-25",
                    0.31
                ],
                [
                    "2025-07-28",
                    2.05
                ],
                [
                    "2025-07-29",
                    -6.80
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    10.99
                ],
                [
                    "2025-02-05",
                    23.90
                ],
                [
                    "2025-02-06",
                    2.39
                ],
                [
                    "2025-02-07",
                    3.64
                ],
                [
                    "2025-02-10",
                    24.52
                ],
                [
                    "2025-02-11",
                    8.81
                ],
                [
                    "2025-02-12",
                    5.22
                ],
                [
                    "2025-02-13",
                    13.30
                ],
                [
                    "2025-02-14",
                    7.75
                ],
                [
                    "2025-02-17",
                    19.70
                ],
                [
                    "2025-02-18",
                    2.45
                ],
                [
                    "2025-02-19",
                    -5.00
                ],
                [
                    "2025-02-20",
                    9.55
                ],
                [
                    "2025-02-21",
                    4.21
                ],
                [
                    "2025-02-24",
                    3.69
                ],
                [
                    "2025-02-25",
                    -13.97
                ],
                [
                    "2025-02-26",
                    1.37
                ],
                [
                    "2025-02-27",
                    8.10
                ],
                [
                    "2025-02-28",
                    -0.42
                ],
                [
                    "2025-03-03",
                    -13.77
                ],
                [
                    "2025-03-04",
                    -8.30
                ],
                [
                    "2025-03-05",
                    -13.05
                ],
                [
                    "2025-03-06",
                    7.12
                ],
                [
                    "2025-03-07",
                    -8.07
                ],
                [
                    "2025-03-10",
                    -5.06
                ],
                [
                    "2025-03-11",
                    -0.58
                ],
                [
                    "2025-03-12",
                    -2.60
                ],
                [
                    "2025-03-13",
                    -2.48
                ],
                [
                    "2025-03-14",
                    0.06
                ],
                [
                    "2025-03-17",
                    -1.41
                ],
                [
                    "2025-03-18",
                    -7.15
                ],
                [
                    "2025-03-19",
                    10.28
                ],
                [
                    "2025-03-20",
                    7.72
                ],
                [
                    "2025-03-21",
                    20.53
                ],
                [
                    "2025-03-24",
                    9.09
                ],
                [
                    "2025-03-25",
                    -3.26
                ],
                [
                    "2025-03-26",
                    5.66
                ],
                [
                    "2025-03-27",
                    -9.30
                ],
                [
                    "2025-03-28",
                    13.16
                ],
                [
                    "2025-03-31",
                    0.51
                ],
                [
                    "2025-04-01",
                    -7.87
                ],
                [
                    "2025-04-02",
                    9.20
                ],
                [
                    "2025-04-03",
                    1.90
                ],
                [
                    "2025-04-07",
                    2.49
                ],
                [
                    "2025-04-08",
                    6.07
                ],
                [
                    "2025-04-09",
                    5.57
                ],
                [
                    "2025-04-10",
                    2.88
                ],
                [
                    "2025-04-11",
                    5.76
                ],
                [
                    "2025-04-14",
                    -11.84
                ],
                [
                    "2025-04-15",
                    -8.77
                ],
                [
                    "2025-04-16",
                    -16.52
                ],
                [
                    "2025-04-17",
                    2.96
                ],
                [
                    "2025-04-18",
                    -9.22
                ],
                [
                    "2025-04-21",
                    -6.97
                ],
                [
                    "2025-04-22",
                    -0.87
                ],
                [
                    "2025-04-23",
                    6.91
                ],
                [
                    "2025-04-24",
                    1.03
                ],
                [
                    "2025-04-25",
                    5.43
                ],
                [
                    "2025-04-28",
                    3.24
                ],
                [
                    "2025-04-29",
                    4.66
                ],
                [
                    "2025-04-30",
                    -7.60
                ],
                [
                    "2025-05-06",
                    -8.03
                ],
                [
                    "2025-05-07",
                    8.21
                ],
                [
                    "2025-05-08",
                    7.81
                ],
                [
                    "2025-05-09",
                    12.35
                ],
                [
                    "2025-05-12",
                    17.85
                ],
                [
                    "2025-05-13",
                    14.06
                ],
                [
                    "2025-05-14",
                    8.42
                ],
                [
                    "2025-05-15",
                    -5.17
                ],
                [
                    "2025-05-16",
                    -7.04
                ],
                [
                    "2025-05-19",
                    1.96
                ],
                [
                    "2025-05-20",
                    5.23
                ],
                [
                    "2025-05-21",
                    -3.51
                ],
                [
                    "2025-05-22",
                    -10.67
                ],
                [
                    "2025-05-23",
                    -2.28
                ],
                [
                    "2025-05-26",
                    -1.07
                ],
                [
                    "2025-05-27",
                    -2.25
                ],
                [
                    "2025-05-28",
                    -10.60
                ],
                [
                    "2025-05-29",
                    -8.60
                ],
                [
                    "2025-05-30",
                    -1.57
                ],
                [
                    "2025-06-03",
                    5.74
                ],
                [
                    "2025-06-04",
                    -6.47
                ],
                [
                    "2025-06-05",
                    -10.76
                ],
                [
                    "2025-06-06",
                    2.13
                ],
                [
                    "2025-06-09",
                    -3.76
                ],
                [
                    "2025-06-10",
                    14.82
                ],
                [
                    "2025-06-11",
                    10.31
                ],
                [
                    "2025-06-12",
                    2.35
                ],
                [
                    "2025-06-13",
                    2.12
                ],
                [
                    "2025-06-16",
                    3.18
                ],
                [
                    "2025-06-17",
                    0.12
                ],
                [
                    "2025-06-18",
                    -5.15
                ],
                [
                    "2025-06-19",
                    -9.81
                ],
                [
                    "2025-06-20",
                    -14.18
                ],
                [
                    "2025-06-23",
                    -4.61
                ],
                [
                    "2025-06-24",
                    -5.43
                ],
                [
                    "2025-06-25",
                    4.89
                ],
                [
                    "2025-06-26",
                    1.42
                ],
                [
                    "2025-06-27",
                    1.34
                ],
                [
                    "2025-06-30",
                    -0.89
                ],
                [
                    "2025-07-01",
                    -2.00
                ],
                [
                    "2025-07-02",
                    24.17
                ],
                [
                    "2025-07-03",
                    4.39
                ],
                [
                    "2025-07-04",
                    -4.08
                ],
                [
                    "2025-07-07",
                    15.44
                ],
                [
                    "2025-07-08",
                    25.28
                ],
                [
                    "2025-07-09",
                    11.34
                ],
                [
                    "2025-07-10",
                    14.37
                ],
                [
                    "2025-07-11",
                    19.78
                ],
                [
                    "2025-07-14",
                    14.15
                ],
                [
                    "2025-07-15",
                    10.57
                ],
                [
                    "2025-07-16",
                    14.95
                ],
                [
                    "2025-07-17",
                    -20.32
                ],
                [
                    "2025-07-18",
                    -9.97
                ],
                [
                    "2025-07-21",
                    2.85
                ],
                [
                    "2025-07-22",
                    3.84
                ],
                [
                    "2025-07-23",
                    6.64
                ],
                [
                    "2025-07-24",
                    1.91
                ],
                [
                    "2025-07-25",
                    -5.65
                ],
                [
                    "2025-07-28",
                    -1.11
                ],
                [
                    "2025-07-29",
                    14.89
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-25",
                    10.52
                ],
                [
                    "2025-02-28",
                    4.52
                ],
                [
                    "2025-03-03",
                    8.10
                ],
                [
                    "2025-03-04",
                    12.90
                ],
                [
                    "2025-03-05",
                    14.83
                ],
                [
                    "2025-03-06",
                    -12.83
                ],
                [
                    "2025-03-07",
                    12.63
                ],
                [
                    "2025-03-10",
                    4.74
                ],
                [
                    "2025-03-11",
                    7.10
                ],
                [
                    "2025-03-12",
                    0.64
                ],
                [
                    "2025-03-13",
                    7.72
                ],
                [
                    "2025-03-14",
                    7.78
                ],
                [
                    "2025-03-17",
                    -0.83
                ],
                [
                    "2025-03-18",
                    5.15
                ],
                [
                    "2025-03-19",
                    -18.55
                ],
                [
                    "2025-04-15",
                    7.86
                ],
                [
                    "2025-04-16",
                    4.70
                ],
                [
                    "2025-04-17",
                    -0.67
                ],
                [
                    "2025-04-18",
                    18.92
                ],
                [
                    "2025-04-21",
                    -11.18
                ],
                [
                    "2025-04-22",
                    -1.24
                ],
                [
                    "2025-04-24",
                    6.04
                ],
                [
                    "2025-05-06",
                    5.49
                ],
                [
                    "2025-05-22",
                    2.33
                ],
                [
                    "2025-06-10",
                    -12.27
                ],
                [
                    "2025-06-20",
                    9.22
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-06",
                    5.72
                ],
                [
                    "2025-03-07",
                    -4.56
                ],
                [
                    "2025-04-15",
                    0.91
                ],
                [
                    "2025-04-16",
                    11.83
                ],
                [
                    "2025-04-17",
                    -2.29
                ],
                [
                    "2025-04-18",
                    -9.71
                ],
                [
                    "2025-04-21",
                    18.15
                ],
                [
                    "2025-04-22",
                    2.11
                ],
                [
                    "2025-04-23",
                    4.34
                ],
                [
                    "2025-04-24",
                    -7.07
                ],
                [
                    "2025-05-21",
                    6.42
                ],
                [
                    "2025-05-22",
                    8.34
                ],
                [
                    "2025-05-23",
                    1.25
                ],
                [
                    "2025-05-26",
                    4.89
                ],
                [
                    "2025-05-27",
                    5.16
                ],
                [
                    "2025-05-28",
                    12.06
                ],
                [
                    "2025-05-29",
                    7.74
                ],
                [
                    "2025-05-30",
                    13.56
                ],
                [
                    "2025-06-03",
                    14.48
                ],
                [
                    "2025-06-04",
                    -3.04
                ],
                [
                    "2025-06-05",
                    9.68
                ],
                [
                    "2025-06-06",
                    -2.03
                ],
                [
                    "2025-06-09",
                    -1.44
                ],
                [
                    "2025-06-10",
                    -2.54
                ],
                [
                    "2025-06-19",
                    7.84
                ],
                [
                    "2025-06-20",
                    4.96
                ],
                [
                    "2025-06-23",
                    19.07
                ],
                [
                    "2025-06-24",
                    2.20
                ],
                [
                    "2025-06-25",
                    5.45
                ],
                [
                    "2025-06-26",
                    6.38
                ],
                [
                    "2025-06-27",
                    8.71
                ],
                [
                    "2025-07-21",
                    -0.06
                ],
                [
                    "2025-07-22",
                    -1.31
                ],
                [
                    "2025-07-23",
                    -2.81
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    24.52
                ],
                [
                    "2025-02-11",
                    8.81
                ],
                [
                    "2025-02-12",
                    5.22
                ],
                [
                    "2025-02-13",
                    13.30
                ],
                [
                    "2025-02-14",
                    7.75
                ],
                [
                    "2025-02-17",
                    19.70
                ],
                [
                    "2025-02-18",
                    2.45
                ],
                [
                    "2025-02-19",
                    -5.00
                ],
                [
                    "2025-02-20",
                    9.55
                ],
                [
                    "2025-02-21",
                    4.21
                ],
                [
                    "2025-02-24",
                    3.69
                ],
                [
                    "2025-03-20",
                    7.72
                ],
                [
                    "2025-03-21",
                    20.53
                ],
                [
                    "2025-03-24",
                    9.09
                ],
                [
                    "2025-03-25",
                    -3.26
                ],
                [
                    "2025-03-26",
                    5.66
                ],
                [
                    "2025-03-27",
                    -9.30
                ],
                [
                    "2025-03-28",
                    13.16
                ],
                [
                    "2025-03-31",
                    0.51
                ],
                [
                    "2025-04-01",
                    -7.87
                ],
                [
                    "2025-04-02",
                    9.20
                ],
                [
                    "2025-04-03",
                    1.90
                ],
                [
                    "2025-04-07",
                    2.49
                ],
                [
                    "2025-04-08",
                    6.07
                ],
                [
                    "2025-04-09",
                    5.57
                ],
                [
                    "2025-04-10",
                    2.88
                ],
                [
                    "2025-04-11",
                    5.76
                ],
                [
                    "2025-04-14",
                    -11.84
                ],
                [
                    "2025-04-25",
                    5.43
                ],
                [
                    "2025-04-28",
                    3.24
                ],
                [
                    "2025-04-29",
                    4.66
                ],
                [
                    "2025-05-08",
                    7.81
                ],
                [
                    "2025-05-09",
                    12.35
                ],
                [
                    "2025-05-12",
                    17.85
                ],
                [
                    "2025-05-13",
                    14.06
                ],
                [
                    "2025-05-14",
                    8.42
                ],
                [
                    "2025-05-15",
                    -5.17
                ],
                [
                    "2025-05-16",
                    -7.04
                ],
                [
                    "2025-05-19",
                    1.96
                ],
                [
                    "2025-05-20",
                    5.23
                ],
                [
                    "2025-06-11",
                    10.31
                ],
                [
                    "2025-06-12",
                    2.35
                ],
                [
                    "2025-06-13",
                    2.12
                ],
                [
                    "2025-06-16",
                    3.18
                ],
                [
                    "2025-06-17",
                    0.12
                ],
                [
                    "2025-06-18",
                    -5.15
                ],
                [
                    "2025-06-30",
                    -0.89
                ],
                [
                    "2025-07-01",
                    -2.00
                ],
                [
                    "2025-07-02",
                    24.17
                ],
                [
                    "2025-07-03",
                    4.39
                ],
                [
                    "2025-07-04",
                    -4.08
                ],
                [
                    "2025-07-07",
                    15.44
                ],
                [
                    "2025-07-08",
                    25.28
                ],
                [
                    "2025-07-09",
                    11.34
                ],
                [
                    "2025-07-10",
                    14.37
                ],
                [
                    "2025-07-11",
                    19.78
                ],
                [
                    "2025-07-14",
                    14.15
                ],
                [
                    "2025-07-15",
                    10.57
                ],
                [
                    "2025-07-16",
                    14.95
                ],
                [
                    "2025-07-17",
                    -20.32
                ],
                [
                    "2025-07-18",
                    -9.97
                ],
                [
                    "2025-07-24",
                    1.91
                ],
                [
                    "2025-07-25",
                    -5.65
                ],
                [
                    "2025-07-28",
                    -1.11
                ],
                [
                    "2025-07-29",
                    14.89
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600510 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_861b95b5b13a4a5db631360c0c176131.setOption(option_861b95b5b13a4a5db631360c0c176131);
            window.addEventListener('resize', function(){
                chart_861b95b5b13a4a5db631360c0c176131.resize();
            })
    </script>
</body>
</html>
