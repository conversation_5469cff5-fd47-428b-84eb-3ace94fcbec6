<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="ddec1ca79d8d40fcb7d3a556678b7e5d" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_ddec1ca79d8d40fcb7d3a556678b7e5d = echarts.init(
            document.getElementById('ddec1ca79d8d40fcb7d3a556678b7e5d'), 'white', {renderer: 'canvas'});
        var option_ddec1ca79d8d40fcb7d3a556678b7e5d = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    4.65,
                    4.64,
                    4.64,
                    4.75
                ],
                [
                    4.68,
                    4.72,
                    4.63,
                    4.74
                ],
                [
                    4.71,
                    4.72,
                    4.65,
                    4.72
                ],
                [
                    4.79,
                    4.78,
                    4.72,
                    4.82
                ],
                [
                    4.79,
                    4.80,
                    4.77,
                    4.84
                ],
                [
                    4.83,
                    4.75,
                    4.71,
                    4.84
                ],
                [
                    4.78,
                    4.82,
                    4.74,
                    4.83
                ],
                [
                    4.82,
                    4.83,
                    4.81,
                    4.89
                ],
                [
                    4.83,
                    4.73,
                    4.70,
                    4.87
                ],
                [
                    4.75,
                    4.78,
                    4.70,
                    4.83
                ],
                [
                    4.80,
                    4.64,
                    4.62,
                    4.80
                ],
                [
                    4.64,
                    4.65,
                    4.61,
                    4.66
                ],
                [
                    4.65,
                    4.66,
                    4.60,
                    4.69
                ],
                [
                    4.69,
                    4.68,
                    4.59,
                    4.71
                ],
                [
                    4.67,
                    4.84,
                    4.67,
                    4.85
                ],
                [
                    4.86,
                    4.94,
                    4.79,
                    5.04
                ],
                [
                    4.96,
                    5.03,
                    4.95,
                    5.09
                ],
                [
                    5.05,
                    5.02,
                    4.95,
                    5.10
                ],
                [
                    5.00,
                    4.96,
                    4.95,
                    5.06
                ],
                [
                    4.98,
                    4.96,
                    4.94,
                    5.06
                ],
                [
                    4.97,
                    5.12,
                    4.97,
                    5.14
                ],
                [
                    5.12,
                    5.10,
                    5.06,
                    5.18
                ],
                [
                    5.11,
                    5.22,
                    5.07,
                    5.25
                ],
                [
                    5.19,
                    5.24,
                    5.14,
                    5.35
                ],
                [
                    5.28,
                    5.27,
                    5.19,
                    5.33
                ],
                [
                    5.22,
                    5.28,
                    5.17,
                    5.30
                ],
                [
                    5.28,
                    5.34,
                    5.28,
                    5.47
                ],
                [
                    5.34,
                    5.28,
                    5.19,
                    5.35
                ],
                [
                    5.30,
                    5.34,
                    5.23,
                    5.38
                ],
                [
                    5.36,
                    5.47,
                    5.34,
                    5.59
                ],
                [
                    5.46,
                    5.46,
                    5.41,
                    5.51
                ],
                [
                    5.47,
                    5.38,
                    5.35,
                    5.47
                ],
                [
                    5.39,
                    5.35,
                    5.33,
                    5.43
                ],
                [
                    5.34,
                    5.28,
                    5.23,
                    5.36
                ],
                [
                    5.25,
                    5.18,
                    5.06,
                    5.28
                ],
                [
                    5.17,
                    5.18,
                    5.12,
                    5.22
                ],
                [
                    5.22,
                    5.25,
                    5.16,
                    5.29
                ],
                [
                    5.25,
                    5.19,
                    5.17,
                    5.29
                ],
                [
                    5.19,
                    5.14,
                    5.12,
                    5.21
                ],
                [
                    5.15,
                    5.12,
                    5.04,
                    5.16
                ],
                [
                    5.13,
                    5.14,
                    5.10,
                    5.22
                ],
                [
                    5.14,
                    5.11,
                    5.09,
                    5.17
                ],
                [
                    5.11,
                    5.18,
                    5.07,
                    5.22
                ],
                [
                    5.00,
                    4.66,
                    4.66,
                    5.00
                ],
                [
                    4.65,
                    4.42,
                    4.34,
                    4.65
                ],
                [
                    4.35,
                    4.55,
                    4.16,
                    4.59
                ],
                [
                    4.57,
                    4.74,
                    4.57,
                    4.80
                ],
                [
                    4.70,
                    4.75,
                    4.68,
                    4.79
                ],
                [
                    4.78,
                    4.79,
                    4.71,
                    4.91
                ],
                [
                    4.79,
                    4.82,
                    4.72,
                    4.88
                ],
                [
                    4.75,
                    4.75,
                    4.73,
                    5.01
                ],
                [
                    4.77,
                    4.84,
                    4.73,
                    4.92
                ],
                [
                    4.91,
                    5.32,
                    4.84,
                    5.32
                ],
                [
                    5.40,
                    5.14,
                    5.10,
                    5.41
                ],
                [
                    5.10,
                    5.32,
                    5.08,
                    5.62
                ],
                [
                    5.33,
                    5.23,
                    5.22,
                    5.45
                ],
                [
                    5.19,
                    5.31,
                    5.19,
                    5.42
                ],
                [
                    5.37,
                    5.32,
                    5.30,
                    5.57
                ],
                [
                    5.29,
                    5.20,
                    5.14,
                    5.36
                ],
                [
                    5.18,
                    5.23,
                    5.16,
                    5.25
                ],
                [
                    5.22,
                    5.21,
                    5.15,
                    5.28
                ],
                [
                    5.20,
                    5.26,
                    5.17,
                    5.28
                ],
                [
                    5.32,
                    5.27,
                    5.24,
                    5.39
                ],
                [
                    5.26,
                    5.28,
                    5.22,
                    5.29
                ],
                [
                    5.30,
                    5.23,
                    5.20,
                    5.30
                ],
                [
                    5.28,
                    5.06,
                    5.04,
                    5.29
                ],
                [
                    5.10,
                    5.02,
                    4.99,
                    5.16
                ],
                [
                    5.07,
                    5.02,
                    5.00,
                    5.07
                ],
                [
                    5.04,
                    4.97,
                    4.96,
                    5.04
                ],
                [
                    5.01,
                    4.96,
                    4.95,
                    5.01
                ],
                [
                    4.96,
                    5.05,
                    4.94,
                    5.07
                ],
                [
                    5.05,
                    5.03,
                    4.99,
                    5.08
                ],
                [
                    5.03,
                    4.99,
                    4.97,
                    5.05
                ],
                [
                    5.01,
                    4.92,
                    4.91,
                    5.03
                ],
                [
                    4.91,
                    4.88,
                    4.86,
                    4.96
                ],
                [
                    4.89,
                    4.90,
                    4.86,
                    4.93
                ],
                [
                    4.90,
                    4.91,
                    4.84,
                    4.93
                ],
                [
                    4.91,
                    4.89,
                    4.87,
                    4.95
                ],
                [
                    4.89,
                    4.90,
                    4.87,
                    4.93
                ],
                [
                    4.90,
                    4.92,
                    4.86,
                    4.92
                ],
                [
                    4.88,
                    4.84,
                    4.84,
                    4.92
                ],
                [
                    4.86,
                    4.97,
                    4.84,
                    4.97
                ],
                [
                    4.95,
                    4.96,
                    4.92,
                    5.07
                ],
                [
                    4.94,
                    4.98,
                    4.94,
                    5.00
                ],
                [
                    4.98,
                    5.30,
                    4.98,
                    5.41
                ],
                [
                    5.28,
                    5.25,
                    5.18,
                    5.32
                ],
                [
                    5.24,
                    5.32,
                    5.21,
                    5.34
                ],
                [
                    5.33,
                    5.25,
                    5.23,
                    5.33
                ],
                [
                    5.25,
                    5.04,
                    5.02,
                    5.27
                ],
                [
                    5.07,
                    5.11,
                    5.07,
                    5.20
                ],
                [
                    5.12,
                    5.14,
                    5.10,
                    5.19
                ],
                [
                    5.12,
                    5.05,
                    4.99,
                    5.14
                ],
                [
                    5.04,
                    4.88,
                    4.86,
                    5.07
                ],
                [
                    4.89,
                    4.88,
                    4.88,
                    4.94
                ],
                [
                    4.86,
                    4.88,
                    4.83,
                    4.90
                ],
                [
                    4.90,
                    4.97,
                    4.87,
                    4.99
                ],
                [
                    4.99,
                    5.06,
                    4.97,
                    5.06
                ],
                [
                    5.05,
                    5.06,
                    5.00,
                    5.08
                ],
                [
                    5.05,
                    5.07,
                    5.03,
                    5.14
                ],
                [
                    5.09,
                    5.09,
                    5.05,
                    5.12
                ],
                [
                    5.10,
                    5.03,
                    4.99,
                    5.10
                ],
                [
                    5.02,
                    5.03,
                    4.99,
                    5.06
                ],
                [
                    5.03,
                    5.02,
                    5.00,
                    5.07
                ],
                [
                    5.05,
                    5.03,
                    4.99,
                    5.05
                ],
                [
                    5.00,
                    5.07,
                    4.99,
                    5.10
                ],
                [
                    5.08,
                    5.13,
                    5.06,
                    5.13
                ],
                [
                    5.13,
                    5.12,
                    5.10,
                    5.15
                ],
                [
                    5.13,
                    5.25,
                    5.12,
                    5.28
                ],
                [
                    5.30,
                    5.32,
                    5.25,
                    5.40
                ],
                [
                    5.32,
                    5.26,
                    5.22,
                    5.39
                ],
                [
                    5.23,
                    5.20,
                    5.13,
                    5.26
                ],
                [
                    5.19,
                    5.20,
                    5.15,
                    5.22
                ],
                [
                    5.20,
                    5.15,
                    5.13,
                    5.22
                ],
                [
                    5.18,
                    5.14,
                    5.12,
                    5.19
                ],
                [
                    5.13,
                    5.22,
                    5.13,
                    5.22
                ],
                [
                    5.23,
                    5.26,
                    5.16,
                    5.27
                ],
                [
                    5.26,
                    5.24,
                    5.22,
                    5.32
                ],
                [
                    5.25,
                    5.37,
                    5.22,
                    5.40
                ],
                [
                    5.38,
                    5.45,
                    5.33,
                    5.50
                ],
                [
                    5.46,
                    5.49,
                    5.41,
                    5.53
                ],
                [
                    5.49,
                    5.48,
                    5.43,
                    5.59
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u7a81\u7834",
            "symbol": "pin",
            "symbolSize": 16,
            "data": [
                [
                    "2025-04-22",
                    5.62
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#1f77b4"
            }
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "symbol": "diamond",
            "symbolSize": 16,
            "data": [
                [
                    "2025-04-21",
                    5.41
                ],
                [
                    "2025-04-22",
                    5.62
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u7a81\u7834",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600510 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_ddec1ca79d8d40fcb7d3a556678b7e5d.setOption(option_ddec1ca79d8d40fcb7d3a556678b7e5d);
            window.addEventListener('resize', function(){
                chart_ddec1ca79d8d40fcb7d3a556678b7e5d.resize();
            })
    </script>
</body>
</html>
