<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="9d16eba9ad2f412ab89be4f1ca499ecc" class="chart-container" style="width:100%; height:500px; "></div>
    <script>
        var chart_9d16eba9ad2f412ab89be4f1ca499ecc = echarts.init(
            document.getElementById('9d16eba9ad2f412ab89be4f1ca499ecc'), 'white', {renderer: 'canvas'});
        var option_9d16eba9ad2f412ab89be4f1ca499ecc = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    36.01,
                    34.98,
                    34.98,
                    36.22
                ],
                [
                    35.16,
                    35.19,
                    34.97,
                    35.6
                ],
                [
                    35.07,
                    36.5,
                    34.9,
                    36.56
                ],
                [
                    36.61,
                    36.58,
                    36.05,
                    37.07
                ],
                [
                    36.64,
                    37.2,
                    36.2,
                    37.7
                ],
                [
                    37.2,
                    36.61,
                    36.45,
                    37.2
                ],
                [
                    36.42,
                    37.03,
                    36.38,
                    37.03
                ],
                [
                    36.9,
                    36.07,
                    36.02,
                    36.95
                ],
                [
                    36.01,
                    36.17,
                    35.7,
                    36.3
                ],
                [
                    36.21,
                    35.99,
                    35.79,
                    36.47
                ],
                [
                    35.9,
                    34.42,
                    34.19,
                    36.26
                ],
                [
                    34.41,
                    35.73,
                    34.37,
                    35.8
                ],
                [
                    36.03,
                    37.75,
                    35.87,
                    38.5
                ],
                [
                    37.52,
                    38.1,
                    37.01,
                    38.15
                ],
                [
                    37.4,
                    37.65,
                    37.38,
                    38.04
                ],
                [
                    37.2,
                    37.6,
                    36.81,
                    37.97
                ],
                [
                    37.59,
                    40.82,
                    37.5,
                    41.15
                ],
                [
                    40.49,
                    41.1,
                    40.03,
                    41.2
                ],
                [
                    41.0,
                    39.52,
                    39.38,
                    41.37
                ],
                [
                    39.5,
                    40.2,
                    39.5,
                    41.0
                ],
                [
                    40.1,
                    42.65,
                    39.94,
                    43.25
                ],
                [
                    42.38,
                    42.23,
                    42.08,
                    43.5
                ],
                [
                    42.03,
                    42.3,
                    41.6,
                    43.19
                ],
                [
                    42.07,
                    43.41,
                    42.07,
                    44.16
                ],
                [
                    43.66,
                    44.81,
                    43.42,
                    46.8
                ],
                [
                    44.37,
                    49.29,
                    44.0,
                    49.29
                ],
                [
                    50.65,
                    54.22,
                    50.0,
                    54.22
                ],
                [
                    54.87,
                    55.6,
                    51.5,
                    58.9
                ],
                [
                    54.98,
                    53.74,
                    52.25,
                    55.39
                ],
                [
                    53.47,
                    53.95,
                    52.97,
                    58.45
                ],
                [
                    54.82,
                    53.08,
                    51.59,
                    55.39
                ],
                [
                    52.54,
                    52.66,
                    51.09,
                    53.16
                ],
                [
                    52.6,
                    53.99,
                    52.16,
                    55.46
                ],
                [
                    53.6,
                    53.97,
                    52.37,
                    54.8
                ],
                [
                    53.49,
                    51.03,
                    49.82,
                    53.74
                ],
                [
                    51.0,
                    56.13,
                    50.0,
                    56.13
                ],
                [
                    55.07,
                    55.61,
                    54.1,
                    56.75
                ],
                [
                    55.5,
                    52.7,
                    52.33,
                    55.74
                ],
                [
                    52.7,
                    53.13,
                    52.7,
                    54.34
                ],
                [
                    53.24,
                    51.85,
                    50.7,
                    53.3
                ],
                [
                    53.0,
                    54.79,
                    53.0,
                    55.9
                ],
                [
                    54.86,
                    55.81,
                    54.6,
                    57.5
                ],
                [
                    55.11,
                    55.81,
                    55.11,
                    58.5
                ],
                [
                    53.0,
                    51.72,
                    50.23,
                    55.8
                ],
                [
                    51.56,
                    51.8,
                    50.3,
                    54.47
                ],
                [
                    51.48,
                    56.98,
                    50.3,
                    56.98
                ],
                [
                    58.47,
                    60.88,
                    57.51,
                    61.96
                ],
                [
                    59.59,
                    60.91,
                    59.51,
                    65.13
                ],
                [
                    62.0,
                    60.86,
                    60.3,
                    62.55
                ],
                [
                    60.86,
                    58.15,
                    56.78,
                    61.27
                ],
                [
                    57.74,
                    57.07,
                    55.9,
                    58.3
                ],
                [
                    56.35,
                    55.29,
                    55.2,
                    58.2
                ],
                [
                    55.0,
                    52.89,
                    52.48,
                    55.68
                ],
                [
                    52.92,
                    53.9,
                    52.52,
                    54.3
                ],
                [
                    53.51,
                    53.49,
                    52.87,
                    54.73
                ],
                [
                    53.49,
                    53.01,
                    52.33,
                    53.87
                ],
                [
                    50.55,
                    52.77,
                    50.55,
                    53.55
                ],
                [
                    52.44,
                    52.94,
                    52.35,
                    54.1
                ],
                [
                    52.94,
                    53.25,
                    52.52,
                    54.25
                ],
                [
                    52.9,
                    52.37,
                    51.61,
                    53.08
                ],
                [
                    52.02,
                    52.11,
                    51.97,
                    53.56
                ],
                [
                    52.21,
                    51.84,
                    50.01,
                    53.0
                ],
                [
                    52.19,
                    53.93,
                    52.19,
                    54.95
                ],
                [
                    53.99,
                    53.14,
                    52.0,
                    53.99
                ],
                [
                    53.27,
                    51.1,
                    50.9,
                    53.45
                ],
                [
                    51.46,
                    56.21,
                    51.12,
                    56.21
                ],
                [
                    56.05,
                    52.53,
                    52.45,
                    56.05
                ],
                [
                    52.68,
                    51.8,
                    51.48,
                    53.04
                ],
                [
                    51.81,
                    50.22,
                    50.01,
                    52.0
                ],
                [
                    49.53,
                    48.28,
                    47.3,
                    50.0
                ],
                [
                    48.1,
                    48.19,
                    47.34,
                    48.35
                ],
                [
                    48.13,
                    48.3,
                    47.35,
                    48.48
                ],
                [
                    48.31,
                    47.46,
                    47.17,
                    48.64
                ],
                [
                    47.21,
                    46.8,
                    46.65,
                    48.5
                ],
                [
                    46.55,
                    46.9,
                    46.55,
                    47.82
                ],
                [
                    46.86,
                    47.52,
                    46.7,
                    47.55
                ],
                [
                    47.12,
                    46.21,
                    45.98,
                    47.26
                ],
                [
                    46.22,
                    45.76,
                    45.6,
                    46.44
                ],
                [
                    45.66,
                    46.72,
                    45.55,
                    46.94
                ],
                [
                    46.84,
                    46.45,
                    46.22,
                    47.03
                ],
                [
                    46.33,
                    46.63,
                    46.1,
                    47.04
                ],
                [
                    46.47,
                    46.9,
                    46.36,
                    47.0
                ],
                [
                    46.89,
                    47.62,
                    46.52,
                    47.9
                ],
                [
                    47.62,
                    46.9,
                    46.82,
                    48.29
                ],
                [
                    46.95,
                    46.2,
                    45.85,
                    47.8
                ],
                [
                    45.66,
                    44.91,
                    44.2,
                    45.78
                ],
                [
                    44.95,
                    45.75,
                    44.6,
                    45.98
                ],
                [
                    45.5,
                    45.64,
                    45.19,
                    46.22
                ],
                [
                    46.51,
                    45.44,
                    45.08,
                    46.66
                ],
                [
                    45.39,
                    45.38,
                    45.11,
                    46.1
                ],
                [
                    45.29,
                    45.36,
                    44.92,
                    45.76
                ],
                [
                    45.3,
                    45.72,
                    45.18,
                    46.1
                ],
                [
                    45.5,
                    45.01,
                    44.88,
                    45.87
                ],
                [
                    44.9,
                    44.37,
                    44.33,
                    45.09
                ],
                [
                    44.23,
                    46.23,
                    44.21,
                    46.64
                ],
                [
                    46.0,
                    46.8,
                    45.55,
                    47.08
                ],
                [
                    46.88,
                    48.8,
                    46.8,
                    49.49
                ],
                [
                    48.81,
                    48.35,
                    48.15,
                    49.5
                ],
                [
                    48.31,
                    48.26,
                    47.97,
                    49.1
                ],
                [
                    48.26,
                    50.27,
                    48.26,
                    50.78
                ],
                [
                    50.4,
                    50.37,
                    50.01,
                    51.58
                ],
                [
                    50.28,
                    49.08,
                    48.78,
                    50.28
                ],
                [
                    48.94,
                    49.31,
                    48.8,
                    50.1
                ],
                [
                    49.01,
                    48.84,
                    48.38,
                    49.48
                ],
                [
                    48.48,
                    48.04,
                    48.03,
                    49.12
                ],
                [
                    48.0,
                    49.04,
                    47.9,
                    49.47
                ],
                [
                    49.5,
                    49.26,
                    49.11,
                    50.37
                ],
                [
                    49.25,
                    49.6,
                    48.89,
                    50.44
                ],
                [
                    50.06,
                    52.63,
                    49.9,
                    52.81
                ],
                [
                    52.8,
                    51.28,
                    50.91,
                    53.2
                ],
                [
                    52.28,
                    51.81,
                    51.35,
                    53.8
                ],
                [
                    51.89,
                    51.73,
                    51.38,
                    52.38
                ],
                [
                    51.9,
                    52.7,
                    51.3,
                    53.0
                ],
                [
                    52.48,
                    52.23,
                    51.91,
                    52.99
                ],
                [
                    52.21,
                    52.27,
                    51.68,
                    53.08
                ],
                [
                    52.25,
                    53.15,
                    51.93,
                    53.85
                ],
                [
                    52.89,
                    51.74,
                    51.52,
                    53.06
                ],
                [
                    51.6,
                    51.97,
                    51.51,
                    52.41
                ],
                [
                    52.03,
                    52.5,
                    51.8,
                    52.7
                ],
                [
                    52.98,
                    54.7,
                    52.81,
                    55.44
                ],
                [
                    54.2,
                    55.6,
                    54.13,
                    55.67
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u9636\u6bb5\u6027\u5e95\u90e8",
            "symbol": "triangle",
            "symbolSize": 20,
            "data": [
                [
                    "2025-04-07",
                    49.23
                ],
                [
                    "2025-04-08",
                    49.29
                ],
                [
                    "2025-05-09",
                    49.88
                ],
                [
                    "2025-05-14",
                    50.45
                ],
                [
                    "2025-05-20",
                    46.4
                ],
                [
                    "2025-06-13",
                    44.18
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#FF6B35",
                "borderColor": "#CC5529",
                "borderWidth": 2,
                "opacity": 0.9
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf",
                "\u9636\u6bb5\u6027\u5e95\u90e8"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603267 \u9636\u6bb5\u6027\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_9d16eba9ad2f412ab89be4f1ca499ecc.setOption(option_9d16eba9ad2f412ab89be4f1ca499ecc);
            window.addEventListener('resize', function(){
                chart_9d16eba9ad2f412ab89be4f1ca499ecc.resize();
            })
    </script>
</body>
</html>
