<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="83b41591c32f4feeb19c48fd1955eb04" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_83b41591c32f4feeb19c48fd1955eb04 = echarts.init(
            document.getElementById('83b41591c32f4feeb19c48fd1955eb04'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_83b41591c32f4feeb19c48fd1955eb04 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    36.01,
                    34.98,
                    34.98,
                    36.22
                ],
                [
                    35.16,
                    35.19,
                    34.97,
                    35.6
                ],
                [
                    35.07,
                    36.5,
                    34.9,
                    36.56
                ],
                [
                    36.61,
                    36.58,
                    36.05,
                    37.07
                ],
                [
                    36.64,
                    37.2,
                    36.2,
                    37.7
                ],
                [
                    37.2,
                    36.61,
                    36.45,
                    37.2
                ],
                [
                    36.42,
                    37.03,
                    36.38,
                    37.03
                ],
                [
                    36.9,
                    36.07,
                    36.02,
                    36.95
                ],
                [
                    36.01,
                    36.17,
                    35.7,
                    36.3
                ],
                [
                    36.21,
                    35.99,
                    35.79,
                    36.47
                ],
                [
                    35.9,
                    34.42,
                    34.19,
                    36.26
                ],
                [
                    34.41,
                    35.73,
                    34.37,
                    35.8
                ],
                [
                    36.03,
                    37.75,
                    35.87,
                    38.5
                ],
                [
                    37.52,
                    38.1,
                    37.01,
                    38.15
                ],
                [
                    37.4,
                    37.65,
                    37.38,
                    38.04
                ],
                [
                    37.2,
                    37.6,
                    36.81,
                    37.97
                ],
                [
                    37.59,
                    40.82,
                    37.5,
                    41.15
                ],
                [
                    40.49,
                    41.1,
                    40.03,
                    41.2
                ],
                [
                    41.0,
                    39.52,
                    39.38,
                    41.37
                ],
                [
                    39.5,
                    40.2,
                    39.5,
                    41.0
                ],
                [
                    40.1,
                    42.65,
                    39.94,
                    43.25
                ],
                [
                    42.38,
                    42.23,
                    42.08,
                    43.5
                ],
                [
                    42.03,
                    42.3,
                    41.6,
                    43.19
                ],
                [
                    42.07,
                    43.41,
                    42.07,
                    44.16
                ],
                [
                    43.66,
                    44.81,
                    43.42,
                    46.8
                ],
                [
                    44.37,
                    49.29,
                    44.0,
                    49.29
                ],
                [
                    50.65,
                    54.22,
                    50.0,
                    54.22
                ],
                [
                    54.87,
                    55.6,
                    51.5,
                    58.9
                ],
                [
                    54.98,
                    53.74,
                    52.25,
                    55.39
                ],
                [
                    53.47,
                    53.95,
                    52.97,
                    58.45
                ],
                [
                    54.82,
                    53.08,
                    51.59,
                    55.39
                ],
                [
                    52.54,
                    52.66,
                    51.09,
                    53.16
                ],
                [
                    52.6,
                    53.99,
                    52.16,
                    55.46
                ],
                [
                    53.6,
                    53.97,
                    52.37,
                    54.8
                ],
                [
                    53.49,
                    51.03,
                    49.82,
                    53.74
                ],
                [
                    51.0,
                    56.13,
                    50.0,
                    56.13
                ],
                [
                    55.07,
                    55.61,
                    54.1,
                    56.75
                ],
                [
                    55.5,
                    52.7,
                    52.33,
                    55.74
                ],
                [
                    52.7,
                    53.13,
                    52.7,
                    54.34
                ],
                [
                    53.24,
                    51.85,
                    50.7,
                    53.3
                ],
                [
                    53.0,
                    54.79,
                    53.0,
                    55.9
                ],
                [
                    54.86,
                    55.81,
                    54.6,
                    57.5
                ],
                [
                    55.11,
                    55.81,
                    55.11,
                    58.5
                ],
                [
                    53.0,
                    51.72,
                    50.23,
                    55.8
                ],
                [
                    51.56,
                    51.8,
                    50.3,
                    54.47
                ],
                [
                    51.48,
                    56.98,
                    50.3,
                    56.98
                ],
                [
                    58.47,
                    60.88,
                    57.51,
                    61.96
                ],
                [
                    59.59,
                    60.91,
                    59.51,
                    65.13
                ],
                [
                    62.0,
                    60.86,
                    60.3,
                    62.55
                ],
                [
                    60.86,
                    58.15,
                    56.78,
                    61.27
                ],
                [
                    57.74,
                    57.07,
                    55.9,
                    58.3
                ],
                [
                    56.35,
                    55.29,
                    55.2,
                    58.2
                ],
                [
                    55.0,
                    52.89,
                    52.48,
                    55.68
                ],
                [
                    52.92,
                    53.9,
                    52.52,
                    54.3
                ],
                [
                    53.51,
                    53.49,
                    52.87,
                    54.73
                ],
                [
                    53.49,
                    53.01,
                    52.33,
                    53.87
                ],
                [
                    50.55,
                    52.77,
                    50.55,
                    53.55
                ],
                [
                    52.44,
                    52.94,
                    52.35,
                    54.1
                ],
                [
                    52.94,
                    53.25,
                    52.52,
                    54.25
                ],
                [
                    52.9,
                    52.37,
                    51.61,
                    53.08
                ],
                [
                    52.02,
                    52.11,
                    51.97,
                    53.56
                ],
                [
                    52.21,
                    51.84,
                    50.01,
                    53.0
                ],
                [
                    52.19,
                    53.93,
                    52.19,
                    54.95
                ],
                [
                    53.99,
                    53.14,
                    52.0,
                    53.99
                ],
                [
                    53.27,
                    51.1,
                    50.9,
                    53.45
                ],
                [
                    51.46,
                    56.21,
                    51.12,
                    56.21
                ],
                [
                    56.05,
                    52.53,
                    52.45,
                    56.05
                ],
                [
                    52.68,
                    51.8,
                    51.48,
                    53.04
                ],
                [
                    51.81,
                    50.22,
                    50.01,
                    52.0
                ],
                [
                    49.53,
                    48.28,
                    47.3,
                    50.0
                ],
                [
                    48.1,
                    48.19,
                    47.34,
                    48.35
                ],
                [
                    48.13,
                    48.3,
                    47.35,
                    48.48
                ],
                [
                    48.31,
                    47.46,
                    47.17,
                    48.64
                ],
                [
                    47.21,
                    46.8,
                    46.65,
                    48.5
                ],
                [
                    46.55,
                    46.9,
                    46.55,
                    47.82
                ],
                [
                    46.86,
                    47.52,
                    46.7,
                    47.55
                ],
                [
                    47.12,
                    46.21,
                    45.98,
                    47.26
                ],
                [
                    46.22,
                    45.76,
                    45.6,
                    46.44
                ],
                [
                    45.66,
                    46.72,
                    45.55,
                    46.94
                ],
                [
                    46.84,
                    46.45,
                    46.22,
                    47.03
                ],
                [
                    46.33,
                    46.63,
                    46.1,
                    47.04
                ],
                [
                    46.47,
                    46.9,
                    46.36,
                    47.0
                ],
                [
                    46.89,
                    47.62,
                    46.52,
                    47.9
                ],
                [
                    47.62,
                    46.9,
                    46.82,
                    48.29
                ],
                [
                    46.95,
                    46.2,
                    45.85,
                    47.8
                ],
                [
                    45.66,
                    44.91,
                    44.2,
                    45.78
                ],
                [
                    44.95,
                    45.75,
                    44.6,
                    45.98
                ],
                [
                    45.5,
                    45.64,
                    45.19,
                    46.22
                ],
                [
                    46.51,
                    45.44,
                    45.08,
                    46.66
                ],
                [
                    45.39,
                    45.38,
                    45.11,
                    46.1
                ],
                [
                    45.29,
                    45.36,
                    44.92,
                    45.76
                ],
                [
                    45.3,
                    45.72,
                    45.18,
                    46.1
                ],
                [
                    45.5,
                    45.01,
                    44.88,
                    45.87
                ],
                [
                    44.9,
                    44.37,
                    44.33,
                    45.09
                ],
                [
                    44.23,
                    46.23,
                    44.21,
                    46.64
                ],
                [
                    46.0,
                    46.8,
                    45.55,
                    47.08
                ],
                [
                    46.88,
                    48.8,
                    46.8,
                    49.49
                ],
                [
                    48.81,
                    48.35,
                    48.15,
                    49.5
                ],
                [
                    48.31,
                    48.26,
                    47.97,
                    49.1
                ],
                [
                    48.26,
                    50.27,
                    48.26,
                    50.78
                ],
                [
                    50.4,
                    50.37,
                    50.01,
                    51.58
                ],
                [
                    50.28,
                    49.08,
                    48.78,
                    50.28
                ],
                [
                    48.94,
                    49.31,
                    48.8,
                    50.1
                ],
                [
                    49.01,
                    48.84,
                    48.38,
                    49.48
                ],
                [
                    48.48,
                    48.04,
                    48.03,
                    49.12
                ],
                [
                    48.0,
                    49.04,
                    47.9,
                    49.47
                ],
                [
                    49.5,
                    49.26,
                    49.11,
                    50.37
                ],
                [
                    49.25,
                    49.6,
                    48.89,
                    50.44
                ],
                [
                    50.06,
                    52.63,
                    49.9,
                    52.81
                ],
                [
                    52.8,
                    51.28,
                    50.91,
                    53.2
                ],
                [
                    52.28,
                    51.81,
                    51.35,
                    53.8
                ],
                [
                    51.89,
                    51.73,
                    51.38,
                    52.38
                ],
                [
                    51.9,
                    52.7,
                    51.3,
                    53.0
                ],
                [
                    52.48,
                    52.23,
                    51.91,
                    52.99
                ],
                [
                    52.21,
                    52.27,
                    51.68,
                    53.08
                ],
                [
                    52.25,
                    53.15,
                    51.93,
                    53.85
                ],
                [
                    52.89,
                    51.74,
                    51.52,
                    53.06
                ],
                [
                    51.6,
                    51.97,
                    51.51,
                    52.41
                ],
                [
                    52.03,
                    52.5,
                    51.8,
                    52.7
                ],
                [
                    52.98,
                    54.7,
                    52.81,
                    55.44
                ],
                [
                    54.2,
                    55.6,
                    54.13,
                    55.67
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -9.18
                ],
                [
                    "2025-02-05",
                    -10.04
                ],
                [
                    "2025-02-06",
                    -6.33
                ],
                [
                    "2025-02-07",
                    -6.27
                ],
                [
                    "2025-02-10",
                    4.49
                ],
                [
                    "2025-02-11",
                    -3.83
                ],
                [
                    "2025-02-12",
                    2.07
                ],
                [
                    "2025-02-13",
                    -8.28
                ],
                [
                    "2025-02-14",
                    -2.74
                ],
                [
                    "2025-02-17",
                    -2.07
                ],
                [
                    "2025-02-18",
                    -16.10
                ],
                [
                    "2025-02-19",
                    -1.48
                ],
                [
                    "2025-02-20",
                    -10.25
                ],
                [
                    "2025-02-21",
                    9.34
                ],
                [
                    "2025-02-24",
                    -2.99
                ],
                [
                    "2025-02-25",
                    -1.72
                ],
                [
                    "2025-02-26",
                    10.72
                ],
                [
                    "2025-02-27",
                    2.44
                ],
                [
                    "2025-02-28",
                    -0.83
                ],
                [
                    "2025-03-03",
                    7.40
                ],
                [
                    "2025-03-04",
                    2.30
                ],
                [
                    "2025-03-05",
                    -5.50
                ],
                [
                    "2025-03-06",
                    5.44
                ],
                [
                    "2025-03-07",
                    5.83
                ],
                [
                    "2025-03-10",
                    -0.97
                ],
                [
                    "2025-03-11",
                    12.88
                ],
                [
                    "2025-03-12",
                    2.33
                ],
                [
                    "2025-03-13",
                    -4.16
                ],
                [
                    "2025-03-14",
                    3.07
                ],
                [
                    "2025-03-17",
                    1.15
                ],
                [
                    "2025-03-18",
                    -10.98
                ],
                [
                    "2025-03-19",
                    -6.33
                ],
                [
                    "2025-03-20",
                    8.64
                ],
                [
                    "2025-03-21",
                    -6.93
                ],
                [
                    "2025-03-24",
                    -10.74
                ],
                [
                    "2025-03-25",
                    18.51
                ],
                [
                    "2025-03-26",
                    2.81
                ],
                [
                    "2025-03-27",
                    -13.72
                ],
                [
                    "2025-03-28",
                    -8.90
                ],
                [
                    "2025-03-31",
                    -1.35
                ],
                [
                    "2025-04-01",
                    2.27
                ],
                [
                    "2025-04-02",
                    3.70
                ],
                [
                    "2025-04-03",
                    3.53
                ],
                [
                    "2025-04-07",
                    -11.32
                ],
                [
                    "2025-04-08",
                    4.34
                ],
                [
                    "2025-04-09",
                    16.05
                ],
                [
                    "2025-04-10",
                    6.60
                ],
                [
                    "2025-04-11",
                    -4.51
                ],
                [
                    "2025-04-14",
                    -6.35
                ],
                [
                    "2025-04-15",
                    -9.85
                ],
                [
                    "2025-04-16",
                    2.72
                ],
                [
                    "2025-04-17",
                    -4.76
                ],
                [
                    "2025-04-18",
                    -13.11
                ],
                [
                    "2025-04-21",
                    -3.23
                ],
                [
                    "2025-04-22",
                    10.54
                ],
                [
                    "2025-04-23",
                    -14.67
                ],
                [
                    "2025-04-24",
                    -4.60
                ],
                [
                    "2025-04-25",
                    5.71
                ],
                [
                    "2025-04-28",
                    2.75
                ],
                [
                    "2025-04-29",
                    -7.21
                ],
                [
                    "2025-04-30",
                    -5.45
                ],
                [
                    "2025-05-06",
                    -16.01
                ],
                [
                    "2025-05-07",
                    8.88
                ],
                [
                    "2025-05-08",
                    0.86
                ],
                [
                    "2025-05-09",
                    -12.87
                ],
                [
                    "2025-05-12",
                    13.59
                ],
                [
                    "2025-05-13",
                    -8.04
                ],
                [
                    "2025-05-14",
                    -5.78
                ],
                [
                    "2025-05-15",
                    -12.30
                ],
                [
                    "2025-05-16",
                    -8.77
                ],
                [
                    "2025-05-19",
                    -5.82
                ],
                [
                    "2025-05-20",
                    4.41
                ],
                [
                    "2025-05-21",
                    -9.84
                ],
                [
                    "2025-05-22",
                    -8.03
                ],
                [
                    "2025-05-23",
                    4.00
                ],
                [
                    "2025-05-26",
                    4.60
                ],
                [
                    "2025-05-27",
                    -15.56
                ],
                [
                    "2025-05-28",
                    -13.22
                ],
                [
                    "2025-05-29",
                    2.43
                ],
                [
                    "2025-05-30",
                    -2.06
                ],
                [
                    "2025-06-03",
                    8.85
                ],
                [
                    "2025-06-04",
                    4.27
                ],
                [
                    "2025-06-05",
                    5.01
                ],
                [
                    "2025-06-06",
                    -5.92
                ],
                [
                    "2025-06-09",
                    -10.45
                ],
                [
                    "2025-06-10",
                    -7.17
                ],
                [
                    "2025-06-11",
                    -1.47
                ],
                [
                    "2025-06-12",
                    -0.58
                ],
                [
                    "2025-06-13",
                    1.73
                ],
                [
                    "2025-06-16",
                    -12.61
                ],
                [
                    "2025-06-17",
                    -4.30
                ],
                [
                    "2025-06-18",
                    6.22
                ],
                [
                    "2025-06-19",
                    -11.54
                ],
                [
                    "2025-06-20",
                    -7.59
                ],
                [
                    "2025-06-23",
                    3.25
                ],
                [
                    "2025-06-24",
                    10.01
                ],
                [
                    "2025-06-25",
                    3.37
                ],
                [
                    "2025-06-26",
                    -1.25
                ],
                [
                    "2025-06-27",
                    -0.14
                ],
                [
                    "2025-06-30",
                    4.78
                ],
                [
                    "2025-07-01",
                    -3.90
                ],
                [
                    "2025-07-02",
                    -7.36
                ],
                [
                    "2025-07-03",
                    -4.77
                ],
                [
                    "2025-07-04",
                    -5.82
                ],
                [
                    "2025-07-07",
                    -8.81
                ],
                [
                    "2025-07-08",
                    2.24
                ],
                [
                    "2025-07-09",
                    0.17
                ],
                [
                    "2025-07-10",
                    11.44
                ],
                [
                    "2025-07-11",
                    11.87
                ],
                [
                    "2025-07-14",
                    9.25
                ],
                [
                    "2025-07-15",
                    -5.85
                ],
                [
                    "2025-07-16",
                    2.75
                ],
                [
                    "2025-07-17",
                    12.59
                ],
                [
                    "2025-07-18",
                    1.48
                ],
                [
                    "2025-07-21",
                    3.44
                ],
                [
                    "2025-07-22",
                    4.75
                ],
                [
                    "2025-07-23",
                    -8.77
                ],
                [
                    "2025-07-24",
                    -18.28
                ],
                [
                    "2025-07-25",
                    -4.42
                ],
                [
                    "2025-07-28",
                    8.12
                ],
                [
                    "2025-07-29",
                    2.24
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    3.34
                ],
                [
                    "2025-02-05",
                    4.32
                ],
                [
                    "2025-02-06",
                    11.50
                ],
                [
                    "2025-02-07",
                    5.36
                ],
                [
                    "2025-02-10",
                    1.81
                ],
                [
                    "2025-02-11",
                    -0.20
                ],
                [
                    "2025-02-12",
                    1.53
                ],
                [
                    "2025-02-13",
                    -0.46
                ],
                [
                    "2025-02-14",
                    -0.98
                ],
                [
                    "2025-02-17",
                    2.51
                ],
                [
                    "2025-02-18",
                    1.13
                ],
                [
                    "2025-02-19",
                    -4.14
                ],
                [
                    "2025-02-20",
                    5.98
                ],
                [
                    "2025-02-21",
                    -10.05
                ],
                [
                    "2025-02-24",
                    -1.42
                ],
                [
                    "2025-02-25",
                    0.98
                ],
                [
                    "2025-02-26",
                    0.18
                ],
                [
                    "2025-02-27",
                    0.72
                ],
                [
                    "2025-02-28",
                    4.52
                ],
                [
                    "2025-03-03",
                    -6.01
                ],
                [
                    "2025-03-04",
                    1.65
                ],
                [
                    "2025-03-05",
                    0.07
                ],
                [
                    "2025-03-06",
                    -3.74
                ],
                [
                    "2025-03-07",
                    -0.37
                ],
                [
                    "2025-03-10",
                    -1.03
                ],
                [
                    "2025-03-11",
                    -9.96
                ],
                [
                    "2025-03-12",
                    -2.10
                ],
                [
                    "2025-03-13",
                    2.48
                ],
                [
                    "2025-03-14",
                    -3.23
                ],
                [
                    "2025-03-17",
                    1.15
                ],
                [
                    "2025-03-18",
                    -8.27
                ],
                [
                    "2025-03-19",
                    -5.14
                ],
                [
                    "2025-03-20",
                    -3.56
                ],
                [
                    "2025-03-21",
                    -0.19
                ],
                [
                    "2025-03-24",
                    -1.68
                ],
                [
                    "2025-03-25",
                    -5.95
                ],
                [
                    "2025-03-26",
                    2.76
                ],
                [
                    "2025-03-27",
                    1.37
                ],
                [
                    "2025-03-28",
                    -0.01
                ],
                [
                    "2025-03-31",
                    -5.18
                ],
                [
                    "2025-04-01",
                    4.80
                ],
                [
                    "2025-04-02",
                    0.50
                ],
                [
                    "2025-04-03",
                    10.51
                ],
                [
                    "2025-04-07",
                    1.42
                ],
                [
                    "2025-04-08",
                    2.10
                ],
                [
                    "2025-04-09",
                    -5.78
                ],
                [
                    "2025-04-10",
                    3.42
                ],
                [
                    "2025-04-11",
                    1.56
                ],
                [
                    "2025-04-14",
                    0.89
                ],
                [
                    "2025-04-15",
                    1.46
                ],
                [
                    "2025-04-16",
                    -2.04
                ],
                [
                    "2025-04-17",
                    -2.11
                ],
                [
                    "2025-04-18",
                    -5.64
                ],
                [
                    "2025-04-21",
                    -4.73
                ],
                [
                    "2025-04-22",
                    0.19
                ],
                [
                    "2025-04-23",
                    0.83
                ],
                [
                    "2025-04-24",
                    2.68
                ],
                [
                    "2025-04-25",
                    -0.61
                ],
                [
                    "2025-04-28",
                    3.23
                ],
                [
                    "2025-04-29",
                    -6.89
                ],
                [
                    "2025-04-30",
                    -4.48
                ],
                [
                    "2025-05-06",
                    -4.12
                ],
                [
                    "2025-05-07",
                    -3.02
                ],
                [
                    "2025-05-08",
                    4.71
                ],
                [
                    "2025-05-09",
                    1.38
                ],
                [
                    "2025-05-12",
                    -12.54
                ],
                [
                    "2025-05-13",
                    7.64
                ],
                [
                    "2025-05-14",
                    1.14
                ],
                [
                    "2025-05-15",
                    2.95
                ],
                [
                    "2025-05-16",
                    -3.83
                ],
                [
                    "2025-05-19",
                    -0.50
                ],
                [
                    "2025-05-20",
                    -0.73
                ],
                [
                    "2025-05-21",
                    -4.09
                ],
                [
                    "2025-05-22",
                    -5.61
                ],
                [
                    "2025-05-23",
                    -11.66
                ],
                [
                    "2025-05-26",
                    -9.35
                ],
                [
                    "2025-05-27",
                    1.09
                ],
                [
                    "2025-05-28",
                    -2.20
                ],
                [
                    "2025-05-29",
                    -8.02
                ],
                [
                    "2025-05-30",
                    7.99
                ],
                [
                    "2025-06-03",
                    2.49
                ],
                [
                    "2025-06-04",
                    4.83
                ],
                [
                    "2025-06-05",
                    1.05
                ],
                [
                    "2025-06-06",
                    -0.84
                ],
                [
                    "2025-06-09",
                    0.33
                ],
                [
                    "2025-06-10",
                    -7.13
                ],
                [
                    "2025-06-11",
                    -2.76
                ],
                [
                    "2025-06-12",
                    2.65
                ],
                [
                    "2025-06-13",
                    1.89
                ],
                [
                    "2025-06-16",
                    -1.43
                ],
                [
                    "2025-06-17",
                    3.57
                ],
                [
                    "2025-06-18",
                    2.48
                ],
                [
                    "2025-06-19",
                    4.70
                ],
                [
                    "2025-06-20",
                    -3.15
                ],
                [
                    "2025-06-23",
                    1.94
                ],
                [
                    "2025-06-24",
                    2.72
                ],
                [
                    "2025-06-25",
                    -0.20
                ],
                [
                    "2025-06-26",
                    1.78
                ],
                [
                    "2025-06-27",
                    1.10
                ],
                [
                    "2025-06-30",
                    -5.41
                ],
                [
                    "2025-07-01",
                    8.45
                ],
                [
                    "2025-07-02",
                    0.79
                ],
                [
                    "2025-07-03",
                    -4.37
                ],
                [
                    "2025-07-04",
                    3.53
                ],
                [
                    "2025-07-07",
                    4.60
                ],
                [
                    "2025-07-08",
                    -3.01
                ],
                [
                    "2025-07-09",
                    9.29
                ],
                [
                    "2025-07-10",
                    -0.59
                ],
                [
                    "2025-07-11",
                    -4.72
                ],
                [
                    "2025-07-14",
                    -2.07
                ],
                [
                    "2025-07-15",
                    -0.33
                ],
                [
                    "2025-07-16",
                    -5.07
                ],
                [
                    "2025-07-17",
                    -0.54
                ],
                [
                    "2025-07-18",
                    -0.48
                ],
                [
                    "2025-07-21",
                    -8.62
                ],
                [
                    "2025-07-22",
                    -0.80
                ],
                [
                    "2025-07-23",
                    0.39
                ],
                [
                    "2025-07-24",
                    0.32
                ],
                [
                    "2025-07-25",
                    -5.29
                ],
                [
                    "2025-07-28",
                    -4.80
                ],
                [
                    "2025-07-29",
                    1.29
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    5.84
                ],
                [
                    "2025-02-05",
                    5.72
                ],
                [
                    "2025-02-06",
                    -5.17
                ],
                [
                    "2025-02-07",
                    0.91
                ],
                [
                    "2025-02-10",
                    -6.30
                ],
                [
                    "2025-02-11",
                    4.03
                ],
                [
                    "2025-02-12",
                    -3.60
                ],
                [
                    "2025-02-13",
                    8.74
                ],
                [
                    "2025-02-14",
                    3.71
                ],
                [
                    "2025-02-17",
                    -0.44
                ],
                [
                    "2025-02-18",
                    14.97
                ],
                [
                    "2025-02-19",
                    5.61
                ],
                [
                    "2025-02-20",
                    4.28
                ],
                [
                    "2025-02-21",
                    0.72
                ],
                [
                    "2025-02-24",
                    4.40
                ],
                [
                    "2025-02-25",
                    0.73
                ],
                [
                    "2025-02-26",
                    -10.91
                ],
                [
                    "2025-02-27",
                    -3.16
                ],
                [
                    "2025-02-28",
                    -3.69
                ],
                [
                    "2025-03-03",
                    -1.39
                ],
                [
                    "2025-03-04",
                    -3.95
                ],
                [
                    "2025-03-05",
                    5.44
                ],
                [
                    "2025-03-06",
                    -1.70
                ],
                [
                    "2025-03-07",
                    -5.46
                ],
                [
                    "2025-03-10",
                    2.00
                ],
                [
                    "2025-03-11",
                    -2.91
                ],
                [
                    "2025-03-12",
                    -0.22
                ],
                [
                    "2025-03-13",
                    1.68
                ],
                [
                    "2025-03-14",
                    0.15
                ],
                [
                    "2025-03-17",
                    -2.31
                ],
                [
                    "2025-03-18",
                    19.25
                ],
                [
                    "2025-03-19",
                    11.47
                ],
                [
                    "2025-03-20",
                    -5.08
                ],
                [
                    "2025-03-21",
                    7.12
                ],
                [
                    "2025-03-24",
                    12.42
                ],
                [
                    "2025-03-25",
                    -12.55
                ],
                [
                    "2025-03-26",
                    -5.57
                ],
                [
                    "2025-03-27",
                    12.34
                ],
                [
                    "2025-03-28",
                    8.91
                ],
                [
                    "2025-03-31",
                    6.53
                ],
                [
                    "2025-04-01",
                    -7.07
                ],
                [
                    "2025-04-02",
                    -4.20
                ],
                [
                    "2025-04-03",
                    -14.04
                ],
                [
                    "2025-04-07",
                    9.90
                ],
                [
                    "2025-04-08",
                    -6.44
                ],
                [
                    "2025-04-09",
                    -10.27
                ],
                [
                    "2025-04-10",
                    -10.02
                ],
                [
                    "2025-04-11",
                    2.96
                ],
                [
                    "2025-04-14",
                    5.46
                ],
                [
                    "2025-04-15",
                    8.39
                ],
                [
                    "2025-04-16",
                    -0.68
                ],
                [
                    "2025-04-17",
                    6.86
                ],
                [
                    "2025-04-18",
                    18.76
                ],
                [
                    "2025-04-21",
                    7.96
                ],
                [
                    "2025-04-22",
                    -10.73
                ],
                [
                    "2025-04-23",
                    13.84
                ],
                [
                    "2025-04-24",
                    1.91
                ],
                [
                    "2025-04-25",
                    -5.10
                ],
                [
                    "2025-04-28",
                    -5.97
                ],
                [
                    "2025-04-29",
                    14.10
                ],
                [
                    "2025-04-30",
                    9.94
                ],
                [
                    "2025-05-06",
                    20.12
                ],
                [
                    "2025-05-07",
                    -5.86
                ],
                [
                    "2025-05-08",
                    -5.57
                ],
                [
                    "2025-05-09",
                    11.49
                ],
                [
                    "2025-05-12",
                    -1.05
                ],
                [
                    "2025-05-13",
                    0.40
                ],
                [
                    "2025-05-14",
                    4.63
                ],
                [
                    "2025-05-15",
                    9.35
                ],
                [
                    "2025-05-16",
                    12.60
                ],
                [
                    "2025-05-19",
                    6.31
                ],
                [
                    "2025-05-20",
                    -3.68
                ],
                [
                    "2025-05-21",
                    13.93
                ],
                [
                    "2025-05-22",
                    13.64
                ],
                [
                    "2025-05-23",
                    7.65
                ],
                [
                    "2025-05-26",
                    4.75
                ],
                [
                    "2025-05-27",
                    14.46
                ],
                [
                    "2025-05-28",
                    15.42
                ],
                [
                    "2025-05-29",
                    5.59
                ],
                [
                    "2025-05-30",
                    -5.93
                ],
                [
                    "2025-06-03",
                    -11.35
                ],
                [
                    "2025-06-04",
                    -9.09
                ],
                [
                    "2025-06-05",
                    -6.06
                ],
                [
                    "2025-06-06",
                    6.76
                ],
                [
                    "2025-06-09",
                    10.13
                ],
                [
                    "2025-06-10",
                    14.30
                ],
                [
                    "2025-06-11",
                    4.23
                ],
                [
                    "2025-06-12",
                    -2.06
                ],
                [
                    "2025-06-13",
                    -3.63
                ],
                [
                    "2025-06-16",
                    14.04
                ],
                [
                    "2025-06-17",
                    0.72
                ],
                [
                    "2025-06-18",
                    -8.70
                ],
                [
                    "2025-06-19",
                    6.84
                ],
                [
                    "2025-06-20",
                    10.75
                ],
                [
                    "2025-06-23",
                    -5.20
                ],
                [
                    "2025-06-24",
                    -12.73
                ],
                [
                    "2025-06-25",
                    -3.16
                ],
                [
                    "2025-06-26",
                    -0.53
                ],
                [
                    "2025-06-27",
                    -0.95
                ],
                [
                    "2025-06-30",
                    0.63
                ],
                [
                    "2025-07-01",
                    -4.55
                ],
                [
                    "2025-07-02",
                    6.58
                ],
                [
                    "2025-07-03",
                    9.13
                ],
                [
                    "2025-07-04",
                    2.29
                ],
                [
                    "2025-07-07",
                    4.21
                ],
                [
                    "2025-07-08",
                    0.77
                ],
                [
                    "2025-07-09",
                    -9.46
                ],
                [
                    "2025-07-10",
                    -10.85
                ],
                [
                    "2025-07-11",
                    -7.16
                ],
                [
                    "2025-07-14",
                    -7.17
                ],
                [
                    "2025-07-15",
                    6.18
                ],
                [
                    "2025-07-16",
                    2.32
                ],
                [
                    "2025-07-17",
                    -12.04
                ],
                [
                    "2025-07-18",
                    -1.00
                ],
                [
                    "2025-07-21",
                    5.18
                ],
                [
                    "2025-07-22",
                    -3.96
                ],
                [
                    "2025-07-23",
                    8.38
                ],
                [
                    "2025-07-24",
                    17.95
                ],
                [
                    "2025-07-25",
                    9.71
                ],
                [
                    "2025-07-28",
                    -3.32
                ],
                [
                    "2025-07-29",
                    -3.53
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-26",
                    10.72
                ],
                [
                    "2025-02-27",
                    2.44
                ],
                [
                    "2025-02-28",
                    -0.83
                ],
                [
                    "2025-03-03",
                    7.40
                ],
                [
                    "2025-03-04",
                    2.30
                ],
                [
                    "2025-03-05",
                    -5.50
                ],
                [
                    "2025-03-06",
                    5.44
                ],
                [
                    "2025-03-07",
                    5.83
                ],
                [
                    "2025-03-10",
                    -0.97
                ],
                [
                    "2025-03-11",
                    12.88
                ],
                [
                    "2025-03-12",
                    2.33
                ],
                [
                    "2025-03-13",
                    -4.16
                ],
                [
                    "2025-03-17",
                    1.15
                ],
                [
                    "2025-03-26",
                    2.81
                ],
                [
                    "2025-04-08",
                    4.34
                ],
                [
                    "2025-04-09",
                    16.05
                ],
                [
                    "2025-04-10",
                    6.60
                ],
                [
                    "2025-04-11",
                    -4.51
                ],
                [
                    "2025-04-14",
                    -6.35
                ],
                [
                    "2025-04-15",
                    -9.85
                ],
                [
                    "2025-05-13",
                    -8.04
                ],
                [
                    "2025-06-04",
                    4.27
                ],
                [
                    "2025-06-05",
                    5.01
                ],
                [
                    "2025-06-06",
                    -5.92
                ],
                [
                    "2025-06-09",
                    -10.45
                ],
                [
                    "2025-06-24",
                    10.01
                ],
                [
                    "2025-06-26",
                    -1.25
                ],
                [
                    "2025-06-27",
                    -0.14
                ],
                [
                    "2025-06-30",
                    4.78
                ],
                [
                    "2025-07-01",
                    -3.90
                ],
                [
                    "2025-07-11",
                    11.87
                ],
                [
                    "2025-07-14",
                    9.25
                ],
                [
                    "2025-07-15",
                    -5.85
                ],
                [
                    "2025-07-16",
                    2.75
                ],
                [
                    "2025-07-17",
                    12.59
                ],
                [
                    "2025-07-18",
                    1.48
                ],
                [
                    "2025-07-22",
                    4.75
                ],
                [
                    "2025-07-23",
                    -8.77
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-11",
                    -0.20
                ],
                [
                    "2025-02-12",
                    1.53
                ],
                [
                    "2025-02-28",
                    4.52
                ],
                [
                    "2025-03-03",
                    -6.01
                ],
                [
                    "2025-03-04",
                    1.65
                ],
                [
                    "2025-03-05",
                    0.07
                ],
                [
                    "2025-04-03",
                    10.51
                ],
                [
                    "2025-04-07",
                    1.42
                ],
                [
                    "2025-04-08",
                    2.10
                ],
                [
                    "2025-04-09",
                    -5.78
                ],
                [
                    "2025-04-10",
                    3.42
                ],
                [
                    "2025-04-11",
                    1.56
                ],
                [
                    "2025-04-14",
                    0.89
                ],
                [
                    "2025-04-15",
                    1.46
                ],
                [
                    "2025-04-28",
                    3.23
                ],
                [
                    "2025-06-04",
                    4.83
                ],
                [
                    "2025-06-05",
                    1.05
                ],
                [
                    "2025-06-06",
                    -0.84
                ],
                [
                    "2025-06-09",
                    0.33
                ],
                [
                    "2025-06-24",
                    2.72
                ],
                [
                    "2025-06-25",
                    -0.20
                ],
                [
                    "2025-06-26",
                    1.78
                ],
                [
                    "2025-06-27",
                    1.10
                ],
                [
                    "2025-07-01",
                    8.45
                ],
                [
                    "2025-07-10",
                    -0.59
                ],
                [
                    "2025-07-11",
                    -4.72
                ],
                [
                    "2025-07-15",
                    -0.33
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    -6.30
                ],
                [
                    "2025-02-13",
                    8.74
                ],
                [
                    "2025-02-14",
                    3.71
                ],
                [
                    "2025-02-17",
                    -0.44
                ],
                [
                    "2025-02-18",
                    14.97
                ],
                [
                    "2025-02-19",
                    5.61
                ],
                [
                    "2025-02-20",
                    4.28
                ],
                [
                    "2025-02-21",
                    0.72
                ],
                [
                    "2025-02-24",
                    4.40
                ],
                [
                    "2025-02-25",
                    0.73
                ],
                [
                    "2025-03-18",
                    19.25
                ],
                [
                    "2025-03-19",
                    11.47
                ],
                [
                    "2025-03-20",
                    -5.08
                ],
                [
                    "2025-03-21",
                    7.12
                ],
                [
                    "2025-03-24",
                    12.42
                ],
                [
                    "2025-03-27",
                    12.34
                ],
                [
                    "2025-03-28",
                    8.91
                ],
                [
                    "2025-03-31",
                    6.53
                ],
                [
                    "2025-04-01",
                    -7.07
                ],
                [
                    "2025-04-02",
                    -4.20
                ],
                [
                    "2025-04-16",
                    -0.68
                ],
                [
                    "2025-04-17",
                    6.86
                ],
                [
                    "2025-04-18",
                    18.76
                ],
                [
                    "2025-04-21",
                    7.96
                ],
                [
                    "2025-04-22",
                    -10.73
                ],
                [
                    "2025-04-23",
                    13.84
                ],
                [
                    "2025-04-24",
                    1.91
                ],
                [
                    "2025-04-25",
                    -5.10
                ],
                [
                    "2025-04-29",
                    14.10
                ],
                [
                    "2025-04-30",
                    9.94
                ],
                [
                    "2025-05-06",
                    20.12
                ],
                [
                    "2025-05-07",
                    -5.86
                ],
                [
                    "2025-05-08",
                    -5.57
                ],
                [
                    "2025-05-09",
                    11.49
                ],
                [
                    "2025-05-12",
                    -1.05
                ],
                [
                    "2025-05-14",
                    4.63
                ],
                [
                    "2025-05-15",
                    9.35
                ],
                [
                    "2025-05-16",
                    12.60
                ],
                [
                    "2025-05-19",
                    6.31
                ],
                [
                    "2025-05-20",
                    -3.68
                ],
                [
                    "2025-05-21",
                    13.93
                ],
                [
                    "2025-05-22",
                    13.64
                ],
                [
                    "2025-05-23",
                    7.65
                ],
                [
                    "2025-05-26",
                    4.75
                ],
                [
                    "2025-05-27",
                    14.46
                ],
                [
                    "2025-05-28",
                    15.42
                ],
                [
                    "2025-05-29",
                    5.59
                ],
                [
                    "2025-05-30",
                    -5.93
                ],
                [
                    "2025-06-03",
                    -11.35
                ],
                [
                    "2025-06-10",
                    14.30
                ],
                [
                    "2025-06-11",
                    4.23
                ],
                [
                    "2025-06-12",
                    -2.06
                ],
                [
                    "2025-06-13",
                    -3.63
                ],
                [
                    "2025-06-16",
                    14.04
                ],
                [
                    "2025-06-17",
                    0.72
                ],
                [
                    "2025-06-18",
                    -8.70
                ],
                [
                    "2025-06-19",
                    6.84
                ],
                [
                    "2025-06-20",
                    10.75
                ],
                [
                    "2025-06-23",
                    -5.20
                ],
                [
                    "2025-07-02",
                    6.58
                ],
                [
                    "2025-07-03",
                    9.13
                ],
                [
                    "2025-07-04",
                    2.29
                ],
                [
                    "2025-07-07",
                    4.21
                ],
                [
                    "2025-07-08",
                    0.77
                ],
                [
                    "2025-07-09",
                    -9.46
                ],
                [
                    "2025-07-24",
                    17.95
                ],
                [
                    "2025-07-25",
                    9.71
                ],
                [
                    "2025-07-28",
                    -3.32
                ],
                [
                    "2025-07-29",
                    -3.53
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603267 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_83b41591c32f4feeb19c48fd1955eb04.setOption(option_83b41591c32f4feeb19c48fd1955eb04);
            window.addEventListener('resize', function(){
                chart_83b41591c32f4feeb19c48fd1955eb04.resize();
            })
    </script>
</body>
</html>
