<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="1b01e4df997f4bd588ee432118028bdd" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_1b01e4df997f4bd588ee432118028bdd = echarts.init(
            document.getElementById('1b01e4df997f4bd588ee432118028bdd'), 'white', {renderer: 'canvas'});
        var option_1b01e4df997f4bd588ee432118028bdd = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    6.88,
                    6.56,
                    6.56,
                    6.91
                ],
                [
                    6.66,
                    6.72,
                    6.56,
                    6.82
                ],
                [
                    6.73,
                    6.94,
                    6.67,
                    6.94
                ],
                [
                    7.0,
                    7.02,
                    6.9,
                    7.1
                ],
                [
                    7.05,
                    7.19,
                    7.01,
                    7.19
                ],
                [
                    7.12,
                    7.16,
                    7.02,
                    7.17
                ],
                [
                    7.12,
                    7.31,
                    7.1,
                    7.32
                ],
                [
                    7.32,
                    7.15,
                    7.08,
                    7.32
                ],
                [
                    7.1,
                    7.19,
                    7.05,
                    7.27
                ],
                [
                    7.19,
                    7.23,
                    7.13,
                    7.27
                ],
                [
                    7.23,
                    6.95,
                    6.91,
                    7.25
                ],
                [
                    6.94,
                    7.11,
                    6.92,
                    7.13
                ],
                [
                    7.11,
                    7.24,
                    7.1,
                    7.26
                ],
                [
                    7.24,
                    7.4,
                    7.15,
                    7.7
                ],
                [
                    7.35,
                    7.59,
                    7.33,
                    7.85
                ],
                [
                    7.51,
                    8.35,
                    7.45,
                    8.35
                ],
                [
                    8.96,
                    8.09,
                    8.06,
                    8.96
                ],
                [
                    8.09,
                    8.31,
                    7.96,
                    8.39
                ],
                [
                    8.11,
                    7.48,
                    7.48,
                    8.12
                ],
                [
                    7.44,
                    7.67,
                    7.31,
                    7.75
                ],
                [
                    7.56,
                    7.72,
                    7.41,
                    7.73
                ],
                [
                    7.7,
                    7.78,
                    7.66,
                    8.0
                ],
                [
                    7.76,
                    7.8,
                    7.71,
                    7.87
                ],
                [
                    7.73,
                    7.55,
                    7.5,
                    7.86
                ],
                [
                    7.6,
                    7.53,
                    7.44,
                    7.66
                ],
                [
                    7.4,
                    7.5,
                    7.36,
                    7.51
                ],
                [
                    7.51,
                    7.69,
                    7.51,
                    7.8
                ],
                [
                    7.66,
                    7.48,
                    7.37,
                    7.69
                ],
                [
                    7.45,
                    7.73,
                    7.31,
                    7.8
                ],
                [
                    7.75,
                    7.73,
                    7.65,
                    7.95
                ],
                [
                    7.75,
                    7.71,
                    7.62,
                    7.79
                ],
                [
                    7.72,
                    7.62,
                    7.55,
                    7.72
                ],
                [
                    7.6,
                    7.73,
                    7.51,
                    7.89
                ],
                [
                    7.66,
                    7.67,
                    7.53,
                    7.72
                ],
                [
                    8.05,
                    7.81,
                    7.49,
                    8.44
                ],
                [
                    7.3,
                    7.03,
                    7.03,
                    7.39
                ],
                [
                    6.73,
                    7.07,
                    6.68,
                    7.14
                ],
                [
                    7.07,
                    6.93,
                    6.86,
                    7.09
                ],
                [
                    6.94,
                    6.7,
                    6.7,
                    6.99
                ],
                [
                    6.6,
                    6.67,
                    6.44,
                    6.7
                ],
                [
                    6.68,
                    6.68,
                    6.66,
                    6.79
                ],
                [
                    6.68,
                    6.73,
                    6.65,
                    6.85
                ],
                [
                    6.73,
                    6.75,
                    6.57,
                    6.79
                ],
                [
                    6.4,
                    6.08,
                    6.08,
                    6.4
                ],
                [
                    6.1,
                    5.74,
                    5.5,
                    6.1
                ],
                [
                    5.62,
                    5.83,
                    5.21,
                    5.92
                ],
                [
                    5.99,
                    6.0,
                    5.93,
                    6.11
                ],
                [
                    5.93,
                    6.13,
                    5.93,
                    6.19
                ],
                [
                    6.26,
                    6.29,
                    6.25,
                    6.36
                ],
                [
                    6.29,
                    6.28,
                    6.2,
                    6.38
                ],
                [
                    6.28,
                    6.13,
                    6.02,
                    6.3
                ],
                [
                    6.1,
                    6.17,
                    6.0,
                    6.23
                ],
                [
                    6.13,
                    6.28,
                    6.11,
                    6.34
                ],
                [
                    6.31,
                    6.31,
                    6.23,
                    6.36
                ],
                [
                    6.29,
                    6.31,
                    6.25,
                    6.35
                ],
                [
                    6.31,
                    6.35,
                    6.28,
                    6.39
                ],
                [
                    6.32,
                    6.14,
                    6.08,
                    6.38
                ],
                [
                    6.15,
                    6.09,
                    6.08,
                    6.2
                ],
                [
                    6.05,
                    5.99,
                    5.93,
                    6.12
                ],
                [
                    5.95,
                    6.08,
                    5.89,
                    6.14
                ],
                [
                    6.1,
                    6.15,
                    6.05,
                    6.19
                ],
                [
                    6.17,
                    6.38,
                    6.17,
                    6.38
                ],
                [
                    6.41,
                    6.42,
                    6.33,
                    6.48
                ],
                [
                    6.39,
                    6.55,
                    6.38,
                    6.57
                ],
                [
                    6.54,
                    6.42,
                    6.37,
                    6.55
                ],
                [
                    6.5,
                    6.55,
                    6.42,
                    6.55
                ],
                [
                    6.59,
                    6.48,
                    6.46,
                    6.63
                ],
                [
                    6.5,
                    6.48,
                    6.43,
                    6.57
                ],
                [
                    6.49,
                    6.4,
                    6.37,
                    6.5
                ],
                [
                    6.4,
                    6.48,
                    6.36,
                    6.51
                ],
                [
                    6.5,
                    6.54,
                    6.41,
                    6.54
                ],
                [
                    6.55,
                    6.58,
                    6.46,
                    6.59
                ],
                [
                    6.63,
                    6.44,
                    6.4,
                    6.63
                ],
                [
                    6.43,
                    6.38,
                    6.34,
                    6.53
                ],
                [
                    6.36,
                    6.24,
                    6.2,
                    6.43
                ],
                [
                    6.27,
                    6.36,
                    6.24,
                    6.39
                ],
                [
                    6.31,
                    6.37,
                    6.24,
                    6.38
                ],
                [
                    6.37,
                    6.37,
                    6.32,
                    6.44
                ],
                [
                    6.37,
                    6.52,
                    6.36,
                    6.52
                ],
                [
                    6.51,
                    6.35,
                    6.28,
                    6.51
                ],
                [
                    6.29,
                    6.33,
                    6.29,
                    6.46
                ],
                [
                    6.35,
                    6.37,
                    6.29,
                    6.4
                ],
                [
                    6.36,
                    6.5,
                    6.32,
                    6.51
                ],
                [
                    6.5,
                    6.55,
                    6.44,
                    6.56
                ],
                [
                    6.55,
                    6.58,
                    6.53,
                    6.62
                ],
                [
                    6.56,
                    6.52,
                    6.39,
                    6.62
                ],
                [
                    6.52,
                    6.51,
                    6.49,
                    6.58
                ],
                [
                    6.49,
                    6.46,
                    6.43,
                    6.57
                ],
                [
                    6.43,
                    6.37,
                    6.31,
                    6.52
                ],
                [
                    6.37,
                    6.43,
                    6.35,
                    6.52
                ],
                [
                    6.46,
                    6.43,
                    6.34,
                    6.48
                ],
                [
                    6.41,
                    6.44,
                    6.37,
                    6.47
                ],
                [
                    6.4,
                    6.26,
                    6.24,
                    6.47
                ],
                [
                    6.26,
                    6.13,
                    6.12,
                    6.32
                ],
                [
                    6.11,
                    6.37,
                    6.08,
                    6.4
                ],
                [
                    6.37,
                    6.56,
                    6.37,
                    6.59
                ],
                [
                    6.56,
                    6.57,
                    6.47,
                    6.59
                ],
                [
                    6.58,
                    6.6,
                    6.53,
                    6.64
                ],
                [
                    6.6,
                    6.63,
                    6.59,
                    6.65
                ],
                [
                    6.63,
                    6.73,
                    6.62,
                    6.74
                ],
                [
                    6.75,
                    6.71,
                    6.64,
                    6.76
                ],
                [
                    6.73,
                    6.65,
                    6.59,
                    6.78
                ],
                [
                    6.65,
                    6.66,
                    6.61,
                    6.69
                ],
                [
                    6.66,
                    6.81,
                    6.61,
                    7.27
                ],
                [
                    6.77,
                    6.92,
                    6.68,
                    7.2
                ],
                [
                    6.97,
                    6.93,
                    6.82,
                    7.02
                ],
                [
                    6.91,
                    7.01,
                    6.86,
                    7.14
                ],
                [
                    6.99,
                    7.13,
                    6.91,
                    7.27
                ],
                [
                    7.1,
                    7.16,
                    7.0,
                    7.27
                ],
                [
                    6.81,
                    7.0,
                    6.72,
                    7.01
                ],
                [
                    6.96,
                    6.93,
                    6.82,
                    7.07
                ],
                [
                    6.94,
                    6.84,
                    6.8,
                    6.96
                ],
                [
                    6.84,
                    6.86,
                    6.8,
                    6.88
                ],
                [
                    6.87,
                    6.81,
                    6.77,
                    6.92
                ],
                [
                    6.78,
                    6.87,
                    6.77,
                    6.88
                ],
                [
                    6.9,
                    6.9,
                    6.85,
                    6.98
                ],
                [
                    6.9,
                    6.76,
                    6.74,
                    6.9
                ],
                [
                    6.76,
                    6.8,
                    6.75,
                    6.81
                ],
                [
                    6.79,
                    6.81,
                    6.72,
                    6.81
                ],
                [
                    6.81,
                    6.95,
                    6.78,
                    7.01
                ],
                [
                    6.99,
                    6.96,
                    6.83,
                    6.99
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#880000",
                "borderColor0": "#008F28"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5438\u7b79",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 18,
            "data": [
                [
                    "2025-02-11",
                    7.02
                ],
                [
                    "2025-03-19",
                    7.55
                ],
                [
                    "2025-03-20",
                    7.51
                ],
                [
                    "2025-04-03",
                    6.57
                ],
                [
                    "2025-04-23",
                    6.28
                ],
                [
                    "2025-05-07",
                    6.33
                ],
                [
                    "2025-05-14",
                    6.43
                ],
                [
                    "2025-05-28",
                    6.32
                ],
                [
                    "2025-06-11",
                    6.49
                ],
                [
                    "2025-06-26",
                    6.53
                ],
                [
                    "2025-07-02",
                    6.59
                ],
                [
                    "2025-07-04",
                    6.61
                ],
                [
                    "2025-07-10",
                    6.91
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#F4B400"
            }
        },
        {
            "type": "scatter",
            "name": "\u52a0\u4ed3",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4285F4"
            }
        },
        {
            "type": "scatter",
            "name": "\u6d17\u76d8",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "rect",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#16A085"
            }
        },
        {
            "type": "scatter",
            "name": "\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "circle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#DB4437"
            }
        },
        {
            "type": "scatter",
            "name": "\u9690\u5f62\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "pin",
            "symbolSize": 18,
            "data": [
                [
                    "2025-07-15",
                    6.82
                ],
                [
                    "2025-07-16",
                    6.8
                ],
                [
                    "2025-07-17",
                    6.8
                ],
                [
                    "2025-07-18",
                    6.77
                ],
                [
                    "2025-07-21",
                    6.77
                ],
                [
                    "2025-07-24",
                    6.75
                ],
                [
                    "2025-07-25",
                    6.72
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#8E44AD"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5438\u7b79",
                "\u52a0\u4ed3",
                "\u6d17\u76d8",
                "\u51fa\u8d27",
                "\u9690\u5f62\u51fa\u8d27"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10,
            "top": "2%"
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "interval": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 0
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603421 \u4e3b\u529b\u4fe1\u53f7 (\u542b\u6d17\u76d8)",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "90%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_1b01e4df997f4bd588ee432118028bdd.setOption(option_1b01e4df997f4bd588ee432118028bdd);
            window.addEventListener('resize', function(){
                chart_1b01e4df997f4bd588ee432118028bdd.resize();
            })
    </script>
</body>
</html>
