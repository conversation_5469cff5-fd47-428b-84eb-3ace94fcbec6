<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="a661416a85334d16be0c078f33a24aa0" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_a661416a85334d16be0c078f33a24aa0 = echarts.init(
            document.getElementById('a661416a85334d16be0c078f33a24aa0'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_a661416a85334d16be0c078f33a24aa0 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    6.88,
                    6.56,
                    6.56,
                    6.91
                ],
                [
                    6.66,
                    6.72,
                    6.56,
                    6.82
                ],
                [
                    6.73,
                    6.94,
                    6.67,
                    6.94
                ],
                [
                    7.0,
                    7.02,
                    6.9,
                    7.1
                ],
                [
                    7.05,
                    7.19,
                    7.01,
                    7.19
                ],
                [
                    7.12,
                    7.16,
                    7.02,
                    7.17
                ],
                [
                    7.12,
                    7.31,
                    7.1,
                    7.32
                ],
                [
                    7.32,
                    7.15,
                    7.08,
                    7.32
                ],
                [
                    7.1,
                    7.19,
                    7.05,
                    7.27
                ],
                [
                    7.19,
                    7.23,
                    7.13,
                    7.27
                ],
                [
                    7.23,
                    6.95,
                    6.91,
                    7.25
                ],
                [
                    6.94,
                    7.11,
                    6.92,
                    7.13
                ],
                [
                    7.11,
                    7.24,
                    7.1,
                    7.26
                ],
                [
                    7.24,
                    7.4,
                    7.15,
                    7.7
                ],
                [
                    7.35,
                    7.59,
                    7.33,
                    7.85
                ],
                [
                    7.51,
                    8.35,
                    7.45,
                    8.35
                ],
                [
                    8.96,
                    8.09,
                    8.06,
                    8.96
                ],
                [
                    8.09,
                    8.31,
                    7.96,
                    8.39
                ],
                [
                    8.11,
                    7.48,
                    7.48,
                    8.12
                ],
                [
                    7.44,
                    7.67,
                    7.31,
                    7.75
                ],
                [
                    7.56,
                    7.72,
                    7.41,
                    7.73
                ],
                [
                    7.7,
                    7.78,
                    7.66,
                    8.0
                ],
                [
                    7.76,
                    7.8,
                    7.71,
                    7.87
                ],
                [
                    7.73,
                    7.55,
                    7.5,
                    7.86
                ],
                [
                    7.6,
                    7.53,
                    7.44,
                    7.66
                ],
                [
                    7.4,
                    7.5,
                    7.36,
                    7.51
                ],
                [
                    7.51,
                    7.69,
                    7.51,
                    7.8
                ],
                [
                    7.66,
                    7.48,
                    7.37,
                    7.69
                ],
                [
                    7.45,
                    7.73,
                    7.31,
                    7.8
                ],
                [
                    7.75,
                    7.73,
                    7.65,
                    7.95
                ],
                [
                    7.75,
                    7.71,
                    7.62,
                    7.79
                ],
                [
                    7.72,
                    7.62,
                    7.55,
                    7.72
                ],
                [
                    7.6,
                    7.73,
                    7.51,
                    7.89
                ],
                [
                    7.66,
                    7.67,
                    7.53,
                    7.72
                ],
                [
                    8.05,
                    7.81,
                    7.49,
                    8.44
                ],
                [
                    7.3,
                    7.03,
                    7.03,
                    7.39
                ],
                [
                    6.73,
                    7.07,
                    6.68,
                    7.14
                ],
                [
                    7.07,
                    6.93,
                    6.86,
                    7.09
                ],
                [
                    6.94,
                    6.7,
                    6.7,
                    6.99
                ],
                [
                    6.6,
                    6.67,
                    6.44,
                    6.7
                ],
                [
                    6.68,
                    6.68,
                    6.66,
                    6.79
                ],
                [
                    6.68,
                    6.73,
                    6.65,
                    6.85
                ],
                [
                    6.73,
                    6.75,
                    6.57,
                    6.79
                ],
                [
                    6.4,
                    6.08,
                    6.08,
                    6.4
                ],
                [
                    6.1,
                    5.74,
                    5.5,
                    6.1
                ],
                [
                    5.62,
                    5.83,
                    5.21,
                    5.92
                ],
                [
                    5.99,
                    6.0,
                    5.93,
                    6.11
                ],
                [
                    5.93,
                    6.13,
                    5.93,
                    6.19
                ],
                [
                    6.26,
                    6.29,
                    6.25,
                    6.36
                ],
                [
                    6.29,
                    6.28,
                    6.2,
                    6.38
                ],
                [
                    6.28,
                    6.13,
                    6.02,
                    6.3
                ],
                [
                    6.1,
                    6.17,
                    6.0,
                    6.23
                ],
                [
                    6.13,
                    6.28,
                    6.11,
                    6.34
                ],
                [
                    6.31,
                    6.31,
                    6.23,
                    6.36
                ],
                [
                    6.29,
                    6.31,
                    6.25,
                    6.35
                ],
                [
                    6.31,
                    6.35,
                    6.28,
                    6.39
                ],
                [
                    6.32,
                    6.14,
                    6.08,
                    6.38
                ],
                [
                    6.15,
                    6.09,
                    6.08,
                    6.2
                ],
                [
                    6.05,
                    5.99,
                    5.93,
                    6.12
                ],
                [
                    5.95,
                    6.08,
                    5.89,
                    6.14
                ],
                [
                    6.1,
                    6.15,
                    6.05,
                    6.19
                ],
                [
                    6.17,
                    6.38,
                    6.17,
                    6.38
                ],
                [
                    6.41,
                    6.42,
                    6.33,
                    6.48
                ],
                [
                    6.39,
                    6.55,
                    6.38,
                    6.57
                ],
                [
                    6.54,
                    6.42,
                    6.37,
                    6.55
                ],
                [
                    6.5,
                    6.55,
                    6.42,
                    6.55
                ],
                [
                    6.59,
                    6.48,
                    6.46,
                    6.63
                ],
                [
                    6.5,
                    6.48,
                    6.43,
                    6.57
                ],
                [
                    6.49,
                    6.4,
                    6.37,
                    6.5
                ],
                [
                    6.4,
                    6.48,
                    6.36,
                    6.51
                ],
                [
                    6.5,
                    6.54,
                    6.41,
                    6.54
                ],
                [
                    6.55,
                    6.58,
                    6.46,
                    6.59
                ],
                [
                    6.63,
                    6.44,
                    6.4,
                    6.63
                ],
                [
                    6.43,
                    6.38,
                    6.34,
                    6.53
                ],
                [
                    6.36,
                    6.24,
                    6.2,
                    6.43
                ],
                [
                    6.27,
                    6.36,
                    6.24,
                    6.39
                ],
                [
                    6.31,
                    6.37,
                    6.24,
                    6.38
                ],
                [
                    6.37,
                    6.37,
                    6.32,
                    6.44
                ],
                [
                    6.37,
                    6.52,
                    6.36,
                    6.52
                ],
                [
                    6.51,
                    6.35,
                    6.28,
                    6.51
                ],
                [
                    6.29,
                    6.33,
                    6.29,
                    6.46
                ],
                [
                    6.35,
                    6.37,
                    6.29,
                    6.4
                ],
                [
                    6.36,
                    6.5,
                    6.32,
                    6.51
                ],
                [
                    6.5,
                    6.55,
                    6.44,
                    6.56
                ],
                [
                    6.55,
                    6.58,
                    6.53,
                    6.62
                ],
                [
                    6.56,
                    6.52,
                    6.39,
                    6.62
                ],
                [
                    6.52,
                    6.51,
                    6.49,
                    6.58
                ],
                [
                    6.49,
                    6.46,
                    6.43,
                    6.57
                ],
                [
                    6.43,
                    6.37,
                    6.31,
                    6.52
                ],
                [
                    6.37,
                    6.43,
                    6.35,
                    6.52
                ],
                [
                    6.46,
                    6.43,
                    6.34,
                    6.48
                ],
                [
                    6.41,
                    6.44,
                    6.37,
                    6.47
                ],
                [
                    6.4,
                    6.26,
                    6.24,
                    6.47
                ],
                [
                    6.26,
                    6.13,
                    6.12,
                    6.32
                ],
                [
                    6.11,
                    6.37,
                    6.08,
                    6.4
                ],
                [
                    6.37,
                    6.56,
                    6.37,
                    6.59
                ],
                [
                    6.56,
                    6.57,
                    6.47,
                    6.59
                ],
                [
                    6.58,
                    6.6,
                    6.53,
                    6.64
                ],
                [
                    6.6,
                    6.63,
                    6.59,
                    6.65
                ],
                [
                    6.63,
                    6.73,
                    6.62,
                    6.74
                ],
                [
                    6.75,
                    6.71,
                    6.64,
                    6.76
                ],
                [
                    6.73,
                    6.65,
                    6.59,
                    6.78
                ],
                [
                    6.65,
                    6.66,
                    6.61,
                    6.69
                ],
                [
                    6.66,
                    6.81,
                    6.61,
                    7.27
                ],
                [
                    6.77,
                    6.92,
                    6.68,
                    7.2
                ],
                [
                    6.97,
                    6.93,
                    6.82,
                    7.02
                ],
                [
                    6.91,
                    7.01,
                    6.86,
                    7.14
                ],
                [
                    6.99,
                    7.13,
                    6.91,
                    7.27
                ],
                [
                    7.1,
                    7.16,
                    7.0,
                    7.27
                ],
                [
                    6.81,
                    7.0,
                    6.72,
                    7.01
                ],
                [
                    6.96,
                    6.93,
                    6.82,
                    7.07
                ],
                [
                    6.94,
                    6.84,
                    6.8,
                    6.96
                ],
                [
                    6.84,
                    6.86,
                    6.8,
                    6.88
                ],
                [
                    6.87,
                    6.81,
                    6.77,
                    6.92
                ],
                [
                    6.78,
                    6.87,
                    6.77,
                    6.88
                ],
                [
                    6.9,
                    6.9,
                    6.85,
                    6.98
                ],
                [
                    6.9,
                    6.76,
                    6.74,
                    6.9
                ],
                [
                    6.76,
                    6.8,
                    6.75,
                    6.81
                ],
                [
                    6.79,
                    6.81,
                    6.72,
                    6.81
                ],
                [
                    6.81,
                    6.95,
                    6.78,
                    7.01
                ],
                [
                    6.99,
                    6.96,
                    6.83,
                    6.99
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -10.70
                ],
                [
                    "2025-02-05",
                    -0.10
                ],
                [
                    "2025-02-06",
                    2.81
                ],
                [
                    "2025-02-07",
                    -1.65
                ],
                [
                    "2025-02-10",
                    1.80
                ],
                [
                    "2025-02-11",
                    5.74
                ],
                [
                    "2025-02-12",
                    -3.18
                ],
                [
                    "2025-02-13",
                    -6.57
                ],
                [
                    "2025-02-14",
                    -2.71
                ],
                [
                    "2025-02-17",
                    -7.36
                ],
                [
                    "2025-02-18",
                    -15.42
                ],
                [
                    "2025-02-19",
                    -6.02
                ],
                [
                    "2025-02-20",
                    9.66
                ],
                [
                    "2025-02-21",
                    0.87
                ],
                [
                    "2025-02-24",
                    5.71
                ],
                [
                    "2025-02-25",
                    38.48
                ],
                [
                    "2025-02-26",
                    -4.06
                ],
                [
                    "2025-02-27",
                    1.76
                ],
                [
                    "2025-02-28",
                    -8.44
                ],
                [
                    "2025-03-03",
                    2.76
                ],
                [
                    "2025-03-04",
                    2.90
                ],
                [
                    "2025-03-05",
                    -3.50
                ],
                [
                    "2025-03-06",
                    -9.44
                ],
                [
                    "2025-03-07",
                    -6.11
                ],
                [
                    "2025-03-10",
                    -10.05
                ],
                [
                    "2025-03-11",
                    -7.34
                ],
                [
                    "2025-03-12",
                    5.87
                ],
                [
                    "2025-03-13",
                    -2.60
                ],
                [
                    "2025-03-14",
                    7.95
                ],
                [
                    "2025-03-17",
                    -4.52
                ],
                [
                    "2025-03-18",
                    -6.72
                ],
                [
                    "2025-03-19",
                    4.05
                ],
                [
                    "2025-03-20",
                    6.78
                ],
                [
                    "2025-03-21",
                    -3.07
                ],
                [
                    "2025-03-24",
                    -5.81
                ],
                [
                    "2025-03-25",
                    -8.68
                ],
                [
                    "2025-03-26",
                    1.66
                ],
                [
                    "2025-03-27",
                    -3.24
                ],
                [
                    "2025-03-28",
                    -20.41
                ],
                [
                    "2025-03-31",
                    -8.45
                ],
                [
                    "2025-04-01",
                    -6.62
                ],
                [
                    "2025-04-02",
                    0.30
                ],
                [
                    "2025-04-03",
                    8.64
                ],
                [
                    "2025-04-07",
                    -9.11
                ],
                [
                    "2025-04-08",
                    -2.65
                ],
                [
                    "2025-04-09",
                    -9.85
                ],
                [
                    "2025-04-10",
                    -7.63
                ],
                [
                    "2025-04-11",
                    10.51
                ],
                [
                    "2025-04-14",
                    0.28
                ],
                [
                    "2025-04-15",
                    -3.49
                ],
                [
                    "2025-04-16",
                    -7.21
                ],
                [
                    "2025-04-17",
                    -17.27
                ],
                [
                    "2025-04-18",
                    -1.97
                ],
                [
                    "2025-04-21",
                    -5.01
                ],
                [
                    "2025-04-22",
                    0.92
                ],
                [
                    "2025-04-23",
                    10.92
                ],
                [
                    "2025-04-24",
                    -7.47
                ],
                [
                    "2025-04-25",
                    -8.43
                ],
                [
                    "2025-04-28",
                    -19.77
                ],
                [
                    "2025-04-29",
                    5.15
                ],
                [
                    "2025-04-30",
                    -11.15
                ],
                [
                    "2025-05-06",
                    -11.34
                ],
                [
                    "2025-05-07",
                    10.08
                ],
                [
                    "2025-05-08",
                    -6.79
                ],
                [
                    "2025-05-09",
                    2.31
                ],
                [
                    "2025-05-12",
                    4.61
                ],
                [
                    "2025-05-13",
                    0.81
                ],
                [
                    "2025-05-14",
                    8.39
                ],
                [
                    "2025-05-15",
                    0.11
                ],
                [
                    "2025-05-16",
                    -2.88
                ],
                [
                    "2025-05-19",
                    -4.74
                ],
                [
                    "2025-05-20",
                    -2.73
                ],
                [
                    "2025-05-21",
                    -5.14
                ],
                [
                    "2025-05-22",
                    2.72
                ],
                [
                    "2025-05-23",
                    -1.70
                ],
                [
                    "2025-05-26",
                    -13.26
                ],
                [
                    "2025-05-27",
                    -0.79
                ],
                [
                    "2025-05-28",
                    5.66
                ],
                [
                    "2025-05-29",
                    -11.75
                ],
                [
                    "2025-05-30",
                    -0.91
                ],
                [
                    "2025-06-03",
                    1.59
                ],
                [
                    "2025-06-04",
                    0.62
                ],
                [
                    "2025-06-05",
                    0.29
                ],
                [
                    "2025-06-06",
                    -6.10
                ],
                [
                    "2025-06-09",
                    -14.30
                ],
                [
                    "2025-06-10",
                    -2.87
                ],
                [
                    "2025-06-11",
                    5.49
                ],
                [
                    "2025-06-12",
                    -9.68
                ],
                [
                    "2025-06-13",
                    2.86
                ],
                [
                    "2025-06-16",
                    -5.54
                ],
                [
                    "2025-06-17",
                    2.43
                ],
                [
                    "2025-06-18",
                    -3.76
                ],
                [
                    "2025-06-19",
                    1.68
                ],
                [
                    "2025-06-20",
                    -23.84
                ],
                [
                    "2025-06-23",
                    -2.65
                ],
                [
                    "2025-06-24",
                    -2.38
                ],
                [
                    "2025-06-25",
                    -6.25
                ],
                [
                    "2025-06-26",
                    14.84
                ],
                [
                    "2025-06-27",
                    -15.37
                ],
                [
                    "2025-06-30",
                    1.79
                ],
                [
                    "2025-07-01",
                    -1.93
                ],
                [
                    "2025-07-02",
                    3.74
                ],
                [
                    "2025-07-03",
                    -9.38
                ],
                [
                    "2025-07-04",
                    9.06
                ],
                [
                    "2025-07-07",
                    2.28
                ],
                [
                    "2025-07-08",
                    1.56
                ],
                [
                    "2025-07-09",
                    -5.80
                ],
                [
                    "2025-07-10",
                    9.07
                ],
                [
                    "2025-07-11",
                    1.29
                ],
                [
                    "2025-07-14",
                    -11.13
                ],
                [
                    "2025-07-15",
                    -8.16
                ],
                [
                    "2025-07-16",
                    -14.32
                ],
                [
                    "2025-07-17",
                    -11.80
                ],
                [
                    "2025-07-18",
                    -8.59
                ],
                [
                    "2025-07-21",
                    -3.17
                ],
                [
                    "2025-07-22",
                    0.99
                ],
                [
                    "2025-07-23",
                    -8.97
                ],
                [
                    "2025-07-24",
                    -7.55
                ],
                [
                    "2025-07-25",
                    -14.71
                ],
                [
                    "2025-07-28",
                    10.33
                ],
                [
                    "2025-07-29",
                    -8.50
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    1.09
                ],
                [
                    "2025-02-05",
                    0.54
                ],
                [
                    "2025-02-06",
                    2.60
                ],
                [
                    "2025-02-07",
                    -2.82
                ],
                [
                    "2025-02-10",
                    -1.71
                ],
                [
                    "2025-02-11",
                    -2.38
                ],
                [
                    "2025-02-12",
                    1.81
                ],
                [
                    "2025-02-13",
                    -3.00
                ],
                [
                    "2025-02-14",
                    -2.10
                ],
                [
                    "2025-02-17",
                    4.25
                ],
                [
                    "2025-02-18",
                    -4.79
                ],
                [
                    "2025-02-19",
                    -3.18
                ],
                [
                    "2025-02-20",
                    -2.79
                ],
                [
                    "2025-02-21",
                    2.02
                ],
                [
                    "2025-02-24",
                    0.57
                ],
                [
                    "2025-02-25",
                    -13.18
                ],
                [
                    "2025-02-26",
                    -0.78
                ],
                [
                    "2025-02-27",
                    3.46
                ],
                [
                    "2025-02-28",
                    -0.63
                ],
                [
                    "2025-03-03",
                    -2.39
                ],
                [
                    "2025-03-04",
                    1.84
                ],
                [
                    "2025-03-05",
                    1.35
                ],
                [
                    "2025-03-06",
                    -2.02
                ],
                [
                    "2025-03-07",
                    -7.90
                ],
                [
                    "2025-03-10",
                    -4.22
                ],
                [
                    "2025-03-11",
                    -4.96
                ],
                [
                    "2025-03-12",
                    -2.73
                ],
                [
                    "2025-03-13",
                    -3.29
                ],
                [
                    "2025-03-14",
                    -2.24
                ],
                [
                    "2025-03-17",
                    0.76
                ],
                [
                    "2025-03-18",
                    -3.77
                ],
                [
                    "2025-03-19",
                    -7.11
                ],
                [
                    "2025-03-20",
                    1.46
                ],
                [
                    "2025-03-21",
                    -0.32
                ],
                [
                    "2025-03-24",
                    3.61
                ],
                [
                    "2025-03-25",
                    -0.26
                ],
                [
                    "2025-03-26",
                    -5.92
                ],
                [
                    "2025-03-27",
                    -7.33
                ],
                [
                    "2025-03-28",
                    0.01
                ],
                [
                    "2025-03-31",
                    -0.85
                ],
                [
                    "2025-04-01",
                    -3.42
                ],
                [
                    "2025-04-02",
                    -3.14
                ],
                [
                    "2025-04-03",
                    -6.13
                ],
                [
                    "2025-04-07",
                    -1.64
                ],
                [
                    "2025-04-08",
                    3.38
                ],
                [
                    "2025-04-09",
                    1.40
                ],
                [
                    "2025-04-10",
                    -2.37
                ],
                [
                    "2025-04-11",
                    -4.29
                ],
                [
                    "2025-04-14",
                    -10.34
                ],
                [
                    "2025-04-15",
                    -7.87
                ],
                [
                    "2025-04-16",
                    -1.36
                ],
                [
                    "2025-04-17",
                    -6.97
                ],
                [
                    "2025-04-18",
                    4.29
                ],
                [
                    "2025-04-21",
                    -5.02
                ],
                [
                    "2025-04-22",
                    -4.89
                ],
                [
                    "2025-04-23",
                    6.51
                ],
                [
                    "2025-04-24",
                    -8.50
                ],
                [
                    "2025-04-25",
                    0.53
                ],
                [
                    "2025-04-28",
                    -1.14
                ],
                [
                    "2025-04-29",
                    -0.01
                ],
                [
                    "2025-04-30",
                    -7.68
                ],
                [
                    "2025-05-06",
                    1.52
                ],
                [
                    "2025-05-07",
                    -7.24
                ],
                [
                    "2025-05-08",
                    -3.30
                ],
                [
                    "2025-05-09",
                    -5.32
                ],
                [
                    "2025-05-12",
                    0.31
                ],
                [
                    "2025-05-13",
                    -3.03
                ],
                [
                    "2025-05-14",
                    0.91
                ],
                [
                    "2025-05-15",
                    -2.18
                ],
                [
                    "2025-05-16",
                    2.06
                ],
                [
                    "2025-05-19",
                    -6.56
                ],
                [
                    "2025-05-20",
                    4.94
                ],
                [
                    "2025-05-21",
                    -1.13
                ],
                [
                    "2025-05-22",
                    -6.47
                ],
                [
                    "2025-05-23",
                    0.89
                ],
                [
                    "2025-05-26",
                    6.36
                ],
                [
                    "2025-05-27",
                    0.94
                ],
                [
                    "2025-05-28",
                    5.44
                ],
                [
                    "2025-05-29",
                    -2.74
                ],
                [
                    "2025-05-30",
                    8.83
                ],
                [
                    "2025-06-03",
                    0.12
                ],
                [
                    "2025-06-04",
                    0.51
                ],
                [
                    "2025-06-05",
                    0.11
                ],
                [
                    "2025-06-06",
                    -8.46
                ],
                [
                    "2025-06-09",
                    -4.62
                ],
                [
                    "2025-06-10",
                    -0.28
                ],
                [
                    "2025-06-11",
                    5.04
                ],
                [
                    "2025-06-12",
                    8.94
                ],
                [
                    "2025-06-13",
                    11.62
                ],
                [
                    "2025-06-16",
                    -2.78
                ],
                [
                    "2025-06-17",
                    3.23
                ],
                [
                    "2025-06-18",
                    2.52
                ],
                [
                    "2025-06-19",
                    -1.63
                ],
                [
                    "2025-06-20",
                    15.29
                ],
                [
                    "2025-06-23",
                    -6.33
                ],
                [
                    "2025-06-24",
                    -0.06
                ],
                [
                    "2025-06-25",
                    -1.03
                ],
                [
                    "2025-06-26",
                    -9.04
                ],
                [
                    "2025-06-27",
                    4.24
                ],
                [
                    "2025-06-30",
                    -7.54
                ],
                [
                    "2025-07-01",
                    -8.80
                ],
                [
                    "2025-07-02",
                    0.33
                ],
                [
                    "2025-07-03",
                    8.42
                ],
                [
                    "2025-07-04",
                    3.95
                ],
                [
                    "2025-07-07",
                    5.48
                ],
                [
                    "2025-07-08",
                    -3.07
                ],
                [
                    "2025-07-09",
                    1.42
                ],
                [
                    "2025-07-10",
                    0.72
                ],
                [
                    "2025-07-11",
                    -1.09
                ],
                [
                    "2025-07-14",
                    -4.15
                ],
                [
                    "2025-07-15",
                    -4.50
                ],
                [
                    "2025-07-16",
                    -0.57
                ],
                [
                    "2025-07-17",
                    -0.48
                ],
                [
                    "2025-07-18",
                    -3.67
                ],
                [
                    "2025-07-21",
                    -5.04
                ],
                [
                    "2025-07-22",
                    0.79
                ],
                [
                    "2025-07-23",
                    -7.06
                ],
                [
                    "2025-07-24",
                    -0.37
                ],
                [
                    "2025-07-25",
                    -7.25
                ],
                [
                    "2025-07-28",
                    -0.56
                ],
                [
                    "2025-07-29",
                    -1.62
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    9.61
                ],
                [
                    "2025-02-05",
                    -0.45
                ],
                [
                    "2025-02-06",
                    -5.40
                ],
                [
                    "2025-02-07",
                    4.47
                ],
                [
                    "2025-02-10",
                    -0.10
                ],
                [
                    "2025-02-11",
                    -3.37
                ],
                [
                    "2025-02-12",
                    1.37
                ],
                [
                    "2025-02-13",
                    9.57
                ],
                [
                    "2025-02-14",
                    4.81
                ],
                [
                    "2025-02-17",
                    3.11
                ],
                [
                    "2025-02-18",
                    20.21
                ],
                [
                    "2025-02-19",
                    9.20
                ],
                [
                    "2025-02-20",
                    -6.88
                ],
                [
                    "2025-02-21",
                    -2.89
                ],
                [
                    "2025-02-24",
                    -6.28
                ],
                [
                    "2025-02-25",
                    -25.30
                ],
                [
                    "2025-02-26",
                    4.84
                ],
                [
                    "2025-02-27",
                    -5.22
                ],
                [
                    "2025-02-28",
                    9.07
                ],
                [
                    "2025-03-03",
                    -0.37
                ],
                [
                    "2025-03-04",
                    -4.74
                ],
                [
                    "2025-03-05",
                    2.14
                ],
                [
                    "2025-03-06",
                    11.45
                ],
                [
                    "2025-03-07",
                    14.01
                ],
                [
                    "2025-03-10",
                    14.27
                ],
                [
                    "2025-03-11",
                    12.30
                ],
                [
                    "2025-03-12",
                    -3.13
                ],
                [
                    "2025-03-13",
                    5.89
                ],
                [
                    "2025-03-14",
                    -5.70
                ],
                [
                    "2025-03-17",
                    3.76
                ],
                [
                    "2025-03-18",
                    10.50
                ],
                [
                    "2025-03-19",
                    3.06
                ],
                [
                    "2025-03-20",
                    -8.24
                ],
                [
                    "2025-03-21",
                    3.38
                ],
                [
                    "2025-03-24",
                    2.20
                ],
                [
                    "2025-03-25",
                    8.94
                ],
                [
                    "2025-03-26",
                    4.25
                ],
                [
                    "2025-03-27",
                    10.57
                ],
                [
                    "2025-03-28",
                    20.40
                ],
                [
                    "2025-03-31",
                    9.29
                ],
                [
                    "2025-04-01",
                    10.04
                ],
                [
                    "2025-04-02",
                    2.84
                ],
                [
                    "2025-04-03",
                    -2.51
                ],
                [
                    "2025-04-07",
                    10.75
                ],
                [
                    "2025-04-08",
                    -0.72
                ],
                [
                    "2025-04-09",
                    8.45
                ],
                [
                    "2025-04-10",
                    9.99
                ],
                [
                    "2025-04-11",
                    -6.22
                ],
                [
                    "2025-04-14",
                    10.05
                ],
                [
                    "2025-04-15",
                    11.35
                ],
                [
                    "2025-04-16",
                    8.58
                ],
                [
                    "2025-04-17",
                    24.25
                ],
                [
                    "2025-04-18",
                    -2.32
                ],
                [
                    "2025-04-21",
                    10.03
                ],
                [
                    "2025-04-22",
                    3.97
                ],
                [
                    "2025-04-23",
                    -17.42
                ],
                [
                    "2025-04-24",
                    15.97
                ],
                [
                    "2025-04-25",
                    7.90
                ],
                [
                    "2025-04-28",
                    20.90
                ],
                [
                    "2025-04-29",
                    -5.14
                ],
                [
                    "2025-04-30",
                    18.83
                ],
                [
                    "2025-05-06",
                    9.82
                ],
                [
                    "2025-05-07",
                    -2.84
                ],
                [
                    "2025-05-08",
                    10.09
                ],
                [
                    "2025-05-09",
                    3.02
                ],
                [
                    "2025-05-12",
                    -4.92
                ],
                [
                    "2025-05-13",
                    2.23
                ],
                [
                    "2025-05-14",
                    -9.31
                ],
                [
                    "2025-05-15",
                    2.07
                ],
                [
                    "2025-05-16",
                    0.81
                ],
                [
                    "2025-05-19",
                    11.31
                ],
                [
                    "2025-05-20",
                    -2.21
                ],
                [
                    "2025-05-21",
                    6.27
                ],
                [
                    "2025-05-22",
                    3.74
                ],
                [
                    "2025-05-23",
                    0.81
                ],
                [
                    "2025-05-26",
                    6.89
                ],
                [
                    "2025-05-27",
                    -0.16
                ],
                [
                    "2025-05-28",
                    -11.11
                ],
                [
                    "2025-05-29",
                    14.49
                ],
                [
                    "2025-05-30",
                    -7.93
                ],
                [
                    "2025-06-03",
                    -1.71
                ],
                [
                    "2025-06-04",
                    -1.13
                ],
                [
                    "2025-06-05",
                    -0.40
                ],
                [
                    "2025-06-06",
                    14.56
                ],
                [
                    "2025-06-09",
                    18.92
                ],
                [
                    "2025-06-10",
                    3.16
                ],
                [
                    "2025-06-11",
                    -10.53
                ],
                [
                    "2025-06-12",
                    0.74
                ],
                [
                    "2025-06-13",
                    -14.47
                ],
                [
                    "2025-06-16",
                    8.32
                ],
                [
                    "2025-06-17",
                    -5.66
                ],
                [
                    "2025-06-18",
                    1.25
                ],
                [
                    "2025-06-19",
                    -0.05
                ],
                [
                    "2025-06-20",
                    8.55
                ],
                [
                    "2025-06-23",
                    8.98
                ],
                [
                    "2025-06-24",
                    2.44
                ],
                [
                    "2025-06-25",
                    7.28
                ],
                [
                    "2025-06-26",
                    -5.80
                ],
                [
                    "2025-06-27",
                    11.13
                ],
                [
                    "2025-06-30",
                    5.75
                ],
                [
                    "2025-07-01",
                    10.72
                ],
                [
                    "2025-07-02",
                    -4.07
                ],
                [
                    "2025-07-03",
                    0.96
                ],
                [
                    "2025-07-04",
                    -13.00
                ],
                [
                    "2025-07-07",
                    -7.76
                ],
                [
                    "2025-07-08",
                    1.51
                ],
                [
                    "2025-07-09",
                    4.38
                ],
                [
                    "2025-07-10",
                    -9.78
                ],
                [
                    "2025-07-11",
                    -0.20
                ],
                [
                    "2025-07-14",
                    15.27
                ],
                [
                    "2025-07-15",
                    12.66
                ],
                [
                    "2025-07-16",
                    14.89
                ],
                [
                    "2025-07-17",
                    12.28
                ],
                [
                    "2025-07-18",
                    12.26
                ],
                [
                    "2025-07-21",
                    8.21
                ],
                [
                    "2025-07-22",
                    -1.78
                ],
                [
                    "2025-07-23",
                    16.04
                ],
                [
                    "2025-07-24",
                    7.91
                ],
                [
                    "2025-07-25",
                    21.95
                ],
                [
                    "2025-07-28",
                    -9.78
                ],
                [
                    "2025-07-29",
                    10.11
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-11",
                    5.74
                ],
                [
                    "2025-02-12",
                    -3.18
                ],
                [
                    "2025-02-25",
                    38.48
                ],
                [
                    "2025-02-26",
                    -4.06
                ],
                [
                    "2025-02-27",
                    1.76
                ],
                [
                    "2025-02-28",
                    -8.44
                ],
                [
                    "2025-03-03",
                    2.76
                ],
                [
                    "2025-05-15",
                    0.11
                ],
                [
                    "2025-05-16",
                    -2.88
                ],
                [
                    "2025-07-07",
                    2.28
                ],
                [
                    "2025-07-08",
                    1.56
                ],
                [
                    "2025-07-10",
                    9.07
                ],
                [
                    "2025-07-11",
                    1.29
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-06-03",
                    0.12
                ],
                [
                    "2025-06-04",
                    0.51
                ],
                [
                    "2025-06-13",
                    11.62
                ],
                [
                    "2025-06-16",
                    -2.78
                ],
                [
                    "2025-06-17",
                    3.23
                ],
                [
                    "2025-06-18",
                    2.52
                ],
                [
                    "2025-06-19",
                    -1.63
                ],
                [
                    "2025-07-07",
                    5.48
                ],
                [
                    "2025-07-08",
                    -3.07
                ],
                [
                    "2025-07-09",
                    1.42
                ],
                [
                    "2025-07-10",
                    0.72
                ],
                [
                    "2025-07-11",
                    -1.09
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    -0.10
                ],
                [
                    "2025-02-13",
                    9.57
                ],
                [
                    "2025-02-14",
                    4.81
                ],
                [
                    "2025-02-17",
                    3.11
                ],
                [
                    "2025-02-18",
                    20.21
                ],
                [
                    "2025-02-19",
                    9.20
                ],
                [
                    "2025-02-20",
                    -6.88
                ],
                [
                    "2025-02-21",
                    -2.89
                ],
                [
                    "2025-02-24",
                    -6.28
                ],
                [
                    "2025-03-04",
                    -4.74
                ],
                [
                    "2025-03-05",
                    2.14
                ],
                [
                    "2025-03-06",
                    11.45
                ],
                [
                    "2025-03-07",
                    14.01
                ],
                [
                    "2025-03-10",
                    14.27
                ],
                [
                    "2025-03-11",
                    12.30
                ],
                [
                    "2025-03-12",
                    -3.13
                ],
                [
                    "2025-03-13",
                    5.89
                ],
                [
                    "2025-03-14",
                    -5.70
                ],
                [
                    "2025-03-17",
                    3.76
                ],
                [
                    "2025-03-18",
                    10.50
                ],
                [
                    "2025-03-19",
                    3.06
                ],
                [
                    "2025-03-21",
                    3.38
                ],
                [
                    "2025-03-24",
                    2.20
                ],
                [
                    "2025-03-25",
                    8.94
                ],
                [
                    "2025-03-26",
                    4.25
                ],
                [
                    "2025-03-27",
                    10.57
                ],
                [
                    "2025-03-28",
                    20.40
                ],
                [
                    "2025-03-31",
                    9.29
                ],
                [
                    "2025-04-01",
                    10.04
                ],
                [
                    "2025-04-02",
                    2.84
                ],
                [
                    "2025-04-03",
                    -2.51
                ],
                [
                    "2025-04-07",
                    10.75
                ],
                [
                    "2025-04-08",
                    -0.72
                ],
                [
                    "2025-04-09",
                    8.45
                ],
                [
                    "2025-04-10",
                    9.99
                ],
                [
                    "2025-04-11",
                    -6.22
                ],
                [
                    "2025-04-14",
                    10.05
                ],
                [
                    "2025-04-15",
                    11.35
                ],
                [
                    "2025-04-16",
                    8.58
                ],
                [
                    "2025-04-17",
                    24.25
                ],
                [
                    "2025-04-18",
                    -2.32
                ],
                [
                    "2025-04-21",
                    10.03
                ],
                [
                    "2025-04-22",
                    3.97
                ],
                [
                    "2025-04-23",
                    -17.42
                ],
                [
                    "2025-04-24",
                    15.97
                ],
                [
                    "2025-04-25",
                    7.90
                ],
                [
                    "2025-04-28",
                    20.90
                ],
                [
                    "2025-04-29",
                    -5.14
                ],
                [
                    "2025-04-30",
                    18.83
                ],
                [
                    "2025-05-06",
                    9.82
                ],
                [
                    "2025-05-07",
                    -2.84
                ],
                [
                    "2025-05-08",
                    10.09
                ],
                [
                    "2025-05-09",
                    3.02
                ],
                [
                    "2025-05-12",
                    -4.92
                ],
                [
                    "2025-05-20",
                    -2.21
                ],
                [
                    "2025-05-21",
                    6.27
                ],
                [
                    "2025-05-22",
                    3.74
                ],
                [
                    "2025-05-23",
                    0.81
                ],
                [
                    "2025-05-26",
                    6.89
                ],
                [
                    "2025-05-27",
                    -0.16
                ],
                [
                    "2025-05-28",
                    -11.11
                ],
                [
                    "2025-05-29",
                    14.49
                ],
                [
                    "2025-05-30",
                    -7.93
                ],
                [
                    "2025-06-05",
                    -0.40
                ],
                [
                    "2025-06-06",
                    14.56
                ],
                [
                    "2025-06-09",
                    18.92
                ],
                [
                    "2025-06-10",
                    3.16
                ],
                [
                    "2025-06-11",
                    -10.53
                ],
                [
                    "2025-06-12",
                    0.74
                ],
                [
                    "2025-06-20",
                    8.55
                ],
                [
                    "2025-06-23",
                    8.98
                ],
                [
                    "2025-06-24",
                    2.44
                ],
                [
                    "2025-06-25",
                    7.28
                ],
                [
                    "2025-06-26",
                    -5.80
                ],
                [
                    "2025-06-27",
                    11.13
                ],
                [
                    "2025-06-30",
                    5.75
                ],
                [
                    "2025-07-01",
                    10.72
                ],
                [
                    "2025-07-03",
                    0.96
                ],
                [
                    "2025-07-14",
                    15.27
                ],
                [
                    "2025-07-15",
                    12.66
                ],
                [
                    "2025-07-16",
                    14.89
                ],
                [
                    "2025-07-17",
                    12.28
                ],
                [
                    "2025-07-18",
                    12.26
                ],
                [
                    "2025-07-21",
                    8.21
                ],
                [
                    "2025-07-22",
                    -1.78
                ],
                [
                    "2025-07-23",
                    16.04
                ],
                [
                    "2025-07-24",
                    7.91
                ],
                [
                    "2025-07-25",
                    21.95
                ],
                [
                    "2025-07-28",
                    -9.78
                ],
                [
                    "2025-07-29",
                    10.11
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603421 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_a661416a85334d16be0c078f33a24aa0.setOption(option_a661416a85334d16be0c078f33a24aa0);
            window.addEventListener('resize', function(){
                chart_a661416a85334d16be0c078f33a24aa0.resize();
            })
    </script>
</body>
</html>
