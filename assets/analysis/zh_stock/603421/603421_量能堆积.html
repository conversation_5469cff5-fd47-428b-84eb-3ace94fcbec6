<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="ffdd5ac046b24182967b149716274b9b" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_ffdd5ac046b24182967b149716274b9b = echarts.init(
            document.getElementById('ffdd5ac046b24182967b149716274b9b'), 'white', {renderer: 'canvas'});
        var option_ffdd5ac046b24182967b149716274b9b = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    6.88,
                    6.56,
                    6.56,
                    6.91
                ],
                [
                    6.66,
                    6.72,
                    6.56,
                    6.82
                ],
                [
                    6.73,
                    6.94,
                    6.67,
                    6.94
                ],
                [
                    7.00,
                    7.02,
                    6.90,
                    7.10
                ],
                [
                    7.05,
                    7.19,
                    7.01,
                    7.19
                ],
                [
                    7.12,
                    7.16,
                    7.02,
                    7.17
                ],
                [
                    7.12,
                    7.31,
                    7.10,
                    7.32
                ],
                [
                    7.32,
                    7.15,
                    7.08,
                    7.32
                ],
                [
                    7.10,
                    7.19,
                    7.05,
                    7.27
                ],
                [
                    7.19,
                    7.23,
                    7.13,
                    7.27
                ],
                [
                    7.23,
                    6.95,
                    6.91,
                    7.25
                ],
                [
                    6.94,
                    7.11,
                    6.92,
                    7.13
                ],
                [
                    7.11,
                    7.24,
                    7.10,
                    7.26
                ],
                [
                    7.24,
                    7.40,
                    7.15,
                    7.70
                ],
                [
                    7.35,
                    7.59,
                    7.33,
                    7.85
                ],
                [
                    7.51,
                    8.35,
                    7.45,
                    8.35
                ],
                [
                    8.96,
                    8.09,
                    8.06,
                    8.96
                ],
                [
                    8.09,
                    8.31,
                    7.96,
                    8.39
                ],
                [
                    8.11,
                    7.48,
                    7.48,
                    8.12
                ],
                [
                    7.44,
                    7.67,
                    7.31,
                    7.75
                ],
                [
                    7.56,
                    7.72,
                    7.41,
                    7.73
                ],
                [
                    7.70,
                    7.78,
                    7.66,
                    8.00
                ],
                [
                    7.76,
                    7.80,
                    7.71,
                    7.87
                ],
                [
                    7.73,
                    7.55,
                    7.50,
                    7.86
                ],
                [
                    7.60,
                    7.53,
                    7.44,
                    7.66
                ],
                [
                    7.40,
                    7.50,
                    7.36,
                    7.51
                ],
                [
                    7.51,
                    7.69,
                    7.51,
                    7.80
                ],
                [
                    7.66,
                    7.48,
                    7.37,
                    7.69
                ],
                [
                    7.45,
                    7.73,
                    7.31,
                    7.80
                ],
                [
                    7.75,
                    7.73,
                    7.65,
                    7.95
                ],
                [
                    7.75,
                    7.71,
                    7.62,
                    7.79
                ],
                [
                    7.72,
                    7.62,
                    7.55,
                    7.72
                ],
                [
                    7.60,
                    7.73,
                    7.51,
                    7.89
                ],
                [
                    7.66,
                    7.67,
                    7.53,
                    7.72
                ],
                [
                    8.05,
                    7.81,
                    7.49,
                    8.44
                ],
                [
                    7.30,
                    7.03,
                    7.03,
                    7.39
                ],
                [
                    6.73,
                    7.07,
                    6.68,
                    7.14
                ],
                [
                    7.07,
                    6.93,
                    6.86,
                    7.09
                ],
                [
                    6.94,
                    6.70,
                    6.70,
                    6.99
                ],
                [
                    6.60,
                    6.67,
                    6.44,
                    6.70
                ],
                [
                    6.68,
                    6.68,
                    6.66,
                    6.79
                ],
                [
                    6.68,
                    6.73,
                    6.65,
                    6.85
                ],
                [
                    6.73,
                    6.75,
                    6.57,
                    6.79
                ],
                [
                    6.40,
                    6.08,
                    6.08,
                    6.40
                ],
                [
                    6.10,
                    5.74,
                    5.50,
                    6.10
                ],
                [
                    5.62,
                    5.83,
                    5.21,
                    5.92
                ],
                [
                    5.99,
                    6.00,
                    5.93,
                    6.11
                ],
                [
                    5.93,
                    6.13,
                    5.93,
                    6.19
                ],
                [
                    6.26,
                    6.29,
                    6.25,
                    6.36
                ],
                [
                    6.29,
                    6.28,
                    6.20,
                    6.38
                ],
                [
                    6.28,
                    6.13,
                    6.02,
                    6.30
                ],
                [
                    6.10,
                    6.17,
                    6.00,
                    6.23
                ],
                [
                    6.13,
                    6.28,
                    6.11,
                    6.34
                ],
                [
                    6.31,
                    6.31,
                    6.23,
                    6.36
                ],
                [
                    6.29,
                    6.31,
                    6.25,
                    6.35
                ],
                [
                    6.31,
                    6.35,
                    6.28,
                    6.39
                ],
                [
                    6.32,
                    6.14,
                    6.08,
                    6.38
                ],
                [
                    6.15,
                    6.09,
                    6.08,
                    6.20
                ],
                [
                    6.05,
                    5.99,
                    5.93,
                    6.12
                ],
                [
                    5.95,
                    6.08,
                    5.89,
                    6.14
                ],
                [
                    6.10,
                    6.15,
                    6.05,
                    6.19
                ],
                [
                    6.17,
                    6.38,
                    6.17,
                    6.38
                ],
                [
                    6.41,
                    6.42,
                    6.33,
                    6.48
                ],
                [
                    6.39,
                    6.55,
                    6.38,
                    6.57
                ],
                [
                    6.54,
                    6.42,
                    6.37,
                    6.55
                ],
                [
                    6.50,
                    6.55,
                    6.42,
                    6.55
                ],
                [
                    6.59,
                    6.48,
                    6.46,
                    6.63
                ],
                [
                    6.50,
                    6.48,
                    6.43,
                    6.57
                ],
                [
                    6.49,
                    6.40,
                    6.37,
                    6.50
                ],
                [
                    6.40,
                    6.48,
                    6.36,
                    6.51
                ],
                [
                    6.50,
                    6.54,
                    6.41,
                    6.54
                ],
                [
                    6.55,
                    6.58,
                    6.46,
                    6.59
                ],
                [
                    6.63,
                    6.44,
                    6.40,
                    6.63
                ],
                [
                    6.43,
                    6.38,
                    6.34,
                    6.53
                ],
                [
                    6.36,
                    6.24,
                    6.20,
                    6.43
                ],
                [
                    6.27,
                    6.36,
                    6.24,
                    6.39
                ],
                [
                    6.31,
                    6.37,
                    6.24,
                    6.38
                ],
                [
                    6.37,
                    6.37,
                    6.32,
                    6.44
                ],
                [
                    6.37,
                    6.52,
                    6.36,
                    6.52
                ],
                [
                    6.51,
                    6.35,
                    6.28,
                    6.51
                ],
                [
                    6.29,
                    6.33,
                    6.29,
                    6.46
                ],
                [
                    6.35,
                    6.37,
                    6.29,
                    6.40
                ],
                [
                    6.36,
                    6.50,
                    6.32,
                    6.51
                ],
                [
                    6.50,
                    6.55,
                    6.44,
                    6.56
                ],
                [
                    6.55,
                    6.58,
                    6.53,
                    6.62
                ],
                [
                    6.56,
                    6.52,
                    6.39,
                    6.62
                ],
                [
                    6.52,
                    6.51,
                    6.49,
                    6.58
                ],
                [
                    6.49,
                    6.46,
                    6.43,
                    6.57
                ],
                [
                    6.43,
                    6.37,
                    6.31,
                    6.52
                ],
                [
                    6.37,
                    6.43,
                    6.35,
                    6.52
                ],
                [
                    6.46,
                    6.43,
                    6.34,
                    6.48
                ],
                [
                    6.41,
                    6.44,
                    6.37,
                    6.47
                ],
                [
                    6.40,
                    6.26,
                    6.24,
                    6.47
                ],
                [
                    6.26,
                    6.13,
                    6.12,
                    6.32
                ],
                [
                    6.11,
                    6.37,
                    6.08,
                    6.40
                ],
                [
                    6.37,
                    6.56,
                    6.37,
                    6.59
                ],
                [
                    6.56,
                    6.57,
                    6.47,
                    6.59
                ],
                [
                    6.58,
                    6.60,
                    6.53,
                    6.64
                ],
                [
                    6.60,
                    6.63,
                    6.59,
                    6.65
                ],
                [
                    6.63,
                    6.73,
                    6.62,
                    6.74
                ],
                [
                    6.75,
                    6.71,
                    6.64,
                    6.76
                ],
                [
                    6.73,
                    6.65,
                    6.59,
                    6.78
                ],
                [
                    6.65,
                    6.66,
                    6.61,
                    6.69
                ],
                [
                    6.66,
                    6.81,
                    6.61,
                    7.27
                ],
                [
                    6.77,
                    6.92,
                    6.68,
                    7.20
                ],
                [
                    6.97,
                    6.93,
                    6.82,
                    7.02
                ],
                [
                    6.91,
                    7.01,
                    6.86,
                    7.14
                ],
                [
                    6.99,
                    7.13,
                    6.91,
                    7.27
                ],
                [
                    7.10,
                    7.16,
                    7.00,
                    7.27
                ],
                [
                    6.81,
                    7.00,
                    6.72,
                    7.01
                ],
                [
                    6.96,
                    6.93,
                    6.82,
                    7.07
                ],
                [
                    6.94,
                    6.84,
                    6.80,
                    6.96
                ],
                [
                    6.84,
                    6.86,
                    6.80,
                    6.88
                ],
                [
                    6.87,
                    6.81,
                    6.77,
                    6.92
                ],
                [
                    6.78,
                    6.87,
                    6.77,
                    6.88
                ],
                [
                    6.90,
                    6.90,
                    6.85,
                    6.98
                ],
                [
                    6.90,
                    6.76,
                    6.74,
                    6.90
                ],
                [
                    6.76,
                    6.80,
                    6.75,
                    6.81
                ],
                [
                    6.79,
                    6.81,
                    6.72,
                    6.81
                ],
                [
                    6.81,
                    6.95,
                    6.78,
                    7.01
                ],
                [
                    6.99,
                    6.96,
                    6.83,
                    6.99
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603421 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_ffdd5ac046b24182967b149716274b9b.setOption(option_ffdd5ac046b24182967b149716274b9b);
            window.addEventListener('resize', function(){
                chart_ffdd5ac046b24182967b149716274b9b.resize();
            })
    </script>
</body>
</html>
