<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="f4dbb76daa3e42b8bd415f35c3d1d97c" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_f4dbb76daa3e42b8bd415f35c3d1d97c = echarts.init(
            document.getElementById('f4dbb76daa3e42b8bd415f35c3d1d97c'), 'white', {renderer: 'canvas'});
        var option_f4dbb76daa3e42b8bd415f35c3d1d97c = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    7.41,
                    7.42,
                    7.37,
                    7.52
                ],
                [
                    7.42,
                    7.26,
                    7.24,
                    7.46
                ],
                [
                    7.24,
                    7.29,
                    7.18,
                    7.31
                ],
                [
                    7.29,
                    7.33,
                    7.22,
                    7.38
                ],
                [
                    7.33,
                    7.32,
                    7.32,
                    7.43
                ],
                [
                    7.33,
                    7.24,
                    7.20,
                    7.37
                ],
                [
                    7.24,
                    7.28,
                    7.18,
                    7.29
                ],
                [
                    7.28,
                    7.29,
                    7.25,
                    7.35
                ],
                [
                    7.29,
                    7.24,
                    7.18,
                    7.32
                ],
                [
                    7.24,
                    7.22,
                    7.20,
                    7.30
                ],
                [
                    7.22,
                    7.13,
                    7.09,
                    7.23
                ],
                [
                    7.12,
                    7.12,
                    7.09,
                    7.16
                ],
                [
                    7.11,
                    7.09,
                    7.06,
                    7.12
                ],
                [
                    7.09,
                    7.06,
                    7.02,
                    7.11
                ],
                [
                    7.06,
                    7.11,
                    7.04,
                    7.16
                ],
                [
                    7.06,
                    6.99,
                    6.98,
                    7.08
                ],
                [
                    6.98,
                    7.12,
                    6.98,
                    7.13
                ],
                [
                    7.12,
                    7.14,
                    7.11,
                    7.19
                ],
                [
                    7.13,
                    7.04,
                    7.03,
                    7.16
                ],
                [
                    7.04,
                    7.06,
                    7.03,
                    7.11
                ],
                [
                    7.05,
                    7.09,
                    7.02,
                    7.11
                ],
                [
                    7.11,
                    7.09,
                    7.01,
                    7.11
                ],
                [
                    7.10,
                    7.34,
                    7.07,
                    7.46
                ],
                [
                    7.34,
                    7.39,
                    7.25,
                    7.49
                ],
                [
                    7.39,
                    7.23,
                    7.18,
                    7.40
                ],
                [
                    7.19,
                    7.27,
                    7.14,
                    7.27
                ],
                [
                    7.27,
                    7.30,
                    7.21,
                    7.33
                ],
                [
                    7.31,
                    7.26,
                    7.19,
                    7.31
                ],
                [
                    7.28,
                    7.41,
                    7.27,
                    7.41
                ],
                [
                    7.47,
                    7.46,
                    7.41,
                    7.51
                ],
                [
                    7.48,
                    7.43,
                    7.42,
                    7.50
                ],
                [
                    7.40,
                    7.40,
                    7.36,
                    7.44
                ],
                [
                    7.40,
                    7.35,
                    7.34,
                    7.43
                ],
                [
                    7.35,
                    7.35,
                    7.29,
                    7.42
                ],
                [
                    7.35,
                    7.35,
                    7.27,
                    7.37
                ],
                [
                    7.36,
                    7.39,
                    7.32,
                    7.39
                ],
                [
                    7.40,
                    7.39,
                    7.37,
                    7.43
                ],
                [
                    7.37,
                    7.34,
                    7.31,
                    7.39
                ],
                [
                    7.35,
                    7.36,
                    7.30,
                    7.38
                ],
                [
                    7.37,
                    7.30,
                    7.28,
                    7.46
                ],
                [
                    7.29,
                    7.32,
                    7.26,
                    7.38
                ],
                [
                    7.32,
                    7.33,
                    7.30,
                    7.40
                ],
                [
                    7.30,
                    7.36,
                    7.29,
                    7.41
                ],
                [
                    7.17,
                    6.65,
                    6.62,
                    7.17
                ],
                [
                    6.63,
                    6.76,
                    6.63,
                    6.86
                ],
                [
                    6.71,
                    6.78,
                    6.55,
                    6.85
                ],
                [
                    6.84,
                    6.86,
                    6.81,
                    6.93
                ],
                [
                    6.83,
                    6.85,
                    6.80,
                    6.90
                ],
                [
                    6.87,
                    6.89,
                    6.87,
                    6.92
                ],
                [
                    6.88,
                    6.86,
                    6.83,
                    6.90
                ],
                [
                    6.87,
                    6.87,
                    6.81,
                    6.92
                ],
                [
                    6.87,
                    6.97,
                    6.81,
                    7.02
                ],
                [
                    6.95,
                    7.05,
                    6.94,
                    7.08
                ],
                [
                    7.09,
                    7.16,
                    7.03,
                    7.18
                ],
                [
                    7.15,
                    7.35,
                    7.13,
                    7.36
                ],
                [
                    7.38,
                    7.30,
                    7.28,
                    7.42
                ],
                [
                    7.33,
                    7.48,
                    7.31,
                    7.50
                ],
                [
                    7.52,
                    7.47,
                    7.45,
                    7.60
                ],
                [
                    7.48,
                    7.32,
                    7.32,
                    7.50
                ],
                [
                    7.36,
                    7.33,
                    7.28,
                    7.38
                ],
                [
                    7.34,
                    7.22,
                    7.21,
                    7.38
                ],
                [
                    7.27,
                    7.26,
                    7.24,
                    7.30
                ],
                [
                    7.32,
                    7.33,
                    7.32,
                    7.43
                ],
                [
                    7.35,
                    7.37,
                    7.26,
                    7.38
                ],
                [
                    7.38,
                    7.51,
                    7.34,
                    7.51
                ],
                [
                    7.53,
                    7.59,
                    7.50,
                    7.68
                ],
                [
                    7.59,
                    7.52,
                    7.50,
                    7.60
                ],
                [
                    7.51,
                    7.55,
                    7.45,
                    7.58
                ],
                [
                    7.56,
                    7.46,
                    7.46,
                    7.58
                ],
                [
                    7.48,
                    7.45,
                    7.43,
                    7.52
                ],
                [
                    7.45,
                    7.52,
                    7.45,
                    7.57
                ],
                [
                    7.55,
                    7.54,
                    7.49,
                    7.58
                ],
                [
                    7.55,
                    7.55,
                    7.53,
                    7.58
                ],
                [
                    7.54,
                    7.50,
                    7.50,
                    7.57
                ],
                [
                    7.49,
                    7.50,
                    7.49,
                    7.61
                ],
                [
                    7.52,
                    7.50,
                    7.49,
                    7.58
                ],
                [
                    7.52,
                    7.49,
                    7.45,
                    7.54
                ],
                [
                    7.49,
                    7.48,
                    7.41,
                    7.50
                ],
                [
                    7.49,
                    7.51,
                    7.47,
                    7.53
                ],
                [
                    7.51,
                    7.60,
                    7.50,
                    7.62
                ],
                [
                    7.58,
                    7.60,
                    7.54,
                    7.63
                ],
                [
                    7.62,
                    7.61,
                    7.57,
                    7.64
                ],
                [
                    7.61,
                    7.57,
                    7.54,
                    7.63
                ],
                [
                    7.58,
                    7.59,
                    7.57,
                    7.62
                ],
                [
                    7.59,
                    7.59,
                    7.57,
                    7.61
                ],
                [
                    7.60,
                    7.73,
                    7.59,
                    7.79
                ],
                [
                    7.75,
                    7.80,
                    7.72,
                    7.87
                ],
                [
                    7.79,
                    7.78,
                    7.72,
                    7.82
                ],
                [
                    7.78,
                    7.73,
                    7.71,
                    7.81
                ],
                [
                    7.72,
                    7.70,
                    7.67,
                    7.75
                ],
                [
                    7.73,
                    7.71,
                    7.69,
                    7.74
                ],
                [
                    7.69,
                    7.66,
                    7.63,
                    7.72
                ],
                [
                    7.64,
                    7.54,
                    7.50,
                    7.67
                ],
                [
                    7.53,
                    7.59,
                    7.53,
                    7.62
                ],
                [
                    7.60,
                    7.59,
                    7.54,
                    7.62
                ],
                [
                    7.62,
                    7.67,
                    7.60,
                    7.69
                ],
                [
                    7.67,
                    7.77,
                    7.66,
                    7.77
                ],
                [
                    7.77,
                    7.77,
                    7.74,
                    7.78
                ],
                [
                    7.77,
                    7.79,
                    7.77,
                    7.91
                ],
                [
                    7.83,
                    7.71,
                    7.70,
                    7.84
                ],
                [
                    7.71,
                    7.71,
                    7.69,
                    7.73
                ],
                [
                    7.72,
                    7.76,
                    7.71,
                    7.78
                ],
                [
                    7.78,
                    7.77,
                    7.73,
                    7.80
                ],
                [
                    7.79,
                    7.80,
                    7.75,
                    7.83
                ],
                [
                    7.81,
                    7.89,
                    7.79,
                    7.90
                ],
                [
                    7.90,
                    7.88,
                    7.85,
                    7.93
                ],
                [
                    7.86,
                    7.91,
                    7.86,
                    7.95
                ],
                [
                    7.90,
                    8.10,
                    7.90,
                    8.13
                ],
                [
                    8.12,
                    8.04,
                    8.04,
                    8.15
                ],
                [
                    8.05,
                    8.05,
                    8.03,
                    8.10
                ],
                [
                    8.04,
                    8.04,
                    7.95,
                    8.07
                ],
                [
                    8.06,
                    8.04,
                    8.00,
                    8.08
                ],
                [
                    8.06,
                    8.01,
                    7.99,
                    8.10
                ],
                [
                    8.04,
                    8.05,
                    8.00,
                    8.06
                ],
                [
                    8.08,
                    8.09,
                    8.07,
                    8.13
                ],
                [
                    8.10,
                    8.12,
                    8.02,
                    8.14
                ],
                [
                    7.86,
                    7.81,
                    7.79,
                    7.89
                ],
                [
                    7.82,
                    7.87,
                    7.80,
                    7.89
                ],
                [
                    7.88,
                    7.83,
                    7.81,
                    7.89
                ],
                [
                    7.83,
                    7.81,
                    7.78,
                    7.87
                ],
                [
                    7.81,
                    7.81,
                    7.72,
                    7.84
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600064 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_f4dbb76daa3e42b8bd415f35c3d1d97c.setOption(option_f4dbb76daa3e42b8bd415f35c3d1d97c);
            window.addEventListener('resize', function(){
                chart_f4dbb76daa3e42b8bd415f35c3d1d97c.resize();
            })
    </script>
</body>
</html>
