<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="0d481d515c1e4158b8f1b76c0a0ebace" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_0d481d515c1e4158b8f1b76c0a0ebace = echarts.init(
            document.getElementById('0d481d515c1e4158b8f1b76c0a0ebace'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_0d481d515c1e4158b8f1b76c0a0ebace = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    11.2,
                    10.77,
                    10.7,
                    11.25
                ],
                [
                    11.15,
                    11.25,
                    10.98,
                    11.4
                ],
                [
                    11.25,
                    11.86,
                    11.13,
                    11.92
                ],
                [
                    11.87,
                    12.16,
                    11.83,
                    12.5
                ],
                [
                    12.36,
                    13.38,
                    12.28,
                    13.38
                ],
                [
                    13.6,
                    13.35,
                    13.21,
                    13.95
                ],
                [
                    13.22,
                    13.41,
                    13.11,
                    13.63
                ],
                [
                    13.52,
                    13.2,
                    13.01,
                    13.59
                ],
                [
                    13.12,
                    13.25,
                    12.83,
                    13.45
                ],
                [
                    13.72,
                    13.8,
                    13.55,
                    14.1
                ],
                [
                    13.6,
                    14.02,
                    13.44,
                    14.49
                ],
                [
                    14.0,
                    14.3,
                    13.47,
                    14.4
                ],
                [
                    14.16,
                    13.88,
                    13.8,
                    14.2
                ],
                [
                    14.13,
                    14.43,
                    13.8,
                    14.8
                ],
                [
                    14.37,
                    14.08,
                    13.83,
                    14.37
                ],
                [
                    13.62,
                    13.52,
                    13.41,
                    13.8
                ],
                [
                    13.52,
                    13.61,
                    13.38,
                    13.67
                ],
                [
                    13.72,
                    13.62,
                    13.38,
                    14.05
                ],
                [
                    13.44,
                    12.61,
                    12.56,
                    13.5
                ],
                [
                    12.6,
                    12.73,
                    12.5,
                    13.09
                ],
                [
                    12.6,
                    12.71,
                    12.48,
                    12.77
                ],
                [
                    12.79,
                    12.9,
                    12.55,
                    12.9
                ],
                [
                    13.0,
                    13.48,
                    12.99,
                    13.65
                ],
                [
                    13.26,
                    13.11,
                    13.01,
                    13.62
                ],
                [
                    13.09,
                    13.45,
                    12.9,
                    13.67
                ],
                [
                    13.26,
                    13.21,
                    13.03,
                    13.35
                ],
                [
                    13.22,
                    13.92,
                    13.22,
                    14.39
                ],
                [
                    13.22,
                    13.25,
                    13.2,
                    13.57
                ],
                [
                    13.26,
                    13.27,
                    12.81,
                    13.3
                ],
                [
                    13.29,
                    13.39,
                    13.19,
                    13.48
                ],
                [
                    13.41,
                    13.88,
                    13.25,
                    14.06
                ],
                [
                    13.75,
                    13.5,
                    13.45,
                    13.75
                ],
                [
                    13.66,
                    13.19,
                    13.18,
                    13.67
                ],
                [
                    13.01,
                    13.05,
                    12.84,
                    13.19
                ],
                [
                    13.1,
                    13.33,
                    13.05,
                    13.87
                ],
                [
                    13.15,
                    12.88,
                    12.88,
                    13.35
                ],
                [
                    12.88,
                    12.75,
                    12.72,
                    13.05
                ],
                [
                    12.8,
                    12.28,
                    12.16,
                    12.81
                ],
                [
                    12.29,
                    12.21,
                    12.2,
                    12.53
                ],
                [
                    12.16,
                    11.87,
                    11.68,
                    12.18
                ],
                [
                    12.02,
                    12.1,
                    12.02,
                    12.32
                ],
                [
                    12.05,
                    12.04,
                    12.01,
                    12.21
                ],
                [
                    11.88,
                    11.85,
                    11.78,
                    12.07
                ],
                [
                    10.67,
                    10.67,
                    10.67,
                    10.93
                ],
                [
                    9.62,
                    9.92,
                    9.6,
                    10.4
                ],
                [
                    9.7,
                    10.11,
                    8.94,
                    10.26
                ],
                [
                    10.41,
                    11.12,
                    10.3,
                    11.12
                ],
                [
                    10.92,
                    10.95,
                    10.78,
                    11.26
                ],
                [
                    11.28,
                    11.16,
                    11.1,
                    11.34
                ],
                [
                    11.02,
                    10.95,
                    10.88,
                    11.3
                ],
                [
                    10.8,
                    10.57,
                    10.45,
                    10.9
                ],
                [
                    10.47,
                    10.61,
                    10.4,
                    10.86
                ],
                [
                    10.95,
                    10.66,
                    10.64,
                    11.28
                ],
                [
                    10.72,
                    10.84,
                    10.47,
                    10.85
                ],
                [
                    10.79,
                    10.65,
                    10.58,
                    10.89
                ],
                [
                    10.7,
                    10.72,
                    10.7,
                    10.85
                ],
                [
                    10.7,
                    10.85,
                    10.44,
                    11.31
                ],
                [
                    11.66,
                    11.94,
                    11.66,
                    11.94
                ],
                [
                    13.13,
                    13.13,
                    13.13,
                    13.13
                ],
                [
                    14.21,
                    14.44,
                    13.7,
                    14.44
                ],
                [
                    15.71,
                    15.88,
                    14.9,
                    15.88
                ],
                [
                    16.87,
                    17.2,
                    16.2,
                    17.47
                ],
                [
                    17.22,
                    18.21,
                    16.69,
                    18.36
                ],
                [
                    17.05,
                    16.39,
                    16.39,
                    17.25
                ],
                [
                    15.98,
                    16.15,
                    15.68,
                    16.84
                ],
                [
                    15.8,
                    16.06,
                    15.51,
                    16.38
                ],
                [
                    16.21,
                    15.25,
                    15.2,
                    16.26
                ],
                [
                    15.5,
                    15.56,
                    15.3,
                    15.98
                ],
                [
                    15.51,
                    15.57,
                    15.38,
                    16.19
                ],
                [
                    15.25,
                    16.23,
                    14.66,
                    16.88
                ],
                [
                    15.83,
                    15.95,
                    15.32,
                    16.16
                ],
                [
                    16.14,
                    15.8,
                    15.8,
                    16.75
                ],
                [
                    15.53,
                    15.16,
                    15.11,
                    16.1
                ],
                [
                    15.01,
                    15.98,
                    14.95,
                    16.68
                ],
                [
                    15.8,
                    15.56,
                    15.43,
                    16.61
                ],
                [
                    16.0,
                    17.12,
                    15.52,
                    17.12
                ],
                [
                    17.3,
                    16.64,
                    16.58,
                    17.4
                ],
                [
                    16.88,
                    17.43,
                    16.66,
                    17.68
                ],
                [
                    17.11,
                    19.17,
                    17.11,
                    19.17
                ],
                [
                    18.9,
                    17.61,
                    17.54,
                    18.99
                ],
                [
                    16.87,
                    16.54,
                    16.45,
                    17.49
                ],
                [
                    16.69,
                    16.65,
                    16.31,
                    17.21
                ],
                [
                    16.6,
                    17.19,
                    16.06,
                    17.89
                ],
                [
                    16.78,
                    16.53,
                    16.51,
                    17.1
                ],
                [
                    16.55,
                    16.78,
                    16.45,
                    16.95
                ],
                [
                    16.89,
                    16.06,
                    15.98,
                    17.03
                ],
                [
                    15.98,
                    15.87,
                    15.71,
                    16.2
                ],
                [
                    15.74,
                    15.71,
                    15.6,
                    15.99
                ],
                [
                    15.52,
                    15.2,
                    15.15,
                    15.6
                ],
                [
                    15.02,
                    15.52,
                    14.98,
                    15.76
                ],
                [
                    15.56,
                    15.41,
                    15.26,
                    15.63
                ],
                [
                    15.3,
                    15.71,
                    15.13,
                    15.85
                ],
                [
                    15.62,
                    16.2,
                    15.55,
                    17.0
                ],
                [
                    15.96,
                    16.27,
                    15.8,
                    16.75
                ],
                [
                    15.78,
                    16.47,
                    15.36,
                    16.59
                ],
                [
                    16.57,
                    16.66,
                    16.55,
                    17.14
                ],
                [
                    16.59,
                    17.1,
                    16.09,
                    17.18
                ],
                [
                    17.4,
                    17.12,
                    16.88,
                    18.08
                ],
                [
                    16.85,
                    16.94,
                    16.62,
                    17.44
                ],
                [
                    17.1,
                    17.02,
                    16.81,
                    17.68
                ],
                [
                    16.88,
                    16.76,
                    16.66,
                    17.24
                ],
                [
                    16.5,
                    16.28,
                    16.14,
                    16.72
                ],
                [
                    16.28,
                    16.92,
                    16.26,
                    17.55
                ],
                [
                    17.12,
                    17.28,
                    16.92,
                    17.95
                ],
                [
                    17.06,
                    17.39,
                    17.06,
                    17.66
                ],
                [
                    17.65,
                    18.18,
                    17.2,
                    18.55
                ],
                [
                    18.15,
                    17.87,
                    17.8,
                    18.38
                ],
                [
                    18.0,
                    17.55,
                    17.43,
                    18.18
                ],
                [
                    17.46,
                    17.92,
                    17.43,
                    18.2
                ],
                [
                    17.93,
                    17.69,
                    17.61,
                    18.46
                ],
                [
                    16.51,
                    16.99,
                    16.51,
                    17.4
                ],
                [
                    17.1,
                    18.69,
                    17.1,
                    18.69
                ],
                [
                    19.4,
                    20.56,
                    19.2,
                    20.56
                ],
                [
                    21.2,
                    19.55,
                    19.5,
                    21.31
                ],
                [
                    19.58,
                    19.41,
                    19.32,
                    19.98
                ],
                [
                    19.24,
                    18.82,
                    18.72,
                    19.37
                ],
                [
                    18.65,
                    18.61,
                    18.42,
                    18.99
                ],
                [
                    18.68,
                    18.94,
                    18.54,
                    18.98
                ],
                [
                    18.95,
                    19.79,
                    18.95,
                    19.86
                ],
                [
                    19.99,
                    19.56,
                    19.15,
                    19.99
                ],
                [
                    19.36,
                    19.57,
                    19.15,
                    19.92
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -13.18
                ],
                [
                    "2025-02-05",
                    2.38
                ],
                [
                    "2025-02-06",
                    3.85
                ],
                [
                    "2025-02-07",
                    2.20
                ],
                [
                    "2025-02-10",
                    27.86
                ],
                [
                    "2025-02-11",
                    -4.34
                ],
                [
                    "2025-02-12",
                    -5.31
                ],
                [
                    "2025-02-13",
                    -6.51
                ],
                [
                    "2025-02-14",
                    -0.97
                ],
                [
                    "2025-02-17",
                    -2.87
                ],
                [
                    "2025-02-18",
                    2.57
                ],
                [
                    "2025-02-19",
                    1.27
                ],
                [
                    "2025-02-20",
                    -9.47
                ],
                [
                    "2025-02-21",
                    7.38
                ],
                [
                    "2025-02-24",
                    -13.45
                ],
                [
                    "2025-02-25",
                    -9.63
                ],
                [
                    "2025-02-26",
                    -5.22
                ],
                [
                    "2025-02-27",
                    -4.30
                ],
                [
                    "2025-02-28",
                    -17.03
                ],
                [
                    "2025-03-03",
                    1.22
                ],
                [
                    "2025-03-04",
                    -11.84
                ],
                [
                    "2025-03-05",
                    -1.32
                ],
                [
                    "2025-03-06",
                    6.10
                ],
                [
                    "2025-03-07",
                    -6.92
                ],
                [
                    "2025-03-10",
                    8.41
                ],
                [
                    "2025-03-11",
                    -15.31
                ],
                [
                    "2025-03-12",
                    14.03
                ],
                [
                    "2025-03-13",
                    -14.70
                ],
                [
                    "2025-03-14",
                    -7.24
                ],
                [
                    "2025-03-17",
                    3.79
                ],
                [
                    "2025-03-18",
                    6.82
                ],
                [
                    "2025-03-19",
                    -14.99
                ],
                [
                    "2025-03-20",
                    -15.21
                ],
                [
                    "2025-03-21",
                    -7.57
                ],
                [
                    "2025-03-24",
                    2.76
                ],
                [
                    "2025-03-25",
                    -16.57
                ],
                [
                    "2025-03-26",
                    -16.67
                ],
                [
                    "2025-03-27",
                    -14.76
                ],
                [
                    "2025-03-28",
                    -8.54
                ],
                [
                    "2025-03-31",
                    -10.51
                ],
                [
                    "2025-04-01",
                    2.46
                ],
                [
                    "2025-04-02",
                    -4.16
                ],
                [
                    "2025-04-03",
                    -12.12
                ],
                [
                    "2025-04-07",
                    -17.06
                ],
                [
                    "2025-04-08",
                    -6.23
                ],
                [
                    "2025-04-09",
                    -1.56
                ],
                [
                    "2025-04-10",
                    24.21
                ],
                [
                    "2025-04-11",
                    0.98
                ],
                [
                    "2025-04-14",
                    0.81
                ],
                [
                    "2025-04-15",
                    -17.15
                ],
                [
                    "2025-04-16",
                    -9.83
                ],
                [
                    "2025-04-17",
                    -3.40
                ],
                [
                    "2025-04-18",
                    4.32
                ],
                [
                    "2025-04-21",
                    4.70
                ],
                [
                    "2025-04-22",
                    -17.15
                ],
                [
                    "2025-04-23",
                    3.21
                ],
                [
                    "2025-04-24",
                    9.44
                ],
                [
                    "2025-04-25",
                    55.74
                ],
                [
                    "2025-04-28",
                    20.97
                ],
                [
                    "2025-04-29",
                    24.73
                ],
                [
                    "2025-04-30",
                    10.65
                ],
                [
                    "2025-05-06",
                    -13.42
                ],
                [
                    "2025-05-07",
                    -3.34
                ],
                [
                    "2025-05-08",
                    -14.11
                ],
                [
                    "2025-05-09",
                    0.70
                ],
                [
                    "2025-05-12",
                    -0.44
                ],
                [
                    "2025-05-13",
                    -11.38
                ],
                [
                    "2025-05-14",
                    -3.47
                ],
                [
                    "2025-05-15",
                    -6.76
                ],
                [
                    "2025-05-16",
                    4.62
                ],
                [
                    "2025-05-19",
                    -5.00
                ],
                [
                    "2025-05-20",
                    -8.52
                ],
                [
                    "2025-05-21",
                    -7.36
                ],
                [
                    "2025-05-22",
                    13.70
                ],
                [
                    "2025-05-23",
                    -5.96
                ],
                [
                    "2025-05-26",
                    16.83
                ],
                [
                    "2025-05-27",
                    -10.10
                ],
                [
                    "2025-05-28",
                    1.32
                ],
                [
                    "2025-05-29",
                    14.58
                ],
                [
                    "2025-05-30",
                    -15.85
                ],
                [
                    "2025-06-03",
                    -8.98
                ],
                [
                    "2025-06-04",
                    -3.99
                ],
                [
                    "2025-06-05",
                    -4.02
                ],
                [
                    "2025-06-06",
                    -7.92
                ],
                [
                    "2025-06-09",
                    -0.53
                ],
                [
                    "2025-06-10",
                    -17.07
                ],
                [
                    "2025-06-11",
                    -3.65
                ],
                [
                    "2025-06-12",
                    -7.40
                ],
                [
                    "2025-06-13",
                    -13.95
                ],
                [
                    "2025-06-16",
                    3.52
                ],
                [
                    "2025-06-17",
                    -10.52
                ],
                [
                    "2025-06-18",
                    3.17
                ],
                [
                    "2025-06-19",
                    3.87
                ],
                [
                    "2025-06-20",
                    -4.97
                ],
                [
                    "2025-06-23",
                    3.74
                ],
                [
                    "2025-06-24",
                    -1.63
                ],
                [
                    "2025-06-25",
                    1.66
                ],
                [
                    "2025-06-26",
                    -0.90
                ],
                [
                    "2025-06-27",
                    -5.28
                ],
                [
                    "2025-06-30",
                    -12.32
                ],
                [
                    "2025-07-01",
                    -4.31
                ],
                [
                    "2025-07-02",
                    -10.81
                ],
                [
                    "2025-07-03",
                    8.16
                ],
                [
                    "2025-07-04",
                    3.43
                ],
                [
                    "2025-07-07",
                    -5.46
                ],
                [
                    "2025-07-08",
                    9.40
                ],
                [
                    "2025-07-09",
                    -4.52
                ],
                [
                    "2025-07-10",
                    -11.79
                ],
                [
                    "2025-07-11",
                    8.14
                ],
                [
                    "2025-07-14",
                    -5.27
                ],
                [
                    "2025-07-15",
                    -11.10
                ],
                [
                    "2025-07-16",
                    46.91
                ],
                [
                    "2025-07-17",
                    -1.66
                ],
                [
                    "2025-07-18",
                    -11.40
                ],
                [
                    "2025-07-21",
                    -6.09
                ],
                [
                    "2025-07-22",
                    -7.82
                ],
                [
                    "2025-07-23",
                    -6.72
                ],
                [
                    "2025-07-24",
                    -2.48
                ],
                [
                    "2025-07-25",
                    1.25
                ],
                [
                    "2025-07-28",
                    -7.03
                ],
                [
                    "2025-07-29",
                    -5.14
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    0.14
                ],
                [
                    "2025-02-05",
                    -1.43
                ],
                [
                    "2025-02-06",
                    -0.30
                ],
                [
                    "2025-02-07",
                    -1.65
                ],
                [
                    "2025-02-10",
                    -12.99
                ],
                [
                    "2025-02-11",
                    3.10
                ],
                [
                    "2025-02-12",
                    0.03
                ],
                [
                    "2025-02-13",
                    -2.16
                ],
                [
                    "2025-02-14",
                    -0.33
                ],
                [
                    "2025-02-17",
                    1.75
                ],
                [
                    "2025-02-18",
                    -1.21
                ],
                [
                    "2025-02-19",
                    0.72
                ],
                [
                    "2025-02-20",
                    -0.65
                ],
                [
                    "2025-02-21",
                    -2.14
                ],
                [
                    "2025-02-24",
                    2.46
                ],
                [
                    "2025-02-25",
                    -1.70
                ],
                [
                    "2025-02-26",
                    -3.38
                ],
                [
                    "2025-02-27",
                    2.59
                ],
                [
                    "2025-02-28",
                    -2.90
                ],
                [
                    "2025-03-03",
                    -6.86
                ],
                [
                    "2025-03-04",
                    -5.04
                ],
                [
                    "2025-03-05",
                    2.40
                ],
                [
                    "2025-03-06",
                    -0.39
                ],
                [
                    "2025-03-07",
                    -1.56
                ],
                [
                    "2025-03-10",
                    2.79
                ],
                [
                    "2025-03-11",
                    -1.07
                ],
                [
                    "2025-03-12",
                    -5.33
                ],
                [
                    "2025-03-13",
                    2.30
                ],
                [
                    "2025-03-14",
                    -2.25
                ],
                [
                    "2025-03-17",
                    -2.65
                ],
                [
                    "2025-03-18",
                    -1.94
                ],
                [
                    "2025-03-19",
                    1.60
                ],
                [
                    "2025-03-20",
                    -0.76
                ],
                [
                    "2025-03-21",
                    -3.97
                ],
                [
                    "2025-03-24",
                    1.83
                ],
                [
                    "2025-03-25",
                    2.99
                ],
                [
                    "2025-03-26",
                    -1.34
                ],
                [
                    "2025-03-27",
                    -2.92
                ],
                [
                    "2025-03-28",
                    -2.88
                ],
                [
                    "2025-03-31",
                    0.22
                ],
                [
                    "2025-04-01",
                    -2.47
                ],
                [
                    "2025-04-02",
                    -0.80
                ],
                [
                    "2025-04-03",
                    -3.89
                ],
                [
                    "2025-04-07",
                    -6.39
                ],
                [
                    "2025-04-08",
                    -2.61
                ],
                [
                    "2025-04-09",
                    0.48
                ],
                [
                    "2025-04-10",
                    -12.39
                ],
                [
                    "2025-04-11",
                    -0.80
                ],
                [
                    "2025-04-14",
                    -4.73
                ],
                [
                    "2025-04-15",
                    -2.86
                ],
                [
                    "2025-04-16",
                    -10.61
                ],
                [
                    "2025-04-17",
                    -2.01
                ],
                [
                    "2025-04-18",
                    2.57
                ],
                [
                    "2025-04-21",
                    -3.51
                ],
                [
                    "2025-04-22",
                    0.24
                ],
                [
                    "2025-04-23",
                    -2.34
                ],
                [
                    "2025-04-24",
                    -3.52
                ],
                [
                    "2025-04-25",
                    -23.59
                ],
                [
                    "2025-04-28",
                    -4.58
                ],
                [
                    "2025-04-29",
                    -13.19
                ],
                [
                    "2025-04-30",
                    -8.40
                ],
                [
                    "2025-05-06",
                    2.95
                ],
                [
                    "2025-05-07",
                    -0.83
                ],
                [
                    "2025-05-08",
                    0.31
                ],
                [
                    "2025-05-09",
                    -1.12
                ],
                [
                    "2025-05-12",
                    -2.45
                ],
                [
                    "2025-05-13",
                    0.30
                ],
                [
                    "2025-05-14",
                    1.40
                ],
                [
                    "2025-05-15",
                    2.31
                ],
                [
                    "2025-05-16",
                    -1.86
                ],
                [
                    "2025-05-19",
                    -0.84
                ],
                [
                    "2025-05-20",
                    1.99
                ],
                [
                    "2025-05-21",
                    -2.66
                ],
                [
                    "2025-05-22",
                    -9.11
                ],
                [
                    "2025-05-23",
                    -0.03
                ],
                [
                    "2025-05-26",
                    -9.62
                ],
                [
                    "2025-05-27",
                    -0.86
                ],
                [
                    "2025-05-28",
                    -1.35
                ],
                [
                    "2025-05-29",
                    -7.49
                ],
                [
                    "2025-05-30",
                    3.23
                ],
                [
                    "2025-06-03",
                    -0.14
                ],
                [
                    "2025-06-04",
                    -1.33
                ],
                [
                    "2025-06-05",
                    -0.64
                ],
                [
                    "2025-06-06",
                    0.68
                ],
                [
                    "2025-06-09",
                    0.60
                ],
                [
                    "2025-06-10",
                    -1.01
                ],
                [
                    "2025-06-11",
                    -3.33
                ],
                [
                    "2025-06-12",
                    -1.62
                ],
                [
                    "2025-06-13",
                    -4.43
                ],
                [
                    "2025-06-16",
                    1.74
                ],
                [
                    "2025-06-17",
                    -2.15
                ],
                [
                    "2025-06-18",
                    0.42
                ],
                [
                    "2025-06-19",
                    -1.79
                ],
                [
                    "2025-06-20",
                    2.98
                ],
                [
                    "2025-06-23",
                    -0.54
                ],
                [
                    "2025-06-24",
                    1.19
                ],
                [
                    "2025-06-25",
                    -4.19
                ],
                [
                    "2025-06-26",
                    -2.05
                ],
                [
                    "2025-06-27",
                    0.70
                ],
                [
                    "2025-06-30",
                    -0.98
                ],
                [
                    "2025-07-01",
                    0.51
                ],
                [
                    "2025-07-02",
                    -1.68
                ],
                [
                    "2025-07-03",
                    -3.69
                ],
                [
                    "2025-07-04",
                    -1.08
                ],
                [
                    "2025-07-07",
                    0.57
                ],
                [
                    "2025-07-08",
                    -3.10
                ],
                [
                    "2025-07-09",
                    -0.91
                ],
                [
                    "2025-07-10",
                    2.47
                ],
                [
                    "2025-07-11",
                    -2.29
                ],
                [
                    "2025-07-14",
                    2.92
                ],
                [
                    "2025-07-15",
                    1.74
                ],
                [
                    "2025-07-16",
                    -22.61
                ],
                [
                    "2025-07-17",
                    -2.06
                ],
                [
                    "2025-07-18",
                    -0.59
                ],
                [
                    "2025-07-21",
                    0.43
                ],
                [
                    "2025-07-22",
                    -4.40
                ],
                [
                    "2025-07-23",
                    2.64
                ],
                [
                    "2025-07-24",
                    1.63
                ],
                [
                    "2025-07-25",
                    0.43
                ],
                [
                    "2025-07-28",
                    -0.78
                ],
                [
                    "2025-07-29",
                    -0.05
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    13.03
                ],
                [
                    "2025-02-05",
                    -0.94
                ],
                [
                    "2025-02-06",
                    -3.55
                ],
                [
                    "2025-02-07",
                    -0.55
                ],
                [
                    "2025-02-10",
                    -14.87
                ],
                [
                    "2025-02-11",
                    1.25
                ],
                [
                    "2025-02-12",
                    5.28
                ],
                [
                    "2025-02-13",
                    8.67
                ],
                [
                    "2025-02-14",
                    1.30
                ],
                [
                    "2025-02-17",
                    1.12
                ],
                [
                    "2025-02-18",
                    -1.36
                ],
                [
                    "2025-02-19",
                    -2.00
                ],
                [
                    "2025-02-20",
                    10.12
                ],
                [
                    "2025-02-21",
                    -5.24
                ],
                [
                    "2025-02-24",
                    10.99
                ],
                [
                    "2025-02-25",
                    11.33
                ],
                [
                    "2025-02-26",
                    8.60
                ],
                [
                    "2025-02-27",
                    1.70
                ],
                [
                    "2025-02-28",
                    19.92
                ],
                [
                    "2025-03-03",
                    5.65
                ],
                [
                    "2025-03-04",
                    16.87
                ],
                [
                    "2025-03-05",
                    -1.08
                ],
                [
                    "2025-03-06",
                    -5.71
                ],
                [
                    "2025-03-07",
                    8.48
                ],
                [
                    "2025-03-10",
                    -11.20
                ],
                [
                    "2025-03-11",
                    16.38
                ],
                [
                    "2025-03-12",
                    -8.70
                ],
                [
                    "2025-03-13",
                    12.40
                ],
                [
                    "2025-03-14",
                    9.49
                ],
                [
                    "2025-03-17",
                    -1.13
                ],
                [
                    "2025-03-18",
                    -4.88
                ],
                [
                    "2025-03-19",
                    13.38
                ],
                [
                    "2025-03-20",
                    15.97
                ],
                [
                    "2025-03-21",
                    11.54
                ],
                [
                    "2025-03-24",
                    -4.60
                ],
                [
                    "2025-03-25",
                    13.57
                ],
                [
                    "2025-03-26",
                    18.01
                ],
                [
                    "2025-03-27",
                    17.68
                ],
                [
                    "2025-03-28",
                    11.41
                ],
                [
                    "2025-03-31",
                    10.28
                ],
                [
                    "2025-04-01",
                    0.01
                ],
                [
                    "2025-04-02",
                    4.96
                ],
                [
                    "2025-04-03",
                    16.01
                ],
                [
                    "2025-04-07",
                    23.45
                ],
                [
                    "2025-04-08",
                    8.84
                ],
                [
                    "2025-04-09",
                    1.08
                ],
                [
                    "2025-04-10",
                    -11.82
                ],
                [
                    "2025-04-11",
                    -0.18
                ],
                [
                    "2025-04-14",
                    3.92
                ],
                [
                    "2025-04-15",
                    20.00
                ],
                [
                    "2025-04-16",
                    20.44
                ],
                [
                    "2025-04-17",
                    5.41
                ],
                [
                    "2025-04-18",
                    -6.88
                ],
                [
                    "2025-04-21",
                    -1.19
                ],
                [
                    "2025-04-22",
                    16.91
                ],
                [
                    "2025-04-23",
                    -0.88
                ],
                [
                    "2025-04-24",
                    -5.92
                ],
                [
                    "2025-04-25",
                    -32.14
                ],
                [
                    "2025-04-28",
                    -16.40
                ],
                [
                    "2025-04-29",
                    -11.55
                ],
                [
                    "2025-04-30",
                    -2.25
                ],
                [
                    "2025-05-06",
                    10.46
                ],
                [
                    "2025-05-07",
                    4.16
                ],
                [
                    "2025-05-08",
                    13.79
                ],
                [
                    "2025-05-09",
                    0.42
                ],
                [
                    "2025-05-12",
                    2.89
                ],
                [
                    "2025-05-13",
                    11.09
                ],
                [
                    "2025-05-14",
                    2.08
                ],
                [
                    "2025-05-15",
                    4.46
                ],
                [
                    "2025-05-16",
                    -2.75
                ],
                [
                    "2025-05-19",
                    5.84
                ],
                [
                    "2025-05-20",
                    6.52
                ],
                [
                    "2025-05-21",
                    10.02
                ],
                [
                    "2025-05-22",
                    -4.58
                ],
                [
                    "2025-05-23",
                    5.99
                ],
                [
                    "2025-05-26",
                    -7.21
                ],
                [
                    "2025-05-27",
                    10.95
                ],
                [
                    "2025-05-28",
                    0.04
                ],
                [
                    "2025-05-29",
                    -7.09
                ],
                [
                    "2025-05-30",
                    12.62
                ],
                [
                    "2025-06-03",
                    9.13
                ],
                [
                    "2025-06-04",
                    5.32
                ],
                [
                    "2025-06-05",
                    4.66
                ],
                [
                    "2025-06-06",
                    7.24
                ],
                [
                    "2025-06-09",
                    -0.07
                ],
                [
                    "2025-06-10",
                    18.08
                ],
                [
                    "2025-06-11",
                    6.98
                ],
                [
                    "2025-06-12",
                    9.02
                ],
                [
                    "2025-06-13",
                    18.38
                ],
                [
                    "2025-06-16",
                    -5.27
                ],
                [
                    "2025-06-17",
                    12.68
                ],
                [
                    "2025-06-18",
                    -3.59
                ],
                [
                    "2025-06-19",
                    -2.09
                ],
                [
                    "2025-06-20",
                    1.98
                ],
                [
                    "2025-06-23",
                    -3.19
                ],
                [
                    "2025-06-24",
                    0.44
                ],
                [
                    "2025-06-25",
                    2.54
                ],
                [
                    "2025-06-26",
                    2.95
                ],
                [
                    "2025-06-27",
                    4.58
                ],
                [
                    "2025-06-30",
                    13.29
                ],
                [
                    "2025-07-01",
                    3.80
                ],
                [
                    "2025-07-02",
                    12.48
                ],
                [
                    "2025-07-03",
                    -4.47
                ],
                [
                    "2025-07-04",
                    -2.35
                ],
                [
                    "2025-07-07",
                    4.89
                ],
                [
                    "2025-07-08",
                    -6.30
                ],
                [
                    "2025-07-09",
                    5.42
                ],
                [
                    "2025-07-10",
                    9.32
                ],
                [
                    "2025-07-11",
                    -5.86
                ],
                [
                    "2025-07-14",
                    2.36
                ],
                [
                    "2025-07-15",
                    9.37
                ],
                [
                    "2025-07-16",
                    -24.29
                ],
                [
                    "2025-07-17",
                    3.72
                ],
                [
                    "2025-07-18",
                    11.99
                ],
                [
                    "2025-07-21",
                    5.67
                ],
                [
                    "2025-07-22",
                    12.23
                ],
                [
                    "2025-07-23",
                    4.08
                ],
                [
                    "2025-07-24",
                    0.85
                ],
                [
                    "2025-07-25",
                    -1.68
                ],
                [
                    "2025-07-28",
                    7.81
                ],
                [
                    "2025-07-29",
                    5.19
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    27.86
                ],
                [
                    "2025-02-11",
                    -4.34
                ],
                [
                    "2025-02-12",
                    -5.31
                ],
                [
                    "2025-02-13",
                    -6.51
                ],
                [
                    "2025-03-12",
                    14.03
                ],
                [
                    "2025-04-25",
                    55.74
                ],
                [
                    "2025-04-28",
                    20.97
                ],
                [
                    "2025-04-29",
                    24.73
                ],
                [
                    "2025-04-30",
                    10.65
                ],
                [
                    "2025-05-06",
                    -13.42
                ],
                [
                    "2025-05-07",
                    -3.34
                ],
                [
                    "2025-06-24",
                    -1.63
                ],
                [
                    "2025-06-25",
                    1.66
                ],
                [
                    "2025-07-09",
                    -4.52
                ],
                [
                    "2025-07-16",
                    46.91
                ],
                [
                    "2025-07-17",
                    -1.66
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-06-24",
                    1.19
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-17",
                    1.12
                ],
                [
                    "2025-02-18",
                    -1.36
                ],
                [
                    "2025-02-19",
                    -2.00
                ],
                [
                    "2025-02-20",
                    10.12
                ],
                [
                    "2025-02-21",
                    -5.24
                ],
                [
                    "2025-02-24",
                    10.99
                ],
                [
                    "2025-02-25",
                    11.33
                ],
                [
                    "2025-02-26",
                    8.60
                ],
                [
                    "2025-02-27",
                    1.70
                ],
                [
                    "2025-02-28",
                    19.92
                ],
                [
                    "2025-03-03",
                    5.65
                ],
                [
                    "2025-03-04",
                    16.87
                ],
                [
                    "2025-03-05",
                    -1.08
                ],
                [
                    "2025-03-06",
                    -5.71
                ],
                [
                    "2025-03-07",
                    8.48
                ],
                [
                    "2025-03-10",
                    -11.20
                ],
                [
                    "2025-03-11",
                    16.38
                ],
                [
                    "2025-03-13",
                    12.40
                ],
                [
                    "2025-03-14",
                    9.49
                ],
                [
                    "2025-03-17",
                    -1.13
                ],
                [
                    "2025-03-19",
                    13.38
                ],
                [
                    "2025-03-20",
                    15.97
                ],
                [
                    "2025-03-21",
                    11.54
                ],
                [
                    "2025-03-24",
                    -4.60
                ],
                [
                    "2025-03-25",
                    13.57
                ],
                [
                    "2025-03-26",
                    18.01
                ],
                [
                    "2025-03-27",
                    17.68
                ],
                [
                    "2025-03-28",
                    11.41
                ],
                [
                    "2025-03-31",
                    10.28
                ],
                [
                    "2025-04-01",
                    0.01
                ],
                [
                    "2025-04-02",
                    4.96
                ],
                [
                    "2025-04-03",
                    16.01
                ],
                [
                    "2025-04-07",
                    23.45
                ],
                [
                    "2025-04-08",
                    8.84
                ],
                [
                    "2025-04-09",
                    1.08
                ],
                [
                    "2025-04-10",
                    -11.82
                ],
                [
                    "2025-04-16",
                    20.44
                ],
                [
                    "2025-04-17",
                    5.41
                ],
                [
                    "2025-04-18",
                    -6.88
                ],
                [
                    "2025-04-21",
                    -1.19
                ],
                [
                    "2025-04-22",
                    16.91
                ],
                [
                    "2025-04-23",
                    -0.88
                ],
                [
                    "2025-05-09",
                    0.42
                ],
                [
                    "2025-05-12",
                    2.89
                ],
                [
                    "2025-05-13",
                    11.09
                ],
                [
                    "2025-05-14",
                    2.08
                ],
                [
                    "2025-05-15",
                    4.46
                ],
                [
                    "2025-05-16",
                    -2.75
                ],
                [
                    "2025-05-19",
                    5.84
                ],
                [
                    "2025-05-20",
                    6.52
                ],
                [
                    "2025-05-21",
                    10.02
                ],
                [
                    "2025-05-22",
                    -4.58
                ],
                [
                    "2025-05-23",
                    5.99
                ],
                [
                    "2025-06-03",
                    9.13
                ],
                [
                    "2025-06-04",
                    5.32
                ],
                [
                    "2025-06-05",
                    4.66
                ],
                [
                    "2025-06-06",
                    7.24
                ],
                [
                    "2025-06-09",
                    -0.07
                ],
                [
                    "2025-06-10",
                    18.08
                ],
                [
                    "2025-06-11",
                    6.98
                ],
                [
                    "2025-06-12",
                    9.02
                ],
                [
                    "2025-06-13",
                    18.38
                ],
                [
                    "2025-06-16",
                    -5.27
                ],
                [
                    "2025-06-17",
                    12.68
                ],
                [
                    "2025-06-18",
                    -3.59
                ],
                [
                    "2025-06-19",
                    -2.09
                ],
                [
                    "2025-06-20",
                    1.98
                ],
                [
                    "2025-06-23",
                    -3.19
                ],
                [
                    "2025-06-26",
                    2.95
                ],
                [
                    "2025-06-27",
                    4.58
                ],
                [
                    "2025-06-30",
                    13.29
                ],
                [
                    "2025-07-01",
                    3.80
                ],
                [
                    "2025-07-02",
                    12.48
                ],
                [
                    "2025-07-03",
                    -4.47
                ],
                [
                    "2025-07-04",
                    -2.35
                ],
                [
                    "2025-07-07",
                    4.89
                ],
                [
                    "2025-07-10",
                    9.32
                ],
                [
                    "2025-07-11",
                    -5.86
                ],
                [
                    "2025-07-14",
                    2.36
                ],
                [
                    "2025-07-15",
                    9.37
                ],
                [
                    "2025-07-23",
                    4.08
                ],
                [
                    "2025-07-24",
                    0.85
                ],
                [
                    "2025-07-25",
                    -1.68
                ],
                [
                    "2025-07-28",
                    7.81
                ],
                [
                    "2025-07-29",
                    5.19
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002229 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_0d481d515c1e4158b8f1b76c0a0ebace.setOption(option_0d481d515c1e4158b8f1b76c0a0ebace);
            window.addEventListener('resize', function(){
                chart_0d481d515c1e4158b8f1b76c0a0ebace.resize();
            })
    </script>
</body>
</html>
