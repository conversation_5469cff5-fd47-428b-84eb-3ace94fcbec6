<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="050145c3191849f6b29b3000551598d1" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_050145c3191849f6b29b3000551598d1 = echarts.init(
            document.getElementById('050145c3191849f6b29b3000551598d1'), 'white', {renderer: 'canvas'});
        var option_050145c3191849f6b29b3000551598d1 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    11.2,
                    10.77,
                    10.7,
                    11.25
                ],
                [
                    11.15,
                    11.25,
                    10.98,
                    11.4
                ],
                [
                    11.25,
                    11.86,
                    11.13,
                    11.92
                ],
                [
                    11.87,
                    12.16,
                    11.83,
                    12.5
                ],
                [
                    12.36,
                    13.38,
                    12.28,
                    13.38
                ],
                [
                    13.6,
                    13.35,
                    13.21,
                    13.95
                ],
                [
                    13.22,
                    13.41,
                    13.11,
                    13.63
                ],
                [
                    13.52,
                    13.2,
                    13.01,
                    13.59
                ],
                [
                    13.12,
                    13.25,
                    12.83,
                    13.45
                ],
                [
                    13.72,
                    13.8,
                    13.55,
                    14.1
                ],
                [
                    13.6,
                    14.02,
                    13.44,
                    14.49
                ],
                [
                    14.0,
                    14.3,
                    13.47,
                    14.4
                ],
                [
                    14.16,
                    13.88,
                    13.8,
                    14.2
                ],
                [
                    14.13,
                    14.43,
                    13.8,
                    14.8
                ],
                [
                    14.37,
                    14.08,
                    13.83,
                    14.37
                ],
                [
                    13.62,
                    13.52,
                    13.41,
                    13.8
                ],
                [
                    13.52,
                    13.61,
                    13.38,
                    13.67
                ],
                [
                    13.72,
                    13.62,
                    13.38,
                    14.05
                ],
                [
                    13.44,
                    12.61,
                    12.56,
                    13.5
                ],
                [
                    12.6,
                    12.73,
                    12.5,
                    13.09
                ],
                [
                    12.6,
                    12.71,
                    12.48,
                    12.77
                ],
                [
                    12.79,
                    12.9,
                    12.55,
                    12.9
                ],
                [
                    13.0,
                    13.48,
                    12.99,
                    13.65
                ],
                [
                    13.26,
                    13.11,
                    13.01,
                    13.62
                ],
                [
                    13.09,
                    13.45,
                    12.9,
                    13.67
                ],
                [
                    13.26,
                    13.21,
                    13.03,
                    13.35
                ],
                [
                    13.22,
                    13.92,
                    13.22,
                    14.39
                ],
                [
                    13.22,
                    13.25,
                    13.2,
                    13.57
                ],
                [
                    13.26,
                    13.27,
                    12.81,
                    13.3
                ],
                [
                    13.29,
                    13.39,
                    13.19,
                    13.48
                ],
                [
                    13.41,
                    13.88,
                    13.25,
                    14.06
                ],
                [
                    13.75,
                    13.5,
                    13.45,
                    13.75
                ],
                [
                    13.66,
                    13.19,
                    13.18,
                    13.67
                ],
                [
                    13.01,
                    13.05,
                    12.84,
                    13.19
                ],
                [
                    13.1,
                    13.33,
                    13.05,
                    13.87
                ],
                [
                    13.15,
                    12.88,
                    12.88,
                    13.35
                ],
                [
                    12.88,
                    12.75,
                    12.72,
                    13.05
                ],
                [
                    12.8,
                    12.28,
                    12.16,
                    12.81
                ],
                [
                    12.29,
                    12.21,
                    12.2,
                    12.53
                ],
                [
                    12.16,
                    11.87,
                    11.68,
                    12.18
                ],
                [
                    12.02,
                    12.1,
                    12.02,
                    12.32
                ],
                [
                    12.05,
                    12.04,
                    12.01,
                    12.21
                ],
                [
                    11.88,
                    11.85,
                    11.78,
                    12.07
                ],
                [
                    10.67,
                    10.67,
                    10.67,
                    10.93
                ],
                [
                    9.62,
                    9.92,
                    9.6,
                    10.4
                ],
                [
                    9.7,
                    10.11,
                    8.94,
                    10.26
                ],
                [
                    10.41,
                    11.12,
                    10.3,
                    11.12
                ],
                [
                    10.92,
                    10.95,
                    10.78,
                    11.26
                ],
                [
                    11.28,
                    11.16,
                    11.1,
                    11.34
                ],
                [
                    11.02,
                    10.95,
                    10.88,
                    11.3
                ],
                [
                    10.8,
                    10.57,
                    10.45,
                    10.9
                ],
                [
                    10.47,
                    10.61,
                    10.4,
                    10.86
                ],
                [
                    10.95,
                    10.66,
                    10.64,
                    11.28
                ],
                [
                    10.72,
                    10.84,
                    10.47,
                    10.85
                ],
                [
                    10.79,
                    10.65,
                    10.58,
                    10.89
                ],
                [
                    10.7,
                    10.72,
                    10.7,
                    10.85
                ],
                [
                    10.7,
                    10.85,
                    10.44,
                    11.31
                ],
                [
                    11.66,
                    11.94,
                    11.66,
                    11.94
                ],
                [
                    13.13,
                    13.13,
                    13.13,
                    13.13
                ],
                [
                    14.21,
                    14.44,
                    13.7,
                    14.44
                ],
                [
                    15.71,
                    15.88,
                    14.9,
                    15.88
                ],
                [
                    16.87,
                    17.2,
                    16.2,
                    17.47
                ],
                [
                    17.22,
                    18.21,
                    16.69,
                    18.36
                ],
                [
                    17.05,
                    16.39,
                    16.39,
                    17.25
                ],
                [
                    15.98,
                    16.15,
                    15.68,
                    16.84
                ],
                [
                    15.8,
                    16.06,
                    15.51,
                    16.38
                ],
                [
                    16.21,
                    15.25,
                    15.2,
                    16.26
                ],
                [
                    15.5,
                    15.56,
                    15.3,
                    15.98
                ],
                [
                    15.51,
                    15.57,
                    15.38,
                    16.19
                ],
                [
                    15.25,
                    16.23,
                    14.66,
                    16.88
                ],
                [
                    15.83,
                    15.95,
                    15.32,
                    16.16
                ],
                [
                    16.14,
                    15.8,
                    15.8,
                    16.75
                ],
                [
                    15.53,
                    15.16,
                    15.11,
                    16.1
                ],
                [
                    15.01,
                    15.98,
                    14.95,
                    16.68
                ],
                [
                    15.8,
                    15.56,
                    15.43,
                    16.61
                ],
                [
                    16.0,
                    17.12,
                    15.52,
                    17.12
                ],
                [
                    17.3,
                    16.64,
                    16.58,
                    17.4
                ],
                [
                    16.88,
                    17.43,
                    16.66,
                    17.68
                ],
                [
                    17.11,
                    19.17,
                    17.11,
                    19.17
                ],
                [
                    18.9,
                    17.61,
                    17.54,
                    18.99
                ],
                [
                    16.87,
                    16.54,
                    16.45,
                    17.49
                ],
                [
                    16.69,
                    16.65,
                    16.31,
                    17.21
                ],
                [
                    16.6,
                    17.19,
                    16.06,
                    17.89
                ],
                [
                    16.78,
                    16.53,
                    16.51,
                    17.1
                ],
                [
                    16.55,
                    16.78,
                    16.45,
                    16.95
                ],
                [
                    16.89,
                    16.06,
                    15.98,
                    17.03
                ],
                [
                    15.98,
                    15.87,
                    15.71,
                    16.2
                ],
                [
                    15.74,
                    15.71,
                    15.6,
                    15.99
                ],
                [
                    15.52,
                    15.2,
                    15.15,
                    15.6
                ],
                [
                    15.02,
                    15.52,
                    14.98,
                    15.76
                ],
                [
                    15.56,
                    15.41,
                    15.26,
                    15.63
                ],
                [
                    15.3,
                    15.71,
                    15.13,
                    15.85
                ],
                [
                    15.62,
                    16.2,
                    15.55,
                    17.0
                ],
                [
                    15.96,
                    16.27,
                    15.8,
                    16.75
                ],
                [
                    15.78,
                    16.47,
                    15.36,
                    16.59
                ],
                [
                    16.57,
                    16.66,
                    16.55,
                    17.14
                ],
                [
                    16.59,
                    17.1,
                    16.09,
                    17.18
                ],
                [
                    17.4,
                    17.12,
                    16.88,
                    18.08
                ],
                [
                    16.85,
                    16.94,
                    16.62,
                    17.44
                ],
                [
                    17.1,
                    17.02,
                    16.81,
                    17.68
                ],
                [
                    16.88,
                    16.76,
                    16.66,
                    17.24
                ],
                [
                    16.5,
                    16.28,
                    16.14,
                    16.72
                ],
                [
                    16.28,
                    16.92,
                    16.26,
                    17.55
                ],
                [
                    17.12,
                    17.28,
                    16.92,
                    17.95
                ],
                [
                    17.06,
                    17.39,
                    17.06,
                    17.66
                ],
                [
                    17.65,
                    18.18,
                    17.2,
                    18.55
                ],
                [
                    18.15,
                    17.87,
                    17.8,
                    18.38
                ],
                [
                    18.0,
                    17.55,
                    17.43,
                    18.18
                ],
                [
                    17.46,
                    17.92,
                    17.43,
                    18.2
                ],
                [
                    17.93,
                    17.69,
                    17.61,
                    18.46
                ],
                [
                    16.51,
                    16.99,
                    16.51,
                    17.4
                ],
                [
                    17.1,
                    18.69,
                    17.1,
                    18.69
                ],
                [
                    19.4,
                    20.56,
                    19.2,
                    20.56
                ],
                [
                    21.2,
                    19.55,
                    19.5,
                    21.31
                ],
                [
                    19.58,
                    19.41,
                    19.32,
                    19.98
                ],
                [
                    19.24,
                    18.82,
                    18.72,
                    19.37
                ],
                [
                    18.65,
                    18.61,
                    18.42,
                    18.99
                ],
                [
                    18.68,
                    18.94,
                    18.54,
                    18.98
                ],
                [
                    18.95,
                    19.79,
                    18.95,
                    19.86
                ],
                [
                    19.99,
                    19.56,
                    19.15,
                    19.99
                ],
                [
                    19.36,
                    19.57,
                    19.15,
                    19.92
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#880000",
                "borderColor0": "#008F28"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5438\u7b79",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 18,
            "data": [
                [
                    "2025-02-21",
                    13.8
                ],
                [
                    "2025-03-17",
                    13.19
                ],
                [
                    "2025-04-18",
                    10.64
                ],
                [
                    "2025-04-21",
                    10.47
                ],
                [
                    "2025-04-23",
                    10.7
                ],
                [
                    "2025-04-24",
                    10.44
                ],
                [
                    "2025-04-29",
                    13.7
                ],
                [
                    "2025-04-30",
                    14.9
                ],
                [
                    "2025-06-19",
                    15.55
                ],
                [
                    "2025-06-23",
                    15.36
                ],
                [
                    "2025-07-04",
                    16.92
                ],
                [
                    "2025-07-08",
                    17.2
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#F4B400"
            }
        },
        {
            "type": "scatter",
            "name": "\u52a0\u4ed3",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 18,
            "data": [
                [
                    "2025-03-06",
                    12.99
                ],
                [
                    "2025-03-12",
                    13.22
                ],
                [
                    "2025-04-29",
                    13.7
                ],
                [
                    "2025-04-30",
                    14.9
                ],
                [
                    "2025-06-19",
                    15.55
                ],
                [
                    "2025-07-03",
                    16.26
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4285F4"
            }
        },
        {
            "type": "scatter",
            "name": "\u6d17\u76d8",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "rect",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#16A085"
            }
        },
        {
            "type": "scatter",
            "name": "\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "circle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#DB4437"
            }
        },
        {
            "type": "scatter",
            "name": "\u9690\u5f62\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "pin",
            "symbolSize": 18,
            "data": [
                [
                    "2025-07-18",
                    19.5
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#8E44AD"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5438\u7b79",
                "\u52a0\u4ed3",
                "\u6d17\u76d8",
                "\u51fa\u8d27",
                "\u9690\u5f62\u51fa\u8d27"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10,
            "top": "2%"
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "interval": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 0
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002229 \u4e3b\u529b\u4fe1\u53f7 (\u542b\u6d17\u76d8)",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "90%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_050145c3191849f6b29b3000551598d1.setOption(option_050145c3191849f6b29b3000551598d1);
            window.addEventListener('resize', function(){
                chart_050145c3191849f6b29b3000551598d1.resize();
            })
    </script>
</body>
</html>
