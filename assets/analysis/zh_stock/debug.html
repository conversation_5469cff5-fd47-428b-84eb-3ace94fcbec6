<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        
        .stock-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .stock-item {
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            text-align: center;
        }
        
        .stock-code {
            font-weight: bold;
            color: #007bff;
        }
        
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 调试页面</h1>
        
        <div id="globalStatus" class="status info">正在初始化...</div>
        
        <div class="test-section">
            <h3>1. 基础功能测试</h3>
            <button onclick="testBasicFunctions()">测试基础函数</button>
            <button onclick="testJSONLoad()">测试JSON加载</button>
            <button onclick="testAPIConnection()">测试API连接</button>
            <div id="basicTestResult"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 数据显示</h3>
            <div>总股票数: <span id="stockCount">0</span></div>
            <div>图表类型数: <span id="chartCount">0</span></div>
            <div id="stockDisplay" class="stock-list"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 控制台日志</h3>
            <button onclick="clearLog()">清除日志</button>
            <div id="consoleLog" class="log"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let stockData = [];
        let chartTypes = [];
        let logMessages = [];

        // 重写console.log来捕获日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type}: ${args.join(' ')}`;
            logMessages.push(message);
            updateLogDisplay();
            
            // 调用原始函数
            if (type === 'ERROR') originalError(...args);
            else if (type === 'WARN') originalWarn(...args);
            else originalLog(...args);
        }

        console.log = (...args) => addToLog('LOG', ...args);
        console.error = (...args) => addToLog('ERROR', ...args);
        console.warn = (...args) => addToLog('WARN', ...args);

        function updateLogDisplay() {
            const logDiv = document.getElementById('consoleLog');
            logDiv.textContent = logMessages.slice(-50).join('\n'); // 只显示最近50条
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            logMessages = [];
            updateLogDisplay();
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('globalStatus');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        // 测试基础函数
        function testBasicFunctions() {
            const resultDiv = document.getElementById('basicTestResult');
            let results = [];
            
            try {
                // 测试基本JavaScript功能
                results.push('✅ JavaScript基础功能正常');
                
                // 测试DOM操作
                const testElement = document.createElement('div');
                testElement.textContent = 'test';
                results.push('✅ DOM操作正常');
                
                // 测试fetch API
                if (typeof fetch !== 'undefined') {
                    results.push('✅ Fetch API可用');
                } else {
                    results.push('❌ Fetch API不可用');
                }
                
                // 测试Promise
                Promise.resolve('test').then(() => {
                    results.push('✅ Promise支持正常');
                    resultDiv.innerHTML = results.map(r => `<div>${r}</div>`).join('');
                });
                
            } catch (error) {
                results.push(`❌ 基础功能测试失败: ${error.message}`);
                resultDiv.innerHTML = results.map(r => `<div>${r}</div>`).join('');
            }
        }

        // 测试JSON加载
        async function testJSONLoad() {
            console.log('🔄 开始测试JSON加载...');
            
            try {
                const timestamp = new Date().getTime();
                const url = `./stocks.json?t=${timestamp}`;
                console.log('📍 请求URL:', url);

                const response = await fetch(url, {
                    cache: 'no-cache',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });

                console.log('📡 响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('📄 JSON数据加载成功');
                
                stockData = data.stocks || [];
                chartTypes = data.chartTypes || [];
                
                // 更新显示
                document.getElementById('stockCount').textContent = stockData.length;
                document.getElementById('chartCount').textContent = chartTypes.length;
                
                // 显示股票列表
                const stockDisplay = document.getElementById('stockDisplay');
                stockDisplay.innerHTML = stockData.slice(0, 10).map(stock => `
                    <div class="stock-item">
                        <div class="stock-code">${stock.code}</div>
                        <div>${stock.name}</div>
                    </div>
                `).join('');
                
                updateStatus(`✅ 成功加载 ${stockData.length} 只股票`, 'success');
                console.log('✅ JSON加载测试完成');
                
            } catch (error) {
                console.error('❌ JSON加载失败:', error);
                updateStatus(`❌ JSON加载失败: ${error.message}`, 'error');
            }
        }

        // 测试API连接
        async function testAPIConnection() {
            console.log('🔄 开始测试API连接...');
            
            try {
                const response = await fetch('http://127.0.0.1:9999/api/v1/local/zh/stock/info?code=000863', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ API连接成功:', data);
                    updateStatus('✅ API连接正常', 'success');
                } else {
                    throw new Error(`API响应错误: ${response.status}`);
                }
                
            } catch (error) {
                console.error('❌ API连接失败:', error);
                updateStatus(`❌ API连接失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动执行
        window.addEventListener('load', function() {
            console.log('🚀 调试页面加载完成');
            updateStatus('页面加载完成，可以开始测试', 'info');
            
            // 自动执行基础测试
            setTimeout(() => {
                testBasicFunctions();
                testJSONLoad();
            }, 500);
        });

        // 错误处理
        window.addEventListener('error', function(event) {
            console.error('❌ 全局错误:', event.error);
            updateStatus(`❌ 页面错误: ${event.error.message}`, 'error');
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('❌ 未处理的Promise拒绝:', event.reason);
            updateStatus(`❌ Promise错误: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
