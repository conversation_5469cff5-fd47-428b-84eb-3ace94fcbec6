<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="efa031368bb64e00b8a4c3a986fc7f2b" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_efa031368bb64e00b8a4c3a986fc7f2b = echarts.init(
            document.getElementById('efa031368bb64e00b8a4c3a986fc7f2b'), 'white', {renderer: 'canvas'});
        var option_efa031368bb64e00b8a4c3a986fc7f2b = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    28.40,
                    28.40,
                    28.32,
                    28.70
                ],
                [
                    28.58,
                    27.81,
                    27.74,
                    28.62
                ],
                [
                    27.82,
                    28.18,
                    27.68,
                    28.25
                ],
                [
                    28.18,
                    28.49,
                    27.95,
                    28.60
                ],
                [
                    28.49,
                    28.72,
                    28.25,
                    28.78
                ],
                [
                    28.71,
                    28.34,
                    28.22,
                    28.73
                ],
                [
                    28.20,
                    28.46,
                    28.10,
                    28.46
                ],
                [
                    28.47,
                    28.77,
                    28.34,
                    29.05
                ],
                [
                    28.75,
                    29.08,
                    28.70,
                    29.08
                ],
                [
                    29.32,
                    28.76,
                    28.56,
                    29.44
                ],
                [
                    28.70,
                    27.99,
                    27.90,
                    28.72
                ],
                [
                    27.96,
                    27.88,
                    27.61,
                    28.09
                ],
                [
                    27.80,
                    28.09,
                    27.70,
                    28.28
                ],
                [
                    28.15,
                    27.80,
                    27.52,
                    28.27
                ],
                [
                    27.70,
                    27.70,
                    27.52,
                    27.83
                ],
                [
                    27.53,
                    27.08,
                    27.04,
                    27.56
                ],
                [
                    27.02,
                    27.34,
                    27.02,
                    27.39
                ],
                [
                    27.39,
                    27.97,
                    27.21,
                    28.05
                ],
                [
                    28.01,
                    27.61,
                    27.57,
                    28.63
                ],
                [
                    27.62,
                    27.53,
                    27.42,
                    28.05
                ],
                [
                    27.47,
                    27.40,
                    27.17,
                    27.52
                ],
                [
                    27.44,
                    27.29,
                    27.05,
                    27.49
                ],
                [
                    27.37,
                    27.78,
                    27.30,
                    28.01
                ],
                [
                    27.70,
                    27.85,
                    27.52,
                    27.94
                ],
                [
                    27.85,
                    27.81,
                    27.64,
                    27.94
                ],
                [
                    27.58,
                    28.60,
                    27.46,
                    28.60
                ],
                [
                    28.75,
                    29.77,
                    28.46,
                    30.10
                ],
                [
                    29.76,
                    29.71,
                    29.62,
                    30.70
                ],
                [
                    29.72,
                    30.63,
                    29.72,
                    30.76
                ],
                [
                    30.70,
                    30.15,
                    30.02,
                    30.88
                ],
                [
                    30.29,
                    30.12,
                    29.68,
                    30.35
                ],
                [
                    30.00,
                    29.63,
                    29.44,
                    30.29
                ],
                [
                    29.63,
                    29.21,
                    29.17,
                    29.74
                ],
                [
                    29.26,
                    29.33,
                    29.20,
                    30.26
                ],
                [
                    29.26,
                    29.04,
                    28.80,
                    29.59
                ],
                [
                    29.14,
                    29.21,
                    28.85,
                    29.25
                ],
                [
                    29.30,
                    30.13,
                    28.92,
                    30.30
                ],
                [
                    30.09,
                    31.04,
                    29.82,
                    31.20
                ],
                [
                    30.83,
                    30.68,
                    30.32,
                    31.08
                ],
                [
                    30.61,
                    30.59,
                    30.26,
                    31.34
                ],
                [
                    30.59,
                    31.81,
                    30.58,
                    32.41
                ],
                [
                    32.50,
                    31.64,
                    31.49,
                    33.08
                ],
                [
                    31.50,
                    31.60,
                    31.22,
                    32.20
                ],
                [
                    29.50,
                    28.44,
                    28.44,
                    30.45
                ],
                [
                    28.52,
                    29.56,
                    28.52,
                    29.80
                ],
                [
                    29.31,
                    29.93,
                    28.72,
                    30.37
                ],
                [
                    30.41,
                    30.85,
                    30.00,
                    31.24
                ],
                [
                    30.58,
                    30.35,
                    30.08,
                    31.03
                ],
                [
                    30.39,
                    30.55,
                    30.39,
                    31.15
                ],
                [
                    30.55,
                    30.59,
                    30.25,
                    31.18
                ],
                [
                    30.59,
                    29.87,
                    29.29,
                    30.70
                ],
                [
                    29.58,
                    29.65,
                    29.32,
                    29.98
                ],
                [
                    29.58,
                    30.01,
                    29.58,
                    30.16
                ],
                [
                    29.98,
                    31.60,
                    29.51,
                    31.84
                ],
                [
                    31.59,
                    31.41,
                    31.03,
                    31.84
                ],
                [
                    31.69,
                    31.17,
                    30.90,
                    31.69
                ],
                [
                    31.17,
                    31.55,
                    31.06,
                    32.05
                ],
                [
                    32.00,
                    31.22,
                    31.22,
                    32.18
                ],
                [
                    31.22,
                    31.32,
                    31.13,
                    31.91
                ],
                [
                    30.58,
                    29.47,
                    28.95,
                    30.58
                ],
                [
                    29.47,
                    29.42,
                    29.39,
                    30.05
                ],
                [
                    29.60,
                    30.82,
                    29.29,
                    30.96
                ],
                [
                    31.00,
                    30.25,
                    30.00,
                    31.14
                ],
                [
                    30.11,
                    30.12,
                    30.01,
                    30.33
                ],
                [
                    30.13,
                    30.05,
                    29.81,
                    30.28
                ],
                [
                    30.06,
                    29.71,
                    29.58,
                    30.21
                ],
                [
                    30.01,
                    29.83,
                    29.71,
                    30.15
                ],
                [
                    29.73,
                    29.91,
                    29.53,
                    30.05
                ],
                [
                    29.90,
                    29.39,
                    29.37,
                    30.25
                ],
                [
                    29.50,
                    29.16,
                    29.05,
                    29.50
                ],
                [
                    28.35,
                    27.83,
                    27.50,
                    28.38
                ],
                [
                    27.82,
                    28.74,
                    27.81,
                    29.06
                ],
                [
                    28.70,
                    28.63,
                    28.33,
                    28.94
                ],
                [
                    28.52,
                    28.23,
                    28.12,
                    28.58
                ],
                [
                    28.20,
                    27.70,
                    27.69,
                    28.33
                ],
                [
                    27.83,
                    28.21,
                    27.60,
                    28.37
                ],
                [
                    28.21,
                    28.70,
                    28.03,
                    28.96
                ],
                [
                    28.71,
                    29.57,
                    28.60,
                    29.66
                ],
                [
                    29.41,
                    29.48,
                    29.00,
                    29.78
                ],
                [
                    29.49,
                    29.51,
                    29.28,
                    29.88
                ],
                [
                    29.36,
                    30.71,
                    29.28,
                    31.09
                ],
                [
                    30.70,
                    30.64,
                    30.50,
                    30.95
                ],
                [
                    30.66,
                    31.76,
                    30.63,
                    32.40
                ],
                [
                    31.75,
                    31.53,
                    31.08,
                    31.85
                ],
                [
                    31.69,
                    34.35,
                    31.49,
                    34.68
                ],
                [
                    34.75,
                    34.90,
                    34.36,
                    35.99
                ],
                [
                    35.00,
                    35.07,
                    34.42,
                    35.30
                ],
                [
                    35.16,
                    34.58,
                    34.51,
                    36.00
                ],
                [
                    34.58,
                    33.16,
                    32.90,
                    34.75
                ],
                [
                    32.90,
                    33.87,
                    32.85,
                    34.04
                ],
                [
                    33.81,
                    32.68,
                    32.50,
                    34.69
                ],
                [
                    32.89,
                    31.65,
                    31.50,
                    32.91
                ],
                [
                    31.54,
                    30.83,
                    30.60,
                    31.71
                ],
                [
                    30.83,
                    29.93,
                    29.71,
                    30.92
                ],
                [
                    29.76,
                    29.82,
                    29.50,
                    30.12
                ],
                [
                    30.02,
                    29.86,
                    29.65,
                    30.37
                ],
                [
                    29.86,
                    29.84,
                    29.62,
                    30.04
                ],
                [
                    29.12,
                    28.63,
                    28.18,
                    29.13
                ],
                [
                    28.50,
                    28.63,
                    28.40,
                    28.79
                ],
                [
                    28.63,
                    28.99,
                    28.55,
                    29.02
                ],
                [
                    29.09,
                    29.35,
                    28.83,
                    29.42
                ],
                [
                    29.44,
                    29.04,
                    28.94,
                    29.45
                ],
                [
                    28.95,
                    29.13,
                    28.82,
                    29.30
                ],
                [
                    29.08,
                    29.07,
                    28.95,
                    29.33
                ],
                [
                    29.11,
                    29.62,
                    29.00,
                    29.75
                ],
                [
                    29.62,
                    29.53,
                    29.50,
                    29.88
                ],
                [
                    29.56,
                    29.84,
                    29.53,
                    30.24
                ],
                [
                    29.78,
                    29.73,
                    29.56,
                    29.89
                ],
                [
                    29.73,
                    29.66,
                    29.57,
                    29.80
                ],
                [
                    29.67,
                    29.31,
                    29.16,
                    29.70
                ],
                [
                    29.21,
                    29.23,
                    28.99,
                    29.41
                ],
                [
                    29.23,
                    29.32,
                    29.22,
                    29.62
                ],
                [
                    29.19,
                    29.35,
                    29.18,
                    29.59
                ],
                [
                    29.36,
                    29.45,
                    29.27,
                    29.58
                ],
                [
                    29.57,
                    29.91,
                    29.42,
                    29.94
                ],
                [
                    29.91,
                    30.90,
                    29.78,
                    31.11
                ],
                [
                    30.92,
                    30.61,
                    30.37,
                    31.00
                ],
                [
                    30.60,
                    31.00,
                    30.46,
                    31.23
                ],
                [
                    31.12,
                    31.41,
                    30.76,
                    31.55
                ],
                [
                    31.43,
                    31.07,
                    31.04,
                    31.50
                ],
                [
                    31.14,
                    31.27,
                    30.97,
                    31.97
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u7a81\u7834",
            "symbol": "pin",
            "symbolSize": 16,
            "data": [
                [
                    "2025-03-12",
                    30.10
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#1f77b4"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u7a81\u7834"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603899 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_efa031368bb64e00b8a4c3a986fc7f2b.setOption(option_efa031368bb64e00b8a4c3a986fc7f2b);
            window.addEventListener('resize', function(){
                chart_efa031368bb64e00b8a4c3a986fc7f2b.resize();
            })
    </script>
</body>
</html>
