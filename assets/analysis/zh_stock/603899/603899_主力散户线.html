<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="b59a9e430a1a49c7a51346f35fe13fdf" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_b59a9e430a1a49c7a51346f35fe13fdf = echarts.init(
            document.getElementById('b59a9e430a1a49c7a51346f35fe13fdf'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_b59a9e430a1a49c7a51346f35fe13fdf = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    28.4,
                    28.4,
                    28.32,
                    28.7
                ],
                [
                    28.58,
                    27.81,
                    27.74,
                    28.62
                ],
                [
                    27.82,
                    28.18,
                    27.68,
                    28.25
                ],
                [
                    28.18,
                    28.49,
                    27.95,
                    28.6
                ],
                [
                    28.49,
                    28.72,
                    28.25,
                    28.78
                ],
                [
                    28.71,
                    28.34,
                    28.22,
                    28.73
                ],
                [
                    28.2,
                    28.46,
                    28.1,
                    28.46
                ],
                [
                    28.47,
                    28.77,
                    28.34,
                    29.05
                ],
                [
                    28.75,
                    29.08,
                    28.7,
                    29.08
                ],
                [
                    29.32,
                    28.76,
                    28.56,
                    29.44
                ],
                [
                    28.7,
                    27.99,
                    27.9,
                    28.72
                ],
                [
                    27.96,
                    27.88,
                    27.61,
                    28.09
                ],
                [
                    27.8,
                    28.09,
                    27.7,
                    28.28
                ],
                [
                    28.15,
                    27.8,
                    27.52,
                    28.27
                ],
                [
                    27.7,
                    27.7,
                    27.52,
                    27.83
                ],
                [
                    27.53,
                    27.08,
                    27.04,
                    27.56
                ],
                [
                    27.02,
                    27.34,
                    27.02,
                    27.39
                ],
                [
                    27.39,
                    27.97,
                    27.21,
                    28.05
                ],
                [
                    28.01,
                    27.61,
                    27.57,
                    28.63
                ],
                [
                    27.62,
                    27.53,
                    27.42,
                    28.05
                ],
                [
                    27.47,
                    27.4,
                    27.17,
                    27.52
                ],
                [
                    27.44,
                    27.29,
                    27.05,
                    27.49
                ],
                [
                    27.37,
                    27.78,
                    27.3,
                    28.01
                ],
                [
                    27.7,
                    27.85,
                    27.52,
                    27.94
                ],
                [
                    27.85,
                    27.81,
                    27.64,
                    27.94
                ],
                [
                    27.58,
                    28.6,
                    27.46,
                    28.6
                ],
                [
                    28.75,
                    29.77,
                    28.46,
                    30.1
                ],
                [
                    29.76,
                    29.71,
                    29.62,
                    30.7
                ],
                [
                    29.72,
                    30.63,
                    29.72,
                    30.76
                ],
                [
                    30.7,
                    30.15,
                    30.02,
                    30.88
                ],
                [
                    30.29,
                    30.12,
                    29.68,
                    30.35
                ],
                [
                    30.0,
                    29.63,
                    29.44,
                    30.29
                ],
                [
                    29.63,
                    29.21,
                    29.17,
                    29.74
                ],
                [
                    29.26,
                    29.33,
                    29.2,
                    30.26
                ],
                [
                    29.26,
                    29.04,
                    28.8,
                    29.59
                ],
                [
                    29.14,
                    29.21,
                    28.85,
                    29.25
                ],
                [
                    29.3,
                    30.13,
                    28.92,
                    30.3
                ],
                [
                    30.09,
                    31.04,
                    29.82,
                    31.2
                ],
                [
                    30.83,
                    30.68,
                    30.32,
                    31.08
                ],
                [
                    30.61,
                    30.59,
                    30.26,
                    31.34
                ],
                [
                    30.59,
                    31.81,
                    30.58,
                    32.41
                ],
                [
                    32.5,
                    31.64,
                    31.49,
                    33.08
                ],
                [
                    31.5,
                    31.6,
                    31.22,
                    32.2
                ],
                [
                    29.5,
                    28.44,
                    28.44,
                    30.45
                ],
                [
                    28.52,
                    29.56,
                    28.52,
                    29.8
                ],
                [
                    29.31,
                    29.93,
                    28.72,
                    30.37
                ],
                [
                    30.41,
                    30.85,
                    30.0,
                    31.24
                ],
                [
                    30.58,
                    30.35,
                    30.08,
                    31.03
                ],
                [
                    30.39,
                    30.55,
                    30.39,
                    31.15
                ],
                [
                    30.55,
                    30.59,
                    30.25,
                    31.18
                ],
                [
                    30.59,
                    29.87,
                    29.29,
                    30.7
                ],
                [
                    29.58,
                    29.65,
                    29.32,
                    29.98
                ],
                [
                    29.58,
                    30.01,
                    29.58,
                    30.16
                ],
                [
                    29.98,
                    31.6,
                    29.51,
                    31.84
                ],
                [
                    31.59,
                    31.41,
                    31.03,
                    31.84
                ],
                [
                    31.69,
                    31.17,
                    30.9,
                    31.69
                ],
                [
                    31.17,
                    31.55,
                    31.06,
                    32.05
                ],
                [
                    32.0,
                    31.22,
                    31.22,
                    32.18
                ],
                [
                    31.22,
                    31.32,
                    31.13,
                    31.91
                ],
                [
                    30.58,
                    29.47,
                    28.95,
                    30.58
                ],
                [
                    29.47,
                    29.42,
                    29.39,
                    30.05
                ],
                [
                    29.6,
                    30.82,
                    29.29,
                    30.96
                ],
                [
                    31.0,
                    30.25,
                    30.0,
                    31.14
                ],
                [
                    30.11,
                    30.12,
                    30.01,
                    30.33
                ],
                [
                    30.13,
                    30.05,
                    29.81,
                    30.28
                ],
                [
                    30.06,
                    29.71,
                    29.58,
                    30.21
                ],
                [
                    30.01,
                    29.83,
                    29.71,
                    30.15
                ],
                [
                    29.73,
                    29.91,
                    29.53,
                    30.05
                ],
                [
                    29.9,
                    29.39,
                    29.37,
                    30.25
                ],
                [
                    29.5,
                    29.16,
                    29.05,
                    29.5
                ],
                [
                    28.35,
                    27.83,
                    27.5,
                    28.38
                ],
                [
                    27.82,
                    28.74,
                    27.81,
                    29.06
                ],
                [
                    28.7,
                    28.63,
                    28.33,
                    28.94
                ],
                [
                    28.52,
                    28.23,
                    28.12,
                    28.58
                ],
                [
                    28.2,
                    27.7,
                    27.69,
                    28.33
                ],
                [
                    27.83,
                    28.21,
                    27.6,
                    28.37
                ],
                [
                    28.21,
                    28.7,
                    28.03,
                    28.96
                ],
                [
                    28.71,
                    29.57,
                    28.6,
                    29.66
                ],
                [
                    29.41,
                    29.48,
                    29.0,
                    29.78
                ],
                [
                    29.49,
                    29.51,
                    29.28,
                    29.88
                ],
                [
                    29.36,
                    30.71,
                    29.28,
                    31.09
                ],
                [
                    30.7,
                    30.64,
                    30.5,
                    30.95
                ],
                [
                    30.66,
                    31.76,
                    30.63,
                    32.4
                ],
                [
                    31.75,
                    31.53,
                    31.08,
                    31.85
                ],
                [
                    31.69,
                    34.35,
                    31.49,
                    34.68
                ],
                [
                    34.75,
                    34.9,
                    34.36,
                    35.99
                ],
                [
                    35.0,
                    35.07,
                    34.42,
                    35.3
                ],
                [
                    35.16,
                    34.58,
                    34.51,
                    36.0
                ],
                [
                    34.58,
                    33.16,
                    32.9,
                    34.75
                ],
                [
                    32.9,
                    33.87,
                    32.85,
                    34.04
                ],
                [
                    33.81,
                    32.68,
                    32.5,
                    34.69
                ],
                [
                    32.89,
                    31.65,
                    31.5,
                    32.91
                ],
                [
                    31.54,
                    30.83,
                    30.6,
                    31.71
                ],
                [
                    30.83,
                    29.93,
                    29.71,
                    30.92
                ],
                [
                    29.76,
                    29.82,
                    29.5,
                    30.12
                ],
                [
                    30.02,
                    29.86,
                    29.65,
                    30.37
                ],
                [
                    29.86,
                    29.84,
                    29.62,
                    30.04
                ],
                [
                    29.12,
                    28.63,
                    28.18,
                    29.13
                ],
                [
                    28.5,
                    28.63,
                    28.4,
                    28.79
                ],
                [
                    28.63,
                    28.99,
                    28.55,
                    29.02
                ],
                [
                    29.09,
                    29.35,
                    28.83,
                    29.42
                ],
                [
                    29.44,
                    29.04,
                    28.94,
                    29.45
                ],
                [
                    28.95,
                    29.13,
                    28.82,
                    29.3
                ],
                [
                    29.08,
                    29.07,
                    28.95,
                    29.33
                ],
                [
                    29.11,
                    29.62,
                    29.0,
                    29.75
                ],
                [
                    29.62,
                    29.53,
                    29.5,
                    29.88
                ],
                [
                    29.56,
                    29.84,
                    29.53,
                    30.24
                ],
                [
                    29.78,
                    29.73,
                    29.56,
                    29.89
                ],
                [
                    29.73,
                    29.66,
                    29.57,
                    29.8
                ],
                [
                    29.67,
                    29.31,
                    29.16,
                    29.7
                ],
                [
                    29.21,
                    29.23,
                    28.99,
                    29.41
                ],
                [
                    29.23,
                    29.32,
                    29.22,
                    29.62
                ],
                [
                    29.19,
                    29.35,
                    29.18,
                    29.59
                ],
                [
                    29.36,
                    29.45,
                    29.27,
                    29.58
                ],
                [
                    29.57,
                    29.91,
                    29.42,
                    29.94
                ],
                [
                    29.91,
                    30.9,
                    29.78,
                    31.11
                ],
                [
                    30.92,
                    30.61,
                    30.37,
                    31.0
                ],
                [
                    30.6,
                    31.0,
                    30.46,
                    31.23
                ],
                [
                    31.12,
                    31.41,
                    30.76,
                    31.55
                ],
                [
                    31.43,
                    31.07,
                    31.04,
                    31.5
                ],
                [
                    31.14,
                    31.27,
                    30.97,
                    31.97
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -1.09
                ],
                [
                    "2025-02-05",
                    13.40
                ],
                [
                    "2025-02-06",
                    -2.91
                ],
                [
                    "2025-02-07",
                    -4.68
                ],
                [
                    "2025-02-10",
                    2.19
                ],
                [
                    "2025-02-11",
                    -7.64
                ],
                [
                    "2025-02-12",
                    2.68
                ],
                [
                    "2025-02-13",
                    12.70
                ],
                [
                    "2025-02-14",
                    1.47
                ],
                [
                    "2025-02-17",
                    5.12
                ],
                [
                    "2025-02-18",
                    1.30
                ],
                [
                    "2025-02-19",
                    3.14
                ],
                [
                    "2025-02-20",
                    -3.60
                ],
                [
                    "2025-02-21",
                    -1.74
                ],
                [
                    "2025-02-24",
                    3.36
                ],
                [
                    "2025-02-25",
                    -11.65
                ],
                [
                    "2025-02-26",
                    -8.91
                ],
                [
                    "2025-02-27",
                    3.88
                ],
                [
                    "2025-02-28",
                    3.50
                ],
                [
                    "2025-03-03",
                    -0.16
                ],
                [
                    "2025-03-04",
                    -12.51
                ],
                [
                    "2025-03-05",
                    -17.62
                ],
                [
                    "2025-03-06",
                    -3.58
                ],
                [
                    "2025-03-07",
                    -9.77
                ],
                [
                    "2025-03-10",
                    0.48
                ],
                [
                    "2025-03-11",
                    10.68
                ],
                [
                    "2025-03-12",
                    12.75
                ],
                [
                    "2025-03-13",
                    -0.62
                ],
                [
                    "2025-03-14",
                    -7.63
                ],
                [
                    "2025-03-17",
                    -3.07
                ],
                [
                    "2025-03-18",
                    -14.60
                ],
                [
                    "2025-03-19",
                    -10.91
                ],
                [
                    "2025-03-20",
                    -6.44
                ],
                [
                    "2025-03-21",
                    -5.74
                ],
                [
                    "2025-03-24",
                    -2.12
                ],
                [
                    "2025-03-25",
                    1.39
                ],
                [
                    "2025-03-26",
                    3.46
                ],
                [
                    "2025-03-27",
                    12.53
                ],
                [
                    "2025-03-28",
                    -4.42
                ],
                [
                    "2025-03-31",
                    0.13
                ],
                [
                    "2025-04-01",
                    8.02
                ],
                [
                    "2025-04-02",
                    -6.04
                ],
                [
                    "2025-04-03",
                    6.47
                ],
                [
                    "2025-04-07",
                    -13.85
                ],
                [
                    "2025-04-08",
                    -5.88
                ],
                [
                    "2025-04-09",
                    1.23
                ],
                [
                    "2025-04-10",
                    8.99
                ],
                [
                    "2025-04-11",
                    2.03
                ],
                [
                    "2025-04-14",
                    4.47
                ],
                [
                    "2025-04-15",
                    2.24
                ],
                [
                    "2025-04-16",
                    -17.26
                ],
                [
                    "2025-04-17",
                    -8.69
                ],
                [
                    "2025-04-18",
                    -0.08
                ],
                [
                    "2025-04-21",
                    -0.17
                ],
                [
                    "2025-04-22",
                    -3.48
                ],
                [
                    "2025-04-23",
                    3.32
                ],
                [
                    "2025-04-24",
                    4.71
                ],
                [
                    "2025-04-25",
                    0.19
                ],
                [
                    "2025-04-28",
                    6.73
                ],
                [
                    "2025-04-29",
                    -14.93
                ],
                [
                    "2025-04-30",
                    -1.83
                ],
                [
                    "2025-05-06",
                    17.00
                ],
                [
                    "2025-05-07",
                    3.39
                ],
                [
                    "2025-05-08",
                    -4.14
                ],
                [
                    "2025-05-09",
                    -9.68
                ],
                [
                    "2025-05-12",
                    -3.30
                ],
                [
                    "2025-05-13",
                    3.02
                ],
                [
                    "2025-05-14",
                    -2.55
                ],
                [
                    "2025-05-15",
                    -2.46
                ],
                [
                    "2025-05-16",
                    -12.62
                ],
                [
                    "2025-05-19",
                    -2.67
                ],
                [
                    "2025-05-20",
                    0.78
                ],
                [
                    "2025-05-21",
                    -5.46
                ],
                [
                    "2025-05-22",
                    -15.71
                ],
                [
                    "2025-05-23",
                    -24.44
                ],
                [
                    "2025-05-26",
                    8.90
                ],
                [
                    "2025-05-27",
                    12.41
                ],
                [
                    "2025-05-28",
                    -4.32
                ],
                [
                    "2025-05-29",
                    -3.52
                ],
                [
                    "2025-05-30",
                    3.03
                ],
                [
                    "2025-06-03",
                    10.28
                ],
                [
                    "2025-06-04",
                    -11.00
                ],
                [
                    "2025-06-05",
                    -7.77
                ],
                [
                    "2025-06-06",
                    -6.98
                ],
                [
                    "2025-06-09",
                    4.26
                ],
                [
                    "2025-06-10",
                    -4.44
                ],
                [
                    "2025-06-11",
                    6.98
                ],
                [
                    "2025-06-12",
                    -7.71
                ],
                [
                    "2025-06-13",
                    -11.39
                ],
                [
                    "2025-06-16",
                    14.89
                ],
                [
                    "2025-06-17",
                    -1.72
                ],
                [
                    "2025-06-18",
                    -4.23
                ],
                [
                    "2025-06-19",
                    -4.59
                ],
                [
                    "2025-06-20",
                    -4.67
                ],
                [
                    "2025-06-23",
                    4.92
                ],
                [
                    "2025-06-24",
                    1.71
                ],
                [
                    "2025-06-25",
                    5.23
                ],
                [
                    "2025-06-26",
                    -4.04
                ],
                [
                    "2025-06-27",
                    1.74
                ],
                [
                    "2025-06-30",
                    0.34
                ],
                [
                    "2025-07-01",
                    5.72
                ],
                [
                    "2025-07-02",
                    1.92
                ],
                [
                    "2025-07-03",
                    -7.29
                ],
                [
                    "2025-07-04",
                    -6.53
                ],
                [
                    "2025-07-07",
                    -6.21
                ],
                [
                    "2025-07-08",
                    -7.27
                ],
                [
                    "2025-07-09",
                    -4.60
                ],
                [
                    "2025-07-10",
                    -11.80
                ],
                [
                    "2025-07-11",
                    -22.85
                ],
                [
                    "2025-07-14",
                    -11.96
                ],
                [
                    "2025-07-15",
                    -4.85
                ],
                [
                    "2025-07-16",
                    0.32
                ],
                [
                    "2025-07-17",
                    -15.00
                ],
                [
                    "2025-07-18",
                    5.60
                ],
                [
                    "2025-07-21",
                    -2.22
                ],
                [
                    "2025-07-22",
                    0.89
                ],
                [
                    "2025-07-23",
                    -4.44
                ],
                [
                    "2025-07-24",
                    -8.66
                ],
                [
                    "2025-07-25",
                    -0.56
                ],
                [
                    "2025-07-28",
                    -15.74
                ],
                [
                    "2025-07-29",
                    -7.54
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -0.61
                ],
                [
                    "2025-02-05",
                    11.96
                ],
                [
                    "2025-02-06",
                    -6.57
                ],
                [
                    "2025-02-07",
                    7.62
                ],
                [
                    "2025-02-10",
                    2.24
                ],
                [
                    "2025-02-11",
                    3.34
                ],
                [
                    "2025-02-12",
                    -2.21
                ],
                [
                    "2025-02-13",
                    -4.28
                ],
                [
                    "2025-02-14",
                    -3.72
                ],
                [
                    "2025-02-17",
                    -0.64
                ],
                [
                    "2025-02-18",
                    -8.17
                ],
                [
                    "2025-02-19",
                    2.43
                ],
                [
                    "2025-02-20",
                    3.38
                ],
                [
                    "2025-02-21",
                    -3.65
                ],
                [
                    "2025-02-24",
                    -0.96
                ],
                [
                    "2025-02-25",
                    6.47
                ],
                [
                    "2025-02-26",
                    -1.45
                ],
                [
                    "2025-02-27",
                    -2.67
                ],
                [
                    "2025-02-28",
                    -3.44
                ],
                [
                    "2025-03-03",
                    0.34
                ],
                [
                    "2025-03-04",
                    4.84
                ],
                [
                    "2025-03-05",
                    12.48
                ],
                [
                    "2025-03-06",
                    -4.34
                ],
                [
                    "2025-03-07",
                    -0.49
                ],
                [
                    "2025-03-10",
                    -1.98
                ],
                [
                    "2025-03-11",
                    -8.83
                ],
                [
                    "2025-03-12",
                    -7.33
                ],
                [
                    "2025-03-13",
                    -2.75
                ],
                [
                    "2025-03-14",
                    -3.65
                ],
                [
                    "2025-03-17",
                    -0.74
                ],
                [
                    "2025-03-18",
                    -2.91
                ],
                [
                    "2025-03-19",
                    -3.06
                ],
                [
                    "2025-03-20",
                    -1.77
                ],
                [
                    "2025-03-21",
                    1.88
                ],
                [
                    "2025-03-24",
                    -3.82
                ],
                [
                    "2025-03-25",
                    7.63
                ],
                [
                    "2025-03-26",
                    9.70
                ],
                [
                    "2025-03-27",
                    3.36
                ],
                [
                    "2025-03-28",
                    1.04
                ],
                [
                    "2025-03-31",
                    8.72
                ],
                [
                    "2025-04-01",
                    -1.11
                ],
                [
                    "2025-04-02",
                    -1.54
                ],
                [
                    "2025-04-03",
                    -5.92
                ],
                [
                    "2025-04-07",
                    -5.66
                ],
                [
                    "2025-04-08",
                    -3.79
                ],
                [
                    "2025-04-09",
                    -1.61
                ],
                [
                    "2025-04-10",
                    -3.78
                ],
                [
                    "2025-04-11",
                    -1.85
                ],
                [
                    "2025-04-14",
                    -4.98
                ],
                [
                    "2025-04-15",
                    -3.66
                ],
                [
                    "2025-04-16",
                    -2.07
                ],
                [
                    "2025-04-17",
                    -1.75
                ],
                [
                    "2025-04-18",
                    1.01
                ],
                [
                    "2025-04-21",
                    3.73
                ],
                [
                    "2025-04-22",
                    6.34
                ],
                [
                    "2025-04-23",
                    5.75
                ],
                [
                    "2025-04-24",
                    15.84
                ],
                [
                    "2025-04-25",
                    -5.57
                ],
                [
                    "2025-04-28",
                    -5.19
                ],
                [
                    "2025-04-29",
                    6.92
                ],
                [
                    "2025-04-30",
                    -6.23
                ],
                [
                    "2025-05-06",
                    -2.89
                ],
                [
                    "2025-05-07",
                    4.54
                ],
                [
                    "2025-05-08",
                    2.10
                ],
                [
                    "2025-05-09",
                    6.23
                ],
                [
                    "2025-05-12",
                    5.49
                ],
                [
                    "2025-05-13",
                    13.80
                ],
                [
                    "2025-05-14",
                    3.31
                ],
                [
                    "2025-05-15",
                    13.67
                ],
                [
                    "2025-05-16",
                    0.14
                ],
                [
                    "2025-05-19",
                    0.26
                ],
                [
                    "2025-05-20",
                    -2.33
                ],
                [
                    "2025-05-21",
                    -2.14
                ],
                [
                    "2025-05-22",
                    0.73
                ],
                [
                    "2025-05-23",
                    7.20
                ],
                [
                    "2025-05-26",
                    -4.20
                ],
                [
                    "2025-05-27",
                    -7.28
                ],
                [
                    "2025-05-28",
                    -6.42
                ],
                [
                    "2025-05-29",
                    0.28
                ],
                [
                    "2025-05-30",
                    -3.24
                ],
                [
                    "2025-06-03",
                    -2.06
                ],
                [
                    "2025-06-04",
                    -5.65
                ],
                [
                    "2025-06-05",
                    -4.83
                ],
                [
                    "2025-06-06",
                    15.63
                ],
                [
                    "2025-06-09",
                    -4.40
                ],
                [
                    "2025-06-10",
                    5.61
                ],
                [
                    "2025-06-11",
                    -13.55
                ],
                [
                    "2025-06-12",
                    -1.14
                ],
                [
                    "2025-06-13",
                    -3.55
                ],
                [
                    "2025-06-16",
                    -10.07
                ],
                [
                    "2025-06-17",
                    -3.36
                ],
                [
                    "2025-06-18",
                    -4.64
                ],
                [
                    "2025-06-19",
                    6.83
                ],
                [
                    "2025-06-20",
                    -0.99
                ],
                [
                    "2025-06-23",
                    20.64
                ],
                [
                    "2025-06-24",
                    10.46
                ],
                [
                    "2025-06-25",
                    12.16
                ],
                [
                    "2025-06-26",
                    6.71
                ],
                [
                    "2025-06-27",
                    2.49
                ],
                [
                    "2025-06-30",
                    5.53
                ],
                [
                    "2025-07-01",
                    9.82
                ],
                [
                    "2025-07-02",
                    4.29
                ],
                [
                    "2025-07-03",
                    6.12
                ],
                [
                    "2025-07-04",
                    -4.02
                ],
                [
                    "2025-07-07",
                    -6.09
                ],
                [
                    "2025-07-08",
                    -0.59
                ],
                [
                    "2025-07-09",
                    -0.49
                ],
                [
                    "2025-07-10",
                    8.50
                ],
                [
                    "2025-07-11",
                    -5.81
                ],
                [
                    "2025-07-14",
                    -0.82
                ],
                [
                    "2025-07-15",
                    5.01
                ],
                [
                    "2025-07-16",
                    11.70
                ],
                [
                    "2025-07-17",
                    0.40
                ],
                [
                    "2025-07-18",
                    1.01
                ],
                [
                    "2025-07-21",
                    -1.28
                ],
                [
                    "2025-07-22",
                    -9.54
                ],
                [
                    "2025-07-23",
                    -4.40
                ],
                [
                    "2025-07-24",
                    -2.10
                ],
                [
                    "2025-07-25",
                    2.17
                ],
                [
                    "2025-07-28",
                    5.25
                ],
                [
                    "2025-07-29",
                    0.68
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    1.69
                ],
                [
                    "2025-02-05",
                    -25.36
                ],
                [
                    "2025-02-06",
                    9.48
                ],
                [
                    "2025-02-07",
                    -2.94
                ],
                [
                    "2025-02-10",
                    -4.43
                ],
                [
                    "2025-02-11",
                    4.30
                ],
                [
                    "2025-02-12",
                    -0.47
                ],
                [
                    "2025-02-13",
                    -8.43
                ],
                [
                    "2025-02-14",
                    2.25
                ],
                [
                    "2025-02-17",
                    -4.48
                ],
                [
                    "2025-02-18",
                    6.88
                ],
                [
                    "2025-02-19",
                    -5.56
                ],
                [
                    "2025-02-20",
                    0.21
                ],
                [
                    "2025-02-21",
                    5.39
                ],
                [
                    "2025-02-24",
                    -2.41
                ],
                [
                    "2025-02-25",
                    5.17
                ],
                [
                    "2025-02-26",
                    10.36
                ],
                [
                    "2025-02-27",
                    -1.22
                ],
                [
                    "2025-02-28",
                    -0.06
                ],
                [
                    "2025-03-03",
                    -0.18
                ],
                [
                    "2025-03-04",
                    7.68
                ],
                [
                    "2025-03-05",
                    5.14
                ],
                [
                    "2025-03-06",
                    7.91
                ],
                [
                    "2025-03-07",
                    10.26
                ],
                [
                    "2025-03-10",
                    1.50
                ],
                [
                    "2025-03-11",
                    -1.84
                ],
                [
                    "2025-03-12",
                    -5.42
                ],
                [
                    "2025-03-13",
                    3.38
                ],
                [
                    "2025-03-14",
                    11.28
                ],
                [
                    "2025-03-17",
                    3.82
                ],
                [
                    "2025-03-18",
                    17.51
                ],
                [
                    "2025-03-19",
                    13.96
                ],
                [
                    "2025-03-20",
                    8.22
                ],
                [
                    "2025-03-21",
                    3.86
                ],
                [
                    "2025-03-24",
                    5.94
                ],
                [
                    "2025-03-25",
                    -9.02
                ],
                [
                    "2025-03-26",
                    -13.16
                ],
                [
                    "2025-03-27",
                    -15.89
                ],
                [
                    "2025-03-28",
                    3.38
                ],
                [
                    "2025-03-31",
                    -8.84
                ],
                [
                    "2025-04-01",
                    -6.91
                ],
                [
                    "2025-04-02",
                    7.59
                ],
                [
                    "2025-04-03",
                    -0.55
                ],
                [
                    "2025-04-07",
                    19.52
                ],
                [
                    "2025-04-08",
                    9.66
                ],
                [
                    "2025-04-09",
                    0.38
                ],
                [
                    "2025-04-10",
                    -5.21
                ],
                [
                    "2025-04-11",
                    -0.18
                ],
                [
                    "2025-04-14",
                    0.51
                ],
                [
                    "2025-04-15",
                    1.41
                ],
                [
                    "2025-04-16",
                    19.33
                ],
                [
                    "2025-04-17",
                    10.44
                ],
                [
                    "2025-04-18",
                    -0.93
                ],
                [
                    "2025-04-21",
                    -3.56
                ],
                [
                    "2025-04-22",
                    -2.86
                ],
                [
                    "2025-04-23",
                    -9.08
                ],
                [
                    "2025-04-24",
                    -20.55
                ],
                [
                    "2025-04-25",
                    5.37
                ],
                [
                    "2025-04-28",
                    -1.55
                ],
                [
                    "2025-04-29",
                    8.00
                ],
                [
                    "2025-04-30",
                    8.05
                ],
                [
                    "2025-05-06",
                    -14.11
                ],
                [
                    "2025-05-07",
                    -7.94
                ],
                [
                    "2025-05-08",
                    2.04
                ],
                [
                    "2025-05-09",
                    3.46
                ],
                [
                    "2025-05-12",
                    -2.18
                ],
                [
                    "2025-05-13",
                    -16.82
                ],
                [
                    "2025-05-14",
                    -0.77
                ],
                [
                    "2025-05-15",
                    -11.22
                ],
                [
                    "2025-05-16",
                    12.48
                ],
                [
                    "2025-05-19",
                    2.42
                ],
                [
                    "2025-05-20",
                    1.55
                ],
                [
                    "2025-05-21",
                    7.60
                ],
                [
                    "2025-05-22",
                    14.97
                ],
                [
                    "2025-05-23",
                    17.24
                ],
                [
                    "2025-05-26",
                    -4.70
                ],
                [
                    "2025-05-27",
                    -5.13
                ],
                [
                    "2025-05-28",
                    10.75
                ],
                [
                    "2025-05-29",
                    3.24
                ],
                [
                    "2025-05-30",
                    0.21
                ],
                [
                    "2025-06-03",
                    -8.21
                ],
                [
                    "2025-06-04",
                    16.64
                ],
                [
                    "2025-06-05",
                    12.60
                ],
                [
                    "2025-06-06",
                    -8.65
                ],
                [
                    "2025-06-09",
                    0.14
                ],
                [
                    "2025-06-10",
                    -1.17
                ],
                [
                    "2025-06-11",
                    6.58
                ],
                [
                    "2025-06-12",
                    8.84
                ],
                [
                    "2025-06-13",
                    14.94
                ],
                [
                    "2025-06-16",
                    -4.82
                ],
                [
                    "2025-06-17",
                    5.08
                ],
                [
                    "2025-06-18",
                    8.88
                ],
                [
                    "2025-06-19",
                    -2.25
                ],
                [
                    "2025-06-20",
                    5.66
                ],
                [
                    "2025-06-23",
                    -25.56
                ],
                [
                    "2025-06-24",
                    -12.17
                ],
                [
                    "2025-06-25",
                    -17.38
                ],
                [
                    "2025-06-26",
                    -2.67
                ],
                [
                    "2025-06-27",
                    -4.24
                ],
                [
                    "2025-06-30",
                    -5.87
                ],
                [
                    "2025-07-01",
                    -15.54
                ],
                [
                    "2025-07-02",
                    -6.21
                ],
                [
                    "2025-07-03",
                    1.17
                ],
                [
                    "2025-07-04",
                    10.55
                ],
                [
                    "2025-07-07",
                    12.29
                ],
                [
                    "2025-07-08",
                    7.86
                ],
                [
                    "2025-07-09",
                    5.09
                ],
                [
                    "2025-07-10",
                    3.30
                ],
                [
                    "2025-07-11",
                    28.66
                ],
                [
                    "2025-07-14",
                    12.78
                ],
                [
                    "2025-07-15",
                    -0.15
                ],
                [
                    "2025-07-16",
                    -12.02
                ],
                [
                    "2025-07-17",
                    14.60
                ],
                [
                    "2025-07-18",
                    -6.61
                ],
                [
                    "2025-07-21",
                    3.50
                ],
                [
                    "2025-07-22",
                    8.64
                ],
                [
                    "2025-07-23",
                    8.84
                ],
                [
                    "2025-07-24",
                    10.76
                ],
                [
                    "2025-07-25",
                    -1.60
                ],
                [
                    "2025-07-28",
                    10.48
                ],
                [
                    "2025-07-29",
                    6.86
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    2.19
                ],
                [
                    "2025-02-11",
                    -7.64
                ],
                [
                    "2025-02-13",
                    12.70
                ],
                [
                    "2025-02-14",
                    1.47
                ],
                [
                    "2025-02-17",
                    5.12
                ],
                [
                    "2025-02-18",
                    1.30
                ],
                [
                    "2025-02-19",
                    3.14
                ],
                [
                    "2025-02-20",
                    -3.60
                ],
                [
                    "2025-03-27",
                    12.53
                ],
                [
                    "2025-03-28",
                    -4.42
                ],
                [
                    "2025-03-31",
                    0.13
                ],
                [
                    "2025-04-01",
                    8.02
                ],
                [
                    "2025-04-02",
                    -6.04
                ],
                [
                    "2025-04-03",
                    6.47
                ],
                [
                    "2025-04-15",
                    2.24
                ],
                [
                    "2025-04-24",
                    4.71
                ],
                [
                    "2025-04-25",
                    0.19
                ],
                [
                    "2025-04-28",
                    6.73
                ],
                [
                    "2025-04-29",
                    -14.93
                ],
                [
                    "2025-05-07",
                    3.39
                ],
                [
                    "2025-05-09",
                    -9.68
                ],
                [
                    "2025-05-12",
                    -3.30
                ],
                [
                    "2025-06-25",
                    5.23
                ],
                [
                    "2025-06-26",
                    -4.04
                ],
                [
                    "2025-06-27",
                    1.74
                ],
                [
                    "2025-06-30",
                    0.34
                ],
                [
                    "2025-07-01",
                    5.72
                ],
                [
                    "2025-07-02",
                    1.92
                ],
                [
                    "2025-07-03",
                    -7.29
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    2.24
                ],
                [
                    "2025-02-11",
                    3.34
                ],
                [
                    "2025-02-13",
                    -4.28
                ],
                [
                    "2025-03-26",
                    9.70
                ],
                [
                    "2025-03-27",
                    3.36
                ],
                [
                    "2025-03-28",
                    1.04
                ],
                [
                    "2025-03-31",
                    8.72
                ],
                [
                    "2025-04-01",
                    -1.11
                ],
                [
                    "2025-04-02",
                    -1.54
                ],
                [
                    "2025-04-03",
                    -5.92
                ],
                [
                    "2025-04-23",
                    5.75
                ],
                [
                    "2025-04-24",
                    15.84
                ],
                [
                    "2025-04-25",
                    -5.57
                ],
                [
                    "2025-04-28",
                    -5.19
                ],
                [
                    "2025-04-29",
                    6.92
                ],
                [
                    "2025-04-30",
                    -6.23
                ],
                [
                    "2025-05-08",
                    2.10
                ],
                [
                    "2025-05-09",
                    6.23
                ],
                [
                    "2025-05-12",
                    5.49
                ],
                [
                    "2025-05-13",
                    13.80
                ],
                [
                    "2025-05-14",
                    3.31
                ],
                [
                    "2025-05-15",
                    13.67
                ],
                [
                    "2025-05-16",
                    0.14
                ],
                [
                    "2025-05-19",
                    0.26
                ],
                [
                    "2025-06-23",
                    20.64
                ],
                [
                    "2025-06-24",
                    10.46
                ],
                [
                    "2025-06-25",
                    12.16
                ],
                [
                    "2025-06-26",
                    6.71
                ],
                [
                    "2025-06-27",
                    2.49
                ],
                [
                    "2025-06-30",
                    5.53
                ],
                [
                    "2025-07-01",
                    9.82
                ],
                [
                    "2025-07-02",
                    4.29
                ],
                [
                    "2025-07-03",
                    6.12
                ],
                [
                    "2025-07-04",
                    -4.02
                ],
                [
                    "2025-07-21",
                    -1.28
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-12",
                    -0.47
                ],
                [
                    "2025-02-25",
                    5.17
                ],
                [
                    "2025-02-26",
                    10.36
                ],
                [
                    "2025-02-27",
                    -1.22
                ],
                [
                    "2025-02-28",
                    -0.06
                ],
                [
                    "2025-03-03",
                    -0.18
                ],
                [
                    "2025-03-04",
                    7.68
                ],
                [
                    "2025-03-05",
                    5.14
                ],
                [
                    "2025-03-06",
                    7.91
                ],
                [
                    "2025-03-07",
                    10.26
                ],
                [
                    "2025-03-10",
                    1.50
                ],
                [
                    "2025-03-11",
                    -1.84
                ],
                [
                    "2025-03-18",
                    17.51
                ],
                [
                    "2025-03-19",
                    13.96
                ],
                [
                    "2025-03-20",
                    8.22
                ],
                [
                    "2025-03-21",
                    3.86
                ],
                [
                    "2025-03-24",
                    5.94
                ],
                [
                    "2025-03-25",
                    -9.02
                ],
                [
                    "2025-04-07",
                    19.52
                ],
                [
                    "2025-04-08",
                    9.66
                ],
                [
                    "2025-04-09",
                    0.38
                ],
                [
                    "2025-04-10",
                    -5.21
                ],
                [
                    "2025-04-11",
                    -0.18
                ],
                [
                    "2025-04-17",
                    10.44
                ],
                [
                    "2025-04-18",
                    -0.93
                ],
                [
                    "2025-04-21",
                    -3.56
                ],
                [
                    "2025-04-22",
                    -2.86
                ],
                [
                    "2025-05-20",
                    1.55
                ],
                [
                    "2025-05-21",
                    7.60
                ],
                [
                    "2025-05-22",
                    14.97
                ],
                [
                    "2025-05-23",
                    17.24
                ],
                [
                    "2025-05-26",
                    -4.70
                ],
                [
                    "2025-05-27",
                    -5.13
                ],
                [
                    "2025-05-28",
                    10.75
                ],
                [
                    "2025-05-29",
                    3.24
                ],
                [
                    "2025-06-04",
                    16.64
                ],
                [
                    "2025-06-05",
                    12.60
                ],
                [
                    "2025-06-06",
                    -8.65
                ],
                [
                    "2025-06-09",
                    0.14
                ],
                [
                    "2025-06-10",
                    -1.17
                ],
                [
                    "2025-06-11",
                    6.58
                ],
                [
                    "2025-06-12",
                    8.84
                ],
                [
                    "2025-06-13",
                    14.94
                ],
                [
                    "2025-06-16",
                    -4.82
                ],
                [
                    "2025-06-18",
                    8.88
                ],
                [
                    "2025-06-19",
                    -2.25
                ],
                [
                    "2025-06-20",
                    5.66
                ],
                [
                    "2025-07-07",
                    12.29
                ],
                [
                    "2025-07-08",
                    7.86
                ],
                [
                    "2025-07-09",
                    5.09
                ],
                [
                    "2025-07-10",
                    3.30
                ],
                [
                    "2025-07-11",
                    28.66
                ],
                [
                    "2025-07-14",
                    12.78
                ],
                [
                    "2025-07-15",
                    -0.15
                ],
                [
                    "2025-07-16",
                    -12.02
                ],
                [
                    "2025-07-17",
                    14.60
                ],
                [
                    "2025-07-18",
                    -6.61
                ],
                [
                    "2025-07-22",
                    8.64
                ],
                [
                    "2025-07-23",
                    8.84
                ],
                [
                    "2025-07-24",
                    10.76
                ],
                [
                    "2025-07-25",
                    -1.60
                ],
                [
                    "2025-07-28",
                    10.48
                ],
                [
                    "2025-07-29",
                    6.86
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603899 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_b59a9e430a1a49c7a51346f35fe13fdf.setOption(option_b59a9e430a1a49c7a51346f35fe13fdf);
            window.addEventListener('resize', function(){
                chart_b59a9e430a1a49c7a51346f35fe13fdf.resize();
            })
    </script>
</body>
</html>
