<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="80805b89c4144f3687d3eda2f70cbc10" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_80805b89c4144f3687d3eda2f70cbc10 = echarts.init(
            document.getElementById('80805b89c4144f3687d3eda2f70cbc10'), 'white', {renderer: 'canvas'});
        var option_80805b89c4144f3687d3eda2f70cbc10 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    6.9,
                    6.86,
                    6.83,
                    7.0
                ],
                [
                    6.92,
                    6.96,
                    6.87,
                    7.05
                ],
                [
                    6.94,
                    7.07,
                    6.88,
                    7.08
                ],
                [
                    7.08,
                    7.18,
                    7.04,
                    7.25
                ],
                [
                    7.2,
                    7.38,
                    7.2,
                    7.39
                ],
                [
                    7.38,
                    7.3,
                    7.24,
                    7.39
                ],
                [
                    7.3,
                    7.31,
                    7.24,
                    7.34
                ],
                [
                    7.31,
                    7.18,
                    7.17,
                    7.34
                ],
                [
                    7.2,
                    7.21,
                    7.15,
                    7.23
                ],
                [
                    7.24,
                    7.18,
                    7.13,
                    7.28
                ],
                [
                    7.18,
                    6.93,
                    6.92,
                    7.18
                ],
                [
                    6.94,
                    7.08,
                    6.94,
                    7.1
                ],
                [
                    7.13,
                    7.79,
                    7.13,
                    7.79
                ],
                [
                    7.8,
                    7.86,
                    7.69,
                    8.11
                ],
                [
                    7.81,
                    8.03,
                    7.81,
                    8.13
                ],
                [
                    7.9,
                    7.93,
                    7.81,
                    8.1
                ],
                [
                    7.91,
                    8.0,
                    7.86,
                    8.0
                ],
                [
                    7.98,
                    7.82,
                    7.68,
                    8.0
                ],
                [
                    7.79,
                    7.53,
                    7.49,
                    7.86
                ],
                [
                    7.55,
                    7.49,
                    7.43,
                    7.69
                ],
                [
                    7.53,
                    7.7,
                    7.5,
                    7.73
                ],
                [
                    7.68,
                    7.75,
                    7.6,
                    7.77
                ],
                [
                    7.79,
                    7.75,
                    7.7,
                    7.84
                ],
                [
                    7.73,
                    7.74,
                    7.7,
                    7.91
                ],
                [
                    7.8,
                    7.74,
                    7.71,
                    7.86
                ],
                [
                    7.68,
                    7.9,
                    7.63,
                    7.95
                ],
                [
                    7.94,
                    7.85,
                    7.85,
                    8.02
                ],
                [
                    7.86,
                    7.78,
                    7.67,
                    7.89
                ],
                [
                    7.75,
                    7.78,
                    7.64,
                    7.81
                ],
                [
                    7.79,
                    7.75,
                    7.74,
                    7.83
                ],
                [
                    7.74,
                    7.66,
                    7.65,
                    7.75
                ],
                [
                    7.69,
                    7.61,
                    7.59,
                    7.72
                ],
                [
                    7.61,
                    7.66,
                    7.56,
                    7.76
                ],
                [
                    7.63,
                    7.69,
                    7.6,
                    7.91
                ],
                [
                    7.69,
                    7.48,
                    7.3,
                    7.86
                ],
                [
                    7.43,
                    7.51,
                    7.41,
                    7.71
                ],
                [
                    7.47,
                    7.49,
                    7.45,
                    7.61
                ],
                [
                    7.47,
                    7.42,
                    7.3,
                    7.49
                ],
                [
                    7.41,
                    7.3,
                    7.28,
                    7.45
                ],
                [
                    7.25,
                    7.22,
                    7.11,
                    7.3
                ],
                [
                    7.24,
                    7.34,
                    7.24,
                    7.43
                ],
                [
                    7.34,
                    7.29,
                    7.26,
                    7.36
                ],
                [
                    7.23,
                    7.28,
                    7.22,
                    7.35
                ],
                [
                    7.01,
                    6.55,
                    6.55,
                    7.06
                ],
                [
                    6.6,
                    6.57,
                    6.45,
                    6.72
                ],
                [
                    6.55,
                    6.88,
                    6.42,
                    6.95
                ],
                [
                    6.9,
                    6.92,
                    6.85,
                    7.04
                ],
                [
                    6.93,
                    6.93,
                    6.84,
                    7.03
                ],
                [
                    6.95,
                    6.99,
                    6.94,
                    7.02
                ],
                [
                    6.97,
                    6.9,
                    6.85,
                    7.0
                ],
                [
                    6.89,
                    6.81,
                    6.7,
                    6.95
                ],
                [
                    6.76,
                    6.81,
                    6.74,
                    6.92
                ],
                [
                    6.82,
                    6.82,
                    6.77,
                    6.86
                ],
                [
                    6.82,
                    6.97,
                    6.79,
                    6.99
                ],
                [
                    6.96,
                    6.96,
                    6.89,
                    6.97
                ],
                [
                    6.99,
                    6.98,
                    6.96,
                    7.13
                ],
                [
                    6.99,
                    6.95,
                    6.87,
                    7.0
                ],
                [
                    6.95,
                    6.93,
                    6.89,
                    6.99
                ],
                [
                    6.93,
                    6.8,
                    6.8,
                    6.93
                ],
                [
                    6.8,
                    6.82,
                    6.74,
                    6.82
                ],
                [
                    6.82,
                    6.82,
                    6.78,
                    6.85
                ],
                [
                    6.88,
                    6.96,
                    6.86,
                    6.97
                ],
                [
                    7.05,
                    7.25,
                    7.05,
                    7.38
                ],
                [
                    7.38,
                    7.5,
                    7.2,
                    7.5
                ],
                [
                    7.52,
                    7.4,
                    7.33,
                    7.56
                ],
                [
                    7.41,
                    7.65,
                    7.36,
                    7.8
                ],
                [
                    7.7,
                    7.5,
                    7.48,
                    7.7
                ],
                [
                    7.47,
                    7.4,
                    7.36,
                    7.54
                ],
                [
                    7.38,
                    7.26,
                    7.25,
                    7.5
                ],
                [
                    7.25,
                    7.22,
                    7.22,
                    7.39
                ],
                [
                    7.3,
                    7.46,
                    7.23,
                    7.5
                ],
                [
                    7.42,
                    7.4,
                    7.36,
                    7.54
                ],
                [
                    7.41,
                    7.29,
                    7.28,
                    7.43
                ],
                [
                    7.3,
                    7.24,
                    7.21,
                    7.38
                ],
                [
                    7.27,
                    7.15,
                    7.14,
                    7.28
                ],
                [
                    7.16,
                    7.29,
                    7.12,
                    7.32
                ],
                [
                    7.37,
                    7.33,
                    7.26,
                    7.39
                ],
                [
                    7.28,
                    7.25,
                    7.24,
                    7.37
                ],
                [
                    7.25,
                    7.41,
                    7.23,
                    7.42
                ],
                [
                    7.39,
                    7.38,
                    7.34,
                    7.41
                ],
                [
                    7.39,
                    7.41,
                    7.38,
                    7.53
                ],
                [
                    7.39,
                    7.4,
                    7.38,
                    7.51
                ],
                [
                    7.39,
                    7.42,
                    7.3,
                    7.43
                ],
                [
                    7.42,
                    7.42,
                    7.4,
                    7.49
                ],
                [
                    7.45,
                    7.48,
                    7.43,
                    7.53
                ],
                [
                    7.47,
                    7.32,
                    7.25,
                    7.48
                ],
                [
                    7.31,
                    7.33,
                    7.29,
                    7.37
                ],
                [
                    7.31,
                    7.27,
                    7.24,
                    7.32
                ],
                [
                    7.27,
                    7.38,
                    7.24,
                    7.48
                ],
                [
                    7.4,
                    7.38,
                    7.31,
                    7.43
                ],
                [
                    7.37,
                    7.44,
                    7.31,
                    7.44
                ],
                [
                    7.42,
                    7.46,
                    7.4,
                    7.5
                ],
                [
                    7.45,
                    7.31,
                    7.2,
                    7.45
                ],
                [
                    7.28,
                    7.24,
                    7.23,
                    7.32
                ],
                [
                    7.3,
                    7.96,
                    7.21,
                    7.96
                ],
                [
                    7.79,
                    7.8,
                    7.65,
                    7.86
                ],
                [
                    7.78,
                    7.94,
                    7.77,
                    8.03
                ],
                [
                    8.09,
                    8.03,
                    8.01,
                    8.32
                ],
                [
                    7.96,
                    8.14,
                    7.96,
                    8.25
                ],
                [
                    8.2,
                    8.4,
                    8.16,
                    8.46
                ],
                [
                    8.41,
                    8.36,
                    8.21,
                    8.48
                ],
                [
                    8.33,
                    8.12,
                    8.08,
                    8.33
                ],
                [
                    8.11,
                    8.16,
                    8.11,
                    8.26
                ],
                [
                    8.13,
                    8.07,
                    8.02,
                    8.15
                ],
                [
                    8.06,
                    8.03,
                    7.97,
                    8.09
                ],
                [
                    8.03,
                    8.04,
                    7.99,
                    8.07
                ],
                [
                    8.04,
                    8.0,
                    7.98,
                    8.11
                ],
                [
                    7.99,
                    8.02,
                    7.9,
                    8.03
                ],
                [
                    8.02,
                    8.05,
                    7.97,
                    8.07
                ],
                [
                    8.04,
                    8.0,
                    7.97,
                    8.07
                ],
                [
                    7.7,
                    7.61,
                    7.52,
                    7.8
                ],
                [
                    7.61,
                    7.65,
                    7.55,
                    7.71
                ],
                [
                    7.65,
                    7.8,
                    7.57,
                    7.83
                ],
                [
                    7.82,
                    7.91,
                    7.81,
                    8.04
                ],
                [
                    7.93,
                    7.94,
                    7.92,
                    8.01
                ],
                [
                    7.9,
                    7.93,
                    7.88,
                    8.0
                ],
                [
                    7.95,
                    7.82,
                    7.8,
                    7.95
                ],
                [
                    7.83,
                    8.17,
                    7.81,
                    8.38
                ],
                [
                    8.26,
                    8.2,
                    8.13,
                    8.33
                ],
                [
                    8.23,
                    8.28,
                    8.16,
                    8.32
                ],
                [
                    8.25,
                    8.33,
                    8.19,
                    8.33
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#880000",
                "borderColor0": "#008F28"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5438\u7b79",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 18,
            "data": [
                [
                    "2025-03-07",
                    7.7
                ],
                [
                    "2025-03-17",
                    7.74
                ],
                [
                    "2025-03-25",
                    7.41
                ],
                [
                    "2025-04-03",
                    7.22
                ],
                [
                    "2025-04-10",
                    6.85
                ],
                [
                    "2025-04-23",
                    6.96
                ],
                [
                    "2025-05-22",
                    7.21
                ],
                [
                    "2025-05-27",
                    7.26
                ],
                [
                    "2025-05-30",
                    7.34
                ],
                [
                    "2025-07-18",
                    7.81
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#F4B400"
            }
        },
        {
            "type": "scatter",
            "name": "\u52a0\u4ed3",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 18,
            "data": [
                [
                    "2025-02-20",
                    7.13
                ],
                [
                    "2025-05-07",
                    7.05
                ],
                [
                    "2025-05-19",
                    7.23
                ],
                [
                    "2025-06-23",
                    7.21
                ],
                [
                    "2025-07-24",
                    7.81
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4285F4"
            }
        },
        {
            "type": "scatter",
            "name": "\u6d17\u76d8",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "rect",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#16A085"
            }
        },
        {
            "type": "scatter",
            "name": "\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "circle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#DB4437"
            }
        },
        {
            "type": "scatter",
            "name": "\u9690\u5f62\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "pin",
            "symbolSize": 18,
            "data": [
                [
                    "2025-05-14",
                    7.36
                ],
                [
                    "2025-06-10",
                    7.25
                ],
                [
                    "2025-06-11",
                    7.29
                ],
                [
                    "2025-06-12",
                    7.24
                ],
                [
                    "2025-06-20",
                    7.23
                ],
                [
                    "2025-07-02",
                    8.08
                ],
                [
                    "2025-07-03",
                    8.11
                ],
                [
                    "2025-07-04",
                    8.02
                ],
                [
                    "2025-07-07",
                    7.97
                ],
                [
                    "2025-07-10",
                    7.9
                ],
                [
                    "2025-07-16",
                    7.55
                ],
                [
                    "2025-07-23",
                    7.8
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#8E44AD"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5438\u7b79",
                "\u52a0\u4ed3",
                "\u6d17\u76d8",
                "\u51fa\u8d27",
                "\u9690\u5f62\u51fa\u8d27"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10,
            "top": "2%"
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "interval": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 0
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "000547 \u4e3b\u529b\u4fe1\u53f7 (\u542b\u6d17\u76d8)",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "90%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_80805b89c4144f3687d3eda2f70cbc10.setOption(option_80805b89c4144f3687d3eda2f70cbc10);
            window.addEventListener('resize', function(){
                chart_80805b89c4144f3687d3eda2f70cbc10.resize();
            })
    </script>
</body>
</html>
