<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="3d87a53bf9c546899b9c4354bd8554d7" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_3d87a53bf9c546899b9c4354bd8554d7 = echarts.init(
            document.getElementById('3d87a53bf9c546899b9c4354bd8554d7'), 'white', {renderer: 'canvas'});
        var option_3d87a53bf9c546899b9c4354bd8554d7 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    14.85,
                    14.24,
                    14.20,
                    14.96
                ],
                [
                    14.30,
                    15.40,
                    14.14,
                    15.66
                ],
                [
                    15.25,
                    16.94,
                    15.23,
                    16.94
                ],
                [
                    17.13,
                    16.85,
                    16.37,
                    17.33
                ],
                [
                    17.11,
                    16.90,
                    16.55,
                    17.25
                ],
                [
                    16.88,
                    17.61,
                    16.80,
                    17.89
                ],
                [
                    17.20,
                    17.94,
                    17.18,
                    18.52
                ],
                [
                    18.42,
                    17.63,
                    17.59,
                    18.88
                ],
                [
                    17.39,
                    17.91,
                    17.24,
                    18.39
                ],
                [
                    17.79,
                    17.80,
                    17.30,
                    18.00
                ],
                [
                    17.80,
                    17.53,
                    17.44,
                    18.35
                ],
                [
                    17.57,
                    19.28,
                    17.57,
                    19.28
                ],
                [
                    19.09,
                    19.16,
                    18.80,
                    19.67
                ],
                [
                    19.59,
                    21.08,
                    19.29,
                    21.08
                ],
                [
                    21.00,
                    20.81,
                    19.39,
                    21.60
                ],
                [
                    20.00,
                    21.08,
                    20.00,
                    21.89
                ],
                [
                    21.00,
                    23.11,
                    21.00,
                    23.19
                ],
                [
                    23.30,
                    21.65,
                    21.36,
                    23.30
                ],
                [
                    21.66,
                    19.49,
                    19.49,
                    21.70
                ],
                [
                    19.49,
                    18.45,
                    17.70,
                    19.49
                ],
                [
                    18.46,
                    18.59,
                    18.38,
                    19.26
                ],
                [
                    18.57,
                    19.03,
                    18.36,
                    19.06
                ],
                [
                    19.00,
                    19.59,
                    18.95,
                    19.93
                ],
                [
                    19.50,
                    19.56,
                    19.18,
                    19.98
                ],
                [
                    19.38,
                    19.35,
                    18.89,
                    19.75
                ],
                [
                    18.88,
                    18.98,
                    18.50,
                    19.30
                ],
                [
                    19.14,
                    18.61,
                    18.58,
                    19.36
                ],
                [
                    18.68,
                    17.50,
                    17.21,
                    18.68
                ],
                [
                    17.41,
                    17.91,
                    17.11,
                    17.98
                ],
                [
                    18.20,
                    19.70,
                    18.01,
                    19.70
                ],
                [
                    20.00,
                    19.89,
                    19.51,
                    20.66
                ],
                [
                    19.89,
                    20.61,
                    19.40,
                    20.80
                ],
                [
                    20.35,
                    20.60,
                    19.72,
                    21.15
                ],
                [
                    20.39,
                    18.54,
                    18.54,
                    20.42
                ],
                [
                    18.40,
                    18.22,
                    17.43,
                    18.46
                ],
                [
                    18.20,
                    17.60,
                    17.50,
                    18.40
                ],
                [
                    17.47,
                    17.96,
                    17.40,
                    18.48
                ],
                [
                    17.90,
                    17.65,
                    17.56,
                    18.24
                ],
                [
                    17.66,
                    17.77,
                    17.66,
                    18.61
                ],
                [
                    17.50,
                    17.00,
                    16.58,
                    17.56
                ],
                [
                    17.27,
                    16.69,
                    16.68,
                    17.30
                ],
                [
                    16.69,
                    16.70,
                    16.58,
                    17.13
                ],
                [
                    16.36,
                    16.20,
                    16.08,
                    16.83
                ],
                [
                    14.80,
                    14.58,
                    14.58,
                    15.48
                ],
                [
                    14.50,
                    13.77,
                    13.30,
                    14.79
                ],
                [
                    13.49,
                    14.15,
                    12.60,
                    14.30
                ],
                [
                    14.60,
                    14.90,
                    14.60,
                    15.30
                ],
                [
                    14.70,
                    15.26,
                    14.70,
                    15.67
                ],
                [
                    15.60,
                    15.42,
                    15.25,
                    15.83
                ],
                [
                    15.44,
                    15.62,
                    15.18,
                    15.84
                ],
                [
                    15.50,
                    15.14,
                    14.80,
                    15.60
                ],
                [
                    15.05,
                    14.98,
                    14.97,
                    15.30
                ],
                [
                    14.89,
                    15.07,
                    14.68,
                    15.47
                ],
                [
                    15.08,
                    15.40,
                    14.90,
                    15.47
                ],
                [
                    15.30,
                    15.34,
                    15.10,
                    15.51
                ],
                [
                    15.56,
                    15.84,
                    15.46,
                    15.99
                ],
                [
                    15.85,
                    15.56,
                    15.50,
                    15.99
                ],
                [
                    15.51,
                    15.65,
                    15.39,
                    15.79
                ],
                [
                    15.51,
                    15.21,
                    15.10,
                    15.57
                ],
                [
                    15.18,
                    15.66,
                    15.08,
                    15.88
                ],
                [
                    15.82,
                    17.23,
                    15.50,
                    17.23
                ],
                [
                    18.00,
                    18.95,
                    18.00,
                    18.95
                ],
                [
                    19.60,
                    19.50,
                    18.95,
                    20.66
                ],
                [
                    19.00,
                    20.50,
                    19.00,
                    21.15
                ],
                [
                    20.20,
                    19.30,
                    19.10,
                    20.66
                ],
                [
                    19.66,
                    19.93,
                    19.58,
                    20.57
                ],
                [
                    20.30,
                    19.21,
                    19.15,
                    20.33
                ],
                [
                    19.33,
                    18.90,
                    18.61,
                    19.49
                ],
                [
                    18.95,
                    20.79,
                    18.50,
                    20.79
                ],
                [
                    20.79,
                    21.66,
                    20.28,
                    22.67
                ],
                [
                    20.99,
                    21.50,
                    19.97,
                    21.69
                ],
                [
                    21.00,
                    20.47,
                    20.40,
                    21.30
                ],
                [
                    20.17,
                    19.74,
                    19.50,
                    20.59
                ],
                [
                    19.61,
                    19.56,
                    19.30,
                    19.96
                ],
                [
                    19.50,
                    20.42,
                    19.13,
                    21.06
                ],
                [
                    20.53,
                    21.22,
                    19.99,
                    21.66
                ],
                [
                    20.93,
                    20.90,
                    20.40,
                    21.16
                ],
                [
                    21.02,
                    22.14,
                    20.90,
                    22.99
                ],
                [
                    22.55,
                    22.06,
                    21.75,
                    23.18
                ],
                [
                    21.84,
                    21.36,
                    20.85,
                    21.98
                ],
                [
                    21.06,
                    21.87,
                    21.05,
                    22.52
                ],
                [
                    21.80,
                    22.41,
                    21.80,
                    22.90
                ],
                [
                    22.11,
                    22.24,
                    21.60,
                    22.56
                ],
                [
                    22.18,
                    20.78,
                    20.70,
                    22.26
                ],
                [
                    20.78,
                    20.59,
                    20.51,
                    21.36
                ],
                [
                    20.59,
                    19.44,
                    19.35,
                    20.79
                ],
                [
                    19.60,
                    19.29,
                    19.17,
                    19.65
                ],
                [
                    19.28,
                    19.95,
                    18.88,
                    20.40
                ],
                [
                    19.87,
                    19.23,
                    19.04,
                    20.33
                ],
                [
                    19.10,
                    19.23,
                    18.88,
                    19.41
                ],
                [
                    19.16,
                    19.09,
                    18.92,
                    19.30
                ],
                [
                    19.09,
                    19.03,
                    18.60,
                    19.21
                ],
                [
                    18.94,
                    18.38,
                    18.30,
                    19.29
                ],
                [
                    18.28,
                    17.77,
                    17.68,
                    18.40
                ],
                [
                    17.53,
                    17.83,
                    17.53,
                    17.85
                ],
                [
                    17.94,
                    18.42,
                    17.88,
                    18.63
                ],
                [
                    18.45,
                    18.60,
                    18.32,
                    18.86
                ],
                [
                    18.64,
                    18.24,
                    18.23,
                    19.08
                ],
                [
                    18.40,
                    18.24,
                    18.21,
                    18.65
                ],
                [
                    18.44,
                    18.47,
                    18.21,
                    18.62
                ],
                [
                    18.33,
                    18.19,
                    18.01,
                    18.46
                ],
                [
                    18.05,
                    17.90,
                    17.76,
                    18.19
                ],
                [
                    17.90,
                    17.87,
                    17.77,
                    18.08
                ],
                [
                    17.79,
                    17.70,
                    17.59,
                    17.92
                ],
                [
                    17.66,
                    17.57,
                    17.31,
                    17.66
                ],
                [
                    17.57,
                    17.80,
                    17.45,
                    17.87
                ],
                [
                    18.00,
                    17.62,
                    17.60,
                    18.25
                ],
                [
                    17.56,
                    17.40,
                    17.35,
                    17.62
                ],
                [
                    17.44,
                    17.85,
                    17.37,
                    18.18
                ],
                [
                    17.85,
                    18.21,
                    17.78,
                    18.28
                ],
                [
                    18.23,
                    18.09,
                    17.89,
                    18.32
                ],
                [
                    18.11,
                    18.63,
                    18.02,
                    18.85
                ],
                [
                    18.60,
                    19.54,
                    18.28,
                    19.56
                ],
                [
                    19.55,
                    19.52,
                    19.25,
                    19.75
                ],
                [
                    19.73,
                    19.75,
                    19.50,
                    20.40
                ],
                [
                    19.99,
                    19.97,
                    19.80,
                    20.78
                ],
                [
                    19.80,
                    20.81,
                    19.68,
                    21.00
                ],
                [
                    20.65,
                    21.69,
                    20.50,
                    22.28
                ],
                [
                    21.71,
                    21.79,
                    21.25,
                    22.06
                ],
                [
                    21.79,
                    21.80,
                    21.66,
                    22.97
                ],
                [
                    21.79,
                    22.54,
                    21.72,
                    23.00
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u5438\u7b79",
            "symbol": "triangle",
            "symbolSize": 16,
            "data": [
                [
                    "2025-03-18",
                    20.66
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#2ca02c"
            }
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u7a81\u7834",
            "symbol": "pin",
            "symbolSize": 16,
            "data": [
                [
                    "2025-03-19",
                    20.80
                ],
                [
                    "2025-04-30",
                    17.23
                ],
                [
                    "2025-05-16",
                    22.67
                ],
                [
                    "2025-07-24",
                    22.28
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#1f77b4"
            }
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "symbol": "diamond",
            "symbolSize": 16,
            "data": [
                [
                    "2025-03-18",
                    20.66
                ],
                [
                    "2025-05-16",
                    22.67
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u5438\u7b79",
                "\u5806\u91cf\u7a81\u7834",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002664 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_3d87a53bf9c546899b9c4354bd8554d7.setOption(option_3d87a53bf9c546899b9c4354bd8554d7);
            window.addEventListener('resize', function(){
                chart_3d87a53bf9c546899b9c4354bd8554d7.resize();
            })
    </script>
</body>
</html>
