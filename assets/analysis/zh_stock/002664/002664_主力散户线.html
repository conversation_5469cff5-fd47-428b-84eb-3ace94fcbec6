<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="0a627d4f0cfe4fe7944a761a8d8df9f1" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_0a627d4f0cfe4fe7944a761a8d8df9f1 = echarts.init(
            document.getElementById('0a627d4f0cfe4fe7944a761a8d8df9f1'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_0a627d4f0cfe4fe7944a761a8d8df9f1 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    14.85,
                    14.24,
                    14.2,
                    14.96
                ],
                [
                    14.3,
                    15.4,
                    14.14,
                    15.66
                ],
                [
                    15.25,
                    16.94,
                    15.23,
                    16.94
                ],
                [
                    17.13,
                    16.85,
                    16.37,
                    17.33
                ],
                [
                    17.11,
                    16.9,
                    16.55,
                    17.25
                ],
                [
                    16.88,
                    17.61,
                    16.8,
                    17.89
                ],
                [
                    17.2,
                    17.94,
                    17.18,
                    18.52
                ],
                [
                    18.42,
                    17.63,
                    17.59,
                    18.88
                ],
                [
                    17.39,
                    17.91,
                    17.24,
                    18.39
                ],
                [
                    17.79,
                    17.8,
                    17.3,
                    18.0
                ],
                [
                    17.8,
                    17.53,
                    17.44,
                    18.35
                ],
                [
                    17.57,
                    19.28,
                    17.57,
                    19.28
                ],
                [
                    19.09,
                    19.16,
                    18.8,
                    19.67
                ],
                [
                    19.59,
                    21.08,
                    19.29,
                    21.08
                ],
                [
                    21.0,
                    20.81,
                    19.39,
                    21.6
                ],
                [
                    20.0,
                    21.08,
                    20.0,
                    21.89
                ],
                [
                    21.0,
                    23.11,
                    21.0,
                    23.19
                ],
                [
                    23.3,
                    21.65,
                    21.36,
                    23.3
                ],
                [
                    21.66,
                    19.49,
                    19.49,
                    21.7
                ],
                [
                    19.49,
                    18.45,
                    17.7,
                    19.49
                ],
                [
                    18.46,
                    18.59,
                    18.38,
                    19.26
                ],
                [
                    18.57,
                    19.03,
                    18.36,
                    19.06
                ],
                [
                    19.0,
                    19.59,
                    18.95,
                    19.93
                ],
                [
                    19.5,
                    19.56,
                    19.18,
                    19.98
                ],
                [
                    19.38,
                    19.35,
                    18.89,
                    19.75
                ],
                [
                    18.88,
                    18.98,
                    18.5,
                    19.3
                ],
                [
                    19.14,
                    18.61,
                    18.58,
                    19.36
                ],
                [
                    18.68,
                    17.5,
                    17.21,
                    18.68
                ],
                [
                    17.41,
                    17.91,
                    17.11,
                    17.98
                ],
                [
                    18.2,
                    19.7,
                    18.01,
                    19.7
                ],
                [
                    20.0,
                    19.89,
                    19.51,
                    20.66
                ],
                [
                    19.89,
                    20.61,
                    19.4,
                    20.8
                ],
                [
                    20.35,
                    20.6,
                    19.72,
                    21.15
                ],
                [
                    20.39,
                    18.54,
                    18.54,
                    20.42
                ],
                [
                    18.4,
                    18.22,
                    17.43,
                    18.46
                ],
                [
                    18.2,
                    17.6,
                    17.5,
                    18.4
                ],
                [
                    17.47,
                    17.96,
                    17.4,
                    18.48
                ],
                [
                    17.9,
                    17.65,
                    17.56,
                    18.24
                ],
                [
                    17.66,
                    17.77,
                    17.66,
                    18.61
                ],
                [
                    17.5,
                    17.0,
                    16.58,
                    17.56
                ],
                [
                    17.27,
                    16.69,
                    16.68,
                    17.3
                ],
                [
                    16.69,
                    16.7,
                    16.58,
                    17.13
                ],
                [
                    16.36,
                    16.2,
                    16.08,
                    16.83
                ],
                [
                    14.8,
                    14.58,
                    14.58,
                    15.48
                ],
                [
                    14.5,
                    13.77,
                    13.3,
                    14.79
                ],
                [
                    13.49,
                    14.15,
                    12.6,
                    14.3
                ],
                [
                    14.6,
                    14.9,
                    14.6,
                    15.3
                ],
                [
                    14.7,
                    15.26,
                    14.7,
                    15.67
                ],
                [
                    15.6,
                    15.42,
                    15.25,
                    15.83
                ],
                [
                    15.44,
                    15.62,
                    15.18,
                    15.84
                ],
                [
                    15.5,
                    15.14,
                    14.8,
                    15.6
                ],
                [
                    15.05,
                    14.98,
                    14.97,
                    15.3
                ],
                [
                    14.89,
                    15.07,
                    14.68,
                    15.47
                ],
                [
                    15.08,
                    15.4,
                    14.9,
                    15.47
                ],
                [
                    15.3,
                    15.34,
                    15.1,
                    15.51
                ],
                [
                    15.56,
                    15.84,
                    15.46,
                    15.99
                ],
                [
                    15.85,
                    15.56,
                    15.5,
                    15.99
                ],
                [
                    15.51,
                    15.65,
                    15.39,
                    15.79
                ],
                [
                    15.51,
                    15.21,
                    15.1,
                    15.57
                ],
                [
                    15.18,
                    15.66,
                    15.08,
                    15.88
                ],
                [
                    15.82,
                    17.23,
                    15.5,
                    17.23
                ],
                [
                    18.0,
                    18.95,
                    18.0,
                    18.95
                ],
                [
                    19.6,
                    19.5,
                    18.95,
                    20.66
                ],
                [
                    19.0,
                    20.5,
                    19.0,
                    21.15
                ],
                [
                    20.2,
                    19.3,
                    19.1,
                    20.66
                ],
                [
                    19.66,
                    19.93,
                    19.58,
                    20.57
                ],
                [
                    20.3,
                    19.21,
                    19.15,
                    20.33
                ],
                [
                    19.33,
                    18.9,
                    18.61,
                    19.49
                ],
                [
                    18.95,
                    20.79,
                    18.5,
                    20.79
                ],
                [
                    20.79,
                    21.66,
                    20.28,
                    22.67
                ],
                [
                    20.99,
                    21.5,
                    19.97,
                    21.69
                ],
                [
                    21.0,
                    20.47,
                    20.4,
                    21.3
                ],
                [
                    20.17,
                    19.74,
                    19.5,
                    20.59
                ],
                [
                    19.61,
                    19.56,
                    19.3,
                    19.96
                ],
                [
                    19.5,
                    20.42,
                    19.13,
                    21.06
                ],
                [
                    20.53,
                    21.22,
                    19.99,
                    21.66
                ],
                [
                    20.93,
                    20.9,
                    20.4,
                    21.16
                ],
                [
                    21.02,
                    22.14,
                    20.9,
                    22.99
                ],
                [
                    22.55,
                    22.06,
                    21.75,
                    23.18
                ],
                [
                    21.84,
                    21.36,
                    20.85,
                    21.98
                ],
                [
                    21.06,
                    21.87,
                    21.05,
                    22.52
                ],
                [
                    21.8,
                    22.41,
                    21.8,
                    22.9
                ],
                [
                    22.11,
                    22.24,
                    21.6,
                    22.56
                ],
                [
                    22.18,
                    20.78,
                    20.7,
                    22.26
                ],
                [
                    20.78,
                    20.59,
                    20.51,
                    21.36
                ],
                [
                    20.59,
                    19.44,
                    19.35,
                    20.79
                ],
                [
                    19.6,
                    19.29,
                    19.17,
                    19.65
                ],
                [
                    19.28,
                    19.95,
                    18.88,
                    20.4
                ],
                [
                    19.87,
                    19.23,
                    19.04,
                    20.33
                ],
                [
                    19.1,
                    19.23,
                    18.88,
                    19.41
                ],
                [
                    19.16,
                    19.09,
                    18.92,
                    19.3
                ],
                [
                    19.09,
                    19.03,
                    18.6,
                    19.21
                ],
                [
                    18.94,
                    18.38,
                    18.3,
                    19.29
                ],
                [
                    18.28,
                    17.77,
                    17.68,
                    18.4
                ],
                [
                    17.53,
                    17.83,
                    17.53,
                    17.85
                ],
                [
                    17.94,
                    18.42,
                    17.88,
                    18.63
                ],
                [
                    18.45,
                    18.6,
                    18.32,
                    18.86
                ],
                [
                    18.64,
                    18.24,
                    18.23,
                    19.08
                ],
                [
                    18.4,
                    18.24,
                    18.21,
                    18.65
                ],
                [
                    18.44,
                    18.47,
                    18.21,
                    18.62
                ],
                [
                    18.33,
                    18.19,
                    18.01,
                    18.46
                ],
                [
                    18.05,
                    17.9,
                    17.76,
                    18.19
                ],
                [
                    17.9,
                    17.87,
                    17.77,
                    18.08
                ],
                [
                    17.79,
                    17.7,
                    17.59,
                    17.92
                ],
                [
                    17.66,
                    17.57,
                    17.31,
                    17.66
                ],
                [
                    17.57,
                    17.8,
                    17.45,
                    17.87
                ],
                [
                    18.0,
                    17.62,
                    17.6,
                    18.25
                ],
                [
                    17.56,
                    17.4,
                    17.35,
                    17.62
                ],
                [
                    17.44,
                    17.85,
                    17.37,
                    18.18
                ],
                [
                    17.85,
                    18.21,
                    17.78,
                    18.28
                ],
                [
                    18.23,
                    18.09,
                    17.89,
                    18.32
                ],
                [
                    18.11,
                    18.63,
                    18.02,
                    18.85
                ],
                [
                    18.6,
                    19.54,
                    18.28,
                    19.56
                ],
                [
                    19.55,
                    19.52,
                    19.25,
                    19.75
                ],
                [
                    19.73,
                    19.75,
                    19.5,
                    20.4
                ],
                [
                    19.99,
                    19.97,
                    19.8,
                    20.78
                ],
                [
                    19.8,
                    20.81,
                    19.68,
                    21.0
                ],
                [
                    20.65,
                    21.69,
                    20.5,
                    22.28
                ],
                [
                    21.71,
                    21.79,
                    21.25,
                    22.06
                ],
                [
                    21.79,
                    21.8,
                    21.66,
                    22.97
                ],
                [
                    21.79,
                    22.54,
                    21.72,
                    23.0
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -17.67
                ],
                [
                    "2025-02-05",
                    10.05
                ],
                [
                    "2025-02-06",
                    15.72
                ],
                [
                    "2025-02-07",
                    -3.00
                ],
                [
                    "2025-02-10",
                    0.15
                ],
                [
                    "2025-02-11",
                    7.65
                ],
                [
                    "2025-02-12",
                    -4.02
                ],
                [
                    "2025-02-13",
                    -5.18
                ],
                [
                    "2025-02-14",
                    3.19
                ],
                [
                    "2025-02-17",
                    -7.08
                ],
                [
                    "2025-02-18",
                    -0.07
                ],
                [
                    "2025-02-19",
                    18.53
                ],
                [
                    "2025-02-20",
                    -10.13
                ],
                [
                    "2025-02-21",
                    10.44
                ],
                [
                    "2025-02-24",
                    0.50
                ],
                [
                    "2025-02-25",
                    6.29
                ],
                [
                    "2025-02-26",
                    10.97
                ],
                [
                    "2025-02-27",
                    -3.14
                ],
                [
                    "2025-02-28",
                    -20.27
                ],
                [
                    "2025-03-03",
                    -9.16
                ],
                [
                    "2025-03-04",
                    -5.79
                ],
                [
                    "2025-03-05",
                    1.25
                ],
                [
                    "2025-03-06",
                    7.87
                ],
                [
                    "2025-03-07",
                    -8.58
                ],
                [
                    "2025-03-10",
                    -4.51
                ],
                [
                    "2025-03-11",
                    -13.56
                ],
                [
                    "2025-03-12",
                    -22.70
                ],
                [
                    "2025-03-13",
                    -17.59
                ],
                [
                    "2025-03-14",
                    4.02
                ],
                [
                    "2025-03-17",
                    24.20
                ],
                [
                    "2025-03-18",
                    1.48
                ],
                [
                    "2025-03-19",
                    9.51
                ],
                [
                    "2025-03-20",
                    -4.07
                ],
                [
                    "2025-03-21",
                    -15.80
                ],
                [
                    "2025-03-24",
                    -9.38
                ],
                [
                    "2025-03-25",
                    -4.48
                ],
                [
                    "2025-03-26",
                    -3.24
                ],
                [
                    "2025-03-27",
                    -5.11
                ],
                [
                    "2025-03-28",
                    3.60
                ],
                [
                    "2025-03-31",
                    1.72
                ],
                [
                    "2025-04-01",
                    -9.36
                ],
                [
                    "2025-04-02",
                    -11.52
                ],
                [
                    "2025-04-03",
                    -19.17
                ],
                [
                    "2025-04-07",
                    -14.58
                ],
                [
                    "2025-04-08",
                    -13.30
                ],
                [
                    "2025-04-09",
                    -6.98
                ],
                [
                    "2025-04-10",
                    8.39
                ],
                [
                    "2025-04-11",
                    -3.09
                ],
                [
                    "2025-04-14",
                    1.69
                ],
                [
                    "2025-04-15",
                    14.01
                ],
                [
                    "2025-04-16",
                    -8.70
                ],
                [
                    "2025-04-17",
                    -7.85
                ],
                [
                    "2025-04-18",
                    4.19
                ],
                [
                    "2025-04-21",
                    8.35
                ],
                [
                    "2025-04-22",
                    -2.59
                ],
                [
                    "2025-04-23",
                    16.05
                ],
                [
                    "2025-04-24",
                    -0.40
                ],
                [
                    "2025-04-25",
                    -5.83
                ],
                [
                    "2025-04-28",
                    -17.72
                ],
                [
                    "2025-04-29",
                    -2.06
                ],
                [
                    "2025-04-30",
                    25.19
                ],
                [
                    "2025-05-06",
                    6.67
                ],
                [
                    "2025-05-07",
                    -1.03
                ],
                [
                    "2025-05-08",
                    6.43
                ],
                [
                    "2025-05-09",
                    -13.83
                ],
                [
                    "2025-05-12",
                    -6.10
                ],
                [
                    "2025-05-13",
                    -18.30
                ],
                [
                    "2025-05-14",
                    -6.83
                ],
                [
                    "2025-05-15",
                    20.70
                ],
                [
                    "2025-05-16",
                    15.26
                ],
                [
                    "2025-05-19",
                    3.61
                ],
                [
                    "2025-05-20",
                    -9.16
                ],
                [
                    "2025-05-21",
                    -6.90
                ],
                [
                    "2025-05-22",
                    -5.16
                ],
                [
                    "2025-05-23",
                    17.69
                ],
                [
                    "2025-05-26",
                    7.63
                ],
                [
                    "2025-05-27",
                    -10.08
                ],
                [
                    "2025-05-28",
                    16.12
                ],
                [
                    "2025-05-29",
                    -6.06
                ],
                [
                    "2025-05-30",
                    -10.63
                ],
                [
                    "2025-06-03",
                    8.91
                ],
                [
                    "2025-06-04",
                    5.71
                ],
                [
                    "2025-06-05",
                    4.98
                ],
                [
                    "2025-06-06",
                    -13.62
                ],
                [
                    "2025-06-09",
                    -12.32
                ],
                [
                    "2025-06-10",
                    -18.74
                ],
                [
                    "2025-06-11",
                    -8.15
                ],
                [
                    "2025-06-12",
                    14.36
                ],
                [
                    "2025-06-13",
                    -6.62
                ],
                [
                    "2025-06-16",
                    2.15
                ],
                [
                    "2025-06-17",
                    -2.29
                ],
                [
                    "2025-06-18",
                    -3.47
                ],
                [
                    "2025-06-19",
                    -3.29
                ],
                [
                    "2025-06-20",
                    -11.99
                ],
                [
                    "2025-06-23",
                    -7.01
                ],
                [
                    "2025-06-24",
                    14.86
                ],
                [
                    "2025-06-25",
                    -3.80
                ],
                [
                    "2025-06-26",
                    -5.42
                ],
                [
                    "2025-06-27",
                    -0.93
                ],
                [
                    "2025-06-30",
                    -0.88
                ],
                [
                    "2025-07-01",
                    -14.78
                ],
                [
                    "2025-07-02",
                    -12.83
                ],
                [
                    "2025-07-03",
                    -3.37
                ],
                [
                    "2025-07-04",
                    0.94
                ],
                [
                    "2025-07-07",
                    -6.63
                ],
                [
                    "2025-07-08",
                    4.08
                ],
                [
                    "2025-07-09",
                    0.46
                ],
                [
                    "2025-07-10",
                    -18.13
                ],
                [
                    "2025-07-11",
                    10.36
                ],
                [
                    "2025-07-14",
                    9.68
                ],
                [
                    "2025-07-15",
                    -7.71
                ],
                [
                    "2025-07-16",
                    11.88
                ],
                [
                    "2025-07-17",
                    13.87
                ],
                [
                    "2025-07-18",
                    -0.40
                ],
                [
                    "2025-07-21",
                    7.32
                ],
                [
                    "2025-07-22",
                    -3.14
                ],
                [
                    "2025-07-23",
                    6.82
                ],
                [
                    "2025-07-24",
                    16.18
                ],
                [
                    "2025-07-25",
                    1.84
                ],
                [
                    "2025-07-28",
                    -7.14
                ],
                [
                    "2025-07-29",
                    2.11
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    1.50
                ],
                [
                    "2025-02-05",
                    -2.97
                ],
                [
                    "2025-02-06",
                    -9.02
                ],
                [
                    "2025-02-07",
                    -0.44
                ],
                [
                    "2025-02-10",
                    3.11
                ],
                [
                    "2025-02-11",
                    -3.90
                ],
                [
                    "2025-02-12",
                    2.03
                ],
                [
                    "2025-02-13",
                    -3.83
                ],
                [
                    "2025-02-14",
                    -2.96
                ],
                [
                    "2025-02-17",
                    3.11
                ],
                [
                    "2025-02-18",
                    2.03
                ],
                [
                    "2025-02-19",
                    -9.78
                ],
                [
                    "2025-02-20",
                    4.21
                ],
                [
                    "2025-02-21",
                    -7.14
                ],
                [
                    "2025-02-24",
                    -3.36
                ],
                [
                    "2025-02-25",
                    -2.65
                ],
                [
                    "2025-02-26",
                    -8.60
                ],
                [
                    "2025-02-27",
                    0.52
                ],
                [
                    "2025-02-28",
                    5.55
                ],
                [
                    "2025-03-03",
                    -5.62
                ],
                [
                    "2025-03-04",
                    -2.91
                ],
                [
                    "2025-03-05",
                    -0.55
                ],
                [
                    "2025-03-06",
                    -0.82
                ],
                [
                    "2025-03-07",
                    3.22
                ],
                [
                    "2025-03-10",
                    -1.04
                ],
                [
                    "2025-03-11",
                    3.14
                ],
                [
                    "2025-03-12",
                    7.45
                ],
                [
                    "2025-03-13",
                    0.33
                ],
                [
                    "2025-03-14",
                    1.65
                ],
                [
                    "2025-03-17",
                    -9.00
                ],
                [
                    "2025-03-18",
                    -0.60
                ],
                [
                    "2025-03-19",
                    2.43
                ],
                [
                    "2025-03-20",
                    3.82
                ],
                [
                    "2025-03-21",
                    3.97
                ],
                [
                    "2025-03-24",
                    -2.77
                ],
                [
                    "2025-03-25",
                    -2.13
                ],
                [
                    "2025-03-26",
                    0.99
                ],
                [
                    "2025-03-27",
                    1.04
                ],
                [
                    "2025-03-28",
                    0.89
                ],
                [
                    "2025-03-31",
                    -0.74
                ],
                [
                    "2025-04-01",
                    -6.35
                ],
                [
                    "2025-04-02",
                    5.86
                ],
                [
                    "2025-04-03",
                    -4.77
                ],
                [
                    "2025-04-07",
                    3.62
                ],
                [
                    "2025-04-08",
                    2.30
                ],
                [
                    "2025-04-09",
                    0.99
                ],
                [
                    "2025-04-10",
                    -6.51
                ],
                [
                    "2025-04-11",
                    1.42
                ],
                [
                    "2025-04-14",
                    -2.48
                ],
                [
                    "2025-04-15",
                    -1.49
                ],
                [
                    "2025-04-16",
                    0.63
                ],
                [
                    "2025-04-17",
                    -1.55
                ],
                [
                    "2025-04-18",
                    1.93
                ],
                [
                    "2025-04-21",
                    -6.97
                ],
                [
                    "2025-04-22",
                    -5.70
                ],
                [
                    "2025-04-23",
                    -5.20
                ],
                [
                    "2025-04-24",
                    -0.22
                ],
                [
                    "2025-04-25",
                    3.07
                ],
                [
                    "2025-04-28",
                    -1.58
                ],
                [
                    "2025-04-29",
                    -1.95
                ],
                [
                    "2025-04-30",
                    -7.67
                ],
                [
                    "2025-05-06",
                    -3.11
                ],
                [
                    "2025-05-07",
                    -3.81
                ],
                [
                    "2025-05-08",
                    -5.13
                ],
                [
                    "2025-05-09",
                    -1.51
                ],
                [
                    "2025-05-12",
                    -6.33
                ],
                [
                    "2025-05-13",
                    -2.86
                ],
                [
                    "2025-05-14",
                    -6.17
                ],
                [
                    "2025-05-15",
                    -9.94
                ],
                [
                    "2025-05-16",
                    1.69
                ],
                [
                    "2025-05-19",
                    -3.01
                ],
                [
                    "2025-05-20",
                    4.69
                ],
                [
                    "2025-05-21",
                    -7.83
                ],
                [
                    "2025-05-22",
                    -2.54
                ],
                [
                    "2025-05-23",
                    -8.40
                ],
                [
                    "2025-05-26",
                    -4.74
                ],
                [
                    "2025-05-27",
                    0.02
                ],
                [
                    "2025-05-28",
                    -5.89
                ],
                [
                    "2025-05-29",
                    -5.28
                ],
                [
                    "2025-05-30",
                    -1.46
                ],
                [
                    "2025-06-03",
                    3.68
                ],
                [
                    "2025-06-04",
                    -0.21
                ],
                [
                    "2025-06-05",
                    -2.95
                ],
                [
                    "2025-06-06",
                    3.94
                ],
                [
                    "2025-06-09",
                    -0.22
                ],
                [
                    "2025-06-10",
                    -5.02
                ],
                [
                    "2025-06-11",
                    0.78
                ],
                [
                    "2025-06-12",
                    -1.34
                ],
                [
                    "2025-06-13",
                    -1.18
                ],
                [
                    "2025-06-16",
                    -0.89
                ],
                [
                    "2025-06-17",
                    2.34
                ],
                [
                    "2025-06-18",
                    -1.95
                ],
                [
                    "2025-06-19",
                    3.89
                ],
                [
                    "2025-06-20",
                    -11.03
                ],
                [
                    "2025-06-23",
                    3.44
                ],
                [
                    "2025-06-24",
                    -0.27
                ],
                [
                    "2025-06-25",
                    7.43
                ],
                [
                    "2025-06-26",
                    2.55
                ],
                [
                    "2025-06-27",
                    0.43
                ],
                [
                    "2025-06-30",
                    2.21
                ],
                [
                    "2025-07-01",
                    5.25
                ],
                [
                    "2025-07-02",
                    0.65
                ],
                [
                    "2025-07-03",
                    -1.61
                ],
                [
                    "2025-07-04",
                    -3.96
                ],
                [
                    "2025-07-07",
                    -2.47
                ],
                [
                    "2025-07-08",
                    5.27
                ],
                [
                    "2025-07-09",
                    3.58
                ],
                [
                    "2025-07-10",
                    2.88
                ],
                [
                    "2025-07-11",
                    -4.06
                ],
                [
                    "2025-07-14",
                    1.66
                ],
                [
                    "2025-07-15",
                    5.92
                ],
                [
                    "2025-07-16",
                    -0.96
                ],
                [
                    "2025-07-17",
                    -1.71
                ],
                [
                    "2025-07-18",
                    5.89
                ],
                [
                    "2025-07-21",
                    -7.40
                ],
                [
                    "2025-07-22",
                    1.14
                ],
                [
                    "2025-07-23",
                    -2.20
                ],
                [
                    "2025-07-24",
                    -5.19
                ],
                [
                    "2025-07-25",
                    -6.05
                ],
                [
                    "2025-07-28",
                    -3.96
                ],
                [
                    "2025-07-29",
                    -6.57
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    16.17
                ],
                [
                    "2025-02-05",
                    -7.08
                ],
                [
                    "2025-02-06",
                    -6.70
                ],
                [
                    "2025-02-07",
                    3.44
                ],
                [
                    "2025-02-10",
                    -3.25
                ],
                [
                    "2025-02-11",
                    -3.75
                ],
                [
                    "2025-02-12",
                    1.98
                ],
                [
                    "2025-02-13",
                    9.00
                ],
                [
                    "2025-02-14",
                    -0.23
                ],
                [
                    "2025-02-17",
                    3.97
                ],
                [
                    "2025-02-18",
                    -1.96
                ],
                [
                    "2025-02-19",
                    -8.74
                ],
                [
                    "2025-02-20",
                    5.91
                ],
                [
                    "2025-02-21",
                    -3.29
                ],
                [
                    "2025-02-24",
                    2.85
                ],
                [
                    "2025-02-25",
                    -3.64
                ],
                [
                    "2025-02-26",
                    -2.38
                ],
                [
                    "2025-02-27",
                    2.63
                ],
                [
                    "2025-02-28",
                    14.72
                ],
                [
                    "2025-03-03",
                    14.77
                ],
                [
                    "2025-03-04",
                    8.69
                ],
                [
                    "2025-03-05",
                    -0.70
                ],
                [
                    "2025-03-06",
                    -7.06
                ],
                [
                    "2025-03-07",
                    5.36
                ],
                [
                    "2025-03-10",
                    5.55
                ],
                [
                    "2025-03-11",
                    10.42
                ],
                [
                    "2025-03-12",
                    15.25
                ],
                [
                    "2025-03-13",
                    17.25
                ],
                [
                    "2025-03-14",
                    -5.66
                ],
                [
                    "2025-03-17",
                    -15.20
                ],
                [
                    "2025-03-18",
                    -0.89
                ],
                [
                    "2025-03-19",
                    -11.95
                ],
                [
                    "2025-03-20",
                    0.25
                ],
                [
                    "2025-03-21",
                    11.84
                ],
                [
                    "2025-03-24",
                    12.15
                ],
                [
                    "2025-03-25",
                    6.61
                ],
                [
                    "2025-03-26",
                    2.25
                ],
                [
                    "2025-03-27",
                    4.07
                ],
                [
                    "2025-03-28",
                    -4.48
                ],
                [
                    "2025-03-31",
                    -0.98
                ],
                [
                    "2025-04-01",
                    15.71
                ],
                [
                    "2025-04-02",
                    5.66
                ],
                [
                    "2025-04-03",
                    23.95
                ],
                [
                    "2025-04-07",
                    10.96
                ],
                [
                    "2025-04-08",
                    11.01
                ],
                [
                    "2025-04-09",
                    5.98
                ],
                [
                    "2025-04-10",
                    -1.88
                ],
                [
                    "2025-04-11",
                    1.67
                ],
                [
                    "2025-04-14",
                    0.79
                ],
                [
                    "2025-04-15",
                    -12.52
                ],
                [
                    "2025-04-16",
                    8.06
                ],
                [
                    "2025-04-17",
                    9.40
                ],
                [
                    "2025-04-18",
                    -6.12
                ],
                [
                    "2025-04-21",
                    -1.38
                ],
                [
                    "2025-04-22",
                    8.29
                ],
                [
                    "2025-04-23",
                    -10.85
                ],
                [
                    "2025-04-24",
                    0.61
                ],
                [
                    "2025-04-25",
                    2.76
                ],
                [
                    "2025-04-28",
                    19.30
                ],
                [
                    "2025-04-29",
                    4.00
                ],
                [
                    "2025-04-30",
                    -17.52
                ],
                [
                    "2025-05-06",
                    -3.56
                ],
                [
                    "2025-05-07",
                    4.84
                ],
                [
                    "2025-05-08",
                    -1.30
                ],
                [
                    "2025-05-09",
                    15.33
                ],
                [
                    "2025-05-12",
                    12.43
                ],
                [
                    "2025-05-13",
                    21.16
                ],
                [
                    "2025-05-14",
                    13.01
                ],
                [
                    "2025-05-15",
                    -10.76
                ],
                [
                    "2025-05-16",
                    -16.95
                ],
                [
                    "2025-05-19",
                    -0.61
                ],
                [
                    "2025-05-20",
                    4.46
                ],
                [
                    "2025-05-21",
                    14.72
                ],
                [
                    "2025-05-22",
                    7.70
                ],
                [
                    "2025-05-23",
                    -9.28
                ],
                [
                    "2025-05-26",
                    -2.90
                ],
                [
                    "2025-05-27",
                    10.06
                ],
                [
                    "2025-05-28",
                    -10.23
                ],
                [
                    "2025-05-29",
                    11.34
                ],
                [
                    "2025-05-30",
                    12.09
                ],
                [
                    "2025-06-03",
                    -12.59
                ],
                [
                    "2025-06-04",
                    -5.51
                ],
                [
                    "2025-06-05",
                    -2.03
                ],
                [
                    "2025-06-06",
                    9.68
                ],
                [
                    "2025-06-09",
                    12.54
                ],
                [
                    "2025-06-10",
                    23.76
                ],
                [
                    "2025-06-11",
                    7.37
                ],
                [
                    "2025-06-12",
                    -13.02
                ],
                [
                    "2025-06-13",
                    7.80
                ],
                [
                    "2025-06-16",
                    -1.26
                ],
                [
                    "2025-06-17",
                    -0.05
                ],
                [
                    "2025-06-18",
                    5.42
                ],
                [
                    "2025-06-19",
                    -0.60
                ],
                [
                    "2025-06-20",
                    23.03
                ],
                [
                    "2025-06-23",
                    3.58
                ],
                [
                    "2025-06-24",
                    -14.59
                ],
                [
                    "2025-06-25",
                    -3.62
                ],
                [
                    "2025-06-26",
                    2.87
                ],
                [
                    "2025-06-27",
                    0.50
                ],
                [
                    "2025-06-30",
                    -1.33
                ],
                [
                    "2025-07-01",
                    9.53
                ],
                [
                    "2025-07-02",
                    12.17
                ],
                [
                    "2025-07-03",
                    4.98
                ],
                [
                    "2025-07-04",
                    3.03
                ],
                [
                    "2025-07-07",
                    9.10
                ],
                [
                    "2025-07-08",
                    -9.35
                ],
                [
                    "2025-07-09",
                    -4.04
                ],
                [
                    "2025-07-10",
                    15.26
                ],
                [
                    "2025-07-11",
                    -6.30
                ],
                [
                    "2025-07-14",
                    -11.34
                ],
                [
                    "2025-07-15",
                    1.78
                ],
                [
                    "2025-07-16",
                    -10.91
                ],
                [
                    "2025-07-17",
                    -12.17
                ],
                [
                    "2025-07-18",
                    -5.50
                ],
                [
                    "2025-07-21",
                    0.08
                ],
                [
                    "2025-07-22",
                    2.01
                ],
                [
                    "2025-07-23",
                    -4.63
                ],
                [
                    "2025-07-24",
                    -11.00
                ],
                [
                    "2025-07-25",
                    4.22
                ],
                [
                    "2025-07-28",
                    11.10
                ],
                [
                    "2025-07-29",
                    4.47
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-11",
                    7.65
                ],
                [
                    "2025-02-12",
                    -4.02
                ],
                [
                    "2025-02-20",
                    -10.13
                ],
                [
                    "2025-02-21",
                    10.44
                ],
                [
                    "2025-02-24",
                    0.50
                ],
                [
                    "2025-02-25",
                    6.29
                ],
                [
                    "2025-02-26",
                    10.97
                ],
                [
                    "2025-02-27",
                    -3.14
                ],
                [
                    "2025-03-19",
                    9.51
                ],
                [
                    "2025-03-20",
                    -4.07
                ],
                [
                    "2025-03-21",
                    -15.80
                ],
                [
                    "2025-04-15",
                    14.01
                ],
                [
                    "2025-04-16",
                    -8.70
                ],
                [
                    "2025-04-18",
                    4.19
                ],
                [
                    "2025-04-21",
                    8.35
                ],
                [
                    "2025-04-23",
                    16.05
                ],
                [
                    "2025-04-24",
                    -0.40
                ],
                [
                    "2025-04-25",
                    -5.83
                ],
                [
                    "2025-05-08",
                    6.43
                ],
                [
                    "2025-05-09",
                    -13.83
                ],
                [
                    "2025-05-20",
                    -9.16
                ],
                [
                    "2025-05-21",
                    -6.90
                ],
                [
                    "2025-05-28",
                    16.12
                ],
                [
                    "2025-05-29",
                    -6.06
                ],
                [
                    "2025-06-04",
                    5.71
                ],
                [
                    "2025-06-18",
                    -3.47
                ],
                [
                    "2025-06-30",
                    -0.88
                ],
                [
                    "2025-07-14",
                    9.68
                ],
                [
                    "2025-07-16",
                    11.88
                ],
                [
                    "2025-07-17",
                    13.87
                ],
                [
                    "2025-07-18",
                    -0.40
                ],
                [
                    "2025-07-21",
                    7.32
                ],
                [
                    "2025-07-22",
                    -3.14
                ],
                [
                    "2025-07-23",
                    6.82
                ],
                [
                    "2025-07-24",
                    16.18
                ],
                [
                    "2025-07-25",
                    1.84
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-21",
                    3.97
                ],
                [
                    "2025-06-27",
                    0.43
                ],
                [
                    "2025-06-30",
                    2.21
                ],
                [
                    "2025-07-14",
                    1.66
                ],
                [
                    "2025-07-15",
                    5.92
                ],
                [
                    "2025-07-16",
                    -0.96
                ],
                [
                    "2025-07-17",
                    -1.71
                ],
                [
                    "2025-07-18",
                    5.89
                ],
                [
                    "2025-07-21",
                    -7.40
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-13",
                    9.00
                ],
                [
                    "2025-02-17",
                    3.97
                ],
                [
                    "2025-02-18",
                    -1.96
                ],
                [
                    "2025-02-28",
                    14.72
                ],
                [
                    "2025-03-03",
                    14.77
                ],
                [
                    "2025-03-04",
                    8.69
                ],
                [
                    "2025-03-05",
                    -0.70
                ],
                [
                    "2025-03-06",
                    -7.06
                ],
                [
                    "2025-03-07",
                    5.36
                ],
                [
                    "2025-03-10",
                    5.55
                ],
                [
                    "2025-03-11",
                    10.42
                ],
                [
                    "2025-03-12",
                    15.25
                ],
                [
                    "2025-03-13",
                    17.25
                ],
                [
                    "2025-03-14",
                    -5.66
                ],
                [
                    "2025-03-17",
                    -15.20
                ],
                [
                    "2025-03-18",
                    -0.89
                ],
                [
                    "2025-03-24",
                    12.15
                ],
                [
                    "2025-03-25",
                    6.61
                ],
                [
                    "2025-03-26",
                    2.25
                ],
                [
                    "2025-03-27",
                    4.07
                ],
                [
                    "2025-03-28",
                    -4.48
                ],
                [
                    "2025-03-31",
                    -0.98
                ],
                [
                    "2025-04-01",
                    15.71
                ],
                [
                    "2025-04-02",
                    5.66
                ],
                [
                    "2025-04-03",
                    23.95
                ],
                [
                    "2025-04-07",
                    10.96
                ],
                [
                    "2025-04-08",
                    11.01
                ],
                [
                    "2025-04-09",
                    5.98
                ],
                [
                    "2025-04-10",
                    -1.88
                ],
                [
                    "2025-04-11",
                    1.67
                ],
                [
                    "2025-04-14",
                    0.79
                ],
                [
                    "2025-04-17",
                    9.40
                ],
                [
                    "2025-04-22",
                    8.29
                ],
                [
                    "2025-04-28",
                    19.30
                ],
                [
                    "2025-04-29",
                    4.00
                ],
                [
                    "2025-04-30",
                    -17.52
                ],
                [
                    "2025-05-12",
                    12.43
                ],
                [
                    "2025-05-13",
                    21.16
                ],
                [
                    "2025-05-14",
                    13.01
                ],
                [
                    "2025-05-15",
                    -10.76
                ],
                [
                    "2025-05-22",
                    7.70
                ],
                [
                    "2025-05-30",
                    12.09
                ],
                [
                    "2025-06-03",
                    -12.59
                ],
                [
                    "2025-06-06",
                    9.68
                ],
                [
                    "2025-06-09",
                    12.54
                ],
                [
                    "2025-06-10",
                    23.76
                ],
                [
                    "2025-06-11",
                    7.37
                ],
                [
                    "2025-06-12",
                    -13.02
                ],
                [
                    "2025-06-13",
                    7.80
                ],
                [
                    "2025-06-16",
                    -1.26
                ],
                [
                    "2025-06-17",
                    -0.05
                ],
                [
                    "2025-06-19",
                    -0.60
                ],
                [
                    "2025-06-20",
                    23.03
                ],
                [
                    "2025-06-23",
                    3.58
                ],
                [
                    "2025-06-24",
                    -14.59
                ],
                [
                    "2025-06-25",
                    -3.62
                ],
                [
                    "2025-06-26",
                    2.87
                ],
                [
                    "2025-07-01",
                    9.53
                ],
                [
                    "2025-07-02",
                    12.17
                ],
                [
                    "2025-07-03",
                    4.98
                ],
                [
                    "2025-07-04",
                    3.03
                ],
                [
                    "2025-07-07",
                    9.10
                ],
                [
                    "2025-07-08",
                    -9.35
                ],
                [
                    "2025-07-09",
                    -4.04
                ],
                [
                    "2025-07-10",
                    15.26
                ],
                [
                    "2025-07-11",
                    -6.30
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002664 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_0a627d4f0cfe4fe7944a761a8d8df9f1.setOption(option_0a627d4f0cfe4fe7944a761a8d8df9f1);
            window.addEventListener('resize', function(){
                chart_0a627d4f0cfe4fe7944a761a8d8df9f1.resize();
            })
    </script>
</body>
</html>
