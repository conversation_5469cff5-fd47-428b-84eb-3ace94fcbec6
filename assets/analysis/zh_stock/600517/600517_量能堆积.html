<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="32a9dc38d40c4c488268dca0a4e3b9b7" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_32a9dc38d40c4c488268dca0a4e3b9b7 = echarts.init(
            document.getElementById('32a9dc38d40c4c488268dca0a4e3b9b7'), 'white', {renderer: 'canvas'});
        var option_32a9dc38d40c4c488268dca0a4e3b9b7 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    5.19,
                    5.07,
                    5.07,
                    5.23
                ],
                [
                    5.12,
                    5.14,
                    5.08,
                    5.16
                ],
                [
                    5.14,
                    5.15,
                    5.07,
                    5.18
                ],
                [
                    5.15,
                    5.26,
                    5.13,
                    5.32
                ],
                [
                    5.26,
                    5.27,
                    5.24,
                    5.33
                ],
                [
                    5.25,
                    5.16,
                    5.10,
                    5.26
                ],
                [
                    5.13,
                    5.23,
                    5.13,
                    5.24
                ],
                [
                    5.22,
                    5.17,
                    5.16,
                    5.24
                ],
                [
                    5.16,
                    5.20,
                    5.14,
                    5.21
                ],
                [
                    5.21,
                    5.18,
                    5.15,
                    5.24
                ],
                [
                    5.18,
                    5.08,
                    5.04,
                    5.19
                ],
                [
                    5.06,
                    5.11,
                    5.06,
                    5.13
                ],
                [
                    5.11,
                    5.08,
                    5.05,
                    5.12
                ],
                [
                    5.08,
                    5.20,
                    5.06,
                    5.21
                ],
                [
                    5.19,
                    5.13,
                    5.11,
                    5.22
                ],
                [
                    5.09,
                    5.08,
                    5.05,
                    5.14
                ],
                [
                    5.09,
                    5.13,
                    5.06,
                    5.14
                ],
                [
                    5.12,
                    5.07,
                    5.01,
                    5.14
                ],
                [
                    5.04,
                    4.98,
                    4.96,
                    5.10
                ],
                [
                    4.96,
                    5.03,
                    4.96,
                    5.07
                ],
                [
                    5.02,
                    5.05,
                    5.01,
                    5.07
                ],
                [
                    5.04,
                    5.08,
                    4.97,
                    5.09
                ],
                [
                    5.08,
                    5.24,
                    5.06,
                    5.26
                ],
                [
                    5.30,
                    5.11,
                    5.10,
                    5.32
                ],
                [
                    5.04,
                    5.05,
                    5.02,
                    5.10
                ],
                [
                    5.00,
                    5.04,
                    4.98,
                    5.04
                ],
                [
                    5.03,
                    5.06,
                    5.01,
                    5.10
                ],
                [
                    5.03,
                    5.04,
                    5.00,
                    5.07
                ],
                [
                    5.04,
                    5.21,
                    5.03,
                    5.27
                ],
                [
                    5.25,
                    5.24,
                    5.22,
                    5.33
                ],
                [
                    5.24,
                    5.23,
                    5.18,
                    5.29
                ],
                [
                    5.19,
                    5.20,
                    5.17,
                    5.22
                ],
                [
                    5.20,
                    5.18,
                    5.16,
                    5.22
                ],
                [
                    5.17,
                    5.16,
                    5.13,
                    5.26
                ],
                [
                    5.15,
                    5.18,
                    5.09,
                    5.20
                ],
                [
                    5.18,
                    5.20,
                    5.15,
                    5.23
                ],
                [
                    5.18,
                    5.22,
                    5.17,
                    5.23
                ],
                [
                    5.23,
                    5.20,
                    5.15,
                    5.25
                ],
                [
                    5.21,
                    5.21,
                    5.16,
                    5.22
                ],
                [
                    5.20,
                    5.05,
                    5.02,
                    5.20
                ],
                [
                    5.05,
                    5.03,
                    5.01,
                    5.08
                ],
                [
                    5.04,
                    5.09,
                    5.00,
                    5.09
                ],
                [
                    5.06,
                    5.09,
                    5.03,
                    5.13
                ],
                [
                    4.72,
                    4.58,
                    4.58,
                    4.92
                ],
                [
                    4.56,
                    4.60,
                    4.46,
                    4.60
                ],
                [
                    4.56,
                    4.68,
                    4.45,
                    4.69
                ],
                [
                    4.73,
                    4.76,
                    4.70,
                    4.80
                ],
                [
                    4.72,
                    4.66,
                    4.66,
                    4.76
                ],
                [
                    4.72,
                    4.77,
                    4.71,
                    4.80
                ],
                [
                    4.78,
                    4.74,
                    4.72,
                    4.80
                ],
                [
                    4.72,
                    4.70,
                    4.62,
                    4.74
                ],
                [
                    4.70,
                    4.72,
                    4.68,
                    4.75
                ],
                [
                    4.70,
                    4.77,
                    4.69,
                    4.78
                ],
                [
                    4.76,
                    4.79,
                    4.74,
                    4.82
                ],
                [
                    4.79,
                    4.86,
                    4.79,
                    4.88
                ],
                [
                    4.89,
                    4.86,
                    4.85,
                    4.94
                ],
                [
                    4.88,
                    4.86,
                    4.84,
                    4.91
                ],
                [
                    4.90,
                    5.02,
                    4.89,
                    5.04
                ],
                [
                    5.04,
                    4.96,
                    4.94,
                    5.07
                ],
                [
                    4.92,
                    4.92,
                    4.90,
                    5.01
                ],
                [
                    4.92,
                    4.85,
                    4.85,
                    4.96
                ],
                [
                    4.87,
                    4.94,
                    4.86,
                    4.97
                ],
                [
                    5.05,
                    4.95,
                    4.94,
                    5.08
                ],
                [
                    4.93,
                    5.01,
                    4.92,
                    5.05
                ],
                [
                    5.00,
                    4.96,
                    4.94,
                    5.00
                ],
                [
                    4.98,
                    4.99,
                    4.93,
                    5.00
                ],
                [
                    5.02,
                    4.95,
                    4.94,
                    5.03
                ],
                [
                    4.96,
                    5.04,
                    4.90,
                    5.13
                ],
                [
                    5.02,
                    4.94,
                    4.93,
                    5.03
                ],
                [
                    4.94,
                    4.89,
                    4.89,
                    4.96
                ],
                [
                    4.90,
                    4.92,
                    4.87,
                    4.93
                ],
                [
                    4.91,
                    4.93,
                    4.90,
                    4.94
                ],
                [
                    4.94,
                    4.92,
                    4.92,
                    4.95
                ],
                [
                    4.93,
                    4.87,
                    4.86,
                    4.94
                ],
                [
                    4.88,
                    4.79,
                    4.79,
                    4.89
                ],
                [
                    4.79,
                    4.84,
                    4.79,
                    4.85
                ],
                [
                    4.81,
                    4.84,
                    4.77,
                    4.85
                ],
                [
                    4.83,
                    4.85,
                    4.82,
                    4.87
                ],
                [
                    4.86,
                    4.89,
                    4.85,
                    4.90
                ],
                [
                    4.95,
                    4.91,
                    4.88,
                    5.02
                ],
                [
                    4.87,
                    4.90,
                    4.85,
                    4.94
                ],
                [
                    4.90,
                    4.95,
                    4.89,
                    4.95
                ],
                [
                    4.96,
                    4.97,
                    4.95,
                    5.00
                ],
                [
                    4.98,
                    4.96,
                    4.95,
                    4.99
                ],
                [
                    4.96,
                    5.00,
                    4.95,
                    5.01
                ],
                [
                    4.99,
                    4.98,
                    4.93,
                    5.03
                ],
                [
                    4.98,
                    5.00,
                    4.95,
                    5.06
                ],
                [
                    4.99,
                    5.03,
                    4.96,
                    5.04
                ],
                [
                    5.01,
                    4.98,
                    4.96,
                    5.02
                ],
                [
                    4.96,
                    5.02,
                    4.96,
                    5.02
                ],
                [
                    5.04,
                    5.06,
                    5.00,
                    5.07
                ],
                [
                    5.05,
                    5.00,
                    4.98,
                    5.06
                ],
                [
                    4.99,
                    4.93,
                    4.90,
                    5.00
                ],
                [
                    4.93,
                    4.92,
                    4.90,
                    4.95
                ],
                [
                    4.88,
                    4.91,
                    4.88,
                    4.93
                ],
                [
                    4.93,
                    5.03,
                    4.92,
                    5.05
                ],
                [
                    5.05,
                    5.20,
                    5.03,
                    5.22
                ],
                [
                    5.20,
                    5.14,
                    5.12,
                    5.21
                ],
                [
                    5.11,
                    5.13,
                    5.09,
                    5.23
                ],
                [
                    5.14,
                    5.10,
                    5.08,
                    5.16
                ],
                [
                    5.12,
                    5.09,
                    5.05,
                    5.12
                ],
                [
                    5.09,
                    5.10,
                    5.06,
                    5.11
                ],
                [
                    5.07,
                    5.09,
                    5.07,
                    5.13
                ],
                [
                    5.11,
                    5.11,
                    5.08,
                    5.15
                ],
                [
                    5.12,
                    5.18,
                    5.11,
                    5.20
                ],
                [
                    5.17,
                    5.26,
                    5.15,
                    5.30
                ],
                [
                    5.26,
                    5.40,
                    5.25,
                    5.50
                ],
                [
                    5.39,
                    5.46,
                    5.36,
                    5.66
                ],
                [
                    5.45,
                    5.49,
                    5.35,
                    5.58
                ],
                [
                    5.50,
                    5.38,
                    5.36,
                    5.53
                ],
                [
                    5.39,
                    5.34,
                    5.30,
                    5.41
                ],
                [
                    5.34,
                    5.32,
                    5.28,
                    5.36
                ],
                [
                    5.32,
                    5.36,
                    5.31,
                    5.39
                ],
                [
                    5.37,
                    5.35,
                    5.33,
                    5.39
                ],
                [
                    5.39,
                    5.51,
                    5.38,
                    5.53
                ],
                [
                    5.51,
                    5.57,
                    5.43,
                    5.58
                ],
                [
                    5.67,
                    5.49,
                    5.46,
                    5.69
                ],
                [
                    5.43,
                    5.57,
                    5.43,
                    5.59
                ],
                [
                    5.57,
                    5.50,
                    5.48,
                    5.58
                ],
                [
                    5.50,
                    5.46,
                    5.44,
                    5.53
                ],
                [
                    5.45,
                    5.47,
                    5.38,
                    5.47
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600517 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_32a9dc38d40c4c488268dca0a4e3b9b7.setOption(option_32a9dc38d40c4c488268dca0a4e3b9b7);
            window.addEventListener('resize', function(){
                chart_32a9dc38d40c4c488268dca0a4e3b9b7.resize();
            })
    </script>
</body>
</html>
