<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="3c706d96d45648e4b0bafce9327dae90" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_3c706d96d45648e4b0bafce9327dae90 = echarts.init(
            document.getElementById('3c706d96d45648e4b0bafce9327dae90'), 'white', {renderer: 'canvas'});
        var option_3c706d96d45648e4b0bafce9327dae90 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    9.48,
                    9.29,
                    9.28,
                    9.53
                ],
                [
                    9.35,
                    9.33,
                    9.29,
                    9.44
                ],
                [
                    9.54,
                    9.58,
                    9.45,
                    9.92
                ],
                [
                    9.58,
                    9.54,
                    9.44,
                    9.71
                ],
                [
                    9.5,
                    9.39,
                    9.29,
                    9.51
                ],
                [
                    9.38,
                    9.38,
                    9.3,
                    9.46
                ],
                [
                    9.35,
                    9.48,
                    9.32,
                    9.51
                ],
                [
                    9.47,
                    9.27,
                    9.25,
                    9.51
                ],
                [
                    9.26,
                    9.29,
                    9.22,
                    9.37
                ],
                [
                    9.28,
                    9.38,
                    9.26,
                    9.56
                ],
                [
                    9.32,
                    9.12,
                    9.1,
                    9.43
                ],
                [
                    9.1,
                    9.23,
                    9.06,
                    9.34
                ],
                [
                    9.2,
                    9.3,
                    9.14,
                    9.3
                ],
                [
                    9.31,
                    9.1,
                    9.07,
                    9.35
                ],
                [
                    9.11,
                    9.03,
                    8.99,
                    9.15
                ],
                [
                    8.98,
                    8.88,
                    8.86,
                    9.09
                ],
                [
                    8.9,
                    9.0,
                    8.86,
                    9.04
                ],
                [
                    9.04,
                    9.08,
                    8.91,
                    9.1
                ],
                [
                    9.08,
                    8.74,
                    8.71,
                    9.09
                ],
                [
                    8.74,
                    8.74,
                    8.64,
                    8.91
                ],
                [
                    8.64,
                    9.02,
                    8.64,
                    9.06
                ],
                [
                    8.93,
                    9.1,
                    8.93,
                    9.49
                ],
                [
                    9.08,
                    9.05,
                    9.02,
                    9.14
                ],
                [
                    9.01,
                    8.89,
                    8.85,
                    9.03
                ],
                [
                    8.9,
                    9.0,
                    8.88,
                    9.07
                ],
                [
                    8.91,
                    8.96,
                    8.83,
                    8.97
                ],
                [
                    8.98,
                    9.0,
                    8.9,
                    9.1
                ],
                [
                    8.85,
                    8.76,
                    8.64,
                    8.88
                ],
                [
                    8.75,
                    8.96,
                    8.68,
                    8.99
                ],
                [
                    8.85,
                    8.95,
                    8.79,
                    8.98
                ],
                [
                    8.95,
                    9.03,
                    8.91,
                    9.08
                ],
                [
                    8.98,
                    9.19,
                    8.9,
                    9.3
                ],
                [
                    9.13,
                    9.11,
                    9.06,
                    9.21
                ],
                [
                    9.09,
                    8.97,
                    8.9,
                    9.09
                ],
                [
                    8.97,
                    8.6,
                    8.47,
                    9.23
                ],
                [
                    8.6,
                    8.88,
                    8.45,
                    9.26
                ],
                [
                    8.7,
                    8.82,
                    8.63,
                    8.86
                ],
                [
                    8.74,
                    8.84,
                    8.62,
                    8.92
                ],
                [
                    8.79,
                    8.29,
                    8.28,
                    8.82
                ],
                [
                    8.21,
                    8.13,
                    8.07,
                    8.24
                ],
                [
                    8.15,
                    8.19,
                    8.07,
                    8.26
                ],
                [
                    8.32,
                    8.25,
                    8.22,
                    8.45
                ],
                [
                    8.11,
                    8.18,
                    8.08,
                    8.31
                ],
                [
                    7.77,
                    7.36,
                    7.36,
                    7.77
                ],
                [
                    7.22,
                    7.15,
                    7.0,
                    7.41
                ],
                [
                    7.1,
                    7.26,
                    6.67,
                    7.32
                ],
                [
                    7.39,
                    7.53,
                    7.39,
                    7.64
                ],
                [
                    7.44,
                    7.61,
                    7.43,
                    7.69
                ],
                [
                    7.7,
                    7.63,
                    7.59,
                    7.76
                ],
                [
                    7.62,
                    7.65,
                    7.53,
                    7.7
                ],
                [
                    7.6,
                    7.43,
                    7.32,
                    7.61
                ],
                [
                    7.41,
                    7.59,
                    7.39,
                    7.66
                ],
                [
                    7.63,
                    7.59,
                    7.44,
                    7.65
                ],
                [
                    7.61,
                    7.71,
                    7.59,
                    7.73
                ],
                [
                    7.66,
                    7.8,
                    7.66,
                    7.86
                ],
                [
                    7.84,
                    7.78,
                    7.74,
                    7.85
                ],
                [
                    7.79,
                    7.75,
                    7.66,
                    7.81
                ],
                [
                    7.73,
                    7.7,
                    7.68,
                    7.8
                ],
                [
                    7.7,
                    7.56,
                    7.47,
                    7.72
                ],
                [
                    7.55,
                    7.7,
                    7.42,
                    7.76
                ],
                [
                    7.72,
                    7.75,
                    7.71,
                    7.8
                ],
                [
                    7.77,
                    7.88,
                    7.77,
                    7.89
                ],
                [
                    7.98,
                    7.99,
                    7.9,
                    7.99
                ],
                [
                    8.0,
                    8.04,
                    7.99,
                    8.09
                ],
                [
                    8.04,
                    7.92,
                    7.91,
                    8.04
                ],
                [
                    7.99,
                    7.99,
                    7.93,
                    8.04
                ],
                [
                    8.02,
                    7.9,
                    7.88,
                    8.1
                ],
                [
                    7.89,
                    7.93,
                    7.86,
                    7.96
                ],
                [
                    7.93,
                    7.89,
                    7.84,
                    7.95
                ],
                [
                    7.87,
                    7.95,
                    7.85,
                    8.0
                ],
                [
                    7.99,
                    8.04,
                    7.91,
                    8.04
                ],
                [
                    8.03,
                    8.37,
                    8.01,
                    8.38
                ],
                [
                    8.35,
                    8.21,
                    8.15,
                    8.38
                ],
                [
                    8.17,
                    8.13,
                    8.04,
                    8.25
                ],
                [
                    8.1,
                    7.98,
                    7.97,
                    8.17
                ],
                [
                    7.99,
                    8.0,
                    7.87,
                    8.04
                ],
                [
                    7.97,
                    8.03,
                    7.91,
                    8.05
                ],
                [
                    8.03,
                    7.97,
                    7.92,
                    8.05
                ],
                [
                    7.95,
                    8.1,
                    7.95,
                    8.11
                ],
                [
                    8.08,
                    7.94,
                    7.93,
                    8.08
                ],
                [
                    7.82,
                    7.92,
                    7.73,
                    7.94
                ],
                [
                    7.92,
                    8.16,
                    7.92,
                    8.18
                ],
                [
                    8.18,
                    8.05,
                    8.02,
                    8.19
                ],
                [
                    8.04,
                    8.06,
                    7.98,
                    8.19
                ],
                [
                    8.05,
                    8.09,
                    8.04,
                    8.13
                ],
                [
                    8.08,
                    7.98,
                    7.88,
                    8.12
                ],
                [
                    7.98,
                    8.17,
                    7.98,
                    8.41
                ],
                [
                    8.18,
                    8.11,
                    8.04,
                    8.19
                ],
                [
                    8.1,
                    7.86,
                    7.85,
                    8.1
                ],
                [
                    7.83,
                    7.91,
                    7.81,
                    7.97
                ],
                [
                    7.9,
                    7.9,
                    7.85,
                    7.97
                ],
                [
                    7.9,
                    7.81,
                    7.72,
                    7.9
                ],
                [
                    7.79,
                    7.67,
                    7.63,
                    7.87
                ],
                [
                    7.68,
                    7.72,
                    7.6,
                    7.82
                ],
                [
                    7.66,
                    7.88,
                    7.62,
                    7.9
                ],
                [
                    7.93,
                    8.09,
                    7.9,
                    8.1
                ],
                [
                    8.09,
                    8.09,
                    8.03,
                    8.17
                ],
                [
                    8.1,
                    8.1,
                    8.06,
                    8.16
                ],
                [
                    8.11,
                    8.21,
                    8.1,
                    8.27
                ],
                [
                    8.25,
                    8.74,
                    8.25,
                    8.81
                ],
                [
                    8.74,
                    8.83,
                    8.62,
                    8.85
                ],
                [
                    8.76,
                    8.74,
                    8.63,
                    8.8
                ],
                [
                    8.74,
                    8.86,
                    8.69,
                    8.95
                ],
                [
                    8.98,
                    8.7,
                    8.7,
                    9.03
                ],
                [
                    8.67,
                    8.83,
                    8.66,
                    8.84
                ],
                [
                    8.83,
                    8.76,
                    8.71,
                    8.9
                ],
                [
                    8.79,
                    8.71,
                    8.67,
                    8.83
                ],
                [
                    8.71,
                    8.68,
                    8.59,
                    8.71
                ],
                [
                    8.68,
                    8.84,
                    8.65,
                    8.88
                ],
                [
                    8.8,
                    8.87,
                    8.8,
                    9.0
                ],
                [
                    8.87,
                    9.07,
                    8.77,
                    9.07
                ],
                [
                    9.11,
                    9.1,
                    8.92,
                    9.15
                ],
                [
                    9.09,
                    9.05,
                    8.98,
                    9.19
                ],
                [
                    9.05,
                    8.97,
                    8.92,
                    9.07
                ],
                [
                    8.98,
                    9.07,
                    8.93,
                    9.11
                ],
                [
                    9.05,
                    8.99,
                    8.93,
                    9.09
                ],
                [
                    8.96,
                    8.87,
                    8.85,
                    8.99
                ],
                [
                    8.88,
                    9.02,
                    8.88,
                    9.07
                ],
                [
                    9.05,
                    9.21,
                    8.99,
                    9.4
                ],
                [
                    9.24,
                    9.33,
                    9.23,
                    9.39
                ],
                [
                    9.35,
                    9.43,
                    9.28,
                    9.48
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#880000",
                "borderColor0": "#008F28"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5438\u7b79",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 18,
            "data": [
                [
                    "2025-03-05",
                    8.93
                ],
                [
                    "2025-03-10",
                    8.88
                ],
                [
                    "2025-04-22",
                    7.66
                ],
                [
                    "2025-04-24",
                    7.66
                ],
                [
                    "2025-05-08",
                    7.99
                ],
                [
                    "2025-05-14",
                    7.86
                ],
                [
                    "2025-05-22",
                    8.04
                ],
                [
                    "2025-05-28",
                    7.92
                ],
                [
                    "2025-06-27",
                    8.1
                ],
                [
                    "2025-07-01",
                    8.62
                ],
                [
                    "2025-07-02",
                    8.63
                ],
                [
                    "2025-07-03",
                    8.69
                ],
                [
                    "2025-07-14",
                    8.8
                ],
                [
                    "2025-07-16",
                    8.92
                ],
                [
                    "2025-07-22",
                    8.93
                ],
                [
                    "2025-07-25",
                    8.99
                ],
                [
                    "2025-07-28",
                    9.23
                ],
                [
                    "2025-07-29",
                    9.28
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#F4B400"
            }
        },
        {
            "type": "scatter",
            "name": "\u52a0\u4ed3",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4285F4"
            }
        },
        {
            "type": "scatter",
            "name": "\u6d17\u76d8",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "rect",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#16A085"
            }
        },
        {
            "type": "scatter",
            "name": "\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "circle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#DB4437"
            }
        },
        {
            "type": "scatter",
            "name": "\u9690\u5f62\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "pin",
            "symbolSize": 18,
            "data": [
                [
                    "2025-07-10",
                    8.59
                ],
                [
                    "2025-07-21",
                    8.93
                ],
                [
                    "2025-07-24",
                    8.88
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#8E44AD"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5438\u7b79",
                "\u52a0\u4ed3",
                "\u6d17\u76d8",
                "\u51fa\u8d27",
                "\u9690\u5f62\u51fa\u8d27"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10,
            "top": "2%"
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "interval": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 0
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603058 \u4e3b\u529b\u4fe1\u53f7 (\u542b\u6d17\u76d8)",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "90%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_3c706d96d45648e4b0bafce9327dae90.setOption(option_3c706d96d45648e4b0bafce9327dae90);
            window.addEventListener('resize', function(){
                chart_3c706d96d45648e4b0bafce9327dae90.resize();
            })
    </script>
</body>
</html>
