<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="36ae8a59c5804d4f8877652006bcdc11" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_36ae8a59c5804d4f8877652006bcdc11 = echarts.init(
            document.getElementById('36ae8a59c5804d4f8877652006bcdc11'), 'white', {renderer: 'canvas'});
        var option_36ae8a59c5804d4f8877652006bcdc11 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    9.48,
                    9.29,
                    9.28,
                    9.53
                ],
                [
                    9.35,
                    9.33,
                    9.29,
                    9.44
                ],
                [
                    9.54,
                    9.58,
                    9.45,
                    9.92
                ],
                [
                    9.58,
                    9.54,
                    9.44,
                    9.71
                ],
                [
                    9.50,
                    9.39,
                    9.29,
                    9.51
                ],
                [
                    9.38,
                    9.38,
                    9.30,
                    9.46
                ],
                [
                    9.35,
                    9.48,
                    9.32,
                    9.51
                ],
                [
                    9.47,
                    9.27,
                    9.25,
                    9.51
                ],
                [
                    9.26,
                    9.29,
                    9.22,
                    9.37
                ],
                [
                    9.28,
                    9.38,
                    9.26,
                    9.56
                ],
                [
                    9.32,
                    9.12,
                    9.10,
                    9.43
                ],
                [
                    9.10,
                    9.23,
                    9.06,
                    9.34
                ],
                [
                    9.20,
                    9.30,
                    9.14,
                    9.30
                ],
                [
                    9.31,
                    9.10,
                    9.07,
                    9.35
                ],
                [
                    9.11,
                    9.03,
                    8.99,
                    9.15
                ],
                [
                    8.98,
                    8.88,
                    8.86,
                    9.09
                ],
                [
                    8.90,
                    9.00,
                    8.86,
                    9.04
                ],
                [
                    9.04,
                    9.08,
                    8.91,
                    9.10
                ],
                [
                    9.08,
                    8.74,
                    8.71,
                    9.09
                ],
                [
                    8.74,
                    8.74,
                    8.64,
                    8.91
                ],
                [
                    8.64,
                    9.02,
                    8.64,
                    9.06
                ],
                [
                    8.93,
                    9.10,
                    8.93,
                    9.49
                ],
                [
                    9.08,
                    9.05,
                    9.02,
                    9.14
                ],
                [
                    9.01,
                    8.89,
                    8.85,
                    9.03
                ],
                [
                    8.90,
                    9.00,
                    8.88,
                    9.07
                ],
                [
                    8.91,
                    8.96,
                    8.83,
                    8.97
                ],
                [
                    8.98,
                    9.00,
                    8.90,
                    9.10
                ],
                [
                    8.85,
                    8.76,
                    8.64,
                    8.88
                ],
                [
                    8.75,
                    8.96,
                    8.68,
                    8.99
                ],
                [
                    8.85,
                    8.95,
                    8.79,
                    8.98
                ],
                [
                    8.95,
                    9.03,
                    8.91,
                    9.08
                ],
                [
                    8.98,
                    9.19,
                    8.90,
                    9.30
                ],
                [
                    9.13,
                    9.11,
                    9.06,
                    9.21
                ],
                [
                    9.09,
                    8.97,
                    8.90,
                    9.09
                ],
                [
                    8.97,
                    8.60,
                    8.47,
                    9.23
                ],
                [
                    8.60,
                    8.88,
                    8.45,
                    9.26
                ],
                [
                    8.70,
                    8.82,
                    8.63,
                    8.86
                ],
                [
                    8.74,
                    8.84,
                    8.62,
                    8.92
                ],
                [
                    8.79,
                    8.29,
                    8.28,
                    8.82
                ],
                [
                    8.21,
                    8.13,
                    8.07,
                    8.24
                ],
                [
                    8.15,
                    8.19,
                    8.07,
                    8.26
                ],
                [
                    8.32,
                    8.25,
                    8.22,
                    8.45
                ],
                [
                    8.11,
                    8.18,
                    8.08,
                    8.31
                ],
                [
                    7.77,
                    7.36,
                    7.36,
                    7.77
                ],
                [
                    7.22,
                    7.15,
                    7.00,
                    7.41
                ],
                [
                    7.10,
                    7.26,
                    6.67,
                    7.32
                ],
                [
                    7.39,
                    7.53,
                    7.39,
                    7.64
                ],
                [
                    7.44,
                    7.61,
                    7.43,
                    7.69
                ],
                [
                    7.70,
                    7.63,
                    7.59,
                    7.76
                ],
                [
                    7.62,
                    7.65,
                    7.53,
                    7.70
                ],
                [
                    7.60,
                    7.43,
                    7.32,
                    7.61
                ],
                [
                    7.41,
                    7.59,
                    7.39,
                    7.66
                ],
                [
                    7.63,
                    7.59,
                    7.44,
                    7.65
                ],
                [
                    7.61,
                    7.71,
                    7.59,
                    7.73
                ],
                [
                    7.66,
                    7.80,
                    7.66,
                    7.86
                ],
                [
                    7.84,
                    7.78,
                    7.74,
                    7.85
                ],
                [
                    7.79,
                    7.75,
                    7.66,
                    7.81
                ],
                [
                    7.73,
                    7.70,
                    7.68,
                    7.80
                ],
                [
                    7.70,
                    7.56,
                    7.47,
                    7.72
                ],
                [
                    7.55,
                    7.70,
                    7.42,
                    7.76
                ],
                [
                    7.72,
                    7.75,
                    7.71,
                    7.80
                ],
                [
                    7.77,
                    7.88,
                    7.77,
                    7.89
                ],
                [
                    7.98,
                    7.99,
                    7.90,
                    7.99
                ],
                [
                    8.00,
                    8.04,
                    7.99,
                    8.09
                ],
                [
                    8.04,
                    7.92,
                    7.91,
                    8.04
                ],
                [
                    7.99,
                    7.99,
                    7.93,
                    8.04
                ],
                [
                    8.02,
                    7.90,
                    7.88,
                    8.10
                ],
                [
                    7.89,
                    7.93,
                    7.86,
                    7.96
                ],
                [
                    7.93,
                    7.89,
                    7.84,
                    7.95
                ],
                [
                    7.87,
                    7.95,
                    7.85,
                    8.00
                ],
                [
                    7.99,
                    8.04,
                    7.91,
                    8.04
                ],
                [
                    8.03,
                    8.37,
                    8.01,
                    8.38
                ],
                [
                    8.35,
                    8.21,
                    8.15,
                    8.38
                ],
                [
                    8.17,
                    8.13,
                    8.04,
                    8.25
                ],
                [
                    8.10,
                    7.98,
                    7.97,
                    8.17
                ],
                [
                    7.99,
                    8.00,
                    7.87,
                    8.04
                ],
                [
                    7.97,
                    8.03,
                    7.91,
                    8.05
                ],
                [
                    8.03,
                    7.97,
                    7.92,
                    8.05
                ],
                [
                    7.95,
                    8.10,
                    7.95,
                    8.11
                ],
                [
                    8.08,
                    7.94,
                    7.93,
                    8.08
                ],
                [
                    7.82,
                    7.92,
                    7.73,
                    7.94
                ],
                [
                    7.92,
                    8.16,
                    7.92,
                    8.18
                ],
                [
                    8.18,
                    8.05,
                    8.02,
                    8.19
                ],
                [
                    8.04,
                    8.06,
                    7.98,
                    8.19
                ],
                [
                    8.05,
                    8.09,
                    8.04,
                    8.13
                ],
                [
                    8.08,
                    7.98,
                    7.88,
                    8.12
                ],
                [
                    7.98,
                    8.17,
                    7.98,
                    8.41
                ],
                [
                    8.18,
                    8.11,
                    8.04,
                    8.19
                ],
                [
                    8.10,
                    7.86,
                    7.85,
                    8.10
                ],
                [
                    7.83,
                    7.91,
                    7.81,
                    7.97
                ],
                [
                    7.90,
                    7.90,
                    7.85,
                    7.97
                ],
                [
                    7.90,
                    7.81,
                    7.72,
                    7.90
                ],
                [
                    7.79,
                    7.67,
                    7.63,
                    7.87
                ],
                [
                    7.68,
                    7.72,
                    7.60,
                    7.82
                ],
                [
                    7.66,
                    7.88,
                    7.62,
                    7.90
                ],
                [
                    7.93,
                    8.09,
                    7.90,
                    8.10
                ],
                [
                    8.09,
                    8.09,
                    8.03,
                    8.17
                ],
                [
                    8.10,
                    8.10,
                    8.06,
                    8.16
                ],
                [
                    8.11,
                    8.21,
                    8.10,
                    8.27
                ],
                [
                    8.25,
                    8.74,
                    8.25,
                    8.81
                ],
                [
                    8.74,
                    8.83,
                    8.62,
                    8.85
                ],
                [
                    8.76,
                    8.74,
                    8.63,
                    8.80
                ],
                [
                    8.74,
                    8.86,
                    8.69,
                    8.95
                ],
                [
                    8.98,
                    8.70,
                    8.70,
                    9.03
                ],
                [
                    8.67,
                    8.83,
                    8.66,
                    8.84
                ],
                [
                    8.83,
                    8.76,
                    8.71,
                    8.90
                ],
                [
                    8.79,
                    8.71,
                    8.67,
                    8.83
                ],
                [
                    8.71,
                    8.68,
                    8.59,
                    8.71
                ],
                [
                    8.68,
                    8.84,
                    8.65,
                    8.88
                ],
                [
                    8.80,
                    8.87,
                    8.80,
                    9.00
                ],
                [
                    8.87,
                    9.07,
                    8.77,
                    9.07
                ],
                [
                    9.11,
                    9.10,
                    8.92,
                    9.15
                ],
                [
                    9.09,
                    9.05,
                    8.98,
                    9.19
                ],
                [
                    9.05,
                    8.97,
                    8.92,
                    9.07
                ],
                [
                    8.98,
                    9.07,
                    8.93,
                    9.11
                ],
                [
                    9.05,
                    8.99,
                    8.93,
                    9.09
                ],
                [
                    8.96,
                    8.87,
                    8.85,
                    8.99
                ],
                [
                    8.88,
                    9.02,
                    8.88,
                    9.07
                ],
                [
                    9.05,
                    9.21,
                    8.99,
                    9.40
                ],
                [
                    9.24,
                    9.33,
                    9.23,
                    9.39
                ],
                [
                    9.35,
                    9.43,
                    9.28,
                    9.48
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u7a81\u7834",
            "symbol": "pin",
            "symbolSize": 16,
            "data": [
                [
                    "2025-03-25",
                    9.26
                ],
                [
                    "2025-06-30",
                    8.81
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#1f77b4"
            }
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "symbol": "diamond",
            "symbolSize": 16,
            "data": [
                [
                    "2025-03-25",
                    9.26
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u7a81\u7834",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603058 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_36ae8a59c5804d4f8877652006bcdc11.setOption(option_36ae8a59c5804d4f8877652006bcdc11);
            window.addEventListener('resize', function(){
                chart_36ae8a59c5804d4f8877652006bcdc11.resize();
            })
    </script>
</body>
</html>
