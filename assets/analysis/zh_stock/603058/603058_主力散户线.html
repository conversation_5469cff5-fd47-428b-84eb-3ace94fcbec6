<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="c0e09f4cb86443b7b12a6e44f62e8fe6" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_c0e09f4cb86443b7b12a6e44f62e8fe6 = echarts.init(
            document.getElementById('c0e09f4cb86443b7b12a6e44f62e8fe6'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_c0e09f4cb86443b7b12a6e44f62e8fe6 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    9.48,
                    9.29,
                    9.28,
                    9.53
                ],
                [
                    9.35,
                    9.33,
                    9.29,
                    9.44
                ],
                [
                    9.54,
                    9.58,
                    9.45,
                    9.92
                ],
                [
                    9.58,
                    9.54,
                    9.44,
                    9.71
                ],
                [
                    9.5,
                    9.39,
                    9.29,
                    9.51
                ],
                [
                    9.38,
                    9.38,
                    9.3,
                    9.46
                ],
                [
                    9.35,
                    9.48,
                    9.32,
                    9.51
                ],
                [
                    9.47,
                    9.27,
                    9.25,
                    9.51
                ],
                [
                    9.26,
                    9.29,
                    9.22,
                    9.37
                ],
                [
                    9.28,
                    9.38,
                    9.26,
                    9.56
                ],
                [
                    9.32,
                    9.12,
                    9.1,
                    9.43
                ],
                [
                    9.1,
                    9.23,
                    9.06,
                    9.34
                ],
                [
                    9.2,
                    9.3,
                    9.14,
                    9.3
                ],
                [
                    9.31,
                    9.1,
                    9.07,
                    9.35
                ],
                [
                    9.11,
                    9.03,
                    8.99,
                    9.15
                ],
                [
                    8.98,
                    8.88,
                    8.86,
                    9.09
                ],
                [
                    8.9,
                    9.0,
                    8.86,
                    9.04
                ],
                [
                    9.04,
                    9.08,
                    8.91,
                    9.1
                ],
                [
                    9.08,
                    8.74,
                    8.71,
                    9.09
                ],
                [
                    8.74,
                    8.74,
                    8.64,
                    8.91
                ],
                [
                    8.64,
                    9.02,
                    8.64,
                    9.06
                ],
                [
                    8.93,
                    9.1,
                    8.93,
                    9.49
                ],
                [
                    9.08,
                    9.05,
                    9.02,
                    9.14
                ],
                [
                    9.01,
                    8.89,
                    8.85,
                    9.03
                ],
                [
                    8.9,
                    9.0,
                    8.88,
                    9.07
                ],
                [
                    8.91,
                    8.96,
                    8.83,
                    8.97
                ],
                [
                    8.98,
                    9.0,
                    8.9,
                    9.1
                ],
                [
                    8.85,
                    8.76,
                    8.64,
                    8.88
                ],
                [
                    8.75,
                    8.96,
                    8.68,
                    8.99
                ],
                [
                    8.85,
                    8.95,
                    8.79,
                    8.98
                ],
                [
                    8.95,
                    9.03,
                    8.91,
                    9.08
                ],
                [
                    8.98,
                    9.19,
                    8.9,
                    9.3
                ],
                [
                    9.13,
                    9.11,
                    9.06,
                    9.21
                ],
                [
                    9.09,
                    8.97,
                    8.9,
                    9.09
                ],
                [
                    8.97,
                    8.6,
                    8.47,
                    9.23
                ],
                [
                    8.6,
                    8.88,
                    8.45,
                    9.26
                ],
                [
                    8.7,
                    8.82,
                    8.63,
                    8.86
                ],
                [
                    8.74,
                    8.84,
                    8.62,
                    8.92
                ],
                [
                    8.79,
                    8.29,
                    8.28,
                    8.82
                ],
                [
                    8.21,
                    8.13,
                    8.07,
                    8.24
                ],
                [
                    8.15,
                    8.19,
                    8.07,
                    8.26
                ],
                [
                    8.32,
                    8.25,
                    8.22,
                    8.45
                ],
                [
                    8.11,
                    8.18,
                    8.08,
                    8.31
                ],
                [
                    7.77,
                    7.36,
                    7.36,
                    7.77
                ],
                [
                    7.22,
                    7.15,
                    7.0,
                    7.41
                ],
                [
                    7.1,
                    7.26,
                    6.67,
                    7.32
                ],
                [
                    7.39,
                    7.53,
                    7.39,
                    7.64
                ],
                [
                    7.44,
                    7.61,
                    7.43,
                    7.69
                ],
                [
                    7.7,
                    7.63,
                    7.59,
                    7.76
                ],
                [
                    7.62,
                    7.65,
                    7.53,
                    7.7
                ],
                [
                    7.6,
                    7.43,
                    7.32,
                    7.61
                ],
                [
                    7.41,
                    7.59,
                    7.39,
                    7.66
                ],
                [
                    7.63,
                    7.59,
                    7.44,
                    7.65
                ],
                [
                    7.61,
                    7.71,
                    7.59,
                    7.73
                ],
                [
                    7.66,
                    7.8,
                    7.66,
                    7.86
                ],
                [
                    7.84,
                    7.78,
                    7.74,
                    7.85
                ],
                [
                    7.79,
                    7.75,
                    7.66,
                    7.81
                ],
                [
                    7.73,
                    7.7,
                    7.68,
                    7.8
                ],
                [
                    7.7,
                    7.56,
                    7.47,
                    7.72
                ],
                [
                    7.55,
                    7.7,
                    7.42,
                    7.76
                ],
                [
                    7.72,
                    7.75,
                    7.71,
                    7.8
                ],
                [
                    7.77,
                    7.88,
                    7.77,
                    7.89
                ],
                [
                    7.98,
                    7.99,
                    7.9,
                    7.99
                ],
                [
                    8.0,
                    8.04,
                    7.99,
                    8.09
                ],
                [
                    8.04,
                    7.92,
                    7.91,
                    8.04
                ],
                [
                    7.99,
                    7.99,
                    7.93,
                    8.04
                ],
                [
                    8.02,
                    7.9,
                    7.88,
                    8.1
                ],
                [
                    7.89,
                    7.93,
                    7.86,
                    7.96
                ],
                [
                    7.93,
                    7.89,
                    7.84,
                    7.95
                ],
                [
                    7.87,
                    7.95,
                    7.85,
                    8.0
                ],
                [
                    7.99,
                    8.04,
                    7.91,
                    8.04
                ],
                [
                    8.03,
                    8.37,
                    8.01,
                    8.38
                ],
                [
                    8.35,
                    8.21,
                    8.15,
                    8.38
                ],
                [
                    8.17,
                    8.13,
                    8.04,
                    8.25
                ],
                [
                    8.1,
                    7.98,
                    7.97,
                    8.17
                ],
                [
                    7.99,
                    8.0,
                    7.87,
                    8.04
                ],
                [
                    7.97,
                    8.03,
                    7.91,
                    8.05
                ],
                [
                    8.03,
                    7.97,
                    7.92,
                    8.05
                ],
                [
                    7.95,
                    8.1,
                    7.95,
                    8.11
                ],
                [
                    8.08,
                    7.94,
                    7.93,
                    8.08
                ],
                [
                    7.82,
                    7.92,
                    7.73,
                    7.94
                ],
                [
                    7.92,
                    8.16,
                    7.92,
                    8.18
                ],
                [
                    8.18,
                    8.05,
                    8.02,
                    8.19
                ],
                [
                    8.04,
                    8.06,
                    7.98,
                    8.19
                ],
                [
                    8.05,
                    8.09,
                    8.04,
                    8.13
                ],
                [
                    8.08,
                    7.98,
                    7.88,
                    8.12
                ],
                [
                    7.98,
                    8.17,
                    7.98,
                    8.41
                ],
                [
                    8.18,
                    8.11,
                    8.04,
                    8.19
                ],
                [
                    8.1,
                    7.86,
                    7.85,
                    8.1
                ],
                [
                    7.83,
                    7.91,
                    7.81,
                    7.97
                ],
                [
                    7.9,
                    7.9,
                    7.85,
                    7.97
                ],
                [
                    7.9,
                    7.81,
                    7.72,
                    7.9
                ],
                [
                    7.79,
                    7.67,
                    7.63,
                    7.87
                ],
                [
                    7.68,
                    7.72,
                    7.6,
                    7.82
                ],
                [
                    7.66,
                    7.88,
                    7.62,
                    7.9
                ],
                [
                    7.93,
                    8.09,
                    7.9,
                    8.1
                ],
                [
                    8.09,
                    8.09,
                    8.03,
                    8.17
                ],
                [
                    8.1,
                    8.1,
                    8.06,
                    8.16
                ],
                [
                    8.11,
                    8.21,
                    8.1,
                    8.27
                ],
                [
                    8.25,
                    8.74,
                    8.25,
                    8.81
                ],
                [
                    8.74,
                    8.83,
                    8.62,
                    8.85
                ],
                [
                    8.76,
                    8.74,
                    8.63,
                    8.8
                ],
                [
                    8.74,
                    8.86,
                    8.69,
                    8.95
                ],
                [
                    8.98,
                    8.7,
                    8.7,
                    9.03
                ],
                [
                    8.67,
                    8.83,
                    8.66,
                    8.84
                ],
                [
                    8.83,
                    8.76,
                    8.71,
                    8.9
                ],
                [
                    8.79,
                    8.71,
                    8.67,
                    8.83
                ],
                [
                    8.71,
                    8.68,
                    8.59,
                    8.71
                ],
                [
                    8.68,
                    8.84,
                    8.65,
                    8.88
                ],
                [
                    8.8,
                    8.87,
                    8.8,
                    9.0
                ],
                [
                    8.87,
                    9.07,
                    8.77,
                    9.07
                ],
                [
                    9.11,
                    9.1,
                    8.92,
                    9.15
                ],
                [
                    9.09,
                    9.05,
                    8.98,
                    9.19
                ],
                [
                    9.05,
                    8.97,
                    8.92,
                    9.07
                ],
                [
                    8.98,
                    9.07,
                    8.93,
                    9.11
                ],
                [
                    9.05,
                    8.99,
                    8.93,
                    9.09
                ],
                [
                    8.96,
                    8.87,
                    8.85,
                    8.99
                ],
                [
                    8.88,
                    9.02,
                    8.88,
                    9.07
                ],
                [
                    9.05,
                    9.21,
                    8.99,
                    9.4
                ],
                [
                    9.24,
                    9.33,
                    9.23,
                    9.39
                ],
                [
                    9.35,
                    9.43,
                    9.28,
                    9.48
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -22.38
                ],
                [
                    "2025-02-05",
                    -20.72
                ],
                [
                    "2025-02-06",
                    -4.63
                ],
                [
                    "2025-02-07",
                    -9.73
                ],
                [
                    "2025-02-10",
                    -17.92
                ],
                [
                    "2025-02-11",
                    -14.59
                ],
                [
                    "2025-02-12",
                    -10.10
                ],
                [
                    "2025-02-13",
                    -3.30
                ],
                [
                    "2025-02-14",
                    -2.41
                ],
                [
                    "2025-02-17",
                    -0.50
                ],
                [
                    "2025-02-18",
                    -4.70
                ],
                [
                    "2025-02-19",
                    -15.76
                ],
                [
                    "2025-02-20",
                    -17.61
                ],
                [
                    "2025-02-21",
                    -21.33
                ],
                [
                    "2025-02-24",
                    -15.45
                ],
                [
                    "2025-02-25",
                    -29.36
                ],
                [
                    "2025-02-26",
                    -7.17
                ],
                [
                    "2025-02-27",
                    1.48
                ],
                [
                    "2025-02-28",
                    -22.84
                ],
                [
                    "2025-03-03",
                    -28.64
                ],
                [
                    "2025-03-04",
                    12.63
                ],
                [
                    "2025-03-05",
                    3.45
                ],
                [
                    "2025-03-06",
                    -8.54
                ],
                [
                    "2025-03-07",
                    -26.26
                ],
                [
                    "2025-03-10",
                    5.68
                ],
                [
                    "2025-03-11",
                    -11.45
                ],
                [
                    "2025-03-12",
                    1.03
                ],
                [
                    "2025-03-13",
                    -9.88
                ],
                [
                    "2025-03-14",
                    -13.75
                ],
                [
                    "2025-03-17",
                    -6.06
                ],
                [
                    "2025-03-18",
                    -2.49
                ],
                [
                    "2025-03-19",
                    9.00
                ],
                [
                    "2025-03-20",
                    -9.39
                ],
                [
                    "2025-03-21",
                    -3.22
                ],
                [
                    "2025-03-24",
                    -8.16
                ],
                [
                    "2025-03-25",
                    -4.42
                ],
                [
                    "2025-03-26",
                    -5.10
                ],
                [
                    "2025-03-27",
                    -2.12
                ],
                [
                    "2025-03-28",
                    -6.42
                ],
                [
                    "2025-03-31",
                    -15.24
                ],
                [
                    "2025-04-01",
                    -11.61
                ],
                [
                    "2025-04-02",
                    -2.42
                ],
                [
                    "2025-04-03",
                    -13.67
                ],
                [
                    "2025-04-07",
                    -22.83
                ],
                [
                    "2025-04-08",
                    -1.77
                ],
                [
                    "2025-04-09",
                    -14.27
                ],
                [
                    "2025-04-10",
                    -8.39
                ],
                [
                    "2025-04-11",
                    -3.47
                ],
                [
                    "2025-04-14",
                    -13.54
                ],
                [
                    "2025-04-15",
                    -23.71
                ],
                [
                    "2025-04-16",
                    -5.01
                ],
                [
                    "2025-04-17",
                    -11.26
                ],
                [
                    "2025-04-18",
                    1.88
                ],
                [
                    "2025-04-21",
                    -7.52
                ],
                [
                    "2025-04-22",
                    12.34
                ],
                [
                    "2025-04-23",
                    -7.39
                ],
                [
                    "2025-04-24",
                    5.00
                ],
                [
                    "2025-04-25",
                    -19.85
                ],
                [
                    "2025-04-28",
                    -27.33
                ],
                [
                    "2025-04-29",
                    -12.42
                ],
                [
                    "2025-04-30",
                    1.83
                ],
                [
                    "2025-05-06",
                    -0.07
                ],
                [
                    "2025-05-07",
                    -4.87
                ],
                [
                    "2025-05-08",
                    3.33
                ],
                [
                    "2025-05-09",
                    -3.09
                ],
                [
                    "2025-05-12",
                    0.29
                ],
                [
                    "2025-05-13",
                    2.60
                ],
                [
                    "2025-05-14",
                    3.71
                ],
                [
                    "2025-05-15",
                    -7.25
                ],
                [
                    "2025-05-16",
                    -0.93
                ],
                [
                    "2025-05-19",
                    -4.52
                ],
                [
                    "2025-05-20",
                    -4.11
                ],
                [
                    "2025-05-21",
                    -4.47
                ],
                [
                    "2025-05-22",
                    8.50
                ],
                [
                    "2025-05-23",
                    -6.35
                ],
                [
                    "2025-05-26",
                    -16.72
                ],
                [
                    "2025-05-27",
                    -12.26
                ],
                [
                    "2025-05-28",
                    7.75
                ],
                [
                    "2025-05-29",
                    -3.01
                ],
                [
                    "2025-05-30",
                    5.31
                ],
                [
                    "2025-06-03",
                    -2.80
                ],
                [
                    "2025-06-04",
                    -4.17
                ],
                [
                    "2025-06-05",
                    -17.91
                ],
                [
                    "2025-06-06",
                    -14.37
                ],
                [
                    "2025-06-09",
                    -25.02
                ],
                [
                    "2025-06-10",
                    -6.94
                ],
                [
                    "2025-06-11",
                    6.50
                ],
                [
                    "2025-06-12",
                    -4.06
                ],
                [
                    "2025-06-13",
                    -2.47
                ],
                [
                    "2025-06-16",
                    -13.28
                ],
                [
                    "2025-06-17",
                    -0.08
                ],
                [
                    "2025-06-18",
                    -4.58
                ],
                [
                    "2025-06-19",
                    -5.75
                ],
                [
                    "2025-06-20",
                    -10.96
                ],
                [
                    "2025-06-23",
                    -10.26
                ],
                [
                    "2025-06-24",
                    -2.07
                ],
                [
                    "2025-06-25",
                    -14.08
                ],
                [
                    "2025-06-26",
                    -4.72
                ],
                [
                    "2025-06-27",
                    4.04
                ],
                [
                    "2025-06-30",
                    1.10
                ],
                [
                    "2025-07-01",
                    11.50
                ],
                [
                    "2025-07-02",
                    17.16
                ],
                [
                    "2025-07-03",
                    14.45
                ],
                [
                    "2025-07-04",
                    8.57
                ],
                [
                    "2025-07-07",
                    -3.00
                ],
                [
                    "2025-07-08",
                    1.55
                ],
                [
                    "2025-07-09",
                    -9.01
                ],
                [
                    "2025-07-10",
                    -9.87
                ],
                [
                    "2025-07-11",
                    17.11
                ],
                [
                    "2025-07-14",
                    16.71
                ],
                [
                    "2025-07-15",
                    -0.37
                ],
                [
                    "2025-07-16",
                    18.15
                ],
                [
                    "2025-07-17",
                    2.17
                ],
                [
                    "2025-07-18",
                    -1.83
                ],
                [
                    "2025-07-21",
                    -15.26
                ],
                [
                    "2025-07-22",
                    7.27
                ],
                [
                    "2025-07-23",
                    -5.23
                ],
                [
                    "2025-07-24",
                    -3.59
                ],
                [
                    "2025-07-25",
                    8.23
                ],
                [
                    "2025-07-28",
                    8.29
                ],
                [
                    "2025-07-29",
                    7.35
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    4.29
                ],
                [
                    "2025-02-05",
                    -2.05
                ],
                [
                    "2025-02-06",
                    8.03
                ],
                [
                    "2025-02-07",
                    2.55
                ],
                [
                    "2025-02-10",
                    4.17
                ],
                [
                    "2025-02-11",
                    5.47
                ],
                [
                    "2025-02-12",
                    2.36
                ],
                [
                    "2025-02-13",
                    0.79
                ],
                [
                    "2025-02-14",
                    0.49
                ],
                [
                    "2025-02-17",
                    2.84
                ],
                [
                    "2025-02-18",
                    3.89
                ],
                [
                    "2025-02-19",
                    3.77
                ],
                [
                    "2025-02-20",
                    -2.45
                ],
                [
                    "2025-02-21",
                    15.38
                ],
                [
                    "2025-02-24",
                    2.31
                ],
                [
                    "2025-02-25",
                    6.09
                ],
                [
                    "2025-02-26",
                    -0.54
                ],
                [
                    "2025-02-27",
                    -1.89
                ],
                [
                    "2025-02-28",
                    11.18
                ],
                [
                    "2025-03-03",
                    6.28
                ],
                [
                    "2025-03-04",
                    -6.14
                ],
                [
                    "2025-03-05",
                    4.28
                ],
                [
                    "2025-03-06",
                    -0.38
                ],
                [
                    "2025-03-07",
                    14.52
                ],
                [
                    "2025-03-10",
                    -4.47
                ],
                [
                    "2025-03-11",
                    7.16
                ],
                [
                    "2025-03-12",
                    -4.85
                ],
                [
                    "2025-03-13",
                    6.87
                ],
                [
                    "2025-03-14",
                    1.69
                ],
                [
                    "2025-03-17",
                    2.02
                ],
                [
                    "2025-03-18",
                    -6.47
                ],
                [
                    "2025-03-19",
                    -1.64
                ],
                [
                    "2025-03-20",
                    8.23
                ],
                [
                    "2025-03-21",
                    4.21
                ],
                [
                    "2025-03-24",
                    3.06
                ],
                [
                    "2025-03-25",
                    6.20
                ],
                [
                    "2025-03-26",
                    3.69
                ],
                [
                    "2025-03-27",
                    -2.50
                ],
                [
                    "2025-03-28",
                    8.92
                ],
                [
                    "2025-03-31",
                    6.77
                ],
                [
                    "2025-04-01",
                    3.68
                ],
                [
                    "2025-04-02",
                    -1.23
                ],
                [
                    "2025-04-03",
                    9.54
                ],
                [
                    "2025-04-07",
                    8.73
                ],
                [
                    "2025-04-08",
                    2.08
                ],
                [
                    "2025-04-09",
                    1.47
                ],
                [
                    "2025-04-10",
                    9.43
                ],
                [
                    "2025-04-11",
                    -4.47
                ],
                [
                    "2025-04-14",
                    -8.63
                ],
                [
                    "2025-04-15",
                    2.87
                ],
                [
                    "2025-04-16",
                    -3.53
                ],
                [
                    "2025-04-17",
                    -8.70
                ],
                [
                    "2025-04-18",
                    2.41
                ],
                [
                    "2025-04-21",
                    1.05
                ],
                [
                    "2025-04-22",
                    -8.24
                ],
                [
                    "2025-04-23",
                    -0.55
                ],
                [
                    "2025-04-24",
                    -12.72
                ],
                [
                    "2025-04-25",
                    3.12
                ],
                [
                    "2025-04-28",
                    9.92
                ],
                [
                    "2025-04-29",
                    3.27
                ],
                [
                    "2025-04-30",
                    -4.34
                ],
                [
                    "2025-05-06",
                    -9.65
                ],
                [
                    "2025-05-07",
                    0.52
                ],
                [
                    "2025-05-08",
                    -5.18
                ],
                [
                    "2025-05-09",
                    -3.50
                ],
                [
                    "2025-05-12",
                    1.89
                ],
                [
                    "2025-05-13",
                    -3.20
                ],
                [
                    "2025-05-14",
                    2.02
                ],
                [
                    "2025-05-15",
                    -1.78
                ],
                [
                    "2025-05-16",
                    -0.07
                ],
                [
                    "2025-05-19",
                    -8.11
                ],
                [
                    "2025-05-20",
                    11.18
                ],
                [
                    "2025-05-21",
                    10.36
                ],
                [
                    "2025-05-22",
                    -1.06
                ],
                [
                    "2025-05-23",
                    2.64
                ],
                [
                    "2025-05-26",
                    9.06
                ],
                [
                    "2025-05-27",
                    -0.45
                ],
                [
                    "2025-05-28",
                    -3.31
                ],
                [
                    "2025-05-29",
                    2.30
                ],
                [
                    "2025-05-30",
                    3.62
                ],
                [
                    "2025-06-03",
                    3.86
                ],
                [
                    "2025-06-04",
                    3.48
                ],
                [
                    "2025-06-05",
                    3.03
                ],
                [
                    "2025-06-06",
                    7.26
                ],
                [
                    "2025-06-09",
                    0.74
                ],
                [
                    "2025-06-10",
                    -4.34
                ],
                [
                    "2025-06-11",
                    -1.12
                ],
                [
                    "2025-06-12",
                    6.52
                ],
                [
                    "2025-06-13",
                    4.64
                ],
                [
                    "2025-06-16",
                    -0.39
                ],
                [
                    "2025-06-17",
                    1.77
                ],
                [
                    "2025-06-18",
                    4.82
                ],
                [
                    "2025-06-19",
                    -7.33
                ],
                [
                    "2025-06-20",
                    -4.72
                ],
                [
                    "2025-06-23",
                    1.93
                ],
                [
                    "2025-06-24",
                    -7.03
                ],
                [
                    "2025-06-25",
                    8.26
                ],
                [
                    "2025-06-26",
                    -3.27
                ],
                [
                    "2025-06-27",
                    -1.58
                ],
                [
                    "2025-06-30",
                    -0.88
                ],
                [
                    "2025-07-01",
                    -8.08
                ],
                [
                    "2025-07-02",
                    -5.48
                ],
                [
                    "2025-07-03",
                    -1.09
                ],
                [
                    "2025-07-04",
                    -1.60
                ],
                [
                    "2025-07-07",
                    -1.84
                ],
                [
                    "2025-07-08",
                    1.75
                ],
                [
                    "2025-07-09",
                    -0.41
                ],
                [
                    "2025-07-10",
                    4.62
                ],
                [
                    "2025-07-11",
                    -5.79
                ],
                [
                    "2025-07-14",
                    -5.05
                ],
                [
                    "2025-07-15",
                    8.47
                ],
                [
                    "2025-07-16",
                    -6.17
                ],
                [
                    "2025-07-17",
                    -0.14
                ],
                [
                    "2025-07-18",
                    -7.37
                ],
                [
                    "2025-07-21",
                    5.12
                ],
                [
                    "2025-07-22",
                    -0.85
                ],
                [
                    "2025-07-23",
                    -2.15
                ],
                [
                    "2025-07-24",
                    7.12
                ],
                [
                    "2025-07-25",
                    1.57
                ],
                [
                    "2025-07-28",
                    -3.05
                ],
                [
                    "2025-07-29",
                    -2.18
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    18.09
                ],
                [
                    "2025-02-05",
                    22.77
                ],
                [
                    "2025-02-06",
                    -3.40
                ],
                [
                    "2025-02-07",
                    7.18
                ],
                [
                    "2025-02-10",
                    13.75
                ],
                [
                    "2025-02-11",
                    9.11
                ],
                [
                    "2025-02-12",
                    7.75
                ],
                [
                    "2025-02-13",
                    2.51
                ],
                [
                    "2025-02-14",
                    1.92
                ],
                [
                    "2025-02-17",
                    -2.34
                ],
                [
                    "2025-02-18",
                    0.81
                ],
                [
                    "2025-02-19",
                    11.99
                ],
                [
                    "2025-02-20",
                    20.07
                ],
                [
                    "2025-02-21",
                    5.95
                ],
                [
                    "2025-02-24",
                    13.14
                ],
                [
                    "2025-02-25",
                    23.27
                ],
                [
                    "2025-02-26",
                    7.71
                ],
                [
                    "2025-02-27",
                    0.41
                ],
                [
                    "2025-02-28",
                    11.66
                ],
                [
                    "2025-03-03",
                    22.36
                ],
                [
                    "2025-03-04",
                    -6.49
                ],
                [
                    "2025-03-05",
                    -7.73
                ],
                [
                    "2025-03-06",
                    8.92
                ],
                [
                    "2025-03-07",
                    11.73
                ],
                [
                    "2025-03-10",
                    -1.22
                ],
                [
                    "2025-03-11",
                    4.30
                ],
                [
                    "2025-03-12",
                    3.82
                ],
                [
                    "2025-03-13",
                    3.02
                ],
                [
                    "2025-03-14",
                    12.06
                ],
                [
                    "2025-03-17",
                    4.04
                ],
                [
                    "2025-03-18",
                    8.97
                ],
                [
                    "2025-03-19",
                    -7.37
                ],
                [
                    "2025-03-20",
                    1.16
                ],
                [
                    "2025-03-21",
                    -0.99
                ],
                [
                    "2025-03-24",
                    5.10
                ],
                [
                    "2025-03-25",
                    -1.78
                ],
                [
                    "2025-03-26",
                    1.41
                ],
                [
                    "2025-03-27",
                    4.63
                ],
                [
                    "2025-03-28",
                    -2.50
                ],
                [
                    "2025-03-31",
                    8.47
                ],
                [
                    "2025-04-01",
                    7.94
                ],
                [
                    "2025-04-02",
                    3.64
                ],
                [
                    "2025-04-03",
                    4.13
                ],
                [
                    "2025-04-07",
                    14.10
                ],
                [
                    "2025-04-08",
                    -0.31
                ],
                [
                    "2025-04-09",
                    12.80
                ],
                [
                    "2025-04-10",
                    -1.04
                ],
                [
                    "2025-04-11",
                    7.94
                ],
                [
                    "2025-04-14",
                    22.17
                ],
                [
                    "2025-04-15",
                    20.83
                ],
                [
                    "2025-04-16",
                    8.54
                ],
                [
                    "2025-04-17",
                    19.96
                ],
                [
                    "2025-04-18",
                    -4.29
                ],
                [
                    "2025-04-21",
                    6.46
                ],
                [
                    "2025-04-22",
                    -4.10
                ],
                [
                    "2025-04-23",
                    7.95
                ],
                [
                    "2025-04-24",
                    7.73
                ],
                [
                    "2025-04-25",
                    16.74
                ],
                [
                    "2025-04-28",
                    17.41
                ],
                [
                    "2025-04-29",
                    9.15
                ],
                [
                    "2025-04-30",
                    2.51
                ],
                [
                    "2025-05-06",
                    9.71
                ],
                [
                    "2025-05-07",
                    4.35
                ],
                [
                    "2025-05-08",
                    1.84
                ],
                [
                    "2025-05-09",
                    6.59
                ],
                [
                    "2025-05-12",
                    -2.19
                ],
                [
                    "2025-05-13",
                    0.60
                ],
                [
                    "2025-05-14",
                    -5.73
                ],
                [
                    "2025-05-15",
                    9.03
                ],
                [
                    "2025-05-16",
                    0.99
                ],
                [
                    "2025-05-19",
                    12.63
                ],
                [
                    "2025-05-20",
                    -7.06
                ],
                [
                    "2025-05-21",
                    -5.89
                ],
                [
                    "2025-05-22",
                    -7.44
                ],
                [
                    "2025-05-23",
                    3.71
                ],
                [
                    "2025-05-26",
                    7.65
                ],
                [
                    "2025-05-27",
                    12.70
                ],
                [
                    "2025-05-28",
                    -4.44
                ],
                [
                    "2025-05-29",
                    0.71
                ],
                [
                    "2025-05-30",
                    -8.93
                ],
                [
                    "2025-06-03",
                    -1.06
                ],
                [
                    "2025-06-04",
                    0.69
                ],
                [
                    "2025-06-05",
                    14.89
                ],
                [
                    "2025-06-06",
                    7.11
                ],
                [
                    "2025-06-09",
                    24.28
                ],
                [
                    "2025-06-10",
                    11.28
                ],
                [
                    "2025-06-11",
                    -5.39
                ],
                [
                    "2025-06-12",
                    -2.46
                ],
                [
                    "2025-06-13",
                    -2.17
                ],
                [
                    "2025-06-16",
                    13.67
                ],
                [
                    "2025-06-17",
                    -1.69
                ],
                [
                    "2025-06-18",
                    -0.24
                ],
                [
                    "2025-06-19",
                    13.08
                ],
                [
                    "2025-06-20",
                    15.68
                ],
                [
                    "2025-06-23",
                    8.33
                ],
                [
                    "2025-06-24",
                    9.10
                ],
                [
                    "2025-06-25",
                    5.83
                ],
                [
                    "2025-06-26",
                    7.99
                ],
                [
                    "2025-06-27",
                    -2.47
                ],
                [
                    "2025-06-30",
                    -0.23
                ],
                [
                    "2025-07-01",
                    -3.42
                ],
                [
                    "2025-07-02",
                    -11.68
                ],
                [
                    "2025-07-03",
                    -13.35
                ],
                [
                    "2025-07-04",
                    -6.97
                ],
                [
                    "2025-07-07",
                    4.84
                ],
                [
                    "2025-07-08",
                    -3.30
                ],
                [
                    "2025-07-09",
                    9.42
                ],
                [
                    "2025-07-10",
                    5.25
                ],
                [
                    "2025-07-11",
                    -11.32
                ],
                [
                    "2025-07-14",
                    -11.66
                ],
                [
                    "2025-07-15",
                    -8.10
                ],
                [
                    "2025-07-16",
                    -11.98
                ],
                [
                    "2025-07-17",
                    -2.03
                ],
                [
                    "2025-07-18",
                    9.19
                ],
                [
                    "2025-07-21",
                    10.14
                ],
                [
                    "2025-07-22",
                    -6.42
                ],
                [
                    "2025-07-23",
                    7.38
                ],
                [
                    "2025-07-24",
                    -3.53
                ],
                [
                    "2025-07-25",
                    -9.80
                ],
                [
                    "2025-07-28",
                    -5.25
                ],
                [
                    "2025-07-29",
                    -5.17
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-06-04",
                    -4.17
                ],
                [
                    "2025-07-02",
                    17.16
                ],
                [
                    "2025-07-03",
                    14.45
                ],
                [
                    "2025-07-04",
                    8.57
                ],
                [
                    "2025-07-07",
                    -3.00
                ],
                [
                    "2025-07-08",
                    1.55
                ],
                [
                    "2025-07-09",
                    -9.01
                ],
                [
                    "2025-07-14",
                    16.71
                ],
                [
                    "2025-07-15",
                    -0.37
                ],
                [
                    "2025-07-16",
                    18.15
                ],
                [
                    "2025-07-17",
                    2.17
                ],
                [
                    "2025-07-18",
                    -1.83
                ],
                [
                    "2025-07-21",
                    -15.26
                ],
                [
                    "2025-07-22",
                    7.27
                ],
                [
                    "2025-07-28",
                    8.29
                ],
                [
                    "2025-07-29",
                    7.35
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-25",
                    6.20
                ],
                [
                    "2025-05-22",
                    -1.06
                ],
                [
                    "2025-05-23",
                    2.64
                ],
                [
                    "2025-05-26",
                    9.06
                ],
                [
                    "2025-06-03",
                    3.86
                ],
                [
                    "2025-06-04",
                    3.48
                ],
                [
                    "2025-07-15",
                    8.47
                ],
                [
                    "2025-07-25",
                    1.57
                ],
                [
                    "2025-07-28",
                    -3.05
                ],
                [
                    "2025-07-29",
                    -2.18
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    13.75
                ],
                [
                    "2025-02-11",
                    9.11
                ],
                [
                    "2025-02-12",
                    7.75
                ],
                [
                    "2025-02-13",
                    2.51
                ],
                [
                    "2025-02-14",
                    1.92
                ],
                [
                    "2025-02-17",
                    -2.34
                ],
                [
                    "2025-02-18",
                    0.81
                ],
                [
                    "2025-02-19",
                    11.99
                ],
                [
                    "2025-02-20",
                    20.07
                ],
                [
                    "2025-02-21",
                    5.95
                ],
                [
                    "2025-02-24",
                    13.14
                ],
                [
                    "2025-02-25",
                    23.27
                ],
                [
                    "2025-02-26",
                    7.71
                ],
                [
                    "2025-02-27",
                    0.41
                ],
                [
                    "2025-02-28",
                    11.66
                ],
                [
                    "2025-03-03",
                    22.36
                ],
                [
                    "2025-03-04",
                    -6.49
                ],
                [
                    "2025-03-05",
                    -7.73
                ],
                [
                    "2025-03-06",
                    8.92
                ],
                [
                    "2025-03-07",
                    11.73
                ],
                [
                    "2025-03-10",
                    -1.22
                ],
                [
                    "2025-03-11",
                    4.30
                ],
                [
                    "2025-03-12",
                    3.82
                ],
                [
                    "2025-03-13",
                    3.02
                ],
                [
                    "2025-03-14",
                    12.06
                ],
                [
                    "2025-03-17",
                    4.04
                ],
                [
                    "2025-03-18",
                    8.97
                ],
                [
                    "2025-03-19",
                    -7.37
                ],
                [
                    "2025-03-20",
                    1.16
                ],
                [
                    "2025-03-21",
                    -0.99
                ],
                [
                    "2025-03-24",
                    5.10
                ],
                [
                    "2025-03-26",
                    1.41
                ],
                [
                    "2025-03-27",
                    4.63
                ],
                [
                    "2025-03-28",
                    -2.50
                ],
                [
                    "2025-03-31",
                    8.47
                ],
                [
                    "2025-04-01",
                    7.94
                ],
                [
                    "2025-04-02",
                    3.64
                ],
                [
                    "2025-04-03",
                    4.13
                ],
                [
                    "2025-04-07",
                    14.10
                ],
                [
                    "2025-04-08",
                    -0.31
                ],
                [
                    "2025-04-09",
                    12.80
                ],
                [
                    "2025-04-10",
                    -1.04
                ],
                [
                    "2025-04-11",
                    7.94
                ],
                [
                    "2025-04-14",
                    22.17
                ],
                [
                    "2025-04-15",
                    20.83
                ],
                [
                    "2025-04-16",
                    8.54
                ],
                [
                    "2025-04-17",
                    19.96
                ],
                [
                    "2025-04-18",
                    -4.29
                ],
                [
                    "2025-04-21",
                    6.46
                ],
                [
                    "2025-04-22",
                    -4.10
                ],
                [
                    "2025-04-23",
                    7.95
                ],
                [
                    "2025-04-25",
                    16.74
                ],
                [
                    "2025-04-28",
                    17.41
                ],
                [
                    "2025-04-29",
                    9.15
                ],
                [
                    "2025-04-30",
                    2.51
                ],
                [
                    "2025-05-06",
                    9.71
                ],
                [
                    "2025-05-07",
                    4.35
                ],
                [
                    "2025-05-08",
                    1.84
                ],
                [
                    "2025-05-09",
                    6.59
                ],
                [
                    "2025-05-12",
                    -2.19
                ],
                [
                    "2025-05-13",
                    0.60
                ],
                [
                    "2025-05-15",
                    9.03
                ],
                [
                    "2025-05-16",
                    0.99
                ],
                [
                    "2025-05-19",
                    12.63
                ],
                [
                    "2025-05-20",
                    -7.06
                ],
                [
                    "2025-05-21",
                    -5.89
                ],
                [
                    "2025-05-27",
                    12.70
                ],
                [
                    "2025-05-28",
                    -4.44
                ],
                [
                    "2025-05-29",
                    0.71
                ],
                [
                    "2025-05-30",
                    -8.93
                ],
                [
                    "2025-06-05",
                    14.89
                ],
                [
                    "2025-06-06",
                    7.11
                ],
                [
                    "2025-06-09",
                    24.28
                ],
                [
                    "2025-06-10",
                    11.28
                ],
                [
                    "2025-06-11",
                    -5.39
                ],
                [
                    "2025-06-12",
                    -2.46
                ],
                [
                    "2025-06-13",
                    -2.17
                ],
                [
                    "2025-06-16",
                    13.67
                ],
                [
                    "2025-06-17",
                    -1.69
                ],
                [
                    "2025-06-18",
                    -0.24
                ],
                [
                    "2025-06-19",
                    13.08
                ],
                [
                    "2025-06-20",
                    15.68
                ],
                [
                    "2025-06-23",
                    8.33
                ],
                [
                    "2025-06-24",
                    9.10
                ],
                [
                    "2025-06-25",
                    5.83
                ],
                [
                    "2025-06-26",
                    7.99
                ],
                [
                    "2025-06-27",
                    -2.47
                ],
                [
                    "2025-06-30",
                    -0.23
                ],
                [
                    "2025-07-01",
                    -3.42
                ],
                [
                    "2025-07-10",
                    5.25
                ],
                [
                    "2025-07-11",
                    -11.32
                ],
                [
                    "2025-07-23",
                    7.38
                ],
                [
                    "2025-07-24",
                    -3.53
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603058 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_c0e09f4cb86443b7b12a6e44f62e8fe6.setOption(option_c0e09f4cb86443b7b12a6e44f62e8fe6);
            window.addEventListener('resize', function(){
                chart_c0e09f4cb86443b7b12a6e44f62e8fe6.resize();
            })
    </script>
</body>
</html>
