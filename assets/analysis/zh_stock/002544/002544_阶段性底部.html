<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="95766e6e72024ea992642e794174c5ec" class="chart-container" style="width:100%; height:500px; "></div>
    <script>
        var chart_95766e6e72024ea992642e794174c5ec = echarts.init(
            document.getElementById('95766e6e72024ea992642e794174c5ec'), 'white', {renderer: 'canvas'});
        var option_95766e6e72024ea992642e794174c5ec = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    20.2,
                    19.64,
                    19.48,
                    20.4
                ],
                [
                    19.98,
                    19.84,
                    19.7,
                    20.25
                ],
                [
                    19.73,
                    20.3,
                    19.46,
                    20.36
                ],
                [
                    20.46,
                    20.58,
                    20.18,
                    20.99
                ],
                [
                    20.64,
                    20.94,
                    20.59,
                    20.99
                ],
                [
                    20.94,
                    21.0,
                    20.4,
                    21.26
                ],
                [
                    21.13,
                    21.2,
                    20.92,
                    21.27
                ],
                [
                    21.26,
                    21.21,
                    20.85,
                    21.47
                ],
                [
                    21.02,
                    21.24,
                    20.86,
                    21.35
                ],
                [
                    21.24,
                    21.01,
                    20.88,
                    21.42
                ],
                [
                    20.97,
                    20.26,
                    20.16,
                    21.05
                ],
                [
                    20.27,
                    20.59,
                    20.15,
                    20.7
                ],
                [
                    20.6,
                    20.71,
                    20.23,
                    20.72
                ],
                [
                    20.71,
                    21.29,
                    20.51,
                    21.33
                ],
                [
                    21.66,
                    22.32,
                    21.1,
                    22.71
                ],
                [
                    21.91,
                    22.69,
                    21.7,
                    23.55
                ],
                [
                    22.8,
                    22.5,
                    22.46,
                    23.03
                ],
                [
                    22.55,
                    22.0,
                    21.73,
                    22.73
                ],
                [
                    21.9,
                    21.09,
                    21.0,
                    21.91
                ],
                [
                    21.11,
                    21.23,
                    20.93,
                    21.73
                ],
                [
                    21.0,
                    21.63,
                    21.0,
                    21.71
                ],
                [
                    21.68,
                    22.15,
                    21.61,
                    22.52
                ],
                [
                    22.16,
                    22.3,
                    22.1,
                    22.63
                ],
                [
                    22.24,
                    22.3,
                    22.12,
                    22.8
                ],
                [
                    22.17,
                    22.22,
                    21.95,
                    22.66
                ],
                [
                    22.03,
                    23.07,
                    22.0,
                    23.2
                ],
                [
                    23.11,
                    22.62,
                    22.55,
                    23.28
                ],
                [
                    22.67,
                    22.67,
                    22.13,
                    22.76
                ],
                [
                    22.75,
                    22.68,
                    22.08,
                    22.87
                ],
                [
                    22.5,
                    22.28,
                    22.26,
                    22.78
                ],
                [
                    22.21,
                    22.09,
                    21.97,
                    22.48
                ],
                [
                    22.19,
                    21.96,
                    21.7,
                    22.19
                ],
                [
                    21.85,
                    24.16,
                    21.8,
                    24.16
                ],
                [
                    24.42,
                    24.14,
                    23.53,
                    24.48
                ],
                [
                    25.4,
                    24.26,
                    23.95,
                    25.5
                ],
                [
                    24.25,
                    23.32,
                    23.0,
                    24.47
                ],
                [
                    23.22,
                    22.83,
                    22.76,
                    23.49
                ],
                [
                    22.7,
                    22.04,
                    21.9,
                    22.9
                ],
                [
                    22.04,
                    21.87,
                    21.75,
                    22.9
                ],
                [
                    21.4,
                    21.78,
                    21.26,
                    21.89
                ],
                [
                    21.85,
                    21.64,
                    21.52,
                    22.11
                ],
                [
                    21.6,
                    21.96,
                    21.58,
                    22.14
                ],
                [
                    21.79,
                    21.76,
                    21.58,
                    22.21
                ],
                [
                    19.71,
                    19.58,
                    19.58,
                    20.88
                ],
                [
                    19.58,
                    19.66,
                    19.05,
                    20.04
                ],
                [
                    19.5,
                    20.28,
                    18.89,
                    20.4
                ],
                [
                    20.5,
                    20.3,
                    20.2,
                    20.98
                ],
                [
                    20.2,
                    20.37,
                    20.1,
                    20.8
                ],
                [
                    20.6,
                    20.63,
                    20.4,
                    20.99
                ],
                [
                    20.56,
                    20.18,
                    20.12,
                    20.67
                ],
                [
                    20.17,
                    19.89,
                    19.58,
                    20.24
                ],
                [
                    19.88,
                    19.98,
                    19.71,
                    20.28
                ],
                [
                    20.06,
                    19.95,
                    19.63,
                    20.07
                ],
                [
                    19.96,
                    20.6,
                    19.85,
                    20.84
                ],
                [
                    20.6,
                    20.59,
                    20.41,
                    20.85
                ],
                [
                    20.63,
                    20.66,
                    20.56,
                    20.98
                ],
                [
                    20.71,
                    20.21,
                    20.06,
                    20.82
                ],
                [
                    20.15,
                    20.4,
                    20.14,
                    20.66
                ],
                [
                    20.35,
                    19.98,
                    19.84,
                    20.49
                ],
                [
                    20.17,
                    20.35,
                    19.89,
                    20.41
                ],
                [
                    20.3,
                    20.56,
                    20.29,
                    20.94
                ],
                [
                    20.68,
                    22.62,
                    20.67,
                    22.62
                ],
                [
                    23.0,
                    23.03,
                    22.65,
                    23.82
                ],
                [
                    23.05,
                    23.48,
                    22.8,
                    23.68
                ],
                [
                    24.8,
                    23.6,
                    22.89,
                    25.0
                ],
                [
                    23.81,
                    25.06,
                    23.7,
                    25.79
                ],
                [
                    24.82,
                    23.86,
                    23.77,
                    25.14
                ],
                [
                    24.0,
                    23.32,
                    22.78,
                    24.08
                ],
                [
                    24.0,
                    22.58,
                    22.39,
                    24.24
                ],
                [
                    22.6,
                    22.75,
                    22.4,
                    23.35
                ],
                [
                    22.72,
                    22.29,
                    22.18,
                    22.89
                ],
                [
                    22.31,
                    22.2,
                    22.1,
                    22.47
                ],
                [
                    22.27,
                    22.09,
                    22.0,
                    22.67
                ],
                [
                    22.1,
                    22.11,
                    21.82,
                    22.37
                ],
                [
                    22.18,
                    21.6,
                    21.59,
                    22.45
                ],
                [
                    21.61,
                    22.09,
                    21.6,
                    22.16
                ],
                [
                    22.04,
                    22.13,
                    21.88,
                    22.33
                ],
                [
                    22.26,
                    22.13,
                    22.05,
                    22.41
                ],
                [
                    22.09,
                    22.01,
                    21.91,
                    22.43
                ],
                [
                    22.13,
                    22.45,
                    21.81,
                    22.86
                ],
                [
                    22.5,
                    22.57,
                    22.35,
                    23.07
                ],
                [
                    22.49,
                    22.41,
                    22.28,
                    22.86
                ],
                [
                    22.46,
                    23.41,
                    22.34,
                    23.58
                ],
                [
                    23.41,
                    23.25,
                    23.16,
                    23.89
                ],
                [
                    23.6,
                    23.35,
                    23.11,
                    23.9
                ],
                [
                    23.35,
                    22.95,
                    22.74,
                    23.65
                ],
                [
                    22.83,
                    22.75,
                    22.48,
                    23.05
                ],
                [
                    22.58,
                    22.55,
                    22.47,
                    22.74
                ],
                [
                    22.55,
                    22.54,
                    22.29,
                    22.62
                ],
                [
                    22.44,
                    22.68,
                    22.43,
                    22.77
                ],
                [
                    22.66,
                    22.65,
                    22.35,
                    22.74
                ],
                [
                    22.5,
                    22.43,
                    22.18,
                    22.61
                ],
                [
                    22.43,
                    22.07,
                    21.85,
                    22.53
                ],
                [
                    22.0,
                    21.41,
                    21.24,
                    22.0
                ],
                [
                    21.37,
                    21.74,
                    21.2,
                    21.95
                ],
                [
                    21.75,
                    22.33,
                    21.66,
                    22.35
                ],
                [
                    22.33,
                    22.75,
                    22.3,
                    22.77
                ],
                [
                    22.95,
                    23.34,
                    22.95,
                    24.18
                ],
                [
                    23.28,
                    23.38,
                    23.22,
                    23.48
                ],
                [
                    23.68,
                    23.45,
                    23.37,
                    23.88
                ],
                [
                    23.65,
                    23.1,
                    22.95,
                    23.7
                ],
                [
                    23.0,
                    23.0,
                    22.64,
                    23.29
                ],
                [
                    22.9,
                    23.0,
                    22.8,
                    23.6
                ],
                [
                    22.94,
                    22.91,
                    22.75,
                    23.27
                ],
                [
                    22.83,
                    22.77,
                    22.57,
                    22.93
                ],
                [
                    22.84,
                    23.0,
                    22.72,
                    23.12
                ],
                [
                    23.19,
                    22.7,
                    22.65,
                    23.19
                ],
                [
                    22.61,
                    22.55,
                    22.35,
                    22.69
                ],
                [
                    22.59,
                    22.66,
                    22.38,
                    22.82
                ],
                [
                    22.56,
                    22.4,
                    22.33,
                    22.61
                ],
                [
                    22.21,
                    22.11,
                    21.98,
                    22.46
                ],
                [
                    22.16,
                    22.18,
                    22.09,
                    22.46
                ],
                [
                    22.29,
                    23.13,
                    22.22,
                    23.25
                ],
                [
                    23.13,
                    22.92,
                    22.81,
                    23.13
                ],
                [
                    22.81,
                    22.88,
                    22.5,
                    23.02
                ],
                [
                    22.74,
                    22.57,
                    22.4,
                    23.1
                ],
                [
                    22.56,
                    22.37,
                    22.27,
                    22.62
                ],
                [
                    22.38,
                    22.54,
                    22.32,
                    22.57
                ],
                [
                    22.53,
                    22.31,
                    22.18,
                    22.56
                ],
                [
                    22.33,
                    23.01,
                    22.17,
                    23.4
                ],
                [
                    23.31,
                    23.04,
                    22.83,
                    23.67
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u9636\u6bb5\u6027\u5e95\u90e8",
            "symbol": "triangle",
            "symbolSize": 20,
            "data": [
                [
                    "2025-03-18",
                    21.53
                ],
                [
                    "2025-03-19",
                    21.27
                ],
                [
                    "2025-04-28",
                    19.44
                ],
                [
                    "2025-07-09",
                    22.2
                ],
                [
                    "2025-07-10",
                    21.9
                ],
                [
                    "2025-07-11",
                    21.93
                ],
                [
                    "2025-07-14",
                    21.88
                ],
                [
                    "2025-07-15",
                    21.54
                ],
                [
                    "2025-07-16",
                    21.65
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#FF6B35",
                "borderColor": "#CC5529",
                "borderWidth": 2,
                "opacity": 0.9
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf",
                "\u9636\u6bb5\u6027\u5e95\u90e8"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002544 \u9636\u6bb5\u6027\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_95766e6e72024ea992642e794174c5ec.setOption(option_95766e6e72024ea992642e794174c5ec);
            window.addEventListener('resize', function(){
                chart_95766e6e72024ea992642e794174c5ec.resize();
            })
    </script>
</body>
</html>
