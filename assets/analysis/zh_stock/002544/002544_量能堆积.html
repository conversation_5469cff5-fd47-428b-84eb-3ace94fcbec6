<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="0dc6ffa7c3084fe48018e4aa4f4c6137" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_0dc6ffa7c3084fe48018e4aa4f4c6137 = echarts.init(
            document.getElementById('0dc6ffa7c3084fe48018e4aa4f4c6137'), 'white', {renderer: 'canvas'});
        var option_0dc6ffa7c3084fe48018e4aa4f4c6137 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    20.20,
                    19.64,
                    19.48,
                    20.40
                ],
                [
                    19.98,
                    19.84,
                    19.70,
                    20.25
                ],
                [
                    19.73,
                    20.30,
                    19.46,
                    20.36
                ],
                [
                    20.46,
                    20.58,
                    20.18,
                    20.99
                ],
                [
                    20.64,
                    20.94,
                    20.59,
                    20.99
                ],
                [
                    20.94,
                    21.00,
                    20.40,
                    21.26
                ],
                [
                    21.13,
                    21.20,
                    20.92,
                    21.27
                ],
                [
                    21.26,
                    21.21,
                    20.85,
                    21.47
                ],
                [
                    21.02,
                    21.24,
                    20.86,
                    21.35
                ],
                [
                    21.24,
                    21.01,
                    20.88,
                    21.42
                ],
                [
                    20.97,
                    20.26,
                    20.16,
                    21.05
                ],
                [
                    20.27,
                    20.59,
                    20.15,
                    20.70
                ],
                [
                    20.60,
                    20.71,
                    20.23,
                    20.72
                ],
                [
                    20.71,
                    21.29,
                    20.51,
                    21.33
                ],
                [
                    21.66,
                    22.32,
                    21.10,
                    22.71
                ],
                [
                    21.91,
                    22.69,
                    21.70,
                    23.55
                ],
                [
                    22.80,
                    22.50,
                    22.46,
                    23.03
                ],
                [
                    22.55,
                    22.00,
                    21.73,
                    22.73
                ],
                [
                    21.90,
                    21.09,
                    21.00,
                    21.91
                ],
                [
                    21.11,
                    21.23,
                    20.93,
                    21.73
                ],
                [
                    21.00,
                    21.63,
                    21.00,
                    21.71
                ],
                [
                    21.68,
                    22.15,
                    21.61,
                    22.52
                ],
                [
                    22.16,
                    22.30,
                    22.10,
                    22.63
                ],
                [
                    22.24,
                    22.30,
                    22.12,
                    22.80
                ],
                [
                    22.17,
                    22.22,
                    21.95,
                    22.66
                ],
                [
                    22.03,
                    23.07,
                    22.00,
                    23.20
                ],
                [
                    23.11,
                    22.62,
                    22.55,
                    23.28
                ],
                [
                    22.67,
                    22.67,
                    22.13,
                    22.76
                ],
                [
                    22.75,
                    22.68,
                    22.08,
                    22.87
                ],
                [
                    22.50,
                    22.28,
                    22.26,
                    22.78
                ],
                [
                    22.21,
                    22.09,
                    21.97,
                    22.48
                ],
                [
                    22.19,
                    21.96,
                    21.70,
                    22.19
                ],
                [
                    21.85,
                    24.16,
                    21.80,
                    24.16
                ],
                [
                    24.42,
                    24.14,
                    23.53,
                    24.48
                ],
                [
                    25.40,
                    24.26,
                    23.95,
                    25.50
                ],
                [
                    24.25,
                    23.32,
                    23.00,
                    24.47
                ],
                [
                    23.22,
                    22.83,
                    22.76,
                    23.49
                ],
                [
                    22.70,
                    22.04,
                    21.90,
                    22.90
                ],
                [
                    22.04,
                    21.87,
                    21.75,
                    22.90
                ],
                [
                    21.40,
                    21.78,
                    21.26,
                    21.89
                ],
                [
                    21.85,
                    21.64,
                    21.52,
                    22.11
                ],
                [
                    21.60,
                    21.96,
                    21.58,
                    22.14
                ],
                [
                    21.79,
                    21.76,
                    21.58,
                    22.21
                ],
                [
                    19.71,
                    19.58,
                    19.58,
                    20.88
                ],
                [
                    19.58,
                    19.66,
                    19.05,
                    20.04
                ],
                [
                    19.50,
                    20.28,
                    18.89,
                    20.40
                ],
                [
                    20.50,
                    20.30,
                    20.20,
                    20.98
                ],
                [
                    20.20,
                    20.37,
                    20.10,
                    20.80
                ],
                [
                    20.60,
                    20.63,
                    20.40,
                    20.99
                ],
                [
                    20.56,
                    20.18,
                    20.12,
                    20.67
                ],
                [
                    20.17,
                    19.89,
                    19.58,
                    20.24
                ],
                [
                    19.88,
                    19.98,
                    19.71,
                    20.28
                ],
                [
                    20.06,
                    19.95,
                    19.63,
                    20.07
                ],
                [
                    19.96,
                    20.60,
                    19.85,
                    20.84
                ],
                [
                    20.60,
                    20.59,
                    20.41,
                    20.85
                ],
                [
                    20.63,
                    20.66,
                    20.56,
                    20.98
                ],
                [
                    20.71,
                    20.21,
                    20.06,
                    20.82
                ],
                [
                    20.15,
                    20.40,
                    20.14,
                    20.66
                ],
                [
                    20.35,
                    19.98,
                    19.84,
                    20.49
                ],
                [
                    20.17,
                    20.35,
                    19.89,
                    20.41
                ],
                [
                    20.30,
                    20.56,
                    20.29,
                    20.94
                ],
                [
                    20.68,
                    22.62,
                    20.67,
                    22.62
                ],
                [
                    23.00,
                    23.03,
                    22.65,
                    23.82
                ],
                [
                    23.05,
                    23.48,
                    22.80,
                    23.68
                ],
                [
                    24.80,
                    23.60,
                    22.89,
                    25.00
                ],
                [
                    23.81,
                    25.06,
                    23.70,
                    25.79
                ],
                [
                    24.82,
                    23.86,
                    23.77,
                    25.14
                ],
                [
                    24.00,
                    23.32,
                    22.78,
                    24.08
                ],
                [
                    24.00,
                    22.58,
                    22.39,
                    24.24
                ],
                [
                    22.60,
                    22.75,
                    22.40,
                    23.35
                ],
                [
                    22.72,
                    22.29,
                    22.18,
                    22.89
                ],
                [
                    22.31,
                    22.20,
                    22.10,
                    22.47
                ],
                [
                    22.27,
                    22.09,
                    22.00,
                    22.67
                ],
                [
                    22.10,
                    22.11,
                    21.82,
                    22.37
                ],
                [
                    22.18,
                    21.60,
                    21.59,
                    22.45
                ],
                [
                    21.61,
                    22.09,
                    21.60,
                    22.16
                ],
                [
                    22.04,
                    22.13,
                    21.88,
                    22.33
                ],
                [
                    22.26,
                    22.13,
                    22.05,
                    22.41
                ],
                [
                    22.09,
                    22.01,
                    21.91,
                    22.43
                ],
                [
                    22.13,
                    22.45,
                    21.81,
                    22.86
                ],
                [
                    22.50,
                    22.57,
                    22.35,
                    23.07
                ],
                [
                    22.49,
                    22.41,
                    22.28,
                    22.86
                ],
                [
                    22.46,
                    23.41,
                    22.34,
                    23.58
                ],
                [
                    23.41,
                    23.25,
                    23.16,
                    23.89
                ],
                [
                    23.60,
                    23.35,
                    23.11,
                    23.90
                ],
                [
                    23.35,
                    22.95,
                    22.74,
                    23.65
                ],
                [
                    22.83,
                    22.75,
                    22.48,
                    23.05
                ],
                [
                    22.58,
                    22.55,
                    22.47,
                    22.74
                ],
                [
                    22.55,
                    22.54,
                    22.29,
                    22.62
                ],
                [
                    22.44,
                    22.68,
                    22.43,
                    22.77
                ],
                [
                    22.66,
                    22.65,
                    22.35,
                    22.74
                ],
                [
                    22.50,
                    22.43,
                    22.18,
                    22.61
                ],
                [
                    22.43,
                    22.07,
                    21.85,
                    22.53
                ],
                [
                    22.00,
                    21.41,
                    21.24,
                    22.00
                ],
                [
                    21.37,
                    21.74,
                    21.20,
                    21.95
                ],
                [
                    21.75,
                    22.33,
                    21.66,
                    22.35
                ],
                [
                    22.33,
                    22.75,
                    22.30,
                    22.77
                ],
                [
                    22.95,
                    23.34,
                    22.95,
                    24.18
                ],
                [
                    23.28,
                    23.38,
                    23.22,
                    23.48
                ],
                [
                    23.68,
                    23.45,
                    23.37,
                    23.88
                ],
                [
                    23.65,
                    23.10,
                    22.95,
                    23.70
                ],
                [
                    23.00,
                    23.00,
                    22.64,
                    23.29
                ],
                [
                    22.90,
                    23.00,
                    22.80,
                    23.60
                ],
                [
                    22.94,
                    22.91,
                    22.75,
                    23.27
                ],
                [
                    22.83,
                    22.77,
                    22.57,
                    22.93
                ],
                [
                    22.84,
                    23.00,
                    22.72,
                    23.12
                ],
                [
                    23.19,
                    22.70,
                    22.65,
                    23.19
                ],
                [
                    22.61,
                    22.55,
                    22.35,
                    22.69
                ],
                [
                    22.59,
                    22.66,
                    22.38,
                    22.82
                ],
                [
                    22.56,
                    22.40,
                    22.33,
                    22.61
                ],
                [
                    22.21,
                    22.11,
                    21.98,
                    22.46
                ],
                [
                    22.16,
                    22.18,
                    22.09,
                    22.46
                ],
                [
                    22.29,
                    23.13,
                    22.22,
                    23.25
                ],
                [
                    23.13,
                    22.92,
                    22.81,
                    23.13
                ],
                [
                    22.81,
                    22.88,
                    22.50,
                    23.02
                ],
                [
                    22.74,
                    22.57,
                    22.40,
                    23.10
                ],
                [
                    22.56,
                    22.37,
                    22.27,
                    22.62
                ],
                [
                    22.38,
                    22.54,
                    22.32,
                    22.57
                ],
                [
                    22.53,
                    22.31,
                    22.18,
                    22.56
                ],
                [
                    22.33,
                    23.01,
                    22.17,
                    23.40
                ],
                [
                    23.31,
                    23.04,
                    22.83,
                    23.67
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u5438\u7b79",
            "symbol": "triangle",
            "symbolSize": 16,
            "data": [
                [
                    "2025-03-21",
                    24.48
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#2ca02c"
            }
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "symbol": "diamond",
            "symbolSize": 16,
            "data": [
                [
                    "2025-05-07",
                    23.82
                ],
                [
                    "2025-06-26",
                    24.18
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u5438\u7b79",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002544 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_0dc6ffa7c3084fe48018e4aa4f4c6137.setOption(option_0dc6ffa7c3084fe48018e4aa4f4c6137);
            window.addEventListener('resize', function(){
                chart_0dc6ffa7c3084fe48018e4aa4f4c6137.resize();
            })
    </script>
</body>
</html>
