<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="271d99329b014cf69942359c6f3a16cb" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_271d99329b014cf69942359c6f3a16cb = echarts.init(
            document.getElementById('271d99329b014cf69942359c6f3a16cb'), 'white', {renderer: 'canvas'});
        var option_271d99329b014cf69942359c6f3a16cb = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    11.67,
                    11.68,
                    11.53,
                    11.88
                ],
                [
                    12.04,
                    12.22,
                    11.9,
                    12.42
                ],
                [
                    12.1,
                    12.28,
                    11.95,
                    12.28
                ],
                [
                    12.19,
                    12.72,
                    12.19,
                    12.85
                ],
                [
                    12.84,
                    13.17,
                    12.79,
                    13.26
                ],
                [
                    13.15,
                    13.03,
                    12.9,
                    13.22
                ],
                [
                    13.05,
                    13.45,
                    13.02,
                    13.5
                ],
                [
                    13.63,
                    13.26,
                    13.16,
                    13.63
                ],
                [
                    13.25,
                    13.99,
                    13.22,
                    14.19
                ],
                [
                    14.35,
                    13.95,
                    13.85,
                    14.66
                ],
                [
                    13.89,
                    13.39,
                    13.32,
                    14.23
                ],
                [
                    13.46,
                    14.06,
                    13.39,
                    14.11
                ],
                [
                    13.98,
                    13.91,
                    13.85,
                    14.34
                ],
                [
                    13.98,
                    14.24,
                    13.9,
                    14.28
                ],
                [
                    14.11,
                    13.9,
                    13.78,
                    14.24
                ],
                [
                    13.68,
                    14.1,
                    13.5,
                    14.3
                ],
                [
                    14.1,
                    14.0,
                    13.9,
                    14.25
                ],
                [
                    13.98,
                    13.72,
                    13.5,
                    14.08
                ],
                [
                    13.69,
                    13.16,
                    13.01,
                    13.75
                ],
                [
                    13.12,
                    13.26,
                    12.93,
                    13.51
                ],
                [
                    13.08,
                    13.6,
                    13.06,
                    13.76
                ],
                [
                    13.7,
                    13.82,
                    13.63,
                    14.04
                ],
                [
                    13.9,
                    15.2,
                    13.84,
                    15.2
                ],
                [
                    15.5,
                    15.05,
                    14.83,
                    15.53
                ],
                [
                    15.05,
                    14.8,
                    14.59,
                    15.09
                ],
                [
                    14.5,
                    14.63,
                    14.36,
                    14.9
                ],
                [
                    14.75,
                    14.42,
                    14.39,
                    14.77
                ],
                [
                    14.49,
                    14.21,
                    14.07,
                    14.52
                ],
                [
                    14.15,
                    14.43,
                    13.91,
                    14.48
                ],
                [
                    14.62,
                    15.0,
                    14.4,
                    15.09
                ],
                [
                    15.0,
                    15.0,
                    14.82,
                    15.33
                ],
                [
                    14.89,
                    15.04,
                    14.78,
                    15.2
                ],
                [
                    14.97,
                    14.67,
                    14.65,
                    15.01
                ],
                [
                    14.55,
                    14.34,
                    14.21,
                    14.62
                ],
                [
                    14.32,
                    13.53,
                    13.31,
                    14.32
                ],
                [
                    13.8,
                    14.02,
                    13.75,
                    14.27
                ],
                [
                    13.97,
                    14.11,
                    13.85,
                    14.25
                ],
                [
                    14.02,
                    14.11,
                    13.77,
                    14.24
                ],
                [
                    14.11,
                    14.03,
                    13.97,
                    14.23
                ],
                [
                    13.91,
                    14.49,
                    13.8,
                    14.58
                ],
                [
                    14.44,
                    14.49,
                    14.32,
                    14.77
                ],
                [
                    14.45,
                    14.62,
                    14.4,
                    15.02
                ],
                [
                    14.47,
                    14.7,
                    14.4,
                    14.99
                ],
                [
                    13.7,
                    13.26,
                    13.23,
                    14.49
                ],
                [
                    13.59,
                    13.86,
                    13.56,
                    14.1
                ],
                [
                    13.72,
                    14.51,
                    13.23,
                    14.6
                ],
                [
                    14.65,
                    14.69,
                    14.65,
                    15.05
                ],
                [
                    14.55,
                    14.6,
                    14.54,
                    14.84
                ],
                [
                    14.77,
                    14.65,
                    14.57,
                    14.96
                ],
                [
                    14.59,
                    14.25,
                    13.99,
                    14.64
                ],
                [
                    14.19,
                    14.37,
                    14.12,
                    14.8
                ],
                [
                    14.18,
                    15.81,
                    14.12,
                    15.81
                ],
                [
                    16.03,
                    15.43,
                    15.4,
                    16.2
                ],
                [
                    15.51,
                    15.78,
                    15.22,
                    15.93
                ],
                [
                    15.75,
                    15.55,
                    15.46,
                    15.89
                ],
                [
                    15.65,
                    15.15,
                    15.1,
                    15.78
                ],
                [
                    15.1,
                    14.98,
                    14.86,
                    15.26
                ],
                [
                    14.96,
                    14.8,
                    14.77,
                    15.35
                ],
                [
                    14.75,
                    14.37,
                    14.33,
                    15.05
                ],
                [
                    14.55,
                    15.05,
                    14.46,
                    15.25
                ],
                [
                    15.01,
                    14.75,
                    14.74,
                    15.18
                ],
                [
                    14.78,
                    15.16,
                    14.78,
                    15.22
                ],
                [
                    15.31,
                    15.11,
                    14.97,
                    15.49
                ],
                [
                    15.05,
                    15.07,
                    15.01,
                    15.25
                ],
                [
                    15.0,
                    14.63,
                    14.6,
                    15.01
                ],
                [
                    14.75,
                    14.8,
                    14.66,
                    14.88
                ],
                [
                    14.95,
                    14.66,
                    14.55,
                    14.97
                ],
                [
                    14.6,
                    14.73,
                    14.53,
                    14.85
                ],
                [
                    14.65,
                    14.06,
                    14.01,
                    14.66
                ],
                [
                    14.02,
                    13.97,
                    13.91,
                    14.12
                ],
                [
                    13.98,
                    14.21,
                    13.83,
                    14.25
                ],
                [
                    14.21,
                    14.14,
                    14.09,
                    14.24
                ],
                [
                    14.15,
                    14.06,
                    14.01,
                    14.18
                ],
                [
                    14.0,
                    13.9,
                    13.88,
                    14.16
                ],
                [
                    13.89,
                    13.88,
                    13.86,
                    14.23
                ],
                [
                    13.91,
                    13.91,
                    13.78,
                    14.03
                ],
                [
                    13.93,
                    13.82,
                    13.75,
                    13.95
                ],
                [
                    13.86,
                    13.66,
                    13.66,
                    13.93
                ],
                [
                    13.57,
                    14.01,
                    13.49,
                    14.05
                ],
                [
                    14.0,
                    13.67,
                    13.62,
                    14.0
                ],
                [
                    13.8,
                    14.17,
                    13.75,
                    14.37
                ],
                [
                    14.2,
                    14.3,
                    14.13,
                    14.32
                ],
                [
                    14.3,
                    14.45,
                    14.22,
                    14.49
                ],
                [
                    14.45,
                    14.42,
                    14.32,
                    14.55
                ],
                [
                    14.4,
                    14.36,
                    14.25,
                    14.46
                ],
                [
                    14.34,
                    13.9,
                    13.8,
                    14.36
                ],
                [
                    13.9,
                    13.91,
                    13.79,
                    14.08
                ],
                [
                    13.85,
                    13.9,
                    13.71,
                    13.98
                ],
                [
                    13.82,
                    13.62,
                    13.56,
                    13.89
                ],
                [
                    13.57,
                    13.7,
                    13.48,
                    13.87
                ],
                [
                    13.65,
                    13.58,
                    13.53,
                    13.88
                ],
                [
                    13.51,
                    13.32,
                    13.3,
                    13.58
                ],
                [
                    13.3,
                    13.08,
                    13.05,
                    13.5
                ],
                [
                    13.06,
                    13.1,
                    13.03,
                    13.19
                ],
                [
                    13.01,
                    13.08,
                    12.94,
                    13.2
                ],
                [
                    13.09,
                    13.31,
                    13.09,
                    13.39
                ],
                [
                    13.33,
                    13.34,
                    13.2,
                    13.39
                ],
                [
                    13.36,
                    13.28,
                    13.23,
                    13.61
                ],
                [
                    13.29,
                    13.26,
                    13.18,
                    13.45
                ],
                [
                    13.3,
                    13.41,
                    13.25,
                    13.42
                ],
                [
                    13.38,
                    13.18,
                    13.13,
                    13.4
                ],
                [
                    13.16,
                    13.08,
                    13.0,
                    13.18
                ],
                [
                    13.1,
                    13.21,
                    13.08,
                    13.26
                ],
                [
                    13.21,
                    13.13,
                    13.11,
                    13.28
                ],
                [
                    13.14,
                    13.08,
                    13.05,
                    13.16
                ],
                [
                    13.05,
                    13.24,
                    13.01,
                    13.28
                ],
                [
                    13.23,
                    13.42,
                    13.22,
                    13.65
                ],
                [
                    13.42,
                    13.49,
                    13.35,
                    13.52
                ],
                [
                    13.49,
                    13.57,
                    13.43,
                    13.68
                ],
                [
                    13.59,
                    13.47,
                    13.43,
                    13.6
                ],
                [
                    13.45,
                    13.52,
                    13.3,
                    13.58
                ],
                [
                    13.58,
                    13.64,
                    13.5,
                    13.88
                ],
                [
                    13.61,
                    13.65,
                    13.5,
                    13.73
                ],
                [
                    13.65,
                    13.94,
                    13.65,
                    14.04
                ],
                [
                    13.99,
                    13.93,
                    13.82,
                    14.04
                ],
                [
                    14.0,
                    13.96,
                    13.85,
                    14.13
                ],
                [
                    13.6,
                    13.11,
                    13.03,
                    13.6
                ],
                [
                    13.09,
                    13.33,
                    13.09,
                    13.36
                ],
                [
                    13.33,
                    13.37,
                    13.24,
                    13.47
                ],
                [
                    13.36,
                    13.22,
                    13.18,
                    13.39
                ],
                [
                    13.13,
                    13.2,
                    13.07,
                    13.25
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#880000",
                "borderColor0": "#008F28"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5438\u7b79",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 18,
            "data": [
                [
                    "2025-02-05",
                    11.9
                ],
                [
                    "2025-03-19",
                    14.78
                ],
                [
                    "2025-03-27",
                    13.77
                ],
                [
                    "2025-04-01",
                    14.32
                ],
                [
                    "2025-04-02",
                    14.4
                ],
                [
                    "2025-04-03",
                    14.4
                ],
                [
                    "2025-04-11",
                    14.54
                ],
                [
                    "2025-04-14",
                    14.57
                ],
                [
                    "2025-06-04",
                    14.13
                ],
                [
                    "2025-06-05",
                    14.22
                ],
                [
                    "2025-06-11",
                    13.79
                ],
                [
                    "2025-06-23",
                    12.94
                ],
                [
                    "2025-06-30",
                    13.25
                ],
                [
                    "2025-07-03",
                    13.08
                ],
                [
                    "2025-07-08",
                    13.01
                ],
                [
                    "2025-07-09",
                    13.22
                ],
                [
                    "2025-07-16",
                    13.5
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#F4B400"
            }
        },
        {
            "type": "scatter",
            "name": "\u52a0\u4ed3",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 18,
            "data": [
                [
                    "2025-03-06",
                    13.84
                ],
                [
                    "2025-03-17",
                    14.4
                ],
                [
                    "2025-03-25",
                    13.75
                ],
                [
                    "2025-03-31",
                    13.8
                ],
                [
                    "2025-04-09",
                    13.23
                ],
                [
                    "2025-04-17",
                    14.12
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4285F4"
            }
        },
        {
            "type": "scatter",
            "name": "\u6d17\u76d8",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "rect",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#16A085"
            }
        },
        {
            "type": "scatter",
            "name": "\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "circle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#DB4437"
            }
        },
        {
            "type": "scatter",
            "name": "\u9690\u5f62\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "pin",
            "symbolSize": 18,
            "data": [
                [
                    "2025-05-09",
                    14.6
                ],
                [
                    "2025-05-12",
                    14.66
                ],
                [
                    "2025-05-13",
                    14.55
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#8E44AD"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5438\u7b79",
                "\u52a0\u4ed3",
                "\u6d17\u76d8",
                "\u51fa\u8d27",
                "\u9690\u5f62\u51fa\u8d27"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10,
            "top": "2%"
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "interval": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 0
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002410 \u4e3b\u529b\u4fe1\u53f7 (\u542b\u6d17\u76d8)",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "90%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_271d99329b014cf69942359c6f3a16cb.setOption(option_271d99329b014cf69942359c6f3a16cb);
            window.addEventListener('resize', function(){
                chart_271d99329b014cf69942359c6f3a16cb.resize();
            })
    </script>
</body>
</html>
