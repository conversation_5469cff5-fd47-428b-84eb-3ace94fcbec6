<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="94b34875f07b4d63a874789adb723f28" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_94b34875f07b4d63a874789adb723f28 = echarts.init(
            document.getElementById('94b34875f07b4d63a874789adb723f28'), 'white', {renderer: 'canvas'});
        var option_94b34875f07b4d63a874789adb723f28 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    6.27,
                    6.52,
                    6.17,
                    6.75
                ],
                [
                    6.49,
                    6.88,
                    6.49,
                    6.96
                ],
                [
                    6.80,
                    6.71,
                    6.61,
                    7.04
                ],
                [
                    6.70,
                    7.20,
                    6.68,
                    7.25
                ],
                [
                    7.14,
                    7.16,
                    7.05,
                    7.26
                ],
                [
                    7.10,
                    7.11,
                    7.06,
                    7.35
                ],
                [
                    7.07,
                    7.09,
                    7.00,
                    7.17
                ],
                [
                    7.06,
                    7.12,
                    7.06,
                    7.33
                ],
                [
                    7.10,
                    6.81,
                    6.65,
                    7.22
                ],
                [
                    6.83,
                    7.17,
                    6.82,
                    7.25
                ],
                [
                    7.17,
                    6.94,
                    6.92,
                    7.27
                ],
                [
                    6.94,
                    6.83,
                    6.73,
                    7.05
                ],
                [
                    6.83,
                    6.98,
                    6.79,
                    7.11
                ],
                [
                    6.98,
                    6.85,
                    6.74,
                    6.99
                ],
                [
                    6.86,
                    6.69,
                    6.65,
                    7.00
                ],
                [
                    6.70,
                    7.36,
                    6.70,
                    7.36
                ],
                [
                    7.11,
                    7.60,
                    7.11,
                    7.67
                ],
                [
                    7.51,
                    7.76,
                    7.51,
                    7.80
                ],
                [
                    7.75,
                    7.75,
                    7.57,
                    7.78
                ],
                [
                    7.74,
                    7.44,
                    7.37,
                    7.88
                ],
                [
                    7.44,
                    7.53,
                    7.34,
                    7.60
                ],
                [
                    7.46,
                    7.49,
                    7.38,
                    7.55
                ],
                [
                    7.46,
                    7.85,
                    7.44,
                    7.91
                ],
                [
                    7.80,
                    7.67,
                    7.62,
                    8.00
                ],
                [
                    7.69,
                    7.66,
                    7.39,
                    7.95
                ],
                [
                    7.66,
                    7.68,
                    7.51,
                    7.83
                ],
                [
                    7.71,
                    7.62,
                    7.57,
                    7.78
                ],
                [
                    7.58,
                    7.62,
                    7.47,
                    7.68
                ],
                [
                    7.67,
                    7.93,
                    7.57,
                    8.09
                ],
                [
                    7.97,
                    7.87,
                    7.76,
                    7.97
                ],
                [
                    7.94,
                    8.00,
                    7.81,
                    8.03
                ],
                [
                    7.99,
                    7.90,
                    7.86,
                    8.08
                ],
                [
                    7.90,
                    7.79,
                    7.76,
                    7.93
                ],
                [
                    7.80,
                    7.78,
                    7.65,
                    7.85
                ],
                [
                    7.82,
                    7.23,
                    7.03,
                    7.82
                ],
                [
                    7.30,
                    7.51,
                    7.19,
                    7.52
                ],
                [
                    7.39,
                    7.67,
                    7.39,
                    7.70
                ],
                [
                    7.68,
                    7.74,
                    7.56,
                    7.79
                ],
                [
                    7.81,
                    7.56,
                    7.53,
                    7.81
                ],
                [
                    7.53,
                    8.29,
                    7.53,
                    8.32
                ],
                [
                    8.26,
                    8.40,
                    8.06,
                    8.62
                ],
                [
                    8.48,
                    8.72,
                    8.27,
                    8.84
                ],
                [
                    8.62,
                    8.58,
                    8.41,
                    8.78
                ],
                [
                    8.01,
                    7.72,
                    7.72,
                    8.20
                ],
                [
                    7.42,
                    7.35,
                    7.08,
                    7.70
                ],
                [
                    7.35,
                    7.31,
                    6.73,
                    7.40
                ],
                [
                    7.39,
                    7.55,
                    7.39,
                    7.84
                ],
                [
                    7.50,
                    7.62,
                    7.44,
                    7.74
                ],
                [
                    7.69,
                    8.02,
                    7.67,
                    8.10
                ],
                [
                    8.14,
                    8.44,
                    8.10,
                    8.46
                ],
                [
                    8.28,
                    8.35,
                    8.04,
                    8.37
                ],
                [
                    8.36,
                    8.50,
                    8.26,
                    8.54
                ],
                [
                    8.58,
                    8.50,
                    8.40,
                    8.70
                ],
                [
                    8.51,
                    8.59,
                    8.36,
                    8.60
                ],
                [
                    8.62,
                    8.54,
                    8.45,
                    8.69
                ],
                [
                    8.61,
                    8.41,
                    8.41,
                    8.78
                ],
                [
                    8.45,
                    8.36,
                    8.31,
                    8.46
                ],
                [
                    8.36,
                    8.35,
                    8.23,
                    8.46
                ],
                [
                    8.39,
                    8.34,
                    8.24,
                    8.58
                ],
                [
                    8.34,
                    8.83,
                    8.28,
                    8.95
                ],
                [
                    8.87,
                    9.01,
                    8.80,
                    9.24
                ],
                [
                    9.04,
                    9.50,
                    9.04,
                    9.50
                ],
                [
                    9.65,
                    9.52,
                    9.29,
                    9.68
                ],
                [
                    9.47,
                    9.61,
                    9.38,
                    9.80
                ],
                [
                    9.61,
                    9.71,
                    9.38,
                    9.78
                ],
                [
                    9.70,
                    10.17,
                    9.68,
                    10.22
                ],
                [
                    10.18,
                    10.18,
                    10.08,
                    10.35
                ],
                [
                    10.20,
                    10.25,
                    9.98,
                    10.29
                ],
                [
                    10.36,
                    10.80,
                    10.26,
                    10.93
                ],
                [
                    10.75,
                    9.72,
                    9.72,
                    10.76
                ],
                [
                    9.31,
                    8.92,
                    8.75,
                    9.52
                ],
                [
                    8.92,
                    9.08,
                    8.80,
                    9.10
                ],
                [
                    8.99,
                    8.92,
                    8.85,
                    9.08
                ],
                [
                    8.88,
                    8.03,
                    8.03,
                    8.97
                ],
                [
                    7.89,
                    7.92,
                    7.60,
                    8.04
                ],
                [
                    7.96,
                    8.30,
                    7.76,
                    8.42
                ],
                [
                    8.15,
                    8.31,
                    8.13,
                    8.45
                ],
                [
                    8.30,
                    8.12,
                    8.11,
                    8.30
                ],
                [
                    8.11,
                    8.18,
                    8.03,
                    8.18
                ],
                [
                    8.18,
                    8.09,
                    8.03,
                    8.18
                ],
                [
                    8.09,
                    7.99,
                    7.98,
                    8.14
                ],
                [
                    8.00,
                    8.10,
                    7.95,
                    8.14
                ],
                [
                    8.10,
                    8.10,
                    8.00,
                    8.20
                ],
                [
                    8.20,
                    8.36,
                    8.12,
                    8.52
                ],
                [
                    8.48,
                    9.20,
                    8.40,
                    9.20
                ],
                [
                    9.23,
                    9.04,
                    8.98,
                    9.29
                ],
                [
                    9.13,
                    9.30,
                    8.87,
                    9.40
                ],
                [
                    9.25,
                    9.02,
                    8.90,
                    9.54
                ],
                [
                    8.93,
                    8.77,
                    8.74,
                    8.95
                ],
                [
                    8.85,
                    9.17,
                    8.72,
                    9.27
                ],
                [
                    9.18,
                    9.60,
                    9.18,
                    9.86
                ],
                [
                    9.65,
                    9.73,
                    9.45,
                    9.76
                ],
                [
                    9.77,
                    9.49,
                    9.44,
                    9.97
                ],
                [
                    9.44,
                    9.55,
                    9.33,
                    9.62
                ],
                [
                    9.50,
                    9.53,
                    9.30,
                    9.58
                ],
                [
                    9.44,
                    9.72,
                    9.35,
                    9.78
                ],
                [
                    9.72,
                    9.58,
                    9.52,
                    9.83
                ],
                [
                    9.60,
                    9.41,
                    9.28,
                    9.60
                ],
                [
                    9.43,
                    9.36,
                    9.25,
                    9.57
                ],
                [
                    9.33,
                    9.37,
                    9.15,
                    9.44
                ],
                [
                    9.35,
                    9.52,
                    9.20,
                    9.58
                ],
                [
                    9.59,
                    9.42,
                    9.34,
                    9.59
                ],
                [
                    9.34,
                    9.40,
                    9.33,
                    9.49
                ],
                [
                    9.35,
                    9.29,
                    9.27,
                    9.44
                ],
                [
                    9.32,
                    9.26,
                    9.20,
                    9.34
                ],
                [
                    9.30,
                    10.19,
                    9.25,
                    10.19
                ],
                [
                    10.55,
                    10.30,
                    10.20,
                    10.70
                ],
                [
                    10.40,
                    10.13,
                    10.05,
                    10.43
                ],
                [
                    10.15,
                    10.09,
                    10.00,
                    10.17
                ],
                [
                    9.95,
                    10.10,
                    9.88,
                    10.15
                ],
                [
                    9.96,
                    10.03,
                    9.68,
                    10.16
                ],
                [
                    9.98,
                    10.00,
                    9.96,
                    10.38
                ],
                [
                    10.02,
                    9.90,
                    9.88,
                    10.06
                ],
                [
                    9.88,
                    9.91,
                    9.80,
                    10.15
                ],
                [
                    9.90,
                    9.84,
                    9.71,
                    9.95
                ],
                [
                    9.83,
                    9.71,
                    9.67,
                    9.93
                ],
                [
                    9.21,
                    9.39,
                    9.21,
                    9.54
                ],
                [
                    9.30,
                    8.90,
                    8.61,
                    9.36
                ],
                [
                    8.85,
                    8.88,
                    8.76,
                    8.97
                ],
                [
                    8.95,
                    9.13,
                    8.90,
                    9.24
                ],
                [
                    9.13,
                    9.09,
                    9.00,
                    9.19
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u7a81\u7834",
            "symbol": "pin",
            "symbolSize": 16,
            "data": [
                [
                    "2025-06-09",
                    9.20
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#1f77b4"
            }
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "symbol": "diamond",
            "symbolSize": 16,
            "data": [
                [
                    "2025-04-30",
                    9.24
                ],
                [
                    "2025-07-09",
                    10.70
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u7a81\u7834",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603429 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_94b34875f07b4d63a874789adb723f28.setOption(option_94b34875f07b4d63a874789adb723f28);
            window.addEventListener('resize', function(){
                chart_94b34875f07b4d63a874789adb723f28.resize();
            })
    </script>
</body>
</html>
