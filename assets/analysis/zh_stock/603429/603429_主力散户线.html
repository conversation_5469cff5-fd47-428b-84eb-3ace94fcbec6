<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="a851debf3fa24433b566726161d88a9a" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_a851debf3fa24433b566726161d88a9a = echarts.init(
            document.getElementById('a851debf3fa24433b566726161d88a9a'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_a851debf3fa24433b566726161d88a9a = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    6.27,
                    6.52,
                    6.17,
                    6.75
                ],
                [
                    6.49,
                    6.88,
                    6.49,
                    6.96
                ],
                [
                    6.8,
                    6.71,
                    6.61,
                    7.04
                ],
                [
                    6.7,
                    7.2,
                    6.68,
                    7.25
                ],
                [
                    7.14,
                    7.16,
                    7.05,
                    7.26
                ],
                [
                    7.1,
                    7.11,
                    7.06,
                    7.35
                ],
                [
                    7.07,
                    7.09,
                    7.0,
                    7.17
                ],
                [
                    7.06,
                    7.12,
                    7.06,
                    7.33
                ],
                [
                    7.1,
                    6.81,
                    6.65,
                    7.22
                ],
                [
                    6.83,
                    7.17,
                    6.82,
                    7.25
                ],
                [
                    7.17,
                    6.94,
                    6.92,
                    7.27
                ],
                [
                    6.94,
                    6.83,
                    6.73,
                    7.05
                ],
                [
                    6.83,
                    6.98,
                    6.79,
                    7.11
                ],
                [
                    6.98,
                    6.85,
                    6.74,
                    6.99
                ],
                [
                    6.86,
                    6.69,
                    6.65,
                    7.0
                ],
                [
                    6.7,
                    7.36,
                    6.7,
                    7.36
                ],
                [
                    7.11,
                    7.6,
                    7.11,
                    7.67
                ],
                [
                    7.51,
                    7.76,
                    7.51,
                    7.8
                ],
                [
                    7.75,
                    7.75,
                    7.57,
                    7.78
                ],
                [
                    7.74,
                    7.44,
                    7.37,
                    7.88
                ],
                [
                    7.44,
                    7.53,
                    7.34,
                    7.6
                ],
                [
                    7.46,
                    7.49,
                    7.38,
                    7.55
                ],
                [
                    7.46,
                    7.85,
                    7.44,
                    7.91
                ],
                [
                    7.8,
                    7.67,
                    7.62,
                    8.0
                ],
                [
                    7.69,
                    7.66,
                    7.39,
                    7.95
                ],
                [
                    7.66,
                    7.68,
                    7.51,
                    7.83
                ],
                [
                    7.71,
                    7.62,
                    7.57,
                    7.78
                ],
                [
                    7.58,
                    7.62,
                    7.47,
                    7.68
                ],
                [
                    7.67,
                    7.93,
                    7.57,
                    8.09
                ],
                [
                    7.97,
                    7.87,
                    7.76,
                    7.97
                ],
                [
                    7.94,
                    8.0,
                    7.81,
                    8.03
                ],
                [
                    7.99,
                    7.9,
                    7.86,
                    8.08
                ],
                [
                    7.9,
                    7.79,
                    7.76,
                    7.93
                ],
                [
                    7.8,
                    7.78,
                    7.65,
                    7.85
                ],
                [
                    7.82,
                    7.23,
                    7.03,
                    7.82
                ],
                [
                    7.3,
                    7.51,
                    7.19,
                    7.52
                ],
                [
                    7.39,
                    7.67,
                    7.39,
                    7.7
                ],
                [
                    7.68,
                    7.74,
                    7.56,
                    7.79
                ],
                [
                    7.81,
                    7.56,
                    7.53,
                    7.81
                ],
                [
                    7.53,
                    8.29,
                    7.53,
                    8.32
                ],
                [
                    8.26,
                    8.4,
                    8.06,
                    8.62
                ],
                [
                    8.48,
                    8.72,
                    8.27,
                    8.84
                ],
                [
                    8.62,
                    8.58,
                    8.41,
                    8.78
                ],
                [
                    8.01,
                    7.72,
                    7.72,
                    8.2
                ],
                [
                    7.42,
                    7.35,
                    7.08,
                    7.7
                ],
                [
                    7.35,
                    7.31,
                    6.73,
                    7.4
                ],
                [
                    7.39,
                    7.55,
                    7.39,
                    7.84
                ],
                [
                    7.5,
                    7.62,
                    7.44,
                    7.74
                ],
                [
                    7.69,
                    8.02,
                    7.67,
                    8.1
                ],
                [
                    8.14,
                    8.44,
                    8.1,
                    8.46
                ],
                [
                    8.28,
                    8.35,
                    8.04,
                    8.37
                ],
                [
                    8.36,
                    8.5,
                    8.26,
                    8.54
                ],
                [
                    8.58,
                    8.5,
                    8.4,
                    8.7
                ],
                [
                    8.51,
                    8.59,
                    8.36,
                    8.6
                ],
                [
                    8.62,
                    8.54,
                    8.45,
                    8.69
                ],
                [
                    8.61,
                    8.41,
                    8.41,
                    8.78
                ],
                [
                    8.45,
                    8.36,
                    8.31,
                    8.46
                ],
                [
                    8.36,
                    8.35,
                    8.23,
                    8.46
                ],
                [
                    8.39,
                    8.34,
                    8.24,
                    8.58
                ],
                [
                    8.34,
                    8.83,
                    8.28,
                    8.95
                ],
                [
                    8.87,
                    9.01,
                    8.8,
                    9.24
                ],
                [
                    9.04,
                    9.5,
                    9.04,
                    9.5
                ],
                [
                    9.65,
                    9.52,
                    9.29,
                    9.68
                ],
                [
                    9.47,
                    9.61,
                    9.38,
                    9.8
                ],
                [
                    9.61,
                    9.71,
                    9.38,
                    9.78
                ],
                [
                    9.7,
                    10.17,
                    9.68,
                    10.22
                ],
                [
                    10.18,
                    10.18,
                    10.08,
                    10.35
                ],
                [
                    10.2,
                    10.25,
                    9.98,
                    10.29
                ],
                [
                    10.36,
                    10.8,
                    10.26,
                    10.93
                ],
                [
                    10.75,
                    9.72,
                    9.72,
                    10.76
                ],
                [
                    9.31,
                    8.92,
                    8.75,
                    9.52
                ],
                [
                    8.92,
                    9.08,
                    8.8,
                    9.1
                ],
                [
                    8.99,
                    8.92,
                    8.85,
                    9.08
                ],
                [
                    8.88,
                    8.03,
                    8.03,
                    8.97
                ],
                [
                    7.89,
                    7.92,
                    7.6,
                    8.04
                ],
                [
                    7.96,
                    8.3,
                    7.76,
                    8.42
                ],
                [
                    8.15,
                    8.31,
                    8.13,
                    8.45
                ],
                [
                    8.3,
                    8.12,
                    8.11,
                    8.3
                ],
                [
                    8.11,
                    8.18,
                    8.03,
                    8.18
                ],
                [
                    8.18,
                    8.09,
                    8.03,
                    8.18
                ],
                [
                    8.09,
                    7.99,
                    7.98,
                    8.14
                ],
                [
                    8.0,
                    8.1,
                    7.95,
                    8.14
                ],
                [
                    8.1,
                    8.1,
                    8.0,
                    8.2
                ],
                [
                    8.2,
                    8.36,
                    8.12,
                    8.52
                ],
                [
                    8.48,
                    9.2,
                    8.4,
                    9.2
                ],
                [
                    9.23,
                    9.04,
                    8.98,
                    9.29
                ],
                [
                    9.13,
                    9.3,
                    8.87,
                    9.4
                ],
                [
                    9.25,
                    9.02,
                    8.9,
                    9.54
                ],
                [
                    8.93,
                    8.77,
                    8.74,
                    8.95
                ],
                [
                    8.85,
                    9.17,
                    8.72,
                    9.27
                ],
                [
                    9.18,
                    9.6,
                    9.18,
                    9.86
                ],
                [
                    9.65,
                    9.73,
                    9.45,
                    9.76
                ],
                [
                    9.77,
                    9.49,
                    9.44,
                    9.97
                ],
                [
                    9.44,
                    9.55,
                    9.33,
                    9.62
                ],
                [
                    9.5,
                    9.53,
                    9.3,
                    9.58
                ],
                [
                    9.44,
                    9.72,
                    9.35,
                    9.78
                ],
                [
                    9.72,
                    9.58,
                    9.52,
                    9.83
                ],
                [
                    9.6,
                    9.41,
                    9.28,
                    9.6
                ],
                [
                    9.43,
                    9.36,
                    9.25,
                    9.57
                ],
                [
                    9.33,
                    9.37,
                    9.15,
                    9.44
                ],
                [
                    9.35,
                    9.52,
                    9.2,
                    9.58
                ],
                [
                    9.59,
                    9.42,
                    9.34,
                    9.59
                ],
                [
                    9.34,
                    9.4,
                    9.33,
                    9.49
                ],
                [
                    9.35,
                    9.29,
                    9.27,
                    9.44
                ],
                [
                    9.32,
                    9.26,
                    9.2,
                    9.34
                ],
                [
                    9.3,
                    10.19,
                    9.25,
                    10.19
                ],
                [
                    10.55,
                    10.3,
                    10.2,
                    10.7
                ],
                [
                    10.4,
                    10.13,
                    10.05,
                    10.43
                ],
                [
                    10.15,
                    10.09,
                    10.0,
                    10.17
                ],
                [
                    9.95,
                    10.1,
                    9.88,
                    10.15
                ],
                [
                    9.96,
                    10.03,
                    9.68,
                    10.16
                ],
                [
                    9.98,
                    10.0,
                    9.96,
                    10.38
                ],
                [
                    10.02,
                    9.9,
                    9.88,
                    10.06
                ],
                [
                    9.88,
                    9.91,
                    9.8,
                    10.15
                ],
                [
                    9.9,
                    9.84,
                    9.71,
                    9.95
                ],
                [
                    9.83,
                    9.71,
                    9.67,
                    9.93
                ],
                [
                    9.21,
                    9.39,
                    9.21,
                    9.54
                ],
                [
                    9.3,
                    8.9,
                    8.61,
                    9.36
                ],
                [
                    8.85,
                    8.88,
                    8.76,
                    8.97
                ],
                [
                    8.95,
                    9.13,
                    8.9,
                    9.24
                ],
                [
                    9.13,
                    9.09,
                    9.0,
                    9.19
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    16.17
                ],
                [
                    "2025-02-05",
                    12.63
                ],
                [
                    "2025-02-06",
                    -5.01
                ],
                [
                    "2025-02-07",
                    6.17
                ],
                [
                    "2025-02-10",
                    4.98
                ],
                [
                    "2025-02-11",
                    0.72
                ],
                [
                    "2025-02-12",
                    -18.60
                ],
                [
                    "2025-02-13",
                    8.91
                ],
                [
                    "2025-02-14",
                    -28.53
                ],
                [
                    "2025-02-17",
                    1.21
                ],
                [
                    "2025-02-18",
                    5.90
                ],
                [
                    "2025-02-19",
                    -33.06
                ],
                [
                    "2025-02-20",
                    5.77
                ],
                [
                    "2025-02-21",
                    -9.24
                ],
                [
                    "2025-02-24",
                    -26.67
                ],
                [
                    "2025-02-25",
                    39.28
                ],
                [
                    "2025-02-26",
                    10.62
                ],
                [
                    "2025-02-27",
                    12.76
                ],
                [
                    "2025-02-28",
                    -3.39
                ],
                [
                    "2025-03-03",
                    -4.02
                ],
                [
                    "2025-03-04",
                    -10.30
                ],
                [
                    "2025-03-05",
                    -18.19
                ],
                [
                    "2025-03-06",
                    10.81
                ],
                [
                    "2025-03-07",
                    7.37
                ],
                [
                    "2025-03-10",
                    11.13
                ],
                [
                    "2025-03-11",
                    7.41
                ],
                [
                    "2025-03-12",
                    2.66
                ],
                [
                    "2025-03-13",
                    17.64
                ],
                [
                    "2025-03-14",
                    8.01
                ],
                [
                    "2025-03-17",
                    -9.55
                ],
                [
                    "2025-03-18",
                    5.26
                ],
                [
                    "2025-03-19",
                    -8.26
                ],
                [
                    "2025-03-20",
                    -20.68
                ],
                [
                    "2025-03-21",
                    1.30
                ],
                [
                    "2025-03-24",
                    -18.30
                ],
                [
                    "2025-03-25",
                    8.37
                ],
                [
                    "2025-03-26",
                    5.17
                ],
                [
                    "2025-03-27",
                    5.11
                ],
                [
                    "2025-03-28",
                    2.51
                ],
                [
                    "2025-03-31",
                    1.61
                ],
                [
                    "2025-04-01",
                    -2.96
                ],
                [
                    "2025-04-02",
                    6.54
                ],
                [
                    "2025-04-03",
                    -1.81
                ],
                [
                    "2025-04-07",
                    -12.16
                ],
                [
                    "2025-04-08",
                    7.22
                ],
                [
                    "2025-04-09",
                    -2.91
                ],
                [
                    "2025-04-10",
                    6.06
                ],
                [
                    "2025-04-11",
                    -5.61
                ],
                [
                    "2025-04-14",
                    11.93
                ],
                [
                    "2025-04-15",
                    8.90
                ],
                [
                    "2025-04-16",
                    1.24
                ],
                [
                    "2025-04-17",
                    3.70
                ],
                [
                    "2025-04-18",
                    -9.96
                ],
                [
                    "2025-04-21",
                    0.49
                ],
                [
                    "2025-04-22",
                    -4.20
                ],
                [
                    "2025-04-23",
                    -1.42
                ],
                [
                    "2025-04-24",
                    -24.36
                ],
                [
                    "2025-04-25",
                    1.27
                ],
                [
                    "2025-04-28",
                    5.68
                ],
                [
                    "2025-04-29",
                    -1.23
                ],
                [
                    "2025-04-30",
                    7.33
                ],
                [
                    "2025-05-06",
                    2.70
                ],
                [
                    "2025-05-07",
                    -1.00
                ],
                [
                    "2025-05-08",
                    -4.98
                ],
                [
                    "2025-05-09",
                    -4.41
                ],
                [
                    "2025-05-12",
                    7.81
                ],
                [
                    "2025-05-13",
                    5.28
                ],
                [
                    "2025-05-14",
                    -3.70
                ],
                [
                    "2025-05-15",
                    10.48
                ],
                [
                    "2025-05-16",
                    -33.07
                ],
                [
                    "2025-05-19",
                    -14.96
                ],
                [
                    "2025-05-20",
                    -5.99
                ],
                [
                    "2025-05-21",
                    -5.82
                ],
                [
                    "2025-05-22",
                    -37.10
                ],
                [
                    "2025-05-23",
                    -6.83
                ],
                [
                    "2025-05-26",
                    10.53
                ],
                [
                    "2025-05-27",
                    8.29
                ],
                [
                    "2025-05-28",
                    0.40
                ],
                [
                    "2025-05-29",
                    -13.52
                ],
                [
                    "2025-05-30",
                    1.93
                ],
                [
                    "2025-06-03",
                    -15.28
                ],
                [
                    "2025-06-04",
                    -12.26
                ],
                [
                    "2025-06-05",
                    -4.68
                ],
                [
                    "2025-06-06",
                    10.01
                ],
                [
                    "2025-06-09",
                    17.71
                ],
                [
                    "2025-06-10",
                    -12.60
                ],
                [
                    "2025-06-11",
                    6.83
                ],
                [
                    "2025-06-12",
                    -4.35
                ],
                [
                    "2025-06-13",
                    -2.65
                ],
                [
                    "2025-06-16",
                    4.14
                ],
                [
                    "2025-06-17",
                    4.40
                ],
                [
                    "2025-06-18",
                    11.59
                ],
                [
                    "2025-06-19",
                    3.00
                ],
                [
                    "2025-06-20",
                    2.63
                ],
                [
                    "2025-06-23",
                    -5.58
                ],
                [
                    "2025-06-24",
                    -6.46
                ],
                [
                    "2025-06-25",
                    -14.82
                ],
                [
                    "2025-06-26",
                    -17.68
                ],
                [
                    "2025-06-27",
                    -11.50
                ],
                [
                    "2025-06-30",
                    -7.97
                ],
                [
                    "2025-07-01",
                    3.91
                ],
                [
                    "2025-07-02",
                    -5.96
                ],
                [
                    "2025-07-03",
                    -15.64
                ],
                [
                    "2025-07-04",
                    -11.87
                ],
                [
                    "2025-07-07",
                    -3.87
                ],
                [
                    "2025-07-08",
                    16.38
                ],
                [
                    "2025-07-09",
                    -6.36
                ],
                [
                    "2025-07-10",
                    -7.26
                ],
                [
                    "2025-07-11",
                    0.17
                ],
                [
                    "2025-07-14",
                    -3.01
                ],
                [
                    "2025-07-15",
                    -7.16
                ],
                [
                    "2025-07-16",
                    1.20
                ],
                [
                    "2025-07-17",
                    -26.47
                ],
                [
                    "2025-07-18",
                    -7.30
                ],
                [
                    "2025-07-21",
                    -21.12
                ],
                [
                    "2025-07-22",
                    -13.40
                ],
                [
                    "2025-07-23",
                    -12.69
                ],
                [
                    "2025-07-24",
                    -24.19
                ],
                [
                    "2025-07-25",
                    -21.86
                ],
                [
                    "2025-07-28",
                    4.75
                ],
                [
                    "2025-07-29",
                    -5.18
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    6.17
                ],
                [
                    "2025-02-05",
                    2.51
                ],
                [
                    "2025-02-06",
                    4.76
                ],
                [
                    "2025-02-07",
                    -5.19
                ],
                [
                    "2025-02-10",
                    6.30
                ],
                [
                    "2025-02-11",
                    -1.67
                ],
                [
                    "2025-02-12",
                    6.64
                ],
                [
                    "2025-02-13",
                    -1.53
                ],
                [
                    "2025-02-14",
                    9.62
                ],
                [
                    "2025-02-17",
                    -3.84
                ],
                [
                    "2025-02-18",
                    11.28
                ],
                [
                    "2025-02-19",
                    16.69
                ],
                [
                    "2025-02-20",
                    -5.41
                ],
                [
                    "2025-02-21",
                    12.16
                ],
                [
                    "2025-02-24",
                    9.81
                ],
                [
                    "2025-02-25",
                    -19.59
                ],
                [
                    "2025-02-26",
                    0.83
                ],
                [
                    "2025-02-27",
                    0.86
                ],
                [
                    "2025-02-28",
                    3.81
                ],
                [
                    "2025-03-03",
                    4.78
                ],
                [
                    "2025-03-04",
                    -3.43
                ],
                [
                    "2025-03-05",
                    0.36
                ],
                [
                    "2025-03-06",
                    -7.98
                ],
                [
                    "2025-03-07",
                    3.40
                ],
                [
                    "2025-03-10",
                    -6.48
                ],
                [
                    "2025-03-11",
                    3.61
                ],
                [
                    "2025-03-12",
                    -3.73
                ],
                [
                    "2025-03-13",
                    -4.21
                ],
                [
                    "2025-03-14",
                    -1.52
                ],
                [
                    "2025-03-17",
                    12.37
                ],
                [
                    "2025-03-18",
                    -4.19
                ],
                [
                    "2025-03-19",
                    9.62
                ],
                [
                    "2025-03-20",
                    14.42
                ],
                [
                    "2025-03-21",
                    -0.13
                ],
                [
                    "2025-03-24",
                    13.94
                ],
                [
                    "2025-03-25",
                    -3.51
                ],
                [
                    "2025-03-26",
                    1.53
                ],
                [
                    "2025-03-27",
                    3.89
                ],
                [
                    "2025-03-28",
                    5.19
                ],
                [
                    "2025-03-31",
                    1.07
                ],
                [
                    "2025-04-01",
                    -1.75
                ],
                [
                    "2025-04-02",
                    -3.16
                ],
                [
                    "2025-04-03",
                    3.30
                ],
                [
                    "2025-04-07",
                    6.33
                ],
                [
                    "2025-04-08",
                    5.56
                ],
                [
                    "2025-04-09",
                    1.83
                ],
                [
                    "2025-04-10",
                    -7.31
                ],
                [
                    "2025-04-11",
                    1.90
                ],
                [
                    "2025-04-14",
                    -0.56
                ],
                [
                    "2025-04-15",
                    3.28
                ],
                [
                    "2025-04-16",
                    -1.43
                ],
                [
                    "2025-04-17",
                    6.02
                ],
                [
                    "2025-04-18",
                    12.29
                ],
                [
                    "2025-04-21",
                    -8.99
                ],
                [
                    "2025-04-22",
                    5.77
                ],
                [
                    "2025-04-23",
                    0.80
                ],
                [
                    "2025-04-24",
                    1.01
                ],
                [
                    "2025-04-25",
                    1.78
                ],
                [
                    "2025-04-28",
                    8.05
                ],
                [
                    "2025-04-29",
                    3.68
                ],
                [
                    "2025-04-30",
                    -0.20
                ],
                [
                    "2025-05-06",
                    -2.89
                ],
                [
                    "2025-05-07",
                    -3.88
                ],
                [
                    "2025-05-08",
                    4.78
                ],
                [
                    "2025-05-09",
                    2.59
                ],
                [
                    "2025-05-12",
                    -1.05
                ],
                [
                    "2025-05-13",
                    4.03
                ],
                [
                    "2025-05-14",
                    1.63
                ],
                [
                    "2025-05-15",
                    -1.92
                ],
                [
                    "2025-05-16",
                    21.33
                ],
                [
                    "2025-05-19",
                    10.94
                ],
                [
                    "2025-05-20",
                    7.54
                ],
                [
                    "2025-05-21",
                    4.15
                ],
                [
                    "2025-05-22",
                    15.96
                ],
                [
                    "2025-05-23",
                    4.87
                ],
                [
                    "2025-05-26",
                    -5.38
                ],
                [
                    "2025-05-27",
                    -0.86
                ],
                [
                    "2025-05-28",
                    1.00
                ],
                [
                    "2025-05-29",
                    6.87
                ],
                [
                    "2025-05-30",
                    -0.45
                ],
                [
                    "2025-06-03",
                    11.25
                ],
                [
                    "2025-06-04",
                    2.38
                ],
                [
                    "2025-06-05",
                    -0.75
                ],
                [
                    "2025-06-06",
                    -1.28
                ],
                [
                    "2025-06-09",
                    -11.45
                ],
                [
                    "2025-06-10",
                    4.24
                ],
                [
                    "2025-06-11",
                    -3.56
                ],
                [
                    "2025-06-12",
                    10.29
                ],
                [
                    "2025-06-13",
                    7.03
                ],
                [
                    "2025-06-16",
                    0.04
                ],
                [
                    "2025-06-17",
                    -0.16
                ],
                [
                    "2025-06-18",
                    -6.93
                ],
                [
                    "2025-06-19",
                    3.32
                ],
                [
                    "2025-06-20",
                    0.79
                ],
                [
                    "2025-06-23",
                    8.03
                ],
                [
                    "2025-06-24",
                    4.15
                ],
                [
                    "2025-06-25",
                    18.88
                ],
                [
                    "2025-06-26",
                    8.76
                ],
                [
                    "2025-06-27",
                    5.21
                ],
                [
                    "2025-06-30",
                    -1.24
                ],
                [
                    "2025-07-01",
                    -4.27
                ],
                [
                    "2025-07-02",
                    16.21
                ],
                [
                    "2025-07-03",
                    7.05
                ],
                [
                    "2025-07-04",
                    5.34
                ],
                [
                    "2025-07-07",
                    3.29
                ],
                [
                    "2025-07-08",
                    -10.49
                ],
                [
                    "2025-07-09",
                    -1.69
                ],
                [
                    "2025-07-10",
                    0.29
                ],
                [
                    "2025-07-11",
                    2.61
                ],
                [
                    "2025-07-14",
                    0.41
                ],
                [
                    "2025-07-15",
                    2.15
                ],
                [
                    "2025-07-16",
                    3.86
                ],
                [
                    "2025-07-17",
                    14.75
                ],
                [
                    "2025-07-18",
                    13.61
                ],
                [
                    "2025-07-21",
                    7.41
                ],
                [
                    "2025-07-22",
                    8.96
                ],
                [
                    "2025-07-23",
                    3.64
                ],
                [
                    "2025-07-24",
                    14.12
                ],
                [
                    "2025-07-25",
                    4.51
                ],
                [
                    "2025-07-28",
                    -1.82
                ],
                [
                    "2025-07-29",
                    5.67
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -22.34
                ],
                [
                    "2025-02-05",
                    -15.15
                ],
                [
                    "2025-02-06",
                    0.25
                ],
                [
                    "2025-02-07",
                    -0.99
                ],
                [
                    "2025-02-10",
                    -11.28
                ],
                [
                    "2025-02-11",
                    0.95
                ],
                [
                    "2025-02-12",
                    11.96
                ],
                [
                    "2025-02-13",
                    -7.38
                ],
                [
                    "2025-02-14",
                    18.91
                ],
                [
                    "2025-02-17",
                    2.64
                ],
                [
                    "2025-02-18",
                    -17.18
                ],
                [
                    "2025-02-19",
                    16.37
                ],
                [
                    "2025-02-20",
                    -0.36
                ],
                [
                    "2025-02-21",
                    -2.92
                ],
                [
                    "2025-02-24",
                    16.86
                ],
                [
                    "2025-02-25",
                    -19.68
                ],
                [
                    "2025-02-26",
                    -11.45
                ],
                [
                    "2025-02-27",
                    -13.62
                ],
                [
                    "2025-02-28",
                    -0.43
                ],
                [
                    "2025-03-03",
                    -0.76
                ],
                [
                    "2025-03-04",
                    13.73
                ],
                [
                    "2025-03-05",
                    17.83
                ],
                [
                    "2025-03-06",
                    -2.82
                ],
                [
                    "2025-03-07",
                    -10.77
                ],
                [
                    "2025-03-10",
                    -4.65
                ],
                [
                    "2025-03-11",
                    -11.02
                ],
                [
                    "2025-03-12",
                    1.07
                ],
                [
                    "2025-03-13",
                    -13.43
                ],
                [
                    "2025-03-14",
                    -6.49
                ],
                [
                    "2025-03-17",
                    -2.81
                ],
                [
                    "2025-03-18",
                    -1.07
                ],
                [
                    "2025-03-19",
                    -1.37
                ],
                [
                    "2025-03-20",
                    6.25
                ],
                [
                    "2025-03-21",
                    -1.17
                ],
                [
                    "2025-03-24",
                    4.36
                ],
                [
                    "2025-03-25",
                    -4.86
                ],
                [
                    "2025-03-26",
                    -6.70
                ],
                [
                    "2025-03-27",
                    -9.00
                ],
                [
                    "2025-03-28",
                    -7.71
                ],
                [
                    "2025-03-31",
                    -2.68
                ],
                [
                    "2025-04-01",
                    4.71
                ],
                [
                    "2025-04-02",
                    -3.38
                ],
                [
                    "2025-04-03",
                    -1.49
                ],
                [
                    "2025-04-07",
                    5.82
                ],
                [
                    "2025-04-08",
                    -12.78
                ],
                [
                    "2025-04-09",
                    1.08
                ],
                [
                    "2025-04-10",
                    1.25
                ],
                [
                    "2025-04-11",
                    3.71
                ],
                [
                    "2025-04-14",
                    -11.36
                ],
                [
                    "2025-04-15",
                    -12.18
                ],
                [
                    "2025-04-16",
                    0.19
                ],
                [
                    "2025-04-17",
                    -9.72
                ],
                [
                    "2025-04-18",
                    -2.34
                ],
                [
                    "2025-04-21",
                    8.50
                ],
                [
                    "2025-04-22",
                    -1.57
                ],
                [
                    "2025-04-23",
                    0.62
                ],
                [
                    "2025-04-24",
                    23.35
                ],
                [
                    "2025-04-25",
                    -3.05
                ],
                [
                    "2025-04-28",
                    -13.73
                ],
                [
                    "2025-04-29",
                    -2.45
                ],
                [
                    "2025-04-30",
                    -7.13
                ],
                [
                    "2025-05-06",
                    0.18
                ],
                [
                    "2025-05-07",
                    4.88
                ],
                [
                    "2025-05-08",
                    0.20
                ],
                [
                    "2025-05-09",
                    1.82
                ],
                [
                    "2025-05-12",
                    -6.76
                ],
                [
                    "2025-05-13",
                    -9.31
                ],
                [
                    "2025-05-14",
                    2.07
                ],
                [
                    "2025-05-15",
                    -8.56
                ],
                [
                    "2025-05-16",
                    11.74
                ],
                [
                    "2025-05-19",
                    4.01
                ],
                [
                    "2025-05-20",
                    -1.55
                ],
                [
                    "2025-05-21",
                    1.67
                ],
                [
                    "2025-05-22",
                    21.14
                ],
                [
                    "2025-05-23",
                    1.96
                ],
                [
                    "2025-05-26",
                    -5.15
                ],
                [
                    "2025-05-27",
                    -7.43
                ],
                [
                    "2025-05-28",
                    -1.40
                ],
                [
                    "2025-05-29",
                    6.65
                ],
                [
                    "2025-05-30",
                    -1.48
                ],
                [
                    "2025-06-03",
                    4.04
                ],
                [
                    "2025-06-04",
                    9.88
                ],
                [
                    "2025-06-05",
                    5.43
                ],
                [
                    "2025-06-06",
                    -8.72
                ],
                [
                    "2025-06-09",
                    -6.27
                ],
                [
                    "2025-06-10",
                    8.36
                ],
                [
                    "2025-06-11",
                    -3.26
                ],
                [
                    "2025-06-12",
                    -5.95
                ],
                [
                    "2025-06-13",
                    -4.38
                ],
                [
                    "2025-06-16",
                    -4.17
                ],
                [
                    "2025-06-17",
                    -4.24
                ],
                [
                    "2025-06-18",
                    -4.66
                ],
                [
                    "2025-06-19",
                    -6.32
                ],
                [
                    "2025-06-20",
                    -3.43
                ],
                [
                    "2025-06-23",
                    -2.45
                ],
                [
                    "2025-06-24",
                    2.31
                ],
                [
                    "2025-06-25",
                    -4.06
                ],
                [
                    "2025-06-26",
                    8.92
                ],
                [
                    "2025-06-27",
                    6.30
                ],
                [
                    "2025-06-30",
                    9.21
                ],
                [
                    "2025-07-01",
                    0.36
                ],
                [
                    "2025-07-02",
                    -10.25
                ],
                [
                    "2025-07-03",
                    8.59
                ],
                [
                    "2025-07-04",
                    6.52
                ],
                [
                    "2025-07-07",
                    0.58
                ],
                [
                    "2025-07-08",
                    -5.89
                ],
                [
                    "2025-07-09",
                    8.05
                ],
                [
                    "2025-07-10",
                    6.98
                ],
                [
                    "2025-07-11",
                    -2.78
                ],
                [
                    "2025-07-14",
                    2.61
                ],
                [
                    "2025-07-15",
                    5.00
                ],
                [
                    "2025-07-16",
                    -5.06
                ],
                [
                    "2025-07-17",
                    11.72
                ],
                [
                    "2025-07-18",
                    -6.31
                ],
                [
                    "2025-07-21",
                    13.71
                ],
                [
                    "2025-07-22",
                    4.43
                ],
                [
                    "2025-07-23",
                    9.06
                ],
                [
                    "2025-07-24",
                    10.06
                ],
                [
                    "2025-07-25",
                    17.35
                ],
                [
                    "2025-07-28",
                    -2.93
                ],
                [
                    "2025-07-29",
                    -0.49
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    4.98
                ],
                [
                    "2025-02-11",
                    0.72
                ],
                [
                    "2025-02-13",
                    8.91
                ],
                [
                    "2025-02-26",
                    10.62
                ],
                [
                    "2025-02-27",
                    12.76
                ],
                [
                    "2025-02-28",
                    -3.39
                ],
                [
                    "2025-03-03",
                    -4.02
                ],
                [
                    "2025-03-04",
                    -10.30
                ],
                [
                    "2025-03-11",
                    7.41
                ],
                [
                    "2025-03-12",
                    2.66
                ],
                [
                    "2025-03-13",
                    17.64
                ],
                [
                    "2025-03-14",
                    8.01
                ],
                [
                    "2025-03-17",
                    -9.55
                ],
                [
                    "2025-03-18",
                    5.26
                ],
                [
                    "2025-03-19",
                    -8.26
                ],
                [
                    "2025-03-27",
                    5.11
                ],
                [
                    "2025-03-28",
                    2.51
                ],
                [
                    "2025-03-31",
                    1.61
                ],
                [
                    "2025-04-01",
                    -2.96
                ],
                [
                    "2025-04-02",
                    6.54
                ],
                [
                    "2025-04-03",
                    -1.81
                ],
                [
                    "2025-04-14",
                    11.93
                ],
                [
                    "2025-04-15",
                    8.90
                ],
                [
                    "2025-04-16",
                    1.24
                ],
                [
                    "2025-04-17",
                    3.70
                ],
                [
                    "2025-04-18",
                    -9.96
                ],
                [
                    "2025-04-21",
                    0.49
                ],
                [
                    "2025-05-06",
                    2.70
                ],
                [
                    "2025-05-07",
                    -1.00
                ],
                [
                    "2025-05-08",
                    -4.98
                ],
                [
                    "2025-05-13",
                    5.28
                ],
                [
                    "2025-05-15",
                    10.48
                ],
                [
                    "2025-05-30",
                    1.93
                ],
                [
                    "2025-06-11",
                    6.83
                ],
                [
                    "2025-06-12",
                    -4.35
                ],
                [
                    "2025-06-13",
                    -2.65
                ],
                [
                    "2025-06-17",
                    4.40
                ],
                [
                    "2025-06-18",
                    11.59
                ],
                [
                    "2025-06-19",
                    3.00
                ],
                [
                    "2025-06-20",
                    2.63
                ],
                [
                    "2025-06-23",
                    -5.58
                ],
                [
                    "2025-06-24",
                    -6.46
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    6.30
                ],
                [
                    "2025-02-11",
                    -1.67
                ],
                [
                    "2025-02-13",
                    -1.53
                ],
                [
                    "2025-02-21",
                    12.16
                ],
                [
                    "2025-02-27",
                    0.86
                ],
                [
                    "2025-03-04",
                    -3.43
                ],
                [
                    "2025-03-17",
                    12.37
                ],
                [
                    "2025-03-19",
                    9.62
                ],
                [
                    "2025-03-20",
                    14.42
                ],
                [
                    "2025-03-21",
                    -0.13
                ],
                [
                    "2025-03-26",
                    1.53
                ],
                [
                    "2025-03-27",
                    3.89
                ],
                [
                    "2025-03-28",
                    5.19
                ],
                [
                    "2025-03-31",
                    1.07
                ],
                [
                    "2025-04-01",
                    -1.75
                ],
                [
                    "2025-04-02",
                    -3.16
                ],
                [
                    "2025-04-03",
                    3.30
                ],
                [
                    "2025-04-08",
                    5.56
                ],
                [
                    "2025-04-09",
                    1.83
                ],
                [
                    "2025-04-10",
                    -7.31
                ],
                [
                    "2025-04-11",
                    1.90
                ],
                [
                    "2025-04-14",
                    -0.56
                ],
                [
                    "2025-04-17",
                    6.02
                ],
                [
                    "2025-04-18",
                    12.29
                ],
                [
                    "2025-04-21",
                    -8.99
                ],
                [
                    "2025-04-22",
                    5.77
                ],
                [
                    "2025-04-23",
                    0.80
                ],
                [
                    "2025-04-30",
                    -0.20
                ],
                [
                    "2025-05-06",
                    -2.89
                ],
                [
                    "2025-05-07",
                    -3.88
                ],
                [
                    "2025-05-08",
                    4.78
                ],
                [
                    "2025-05-09",
                    2.59
                ],
                [
                    "2025-05-13",
                    4.03
                ],
                [
                    "2025-05-14",
                    1.63
                ],
                [
                    "2025-05-15",
                    -1.92
                ],
                [
                    "2025-05-16",
                    21.33
                ],
                [
                    "2025-05-19",
                    10.94
                ],
                [
                    "2025-05-29",
                    6.87
                ],
                [
                    "2025-05-30",
                    -0.45
                ],
                [
                    "2025-06-13",
                    7.03
                ],
                [
                    "2025-06-16",
                    0.04
                ],
                [
                    "2025-06-17",
                    -0.16
                ],
                [
                    "2025-06-18",
                    -6.93
                ],
                [
                    "2025-06-19",
                    3.32
                ],
                [
                    "2025-06-23",
                    8.03
                ],
                [
                    "2025-06-24",
                    4.15
                ],
                [
                    "2025-06-25",
                    18.88
                ],
                [
                    "2025-07-08",
                    -10.49
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-12",
                    11.96
                ],
                [
                    "2025-02-14",
                    18.91
                ],
                [
                    "2025-02-17",
                    2.64
                ],
                [
                    "2025-02-18",
                    -17.18
                ],
                [
                    "2025-02-19",
                    16.37
                ],
                [
                    "2025-02-20",
                    -0.36
                ],
                [
                    "2025-02-24",
                    16.86
                ],
                [
                    "2025-02-25",
                    -19.68
                ],
                [
                    "2025-03-05",
                    17.83
                ],
                [
                    "2025-03-06",
                    -2.82
                ],
                [
                    "2025-03-07",
                    -10.77
                ],
                [
                    "2025-03-24",
                    4.36
                ],
                [
                    "2025-03-25",
                    -4.86
                ],
                [
                    "2025-04-07",
                    5.82
                ],
                [
                    "2025-04-24",
                    23.35
                ],
                [
                    "2025-04-25",
                    -3.05
                ],
                [
                    "2025-04-28",
                    -13.73
                ],
                [
                    "2025-04-29",
                    -2.45
                ],
                [
                    "2025-05-20",
                    -1.55
                ],
                [
                    "2025-05-21",
                    1.67
                ],
                [
                    "2025-05-22",
                    21.14
                ],
                [
                    "2025-05-23",
                    1.96
                ],
                [
                    "2025-05-26",
                    -5.15
                ],
                [
                    "2025-05-27",
                    -7.43
                ],
                [
                    "2025-05-28",
                    -1.40
                ],
                [
                    "2025-06-03",
                    4.04
                ],
                [
                    "2025-06-04",
                    9.88
                ],
                [
                    "2025-06-05",
                    5.43
                ],
                [
                    "2025-06-06",
                    -8.72
                ],
                [
                    "2025-06-09",
                    -6.27
                ],
                [
                    "2025-06-10",
                    8.36
                ],
                [
                    "2025-06-26",
                    8.92
                ],
                [
                    "2025-06-27",
                    6.30
                ],
                [
                    "2025-06-30",
                    9.21
                ],
                [
                    "2025-07-01",
                    0.36
                ],
                [
                    "2025-07-02",
                    -10.25
                ],
                [
                    "2025-07-03",
                    8.59
                ],
                [
                    "2025-07-04",
                    6.52
                ],
                [
                    "2025-07-07",
                    0.58
                ],
                [
                    "2025-07-09",
                    8.05
                ],
                [
                    "2025-07-10",
                    6.98
                ],
                [
                    "2025-07-11",
                    -2.78
                ],
                [
                    "2025-07-14",
                    2.61
                ],
                [
                    "2025-07-15",
                    5.00
                ],
                [
                    "2025-07-16",
                    -5.06
                ],
                [
                    "2025-07-17",
                    11.72
                ],
                [
                    "2025-07-18",
                    -6.31
                ],
                [
                    "2025-07-21",
                    13.71
                ],
                [
                    "2025-07-22",
                    4.43
                ],
                [
                    "2025-07-23",
                    9.06
                ],
                [
                    "2025-07-24",
                    10.06
                ],
                [
                    "2025-07-25",
                    17.35
                ],
                [
                    "2025-07-28",
                    -2.93
                ],
                [
                    "2025-07-29",
                    -0.49
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603429 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_a851debf3fa24433b566726161d88a9a.setOption(option_a851debf3fa24433b566726161d88a9a);
            window.addEventListener('resize', function(){
                chart_a851debf3fa24433b566726161d88a9a.resize();
            })
    </script>
</body>
</html>
