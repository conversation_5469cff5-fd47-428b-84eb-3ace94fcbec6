<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票技术分析图表导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .control-group {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .control-item label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9em;
        }

        select, input {
            padding: 12px 16px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .stock-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
        }

        .stock-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            overflow: hidden;
            border: 1px solid #e9ecef;
        }

        .stock-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .stock-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stock-info {
            text-align: center;
            flex: 1;
        }

        .stock-code {
            font-size: 1.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stock-name {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .stock-actions {
            display: flex;
            gap: 8px;
        }

        .delete-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 28px;
            height: 28px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
        }

        .delete-btn:hover {
            background: rgba(231, 76, 60, 0.9);
            border-color: rgba(231, 76, 60, 0.9);
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .chart-links {
            padding: 20px;
        }

        .chart-link {
            display: block;
            padding: 12px 16px;
            margin-bottom: 10px;
            background: #f8f9fa;
            color: #495057;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            font-weight: 500;
        }

        .chart-link:hover {
            background: #e9ecef;
            border-left-color: #667eea;
            transform: translateX(5px);
        }

        .chart-link:last-child {
            margin-bottom: 0;
        }

        .chart-icons {
            margin-right: 10px;
            font-size: 1.1em;
        }

        .search-highlight {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border: none;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5em;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #ffeb3b;
        }

        .modal-body {
            padding: 30px;
        }

        .input-section, .batch-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .input-section h3, .batch-section h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.2em;
        }

        .input-group, .batch-input-group {
            display: flex;
            gap: 15px;
            margin: 15px 0;
        }

        .input-group input, .batch-input-group textarea {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus, .batch-input-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .analyze-btn, .batch-analyze-btn, .test-btn {
            padding: 12px 25px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .analyze-btn:hover, .batch-analyze-btn:hover, .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        .test-btn:hover {
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .input-help {
            margin-top: 15px;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .input-help p {
            margin: 5px 0;
            color: #2c3e50;
            font-size: 14px;
        }

        .progress-section, .results-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            font-weight: 600;
            color: #2c3e50;
            margin: 10px 0;
        }

        .progress-log {
            max-height: 200px;
            overflow-y: auto;
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .results-section {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }

        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
            font-size: 1.2em;
        }

        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background: #f8f9fa;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .control-group {
                flex-direction: column;
                align-items: stretch;
            }

            .control-item {
                width: 100%;
            }

            select, input {
                min-width: auto;
                width: 100%;
            }

            .stock-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 观富交易系统分析中心</h1>
            <p>股票技术分析、板块资金流向分析一站式平台</p>
        </div>

        <!-- 快速导航区域 -->
        <div class="quick-nav" style="padding: 20px; background: #f8f9fa; border-bottom: 1px solid #e9ecef;">
            <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                <a href="blank_fundflow.html" class="nav-card" style="display: flex; flex-direction: column; align-items: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; border-radius: 12px; min-width: 200px; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);">
                    <div style="font-size: 2.5em; margin-bottom: 10px;">🏭</div>
                    <div style="font-size: 1.2em; font-weight: bold; margin-bottom: 5px;">板块资金流向</div>
                    <div style="font-size: 0.9em; opacity: 0.9; text-align: center;">查看行业板块资金流向趋势</div>
                </a>
                <a href="#stock-analysis" class="nav-card" style="display: flex; flex-direction: column; align-items: center; padding: 20px; background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%); color: white; text-decoration: none; border-radius: 12px; min-width: 200px; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);">
                    <div style="font-size: 2.5em; margin-bottom: 10px;">📊</div>
                    <div style="font-size: 1.2em; font-weight: bold; margin-bottom: 5px;">个股技术分析</div>
                    <div style="font-size: 0.9em; opacity: 0.9; text-align: center;">查看个股技术分析图表</div>
                </a>
                <a href="javascript:showAnalysisModal()" class="nav-card" style="display: flex; flex-direction: column; align-items: center; padding: 20px; background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; text-decoration: none; border-radius: 12px; min-width: 200px; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);">
                    <div style="font-size: 2.5em; margin-bottom: 10px;">🎯</div>
                    <div style="font-size: 1.2em; font-weight: bold; margin-bottom: 5px;">分析新股票</div>
                    <div style="font-size: 0.9em; opacity: 0.9; text-align: center;">添加新股票进行分析</div>
                </a>
            </div>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalStocks">0</div>
                <div class="stat-label">总股票数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="totalCharts">0</div>
                <div class="stat-label">总图表数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="visibleStocks">0</div>
                <div class="stat-label">显示股票数</div>
            </div>
        </div>

        <div id="stock-analysis" class="controls">
            <div class="control-group">
                <div class="control-item">
                    <label for="stockSearch">🔍 搜索股票代码</label>
                    <input type="text" id="stockSearch" placeholder="输入股票代码..." />
                </div>
                <div class="control-item">
                    <label for="chartFilter">📊 筛选图表类型</label>
                    <select id="chartFilter">
                        <option value="">全部图表</option>
                        <option value="主力入场信号">主力入场信号</option>
                        <option value="主力散户线">主力散户线</option>
                        <option value="量能堆积">量能堆积</option>
                        <option value="阶段性底部">阶段性底部</option>
                    </select>
                </div>
                <div class="control-item">
                    <label for="sortOrder">🔄 排序方式</label>
                    <select id="sortOrder">
                        <option value="asc">代码升序</option>
                        <option value="desc">代码降序</option>
                    </select>
                </div>
                <div class="control-item">
                    <label>&nbsp;</label>
                    <button class="btn" onclick="clearFilters()">清除筛选</button>
                </div>
                <div class="control-item">
                    <label>&nbsp;</label>
                    <button class="btn" onclick="refreshData()" style="background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);">刷新数据</button>
                </div>
                <div class="control-item">
                    <label>&nbsp;</label>
                    <button class="btn" onclick="showAnalysisModal()" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">📊 分析新股票</button>
                </div>
            </div>
        </div>

        <div class="stock-grid" id="stockGrid">
            <!-- 股票卡片将通过JavaScript动态生成 -->
        </div>

        <div class="no-results" id="noResults" style="display: none;">
            <h3>😔 没有找到匹配的股票</h3>
            <p>请尝试调整搜索条件或筛选器</p>
        </div>
    </div>

    <!-- 股票分析模态框 -->
    <div id="analysisModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📊 股票技术分析</h2>
                <span class="close" onclick="closeAnalysisModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="input-section">
                    <h3>🎯 输入股票代码</h3>
                    <div class="input-group">
                        <input type="text" id="stockCodeInput" placeholder="请输入6位股票代码，如：000001" maxlength="6" pattern="[0-9]{6}">
                        <button onclick="analyzeStock()" class="analyze-btn">开始分析</button>
                        <button onclick="testAPIConnection()" class="test-btn" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">🔧 测试连接</button>
                    </div>
                    <div class="input-help">
                        <p>💡 支持A股股票代码（6位数字）</p>
                        <p>📝 示例：000001（平安银行）、600036（招商银行）</p>
                        <p id="connectionStatus" style="margin-top: 10px; font-weight: bold;"></p>
                    </div>
                </div>

                <div class="batch-section">
                    <h3>📋 批量分析</h3>
                    <div class="batch-input-group">
                        <textarea id="batchCodesInput" placeholder="输入多个股票代码，每行一个：&#10;000001&#10;600036&#10;002415" rows="5"></textarea>
                        <button onclick="analyzeBatchStocks()" class="batch-analyze-btn">批量分析</button>
                    </div>
                </div>

                <div id="analysisProgress" class="progress-section" style="display: none;">
                    <h3>📈 分析进度</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                    <div class="progress-log" id="progressLog"></div>
                </div>

                <div id="analysisResults" class="results-section" style="display: none;">
                    <h3>✅ 分析结果</h3>
                    <div id="resultsContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 基础测试
        console.log('🔧 JavaScript开始执行...');
        console.log('🌐 当前URL:', window.location.href);
        console.log('📁 当前路径:', window.location.pathname);

        // 股票数据配置 - 完全动态加载
        let stockData = [];
        let chartTypes = [
            { name: '主力入场信号', icon: '🎯', color: '#e74c3c' },
            { name: '主力散户线', icon: '📊', color: '#3498db' },
            { name: '量能堆积', icon: '📈', color: '#2ecc71' },
            { name: '阶段性底部', icon: '🔍', color: '#f39c12' }
        ];

        console.log('📊 初始化变量完成');
        console.log('📈 默认chartTypes:', chartTypes);

        // 动态加载股票数据的多种方案
        async function loadStockData() {
            console.log('🔄 开始加载股票数据...');

            try {
                // 方案1: 从JSON配置文件加载（推荐）
                console.log('📝 尝试方案1: 从JSON文件加载');
                await loadFromJSON();
                console.log('✅ 方案1成功');
            } catch (error) {
                console.warn('❌ JSON加载失败，尝试目录扫描:', error);
                try {
                    // 方案2: 扫描目录结构
                    console.log('📝 尝试方案2: 目录扫描');
                    await loadFromDirectory();
                    console.log('✅ 方案2成功');
                } catch (error2) {
                    console.warn('❌ 目录扫描失败，使用备用数据:', error2);
                    // 方案3: 备用静态数据
                    console.log('📝 使用方案3: 备用数据');
                    loadFallbackData();
                    console.log('✅ 方案3完成');
                }
            }

            console.log(`📊 最终加载结果: ${stockData.length} 只股票`);
        }

        // 方案1: 从JSON文件加载（完全动态）
        async function loadFromJSON() {
            console.log('🔄 开始从JSON文件加载数据...');

            try {
                // 添加时间戳防止缓存
                const timestamp = new Date().getTime();
                const url = `./stocks.json?t=${timestamp}`;
                console.log('📍 请求URL:', url);

                const response = await fetch(url, {
                    cache: 'no-cache',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });
                console.log('📡 响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error(`JSON文件加载失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                console.log('📄 原始JSON数据:', data);

                stockData = data.stocks || [];
                console.log('📊 解析后的股票数据:', stockData);
                console.log('📊 股票数据长度:', stockData.length);

                // 验证股票数据格式
                if (stockData.length > 0) {
                    console.log('📊 第一只股票示例:', stockData[0]);
                    console.log('📊 最后一只股票示例:', stockData[stockData.length - 1]);
                }

                // 动态加载图表类型配置
                if (data.chartTypes && data.chartTypes.length > 0) {
                    chartTypes = data.chartTypes;
                    console.log('📈 从JSON更新图表类型:', chartTypes);
                } else {
                    console.log('📈 保持默认图表类型:', chartTypes);
                }

                console.log(`✅ 成功加载 ${stockData.length} 只股票，${chartTypes.length} 种图表类型`);

            } catch (error) {
                console.error('❌ 加载JSON文件失败:', error);
                throw error;
            }
        }

        // 方案2: 扫描目录结构（纯动态发现）
        async function loadFromDirectory() {
            const response = await fetch('./');
            const html = await response.text();

            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const links = doc.querySelectorAll('a[href]');

            const stockCodes = [];
            links.forEach(link => {
                const href = link.getAttribute('href');
                if (/^\d{6}\/$/.test(href)) {
                    const code = href.replace('/', '');
                    stockCodes.push(code);
                }
            });

            // 完全动态生成，不依赖硬编码映射
            stockData = stockCodes.map(code => ({
                code: code,
                name: `股票${code}` // 临时名称，等待后续API获取
            }));

            console.log('从目录扫描发现股票代码:', stockCodes);
            console.log('⚠️  使用临时名称，建议运行配置生成脚本获取真实股票名称');
        }

        // 方案3: 备用静态数据（最小化硬编码）
        function loadFallbackData() {
            // 只保留最基本的示例数据，实际使用时应该运行配置生成脚本
            stockData = [
                { code: '001337', name: '股票001337' },
                { code: '600000', name: '股票600000' }
            ];
            console.log('⚠️  使用最小备用数据，请运行配置生成脚本获取完整数据');
            console.log('💡 运行命令: python scripts/generate_stock_config_advanced.py');
        }

        // chartTypes 已在上面定义，删除重复声明

        let currentStocks = [...stockData];

        // 初始化页面
        async function initializePage() {
            // 显示加载状态
            showLoadingState();

            // 动态加载股票数据
            await loadStockData();

            // 暂时禁用动态更新股票名称，因为JSON文件中已有正确名称
            // await updateAllStockNames();

            // 隐藏加载状态
            hideLoadingState();

            // 初始化当前股票列表
            currentStocks = [...stockData];

            renderStockGrid();
            updateStats();
            setupEventListeners();
        }

        // 显示加载状态
        function showLoadingState() {
            const grid = document.getElementById('stockGrid');
            grid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 60px;">
                    <div style="font-size: 3em; margin-bottom: 20px;">📊</div>
                    <h3>正在加载股票数据...</h3>
                    <p style="color: #6c757d; margin-top: 10px;">请稍候</p>
                </div>
            `;
        }

        // 隐藏加载状态
        function hideLoadingState() {
            // 加载完成后会通过renderStockGrid重新渲染
        }

        // 渲染股票网格
        function renderStockGrid() {
            const grid = document.getElementById('stockGrid');
            const noResults = document.getElementById('noResults');

            if (currentStocks.length === 0) {
                grid.style.display = 'none';
                noResults.style.display = 'block';
                return;
            }

            grid.style.display = 'grid';
            noResults.style.display = 'none';

            grid.innerHTML = currentStocks.map(stock => `
                <div class="stock-card">
                    <div class="stock-header">
                        <div class="stock-info">
                            <div class="stock-code">${stock.code}</div>
                            <div class="stock-name">${stock.name}</div>
                        </div>
                        <div class="stock-actions">
                            <button class="delete-btn"
                                    onclick="confirmDeleteStock('${stock.code}', '${stock.name}')"
                                    title="删除股票 ${stock.name}">
                                ×
                            </button>
                        </div>
                    </div>
                    <div class="chart-links">
                        ${chartTypes.map(chart => `
                            <a href="${stock.code}/${stock.code}_${chart.name}.html"
                               class="chart-link"
                               target="_blank"
                               title="查看${stock.name}的${chart.name}">
                                <span class="chart-icons">${chart.icon}</span>
                                ${chart.name}
                            </a>
                        `).join('')}
                    </div>
                </div>
            `).join('');
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalStocks').textContent = stockData.length;
            document.getElementById('totalCharts').textContent = stockData.length * chartTypes.length;
            document.getElementById('visibleStocks').textContent = currentStocks.length;
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 搜索功能
            document.getElementById('stockSearch').addEventListener('input', filterStocks);

            // 图表类型筛选
            document.getElementById('chartFilter').addEventListener('change', filterStocks);

            // 排序功能
            document.getElementById('sortOrder').addEventListener('change', sortStocks);
        }

        // 筛选股票
        function filterStocks() {
            const searchTerm = document.getElementById('stockSearch').value.toLowerCase();
            const chartFilter = document.getElementById('chartFilter').value;

            currentStocks = stockData.filter(stock => {
                const matchesSearch = stock.code.toLowerCase().includes(searchTerm) ||
                                    stock.name.toLowerCase().includes(searchTerm);

                return matchesSearch;
            });

            sortStocks();
        }

        // 排序股票
        function sortStocks() {
            const sortOrder = document.getElementById('sortOrder').value;

            currentStocks.sort((a, b) => {
                if (sortOrder === 'asc') {
                    return a.code.localeCompare(b.code);
                } else {
                    return b.code.localeCompare(a.code);
                }
            });

            renderStockGrid();
            updateStats();
        }

        // 清除筛选
        function clearFilters() {
            document.getElementById('stockSearch').value = '';
            document.getElementById('chartFilter').value = '';
            document.getElementById('sortOrder').value = 'asc';

            currentStocks = [...stockData];
            sortStocks();
        }

        // 刷新数据
        async function refreshData() {
            const btn = event.target;
            const originalText = btn.textContent;

            try {
                btn.textContent = '刷新中...';
                btn.disabled = true;

                // 重新加载数据
                await loadStockData();
                currentStocks = [...stockData];

                // 重新渲染
                renderStockGrid();
                updateStats();

                btn.textContent = '刷新完成 ✅';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.disabled = false;
                }, 2000);

            } catch (error) {
                console.error('刷新数据失败:', error);
                btn.textContent = '刷新失败 ❌';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.disabled = false;
                }, 2000);
            }
        }

        // 股票分析相关函数

        // 显示分析模态框
        function showAnalysisModal() {
            document.getElementById('analysisModal').style.display = 'block';
            document.getElementById('stockCodeInput').focus();
        }

        // 关闭分析模态框
        function closeAnalysisModal() {
            document.getElementById('analysisModal').style.display = 'none';
            resetAnalysisModal();
        }

        // 重置模态框状态
        function resetAnalysisModal() {
            document.getElementById('stockCodeInput').value = '';
            document.getElementById('batchCodesInput').value = '';
            document.getElementById('analysisProgress').style.display = 'none';
            document.getElementById('analysisResults').style.display = 'none';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = '准备中...';
            document.getElementById('progressLog').innerHTML = '';
            document.getElementById('resultsContent').innerHTML = '';
        }

        // 测试API连接
        async function testAPIConnection() {
            const statusElement = document.getElementById('connectionStatus');
            const testBtn = event.target;
            const originalText = testBtn.textContent;

            try {
                testBtn.textContent = '🔄 测试中...';
                testBtn.disabled = true;
                statusElement.textContent = '🔄 正在测试API连接...';
                statusElement.style.color = '#3498db';

                // 使用一个测试股票代码
                const testCode = '000001';
                console.log('🧪 开始API连接测试...');

                const result = await callAnalysisAPI(testCode);

                statusElement.textContent = '✅ API连接正常！可以开始分析股票';
                statusElement.style.color = '#27ae60';

                console.log('✅ API连接测试成功:', result);

            } catch (error) {
                statusElement.textContent = `❌ API连接失败: ${error.message}`;
                statusElement.style.color = '#e74c3c';

                console.error('❌ API连接测试失败:', error);

                // 显示详细的故障排除建议
                setTimeout(() => {
                    statusElement.innerHTML = `
                        ❌ 连接失败<br>
                        <small style="font-weight: normal;">
                        请检查：<br>
                        1. API服务器是否在 http://127.0.0.1:9999 运行<br>
                        2. 防火墙是否阻止了端口9999<br>
                        3. 是否有CORS配置问题
                        </small>
                    `;
                }, 2000);

            } finally {
                testBtn.textContent = originalText;
                testBtn.disabled = false;
            }
        }

        // 单个股票分析
        async function analyzeStock() {
            const stockCode = document.getElementById('stockCodeInput').value.trim();

            if (!stockCode) {
                alert('请输入股票代码');
                return;
            }

            if (!/^\d{6}$/.test(stockCode)) {
                alert('请输入正确的6位股票代码');
                return;
            }

            await performAnalysis([stockCode]);
        }

        // 批量股票分析
        async function analyzeBatchStocks() {
            const batchInput = document.getElementById('batchCodesInput').value.trim();

            if (!batchInput) {
                alert('请输入股票代码');
                return;
            }

            const stockCodes = batchInput.split('\n')
                .map(code => code.trim())
                .filter(code => code)
                .filter(code => /^\d{6}$/.test(code));

            if (stockCodes.length === 0) {
                alert('请输入正确的6位股票代码');
                return;
            }

            await performAnalysis(stockCodes);
        }

        // 执行分析
        async function performAnalysis(stockCodes) {
            const progressSection = document.getElementById('analysisProgress');
            const resultsSection = document.getElementById('analysisResults');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const progressLog = document.getElementById('progressLog');
            const resultsContent = document.getElementById('resultsContent');

            // 显示进度区域
            progressSection.style.display = 'block';
            resultsSection.style.display = 'none';

            // 重置进度
            progressFill.style.width = '0%';
            progressText.textContent = `准备分析 ${stockCodes.length} 只股票...`;
            progressLog.innerHTML = '';
            resultsContent.innerHTML = '';

            const results = [];
            const total = stockCodes.length;

            for (let i = 0; i < stockCodes.length; i++) {
                const code = stockCodes[i];
                const progress = ((i + 1) / total * 100).toFixed(1);

                progressFill.style.width = `${progress}%`;
                progressText.textContent = `正在分析 ${code} (${i + 1}/${total})`;

                addLogMessage(`🔄 开始分析股票 ${code}...`);

                const result = await callAnalysisAPI(code);
                results.push(result);

                if (result.success) {
                    addLogMessage(`✅ ${code} 分析完成`);
                } else {
                    addLogMessage(`❌ ${code} 分析失败: ${result.error}`);
                }

                // 添加延迟避免请求过快
                if (i < stockCodes.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            // 显示结果
            progressText.textContent = `分析完成！成功 ${results.filter(r => r.success).length} 个，失败 ${results.filter(r => !r.success).length} 个`;
            addLogMessage(`🎉 所有分析任务完成！`);

            displayResults(results);
            resultsSection.style.display = 'block';
        }

        // 调用分析API（增强版本）
        async function callAnalysisAPI(stockCode, retryCount = 0) {
            const maxRetries = 2;
            console.log(`📡 调用API分析股票: ${stockCode} (尝试 ${retryCount + 1}/${maxRetries + 1})`);

            try {
                // 添加超时控制
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 300000); // 5分钟超时

                const response = await fetch('http://127.0.0.1:9999/api/v1/analyze/zh/stock/analysis/fund-flow', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: stockCode
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                console.log(`📡 ${stockCode} 响应状态: ${response.status} ${response.statusText}`);

                if (!response.ok) {
                    const errorText = await response.text().catch(() => '无法获取错误详情');
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}\n详情: ${errorText}`);
                }

                const data = await response.json();
                console.log(`✅ ${stockCode} API响应成功:`, data);

                // 根据API返回格式判断是否成功
                const isSuccess = data.code === 0 || data.success === true;

                return {
                    success: isSuccess,
                    code: stockCode,
                    data: data,
                    message: data.message || '分析完成'
                };

            } catch (error) {
                console.error(`❌ ${stockCode} API调用失败:`, error);

                // 如果是网络错误且还有重试次数，则重试
                if (retryCount < maxRetries && (
                    error.name === 'AbortError' ||
                    error.message.includes('fetch') ||
                    error.message.includes('network') ||
                    error.message.includes('CONNECTION_RESET')
                )) {
                    console.log(`🔄 ${stockCode} 网络错误，${2}秒后重试...`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    return callAnalysisAPI(stockCode, retryCount + 1);
                }

                // 提供更友好的错误信息
                let friendlyError = error.message;
                if (error.name === 'AbortError') {
                    friendlyError = '请求超时（5分钟），请检查网络连接或API服务器状态';
                } else if (error.message.includes('CONNECTION_RESET')) {
                    friendlyError = '连接被重置，请检查API服务器是否正常运行';
                } else if (error.message.includes('CORS')) {
                    friendlyError = 'CORS跨域错误，请检查API服务器的CORS配置';
                } else if (error.message.includes('fetch')) {
                    friendlyError = '网络连接失败，请检查API服务器地址和端口';
                }

                // 返回失败结果而不是抛出错误
                return {
                    success: false,
                    code: stockCode,
                    error: friendlyError,
                    message: '分析失败'
                };
            }
        }

        // 添加日志消息
        function addLogMessage(message) {
            const progressLog = document.getElementById('progressLog');
            const timestamp = new Date().toLocaleTimeString();
            progressLog.innerHTML += `[${timestamp}] ${message}\n`;
            progressLog.scrollTop = progressLog.scrollHeight;
        }

        // 显示分析结果
        function displayResults(results) {
            const resultsContent = document.getElementById('resultsContent');

            const successCount = results.filter(r => r.success).length;
            const failCount = results.filter(r => !r.success).length;

            // 如果有成功的分析，自动添加到股票列表
            if (successCount > 0) {
                addNewStocksToList(results.filter(r => r.success));
            }

            let html = `
                <div style="margin-bottom: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                    <h4>📊 分析汇总</h4>
                    <p>✅ 成功: ${successCount} 个 | ❌ 失败: ${failCount} 个</p>
                    ${successCount > 0 ? '<p style="color: #27ae60; font-weight: bold;">🎉 新股票已自动添加到列表中！</p>' : ''}
                </div>
            `;

            results.forEach(result => {
                if (result.success) {
                    html += `
                        <div style="margin-bottom: 15px; padding: 15px; background: #d4edda; border-radius: 8px; border-left: 4px solid #28a745;">
                            <h5>✅ ${result.code} - 分析成功</h5>
                            <p>📈 分析报告已生成，新股票已添加到主列表</p>
                            <button onclick="scrollToNewStock('${result.code}')" style="padding: 8px 15px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                                🔍 查看股票
                            </button>
                            <button onclick="closeAnalysisModal()" style="padding: 8px 15px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                                ✅ 关闭窗口
                            </button>
                        </div>
                    `;
                } else {
                    html += `
                        <div style="margin-bottom: 15px; padding: 15px; background: #f8d7da; border-radius: 8px; border-left: 4px solid #dc3545;">
                            <h5>❌ ${result.code} - 分析失败</h5>
                            <p><strong>错误信息:</strong> ${result.error}</p>
                            <details style="margin-top: 10px;">
                                <summary style="cursor: pointer; color: #6c757d;">🔧 故障排除建议</summary>
                                <div style="margin-top: 10px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 14px;">
                                    <p><strong>请检查以下项目：</strong></p>
                                    <ul style="margin: 5px 0; padding-left: 20px;">
                                        <li>API服务器是否在 http://127.0.0.1:9999 正常运行</li>
                                        <li>股票代码 ${result.code} 是否有效</li>
                                        <li>网络连接是否正常</li>
                                        <li>防火墙是否阻止了端口9999</li>
                                        <li>API服务器是否支持CORS跨域请求</li>
                                    </ul>
                                    <button onclick="testAPIConnection()" style="margin-top: 10px; padding: 5px 10px; background: #3498db; color: white; border: none; border-radius: 3px; cursor: pointer;">
                                        🔧 重新测试连接
                                    </button>
                                </div>
                            </details>
                        </div>
                    `;
                }
            });

            resultsContent.innerHTML = html;
        }

        // 添加新股票到列表
        async function addNewStocksToList(successResults) {
            console.log('🔄 开始添加新股票到列表...', successResults);
            console.log('📊 当前stockData长度:', stockData.length);
            console.log('📈 当前chartTypes:', chartTypes);

            for (const result of successResults) {
                const stockCode = result.code;

                // 检查股票是否已存在
                const existingStock = stockData.find(stock => stock.code === stockCode);
                if (existingStock) {
                    console.log(`📝 股票 ${stockCode} 已存在，跳过添加`);
                    continue;
                }

                try {
                    // 获取股票名称
                    const stockName = await getStockName(stockCode);

                    // 添加到stockData数组
                    const newStock = {
                        code: stockCode,
                        name: stockName,
                        charts: chartTypes.map(chart => ({
                            name: chart.name,
                            file: `${stockCode}_${chart.file}`,
                            icon: chart.icon,
                            color: chart.color
                        }))
                    };

                    stockData.push(newStock);
                    console.log(`✅ 成功添加股票到内存: ${stockCode} - ${stockName}`);

                    // 保存到配置文件
                    await saveStockToConfig(stockCode, stockName);

                } catch (error) {
                    console.error(`❌ 获取股票 ${stockCode} 名称失败:`, error);

                    // 即使获取名称失败，也添加股票（使用代码作为名称）
                    const newStock = {
                        code: stockCode,
                        name: stockCode, // 使用代码作为名称
                        charts: chartTypes.map(chart => ({
                            name: chart.name,
                            file: `${stockCode}_${chart.file}`,
                            icon: chart.icon,
                            color: chart.color
                        }))
                    };

                    stockData.push(newStock);
                    console.log(`⚠️ 添加股票到内存 ${stockCode}（使用代码作为名称）`);

                    // 保存到配置文件（使用代码作为名称）
                    await saveStockToConfig(stockCode, stockCode);
                }
            }

            // 重新渲染页面
            console.log('🔄 重新渲染股票列表...');
            currentStocks = [...stockData];
            renderStockGrid();

            // 更新统计信息
            updateStats();

            console.log('✅ 新股票添加完成，当前股票总数:', stockData.length);
        }

        // 获取股票名称
        async function getStockName(stockCode) {
            try {
                console.log(`🔍 开始获取股票名称: ${stockCode}`);

                // 添加超时控制
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

                const response = await fetch(`http://127.0.0.1:9999/api/v1/local/zh/stock/info?code=${stockCode}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    },
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    const data = await response.json();
                    console.log(`📡 API响应数据:`, data);

                    // 处理嵌套的响应格式
                    if (data.code === 0 && data.data && data.data.data && data.data.data.name) {
                        console.log(`✅ 获取股票名称成功: ${stockCode} - ${data.data.data.name}`);
                        return data.data.data.name;
                    } else if (data.code === 0 && data.data && data.data.name) {
                        console.log(`✅ 获取股票名称成功: ${stockCode} - ${data.data.name}`);
                        return data.data.name;
                    } else {
                        console.warn(`⚠️ API返回格式异常:`, data);
                    }
                } else {
                    console.warn(`⚠️ API请求失败: ${response.status} ${response.statusText}`);
                }

                // 如果API失败，使用备用映射
                const stockNameMap = {
                    '000001': '平安银行',
                    '000002': '万科A',
                    '600036': '招商银行',
                    '600519': '贵州茅台',
                    '000858': '五粮液',
                    '002415': '海康威视',
                    '600675': '中华企业',
                    '300059': '东方财富',
                    '002594': '比亚迪',
                    '600276': '恒瑞医药',
                    '000863': '三湘印象'
                };

                const fallbackName = stockNameMap[stockCode] || `股票${stockCode}`;
                console.log(`📝 使用备用名称: ${stockCode} - ${fallbackName}`);
                return fallbackName;

            } catch (error) {
                console.error(`❌ 获取股票名称失败: ${stockCode}`, error);
                return `股票${stockCode}`;
            }
        }

        // 动态更新所有股票名称（优化版本，避免并发请求过多）
        async function updateAllStockNames() {
            console.log('🔄 开始动态更新所有股票名称...');

            if (!stockData || stockData.length === 0) {
                console.log('📝 没有股票数据需要更新');
                return;
            }

            // 检查是否所有股票都已有真实名称
            const needsUpdate = stockData.filter(stock =>
                !stock.name || stock.name.startsWith('股票')
            );

            if (needsUpdate.length === 0) {
                console.log('✅ 所有股票都已有真实名称，跳过更新');
                return;
            }

            console.log(`📝 需要更新 ${needsUpdate.length} 只股票的名称`);

            // 串行更新，避免并发请求过多导致连接重置
            for (let i = 0; i < needsUpdate.length; i++) {
                const stock = needsUpdate[i];
                try {
                    console.log(`🔍 更新股票名称 (${i + 1}/${needsUpdate.length}): ${stock.code}`);
                    const realName = await getStockName(stock.code);

                    if (realName && realName !== `股票${stock.code}`) {
                        stock.name = realName;
                        console.log(`✅ 更新成功: ${stock.code} -> ${realName}`);
                    } else {
                        console.log(`⚠️ 未能获取真实名称: ${stock.code}`);
                    }

                    // 添加延迟避免请求过快
                    if (i < needsUpdate.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                } catch (error) {
                    console.error(`❌ 更新股票名称失败: ${stock.code}`, error);
                }
            }

            console.log('✅ 所有股票名称更新完成');
        }

        // 保存股票到配置文件
        async function saveStockToConfig(stockCode, stockName) {
            try {
                console.log(`💾 开始保存股票到配置文件: ${stockCode} - ${stockName}`);

                // 添加超时控制
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 300000); // 5分钟超时

                const response = await fetch('http://127.0.0.1:9999/api/v1/local/zh/stock/config/add', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: stockCode,
                        name: stockName
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                const data = await response.json();
                console.log(`📡 保存API响应数据:`, data);

                // 处理嵌套的响应格式
                if (data.code === 0 && data.data && data.data.data) {
                    console.log(`✅ 股票已保存到配置文件: ${stockCode} - ${stockName}`);
                    console.log(`📊 当前配置文件中共有 ${data.data.data.totalStocks} 只股票`);
                } else if (data.code === 0) {
                    console.log(`✅ 股票已保存到配置文件: ${stockCode} - ${stockName}`);
                    console.log(`📊 保存成功`);
                } else {
                    console.warn(`⚠️ 保存股票到配置文件失败: ${data.message || '未知错误'}`);
                }

            } catch (error) {
                console.error(`❌ 保存股票到配置文件时发生错误:`, error);
                // 不抛出错误，因为这不应该影响主要功能
            }
        }

        // 确认删除股票
        function confirmDeleteStock(stockCode, stockName) {
            const confirmed = confirm(`⚠️ 确定要删除股票 ${stockCode} - ${stockName} 吗？\n\n此操作将：\n• 从配置文件中移除股票记录\n• 删除相关的分析图表文件\n• 清除本地缓存数据\n\n此操作不可撤销！`);

            if (confirmed) {
                deleteStock(stockCode, stockName);
            }
        }

        // 删除股票
        async function deleteStock(stockCode, stockName) {
            try {
                console.log(`🗑️ 开始删除股票: ${stockCode} - ${stockName}`);

                // 显示删除进度
                const deleteBtn = event.target;
                const originalContent = deleteBtn.innerHTML;
                deleteBtn.innerHTML = '⋯';
                deleteBtn.disabled = true;

                // 添加超时控制
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 300000); // 5分钟超时

                const response = await fetch('http://127.0.0.1:9999/api/v1/local/zh/stock/config/remove', {
                    method: 'DELETE',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: stockCode
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                const data = await response.json();
                console.log(`📡 删除API响应数据:`, data);

                // 处理嵌套的响应格式
                if (data.code === 0 && data.data && data.data.data) {
                    console.log(`✅ 股票已从配置文件中删除: ${stockCode} - ${stockName}`);
                    console.log(`📊 当前配置文件中剩余 ${data.data.data.totalStocks} 只股票`);
                    console.log(`🧹 清理结果:`, data.data.data.cleanupResults);
                } else if (data.code === 0) {
                    console.log(`✅ 股票已从配置文件中删除: ${stockCode} - ${stockName}`);
                    console.log(`📊 删除成功`);
                }

                // 从内存中移除股票
                console.log(`🔄 删除前stockData长度: ${stockData.length}`);
                stockData = stockData.filter(stock => stock.code !== stockCode);
                console.log(`🔄 删除后stockData长度: ${stockData.length}`);

                // 重新加载最新数据以确保同步
                try {
                    console.log('🔄 重新加载配置文件以确保数据同步...');
                    await loadStockData();
                    console.log(`📊 重新加载后stockData长度: ${stockData.length}`);
                } catch (error) {
                    console.warn('重新加载数据失败，使用内存数据:', error);
                }

                // 重新渲染页面
                currentStocks = [...stockData];
                renderStockGrid();
                updateStats();

                // 显示成功消息
                alert(`✅ 股票 ${stockCode} - ${stockName} 已成功删除！\n\n清理了以下内容：\n• 配置文件记录\n• 分析图表目录\n• 本地缓存数据\n\n当前剩余 ${stockData.length} 只股票`);

            } catch (error) {
                console.error(`❌ 删除股票时发生错误:`, error);
                alert(`❌ 删除失败: ${error.message}`);

                // 恢复按钮状态
                const deleteBtn = event.target;
                deleteBtn.innerHTML = '×';
                deleteBtn.disabled = false;
            }
        }

        // 滚动到新添加的股票
        function scrollToNewStock(stockCode) {
                    console.error(`❌ 删除股票失败: ${data.message}`);
                    alert(`❌ 删除失败: ${data.message}`);

                    // 恢复按钮状态
                    deleteBtn.innerHTML = originalContent;
                    deleteBtn.disabled = false;
                }

        // 滚动到新添加的股票
        function scrollToNewStock(stockCode) {
            const stockCards = document.querySelectorAll('.stock-card');
            for (let card of stockCards) {
                const codeElement = card.querySelector('.stock-code');
                if (codeElement && codeElement.textContent === stockCode) {
                    card.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // 高亮显示
                    card.style.boxShadow = '0 0 20px rgba(52, 152, 219, 0.8)';
                    card.style.transform = 'scale(1.02)';

                    setTimeout(() => {
                        card.style.boxShadow = '';
                        card.style.transform = '';
                    }, 3000);

                    break;
                }
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('analysisModal');
            if (event.target === modal) {
                closeAnalysisModal();
            }
        }

        // 删除重复的DOMContentLoaded监听器

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl+F 聚焦搜索框
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.getElementById('stockSearch').focus();
            }

            // Escape 清除搜索或关闭模态框
            if (e.key === 'Escape') {
                const modal = document.getElementById('analysisModal');
                if (modal.style.display === 'block') {
                    closeAnalysisModal();
                } else {
                    clearFilters();
                }
            }

            // Enter 键在输入框中触发分析
            if (e.key === 'Enter') {
                if (e.target.id === 'stockCodeInput') {
                    e.preventDefault();
                    analyzeStock();
                }
            }
        });

        // 为输入框添加Enter键支持
        document.addEventListener('DOMContentLoaded', function() {
            // 单个股票输入框
            const stockCodeInput = document.getElementById('stockCodeInput');
            if (stockCodeInput) {
                stockCodeInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        analyzeStock();
                    }
                });
            }
        });

        // 添加搜索高亮功能
        function highlightSearchResults() {
            const searchTerm = document.getElementById('stockSearch').value.toLowerCase();
            const cards = document.querySelectorAll('.stock-card');

            cards.forEach(card => {
                if (searchTerm && (
                    card.querySelector('.stock-code').textContent.toLowerCase().includes(searchTerm) ||
                    card.querySelector('.stock-name').textContent.toLowerCase().includes(searchTerm)
                )) {
                    card.classList.add('search-highlight');
                } else {
                    card.classList.remove('search-highlight');
                }
            });
        }

        // 修改筛选函数以包含高亮
        document.getElementById('stockSearch').addEventListener('input', function() {
            filterStocks();
            setTimeout(highlightSearchResults, 100);
        });

        // 添加快速导航功能
        function addQuickNavigation() {
            const quickNav = document.createElement('div');
            quickNav.style.cssText = `
                position: fixed;
                top: 50%;
                right: 20px;
                transform: translateY(-50%);
                background: rgba(255, 255, 255, 0.9);
                border-radius: 10px;
                padding: 15px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                z-index: 1000;
                max-height: 400px;
                overflow-y: auto;
            `;

            quickNav.innerHTML = `
                <h4 style="margin-bottom: 10px; color: #2c3e50;">快速导航</h4>
                ${chartTypes.map(chart => `
                    <button onclick="scrollToChart('${chart.name}')"
                            style="display: block; width: 100%; margin: 5px 0; padding: 8px;
                                   border: none; background: ${chart.color}; color: white;
                                   border-radius: 5px; cursor: pointer; font-size: 12px;">
                        ${chart.icon} ${chart.name}
                    </button>
                `).join('')}
            `;

            document.body.appendChild(quickNav);
        }

        // 滚动到特定图表类型
        function scrollToChart(chartName) {
            const chartLinks = document.querySelectorAll('.chart-link');
            for (let link of chartLinks) {
                if (link.textContent.includes(chartName)) {
                    link.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    link.style.background = '#fff3cd';
                    setTimeout(() => {
                        link.style.background = '#f8f9fa';
                    }, 2000);
                    break;
                }
            }
        }

        // 添加返回顶部按钮
        function addBackToTop() {
            const backToTop = document.createElement('button');
            backToTop.innerHTML = '⬆️';
            backToTop.style.cssText = `
                position: fixed;
                bottom: 30px;
                right: 30px;
                width: 50px;
                height: 50px;
                border: none;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-size: 20px;
                cursor: pointer;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
                z-index: 1000;
                transition: all 0.3s ease;
                display: none;
            `;

            backToTop.onclick = () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            };

            window.addEventListener('scroll', () => {
                if (window.scrollY > 300) {
                    backToTop.style.display = 'block';
                } else {
                    backToTop.style.display = 'none';
                }
            });

            document.body.appendChild(backToTop);
        }

        // 页面加载完成后添加增强功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM加载完成，开始初始化...');
            console.log('📊 初始stockData:', stockData);
            console.log('📈 初始chartTypes:', chartTypes);

            // 简化初始化，先只加载基本功能
            try {
                console.log('🔄 开始基本初始化...');
                initializePage().then(() => {
                    console.log('✅ 基本初始化完成');
                    try {
                        addQuickNavigation();
                        addBackToTop();
                        console.log('✅ 增强功能添加完成');
                    } catch (enhanceError) {
                        console.warn('⚠️ 增强功能添加失败，但不影响基本功能:', enhanceError);
                    }
                }).catch(error => {
                    console.error('❌ 基本初始化失败:', error);
                    showErrorPage(error);
                });
            } catch (error) {
                console.error('❌ 页面初始化失败:', error);
                console.error('❌ 错误堆栈:', error.stack);
                showErrorPage(error);
            }
        });

        function showErrorPage(error) {
            // 如果初始化失败，显示错误信息
            document.body.innerHTML = `
                <div style="padding: 20px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px;">
                    <h3>⚠️ 页面初始化失败</h3>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p><strong>请检查:</strong></p>
                    <ul>
                        <li>浏览器控制台是否有其他错误</li>
                        <li>JSON文件是否可以访问: <a href="./stocks.json" target="_blank">stocks.json</a></li>
                        <li>HTTP服务器是否正常运行</li>
                        <li>尝试刷新页面</li>
                    </ul>
                    <button onclick="location.reload()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">刷新页面</button>
                </div>
            `;
        }
    </script>
</body>
</html>
