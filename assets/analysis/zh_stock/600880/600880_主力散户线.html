<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="fe4de5f8cef3432b96b4221326df6853" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_fe4de5f8cef3432b96b4221326df6853 = echarts.init(
            document.getElementById('fe4de5f8cef3432b96b4221326df6853'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_fe4de5f8cef3432b96b4221326df6853 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    4.6,
                    4.49,
                    4.47,
                    4.68
                ],
                [
                    4.57,
                    4.67,
                    4.56,
                    4.69
                ],
                [
                    4.69,
                    4.81,
                    4.63,
                    4.82
                ],
                [
                    4.81,
                    4.85,
                    4.76,
                    4.92
                ],
                [
                    4.87,
                    5.04,
                    4.85,
                    5.05
                ],
                [
                    5.04,
                    5.16,
                    4.96,
                    5.28
                ],
                [
                    5.14,
                    5.32,
                    5.06,
                    5.41
                ],
                [
                    5.33,
                    5.27,
                    5.17,
                    5.38
                ],
                [
                    5.27,
                    5.27,
                    5.1,
                    5.3
                ],
                [
                    5.27,
                    5.3,
                    5.25,
                    5.39
                ],
                [
                    5.25,
                    5.05,
                    5.02,
                    5.3
                ],
                [
                    5.05,
                    5.09,
                    5.0,
                    5.11
                ],
                [
                    5.08,
                    5.04,
                    5.0,
                    5.13
                ],
                [
                    5.07,
                    5.11,
                    5.0,
                    5.15
                ],
                [
                    5.1,
                    5.1,
                    5.0,
                    5.12
                ],
                [
                    5.04,
                    5.04,
                    4.98,
                    5.13
                ],
                [
                    5.04,
                    5.03,
                    5.0,
                    5.09
                ],
                [
                    5.06,
                    5.02,
                    4.93,
                    5.15
                ],
                [
                    5.0,
                    4.72,
                    4.72,
                    5.0
                ],
                [
                    4.75,
                    4.75,
                    4.71,
                    4.82
                ],
                [
                    4.73,
                    4.79,
                    4.71,
                    4.8
                ],
                [
                    4.78,
                    4.77,
                    4.67,
                    4.78
                ],
                [
                    4.79,
                    5.03,
                    4.78,
                    5.2
                ],
                [
                    4.96,
                    5.02,
                    4.95,
                    5.11
                ],
                [
                    5.05,
                    4.93,
                    4.89,
                    5.06
                ],
                [
                    4.86,
                    4.97,
                    4.83,
                    5.0
                ],
                [
                    5.02,
                    5.07,
                    4.98,
                    5.14
                ],
                [
                    5.05,
                    4.94,
                    4.88,
                    5.09
                ],
                [
                    4.94,
                    5.05,
                    4.92,
                    5.07
                ],
                [
                    5.08,
                    5.02,
                    5.01,
                    5.11
                ],
                [
                    5.03,
                    5.03,
                    4.99,
                    5.08
                ],
                [
                    5.01,
                    4.99,
                    4.94,
                    5.03
                ],
                [
                    4.99,
                    4.94,
                    4.92,
                    4.99
                ],
                [
                    4.92,
                    4.84,
                    4.84,
                    5.08
                ],
                [
                    4.85,
                    4.71,
                    4.59,
                    4.85
                ],
                [
                    4.69,
                    4.63,
                    4.58,
                    4.69
                ],
                [
                    4.63,
                    4.66,
                    4.59,
                    4.69
                ],
                [
                    4.66,
                    4.63,
                    4.57,
                    4.68
                ],
                [
                    4.64,
                    4.64,
                    4.58,
                    4.68
                ],
                [
                    4.61,
                    4.53,
                    4.46,
                    4.62
                ],
                [
                    4.54,
                    4.53,
                    4.53,
                    4.59
                ],
                [
                    4.52,
                    4.54,
                    4.52,
                    4.6
                ],
                [
                    4.49,
                    4.6,
                    4.49,
                    4.63
                ],
                [
                    4.38,
                    4.14,
                    4.14,
                    4.41
                ],
                [
                    4.0,
                    4.06,
                    3.91,
                    4.13
                ],
                [
                    3.98,
                    4.16,
                    3.78,
                    4.18
                ],
                [
                    4.22,
                    4.25,
                    4.19,
                    4.31
                ],
                [
                    4.2,
                    4.26,
                    4.2,
                    4.31
                ],
                [
                    4.33,
                    4.34,
                    4.3,
                    4.45
                ],
                [
                    4.34,
                    4.36,
                    4.32,
                    4.42
                ],
                [
                    4.33,
                    4.31,
                    4.23,
                    4.4
                ],
                [
                    4.25,
                    4.3,
                    4.25,
                    4.35
                ],
                [
                    4.3,
                    4.28,
                    4.25,
                    4.32
                ],
                [
                    4.29,
                    4.35,
                    4.25,
                    4.36
                ],
                [
                    4.35,
                    4.36,
                    4.31,
                    4.4
                ],
                [
                    4.38,
                    4.33,
                    4.33,
                    4.43
                ],
                [
                    4.33,
                    4.27,
                    4.23,
                    4.33
                ],
                [
                    4.27,
                    4.31,
                    4.25,
                    4.36
                ],
                [
                    4.32,
                    4.33,
                    4.27,
                    4.41
                ],
                [
                    4.32,
                    4.39,
                    4.28,
                    4.42
                ],
                [
                    4.41,
                    4.45,
                    4.4,
                    4.49
                ],
                [
                    4.45,
                    4.55,
                    4.45,
                    4.56
                ],
                [
                    4.6,
                    4.51,
                    4.47,
                    4.63
                ],
                [
                    4.49,
                    4.56,
                    4.48,
                    4.58
                ],
                [
                    4.57,
                    4.47,
                    4.46,
                    4.6
                ],
                [
                    4.52,
                    4.49,
                    4.46,
                    4.52
                ],
                [
                    4.52,
                    4.48,
                    4.47,
                    4.55
                ],
                [
                    4.48,
                    4.51,
                    4.46,
                    4.52
                ],
                [
                    4.5,
                    4.44,
                    4.43,
                    4.5
                ],
                [
                    4.43,
                    4.44,
                    4.42,
                    4.47
                ],
                [
                    4.45,
                    4.57,
                    4.43,
                    4.59
                ],
                [
                    4.59,
                    4.63,
                    4.55,
                    4.64
                ],
                [
                    4.61,
                    4.59,
                    4.56,
                    4.64
                ],
                [
                    4.58,
                    4.58,
                    4.53,
                    4.66
                ],
                [
                    4.56,
                    4.46,
                    4.46,
                    4.58
                ],
                [
                    4.46,
                    4.58,
                    4.46,
                    4.6
                ],
                [
                    4.6,
                    4.6,
                    4.52,
                    4.62
                ],
                [
                    4.6,
                    4.55,
                    4.5,
                    4.62
                ],
                [
                    4.53,
                    4.86,
                    4.53,
                    5.01
                ],
                [
                    4.86,
                    4.72,
                    4.71,
                    5.2
                ],
                [
                    4.87,
                    4.96,
                    4.85,
                    5.19
                ],
                [
                    4.86,
                    4.88,
                    4.84,
                    4.94
                ],
                [
                    4.99,
                    4.99,
                    4.9,
                    5.05
                ],
                [
                    4.96,
                    4.9,
                    4.87,
                    5.02
                ],
                [
                    4.93,
                    5.05,
                    4.92,
                    5.17
                ],
                [
                    5.0,
                    4.97,
                    4.89,
                    5.08
                ],
                [
                    4.96,
                    5.22,
                    4.96,
                    5.35
                ],
                [
                    5.2,
                    5.27,
                    5.17,
                    5.32
                ],
                [
                    5.26,
                    5.09,
                    5.07,
                    5.3
                ],
                [
                    5.05,
                    5.31,
                    5.04,
                    5.4
                ],
                [
                    5.26,
                    5.18,
                    5.15,
                    5.31
                ],
                [
                    5.17,
                    5.07,
                    4.99,
                    5.17
                ],
                [
                    5.1,
                    4.98,
                    4.92,
                    5.12
                ],
                [
                    5.0,
                    4.82,
                    4.8,
                    5.03
                ],
                [
                    4.76,
                    4.93,
                    4.75,
                    4.95
                ],
                [
                    4.92,
                    5.06,
                    4.9,
                    5.08
                ],
                [
                    5.06,
                    5.13,
                    4.99,
                    5.21
                ],
                [
                    5.1,
                    5.19,
                    5.03,
                    5.3
                ],
                [
                    5.18,
                    5.19,
                    5.13,
                    5.28
                ],
                [
                    5.18,
                    5.35,
                    5.14,
                    5.43
                ],
                [
                    5.31,
                    5.32,
                    5.27,
                    5.49
                ],
                [
                    5.33,
                    5.25,
                    5.19,
                    5.34
                ],
                [
                    5.28,
                    5.39,
                    5.21,
                    5.5
                ],
                [
                    5.3,
                    5.43,
                    5.26,
                    5.54
                ],
                [
                    5.36,
                    5.33,
                    5.31,
                    5.43
                ],
                [
                    5.32,
                    5.48,
                    5.29,
                    5.57
                ],
                [
                    5.58,
                    5.5,
                    5.47,
                    5.69
                ],
                [
                    5.45,
                    5.6,
                    5.45,
                    5.72
                ],
                [
                    5.72,
                    5.77,
                    5.48,
                    6.07
                ],
                [
                    5.66,
                    5.4,
                    5.33,
                    5.75
                ],
                [
                    5.37,
                    5.3,
                    5.25,
                    5.48
                ],
                [
                    5.37,
                    5.34,
                    5.3,
                    5.44
                ],
                [
                    5.3,
                    5.33,
                    5.28,
                    5.36
                ],
                [
                    5.3,
                    5.28,
                    5.26,
                    5.35
                ],
                [
                    5.3,
                    5.37,
                    5.28,
                    5.46
                ],
                [
                    5.41,
                    5.38,
                    5.29,
                    5.49
                ],
                [
                    5.41,
                    5.42,
                    5.36,
                    5.5
                ],
                [
                    5.49,
                    5.38,
                    5.35,
                    5.49
                ],
                [
                    5.33,
                    5.32,
                    5.14,
                    5.38
                ],
                [
                    5.32,
                    5.4,
                    5.25,
                    5.43
                ],
                [
                    5.39,
                    5.28,
                    5.21,
                    5.41
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -8.84
                ],
                [
                    "2025-02-05",
                    0.22
                ],
                [
                    "2025-02-06",
                    2.46
                ],
                [
                    "2025-02-07",
                    -8.34
                ],
                [
                    "2025-02-10",
                    9.56
                ],
                [
                    "2025-02-11",
                    3.27
                ],
                [
                    "2025-02-12",
                    -2.66
                ],
                [
                    "2025-02-13",
                    -3.31
                ],
                [
                    "2025-02-14",
                    -4.83
                ],
                [
                    "2025-02-17",
                    -7.93
                ],
                [
                    "2025-02-18",
                    -13.45
                ],
                [
                    "2025-02-19",
                    -9.50
                ],
                [
                    "2025-02-20",
                    -14.48
                ],
                [
                    "2025-02-21",
                    0.33
                ],
                [
                    "2025-02-24",
                    -3.80
                ],
                [
                    "2025-02-25",
                    -1.52
                ],
                [
                    "2025-02-26",
                    -2.92
                ],
                [
                    "2025-02-27",
                    4.18
                ],
                [
                    "2025-02-28",
                    -4.05
                ],
                [
                    "2025-03-03",
                    -6.13
                ],
                [
                    "2025-03-04",
                    -15.00
                ],
                [
                    "2025-03-05",
                    -10.20
                ],
                [
                    "2025-03-06",
                    12.34
                ],
                [
                    "2025-03-07",
                    -1.17
                ],
                [
                    "2025-03-10",
                    -13.84
                ],
                [
                    "2025-03-11",
                    -0.75
                ],
                [
                    "2025-03-12",
                    -1.02
                ],
                [
                    "2025-03-13",
                    -13.33
                ],
                [
                    "2025-03-14",
                    2.91
                ],
                [
                    "2025-03-17",
                    -11.49
                ],
                [
                    "2025-03-18",
                    -0.18
                ],
                [
                    "2025-03-19",
                    0.22
                ],
                [
                    "2025-03-20",
                    -1.86
                ],
                [
                    "2025-03-21",
                    -0.31
                ],
                [
                    "2025-03-24",
                    -9.19
                ],
                [
                    "2025-03-25",
                    -18.62
                ],
                [
                    "2025-03-26",
                    0.39
                ],
                [
                    "2025-03-27",
                    -4.45
                ],
                [
                    "2025-03-28",
                    13.34
                ],
                [
                    "2025-03-31",
                    -10.81
                ],
                [
                    "2025-04-01",
                    -12.47
                ],
                [
                    "2025-04-02",
                    6.87
                ],
                [
                    "2025-04-03",
                    5.68
                ],
                [
                    "2025-04-07",
                    -23.47
                ],
                [
                    "2025-04-08",
                    1.25
                ],
                [
                    "2025-04-09",
                    -13.18
                ],
                [
                    "2025-04-10",
                    2.28
                ],
                [
                    "2025-04-11",
                    -2.58
                ],
                [
                    "2025-04-14",
                    1.21
                ],
                [
                    "2025-04-15",
                    -2.12
                ],
                [
                    "2025-04-16",
                    -2.84
                ],
                [
                    "2025-04-17",
                    -5.51
                ],
                [
                    "2025-04-18",
                    -18.44
                ],
                [
                    "2025-04-21",
                    -5.28
                ],
                [
                    "2025-04-22",
                    -11.37
                ],
                [
                    "2025-04-23",
                    -6.23
                ],
                [
                    "2025-04-24",
                    -3.75
                ],
                [
                    "2025-04-25",
                    -2.20
                ],
                [
                    "2025-04-28",
                    14.27
                ],
                [
                    "2025-04-29",
                    -2.53
                ],
                [
                    "2025-04-30",
                    -7.79
                ],
                [
                    "2025-05-06",
                    -10.02
                ],
                [
                    "2025-05-07",
                    -18.48
                ],
                [
                    "2025-05-08",
                    -4.08
                ],
                [
                    "2025-05-09",
                    -13.74
                ],
                [
                    "2025-05-12",
                    -4.46
                ],
                [
                    "2025-05-13",
                    11.55
                ],
                [
                    "2025-05-14",
                    -1.86
                ],
                [
                    "2025-05-15",
                    -18.43
                ],
                [
                    "2025-05-16",
                    4.04
                ],
                [
                    "2025-05-19",
                    4.89
                ],
                [
                    "2025-05-20",
                    -3.92
                ],
                [
                    "2025-05-21",
                    6.07
                ],
                [
                    "2025-05-22",
                    13.70
                ],
                [
                    "2025-05-23",
                    -2.22
                ],
                [
                    "2025-05-26",
                    -4.50
                ],
                [
                    "2025-05-27",
                    -8.33
                ],
                [
                    "2025-05-28",
                    1.86
                ],
                [
                    "2025-05-29",
                    21.06
                ],
                [
                    "2025-05-30",
                    -8.17
                ],
                [
                    "2025-06-03",
                    11.01
                ],
                [
                    "2025-06-04",
                    -2.88
                ],
                [
                    "2025-06-05",
                    5.32
                ],
                [
                    "2025-06-06",
                    -6.13
                ],
                [
                    "2025-06-09",
                    2.49
                ],
                [
                    "2025-06-10",
                    -14.31
                ],
                [
                    "2025-06-11",
                    15.25
                ],
                [
                    "2025-06-12",
                    -5.22
                ],
                [
                    "2025-06-13",
                    -4.85
                ],
                [
                    "2025-06-16",
                    6.55
                ],
                [
                    "2025-06-17",
                    -10.01
                ],
                [
                    "2025-06-18",
                    -3.73
                ],
                [
                    "2025-06-19",
                    -2.63
                ],
                [
                    "2025-06-20",
                    -13.14
                ],
                [
                    "2025-06-23",
                    -11.33
                ],
                [
                    "2025-06-24",
                    -5.34
                ],
                [
                    "2025-06-25",
                    3.41
                ],
                [
                    "2025-06-26",
                    8.63
                ],
                [
                    "2025-06-27",
                    -3.23
                ],
                [
                    "2025-06-30",
                    -1.01
                ],
                [
                    "2025-07-01",
                    -4.78
                ],
                [
                    "2025-07-02",
                    -10.24
                ],
                [
                    "2025-07-03",
                    16.92
                ],
                [
                    "2025-07-04",
                    -8.64
                ],
                [
                    "2025-07-07",
                    -13.47
                ],
                [
                    "2025-07-08",
                    -4.80
                ],
                [
                    "2025-07-09",
                    -3.96
                ],
                [
                    "2025-07-10",
                    2.89
                ],
                [
                    "2025-07-11",
                    7.94
                ],
                [
                    "2025-07-14",
                    -15.72
                ],
                [
                    "2025-07-15",
                    -1.70
                ],
                [
                    "2025-07-16",
                    -1.23
                ],
                [
                    "2025-07-17",
                    -3.18
                ],
                [
                    "2025-07-18",
                    -1.18
                ],
                [
                    "2025-07-21",
                    -8.55
                ],
                [
                    "2025-07-22",
                    10.92
                ],
                [
                    "2025-07-23",
                    5.52
                ],
                [
                    "2025-07-24",
                    -3.97
                ],
                [
                    "2025-07-25",
                    -1.99
                ],
                [
                    "2025-07-28",
                    3.76
                ],
                [
                    "2025-07-29",
                    -8.12
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    6.77
                ],
                [
                    "2025-02-05",
                    -3.57
                ],
                [
                    "2025-02-06",
                    -3.56
                ],
                [
                    "2025-02-07",
                    1.75
                ],
                [
                    "2025-02-10",
                    -9.37
                ],
                [
                    "2025-02-11",
                    2.08
                ],
                [
                    "2025-02-12",
                    5.02
                ],
                [
                    "2025-02-13",
                    -1.78
                ],
                [
                    "2025-02-14",
                    -3.62
                ],
                [
                    "2025-02-17",
                    -1.90
                ],
                [
                    "2025-02-18",
                    -0.98
                ],
                [
                    "2025-02-19",
                    7.42
                ],
                [
                    "2025-02-20",
                    0.40
                ],
                [
                    "2025-02-21",
                    0.98
                ],
                [
                    "2025-02-24",
                    -1.27
                ],
                [
                    "2025-02-25",
                    -1.93
                ],
                [
                    "2025-02-26",
                    3.52
                ],
                [
                    "2025-02-27",
                    2.87
                ],
                [
                    "2025-02-28",
                    4.66
                ],
                [
                    "2025-03-03",
                    2.36
                ],
                [
                    "2025-03-04",
                    10.79
                ],
                [
                    "2025-03-05",
                    1.74
                ],
                [
                    "2025-03-06",
                    -6.20
                ],
                [
                    "2025-03-07",
                    1.62
                ],
                [
                    "2025-03-10",
                    9.64
                ],
                [
                    "2025-03-11",
                    0.83
                ],
                [
                    "2025-03-12",
                    0.68
                ],
                [
                    "2025-03-13",
                    4.66
                ],
                [
                    "2025-03-14",
                    -1.83
                ],
                [
                    "2025-03-17",
                    2.45
                ],
                [
                    "2025-03-18",
                    2.43
                ],
                [
                    "2025-03-19",
                    2.64
                ],
                [
                    "2025-03-20",
                    3.09
                ],
                [
                    "2025-03-21",
                    8.40
                ],
                [
                    "2025-03-24",
                    7.57
                ],
                [
                    "2025-03-25",
                    12.53
                ],
                [
                    "2025-03-26",
                    -0.80
                ],
                [
                    "2025-03-27",
                    3.35
                ],
                [
                    "2025-03-28",
                    -7.12
                ],
                [
                    "2025-03-31",
                    7.29
                ],
                [
                    "2025-04-01",
                    5.42
                ],
                [
                    "2025-04-02",
                    1.82
                ],
                [
                    "2025-04-03",
                    -2.69
                ],
                [
                    "2025-04-07",
                    12.39
                ],
                [
                    "2025-04-08",
                    10.33
                ],
                [
                    "2025-04-09",
                    1.93
                ],
                [
                    "2025-04-10",
                    -3.38
                ],
                [
                    "2025-04-11",
                    -6.40
                ],
                [
                    "2025-04-14",
                    -2.32
                ],
                [
                    "2025-04-15",
                    0.49
                ],
                [
                    "2025-04-16",
                    -1.45
                ],
                [
                    "2025-04-17",
                    -8.11
                ],
                [
                    "2025-04-18",
                    5.91
                ],
                [
                    "2025-04-21",
                    -0.19
                ],
                [
                    "2025-04-22",
                    0.80
                ],
                [
                    "2025-04-23",
                    0.26
                ],
                [
                    "2025-04-24",
                    -3.39
                ],
                [
                    "2025-04-25",
                    -6.00
                ],
                [
                    "2025-04-28",
                    0.97
                ],
                [
                    "2025-04-29",
                    -1.70
                ],
                [
                    "2025-04-30",
                    -1.83
                ],
                [
                    "2025-05-06",
                    -1.53
                ],
                [
                    "2025-05-07",
                    8.35
                ],
                [
                    "2025-05-08",
                    -1.33
                ],
                [
                    "2025-05-09",
                    4.77
                ],
                [
                    "2025-05-12",
                    3.97
                ],
                [
                    "2025-05-13",
                    -3.95
                ],
                [
                    "2025-05-14",
                    5.91
                ],
                [
                    "2025-05-15",
                    7.29
                ],
                [
                    "2025-05-16",
                    -5.43
                ],
                [
                    "2025-05-19",
                    -8.24
                ],
                [
                    "2025-05-20",
                    -0.76
                ],
                [
                    "2025-05-21",
                    -4.59
                ],
                [
                    "2025-05-22",
                    -0.99
                ],
                [
                    "2025-05-23",
                    1.53
                ],
                [
                    "2025-05-26",
                    -0.45
                ],
                [
                    "2025-05-27",
                    1.61
                ],
                [
                    "2025-05-28",
                    -3.24
                ],
                [
                    "2025-05-29",
                    -9.43
                ],
                [
                    "2025-05-30",
                    2.97
                ],
                [
                    "2025-06-03",
                    -5.77
                ],
                [
                    "2025-06-04",
                    6.26
                ],
                [
                    "2025-06-05",
                    1.50
                ],
                [
                    "2025-06-06",
                    -0.43
                ],
                [
                    "2025-06-09",
                    -2.49
                ],
                [
                    "2025-06-10",
                    2.50
                ],
                [
                    "2025-06-11",
                    -5.86
                ],
                [
                    "2025-06-12",
                    2.67
                ],
                [
                    "2025-06-13",
                    1.77
                ],
                [
                    "2025-06-16",
                    -2.77
                ],
                [
                    "2025-06-17",
                    4.63
                ],
                [
                    "2025-06-18",
                    -2.79
                ],
                [
                    "2025-06-19",
                    0.57
                ],
                [
                    "2025-06-20",
                    7.34
                ],
                [
                    "2025-06-23",
                    -6.85
                ],
                [
                    "2025-06-24",
                    -5.52
                ],
                [
                    "2025-06-25",
                    0.18
                ],
                [
                    "2025-06-26",
                    -7.65
                ],
                [
                    "2025-06-27",
                    -3.05
                ],
                [
                    "2025-06-30",
                    -4.55
                ],
                [
                    "2025-07-01",
                    -1.85
                ],
                [
                    "2025-07-02",
                    -1.88
                ],
                [
                    "2025-07-03",
                    -5.52
                ],
                [
                    "2025-07-04",
                    0.80
                ],
                [
                    "2025-07-07",
                    3.09
                ],
                [
                    "2025-07-08",
                    -2.57
                ],
                [
                    "2025-07-09",
                    -1.13
                ],
                [
                    "2025-07-10",
                    0.29
                ],
                [
                    "2025-07-11",
                    -0.89
                ],
                [
                    "2025-07-14",
                    10.68
                ],
                [
                    "2025-07-15",
                    -4.57
                ],
                [
                    "2025-07-16",
                    1.72
                ],
                [
                    "2025-07-17",
                    -2.00
                ],
                [
                    "2025-07-18",
                    -4.08
                ],
                [
                    "2025-07-21",
                    3.82
                ],
                [
                    "2025-07-22",
                    -1.10
                ],
                [
                    "2025-07-23",
                    2.16
                ],
                [
                    "2025-07-24",
                    8.94
                ],
                [
                    "2025-07-25",
                    -0.75
                ],
                [
                    "2025-07-28",
                    -4.17
                ],
                [
                    "2025-07-29",
                    -0.75
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    2.07
                ],
                [
                    "2025-02-05",
                    3.35
                ],
                [
                    "2025-02-06",
                    1.09
                ],
                [
                    "2025-02-07",
                    6.59
                ],
                [
                    "2025-02-10",
                    -0.18
                ],
                [
                    "2025-02-11",
                    -5.35
                ],
                [
                    "2025-02-12",
                    -2.36
                ],
                [
                    "2025-02-13",
                    5.10
                ],
                [
                    "2025-02-14",
                    8.45
                ],
                [
                    "2025-02-17",
                    9.82
                ],
                [
                    "2025-02-18",
                    14.44
                ],
                [
                    "2025-02-19",
                    2.08
                ],
                [
                    "2025-02-20",
                    14.08
                ],
                [
                    "2025-02-21",
                    -1.31
                ],
                [
                    "2025-02-24",
                    5.07
                ],
                [
                    "2025-02-25",
                    3.45
                ],
                [
                    "2025-02-26",
                    -0.60
                ],
                [
                    "2025-02-27",
                    -7.04
                ],
                [
                    "2025-02-28",
                    -0.61
                ],
                [
                    "2025-03-03",
                    3.78
                ],
                [
                    "2025-03-04",
                    4.21
                ],
                [
                    "2025-03-05",
                    8.46
                ],
                [
                    "2025-03-06",
                    -6.15
                ],
                [
                    "2025-03-07",
                    -0.45
                ],
                [
                    "2025-03-10",
                    4.20
                ],
                [
                    "2025-03-11",
                    -0.07
                ],
                [
                    "2025-03-12",
                    0.34
                ],
                [
                    "2025-03-13",
                    8.67
                ],
                [
                    "2025-03-14",
                    -1.08
                ],
                [
                    "2025-03-17",
                    9.04
                ],
                [
                    "2025-03-18",
                    -2.26
                ],
                [
                    "2025-03-19",
                    -2.86
                ],
                [
                    "2025-03-20",
                    -1.23
                ],
                [
                    "2025-03-21",
                    -8.08
                ],
                [
                    "2025-03-24",
                    1.63
                ],
                [
                    "2025-03-25",
                    6.09
                ],
                [
                    "2025-03-26",
                    0.40
                ],
                [
                    "2025-03-27",
                    1.11
                ],
                [
                    "2025-03-28",
                    -6.22
                ],
                [
                    "2025-03-31",
                    3.52
                ],
                [
                    "2025-04-01",
                    7.05
                ],
                [
                    "2025-04-02",
                    -8.69
                ],
                [
                    "2025-04-03",
                    -2.99
                ],
                [
                    "2025-04-07",
                    11.08
                ],
                [
                    "2025-04-08",
                    -11.59
                ],
                [
                    "2025-04-09",
                    11.25
                ],
                [
                    "2025-04-10",
                    1.10
                ],
                [
                    "2025-04-11",
                    8.99
                ],
                [
                    "2025-04-14",
                    1.11
                ],
                [
                    "2025-04-15",
                    1.62
                ],
                [
                    "2025-04-16",
                    4.29
                ],
                [
                    "2025-04-17",
                    13.62
                ],
                [
                    "2025-04-18",
                    12.53
                ],
                [
                    "2025-04-21",
                    5.48
                ],
                [
                    "2025-04-22",
                    10.57
                ],
                [
                    "2025-04-23",
                    5.97
                ],
                [
                    "2025-04-24",
                    7.14
                ],
                [
                    "2025-04-25",
                    8.21
                ],
                [
                    "2025-04-28",
                    -15.23
                ],
                [
                    "2025-04-29",
                    4.24
                ],
                [
                    "2025-04-30",
                    9.62
                ],
                [
                    "2025-05-06",
                    11.55
                ],
                [
                    "2025-05-07",
                    10.14
                ],
                [
                    "2025-05-08",
                    5.41
                ],
                [
                    "2025-05-09",
                    8.97
                ],
                [
                    "2025-05-12",
                    0.49
                ],
                [
                    "2025-05-13",
                    -7.60
                ],
                [
                    "2025-05-14",
                    -4.04
                ],
                [
                    "2025-05-15",
                    11.14
                ],
                [
                    "2025-05-16",
                    1.39
                ],
                [
                    "2025-05-19",
                    3.35
                ],
                [
                    "2025-05-20",
                    4.68
                ],
                [
                    "2025-05-21",
                    -1.48
                ],
                [
                    "2025-05-22",
                    -12.71
                ],
                [
                    "2025-05-23",
                    0.69
                ],
                [
                    "2025-05-26",
                    4.95
                ],
                [
                    "2025-05-27",
                    6.72
                ],
                [
                    "2025-05-28",
                    1.38
                ],
                [
                    "2025-05-29",
                    -11.63
                ],
                [
                    "2025-05-30",
                    5.21
                ],
                [
                    "2025-06-03",
                    -5.25
                ],
                [
                    "2025-06-04",
                    -3.38
                ],
                [
                    "2025-06-05",
                    -6.82
                ],
                [
                    "2025-06-06",
                    6.56
                ],
                [
                    "2025-06-09",
                    0.00
                ],
                [
                    "2025-06-10",
                    11.81
                ],
                [
                    "2025-06-11",
                    -9.39
                ],
                [
                    "2025-06-12",
                    2.56
                ],
                [
                    "2025-06-13",
                    3.07
                ],
                [
                    "2025-06-16",
                    -3.78
                ],
                [
                    "2025-06-17",
                    5.38
                ],
                [
                    "2025-06-18",
                    6.53
                ],
                [
                    "2025-06-19",
                    2.06
                ],
                [
                    "2025-06-20",
                    5.80
                ],
                [
                    "2025-06-23",
                    18.18
                ],
                [
                    "2025-06-24",
                    10.86
                ],
                [
                    "2025-06-25",
                    -3.60
                ],
                [
                    "2025-06-26",
                    -0.98
                ],
                [
                    "2025-06-27",
                    6.29
                ],
                [
                    "2025-06-30",
                    5.56
                ],
                [
                    "2025-07-01",
                    6.63
                ],
                [
                    "2025-07-02",
                    12.12
                ],
                [
                    "2025-07-03",
                    -11.41
                ],
                [
                    "2025-07-04",
                    7.85
                ],
                [
                    "2025-07-07",
                    10.37
                ],
                [
                    "2025-07-08",
                    7.37
                ],
                [
                    "2025-07-09",
                    5.09
                ],
                [
                    "2025-07-10",
                    -3.19
                ],
                [
                    "2025-07-11",
                    -7.06
                ],
                [
                    "2025-07-14",
                    5.04
                ],
                [
                    "2025-07-15",
                    6.28
                ],
                [
                    "2025-07-16",
                    -0.49
                ],
                [
                    "2025-07-17",
                    5.18
                ],
                [
                    "2025-07-18",
                    5.26
                ],
                [
                    "2025-07-21",
                    4.73
                ],
                [
                    "2025-07-22",
                    -9.81
                ],
                [
                    "2025-07-23",
                    -7.68
                ],
                [
                    "2025-07-24",
                    -4.96
                ],
                [
                    "2025-07-25",
                    2.74
                ],
                [
                    "2025-07-28",
                    0.40
                ],
                [
                    "2025-07-29",
                    8.87
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-12",
                    -2.66
                ],
                [
                    "2025-04-03",
                    5.68
                ],
                [
                    "2025-05-22",
                    13.70
                ],
                [
                    "2025-05-23",
                    -2.22
                ],
                [
                    "2025-05-26",
                    -4.50
                ],
                [
                    "2025-05-27",
                    -8.33
                ],
                [
                    "2025-06-03",
                    11.01
                ],
                [
                    "2025-06-04",
                    -2.88
                ],
                [
                    "2025-06-05",
                    5.32
                ],
                [
                    "2025-06-09",
                    2.49
                ],
                [
                    "2025-06-17",
                    -10.01
                ],
                [
                    "2025-07-23",
                    5.52
                ],
                [
                    "2025-07-24",
                    -3.97
                ],
                [
                    "2025-07-25",
                    -1.99
                ],
                [
                    "2025-07-28",
                    3.76
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-27",
                    2.87
                ],
                [
                    "2025-03-03",
                    2.36
                ],
                [
                    "2025-03-04",
                    10.79
                ],
                [
                    "2025-03-12",
                    0.68
                ],
                [
                    "2025-03-21",
                    8.40
                ],
                [
                    "2025-03-24",
                    7.57
                ],
                [
                    "2025-03-25",
                    12.53
                ],
                [
                    "2025-03-26",
                    -0.80
                ],
                [
                    "2025-04-02",
                    1.82
                ],
                [
                    "2025-04-03",
                    -2.69
                ],
                [
                    "2025-04-08",
                    10.33
                ],
                [
                    "2025-04-09",
                    1.93
                ],
                [
                    "2025-06-06",
                    -0.43
                ],
                [
                    "2025-06-17",
                    4.63
                ],
                [
                    "2025-07-24",
                    8.94
                ],
                [
                    "2025-07-25",
                    -0.75
                ],
                [
                    "2025-07-28",
                    -4.17
                ],
                [
                    "2025-07-29",
                    -0.75
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    -0.18
                ],
                [
                    "2025-02-13",
                    5.10
                ],
                [
                    "2025-02-17",
                    9.82
                ],
                [
                    "2025-02-18",
                    14.44
                ],
                [
                    "2025-02-19",
                    2.08
                ],
                [
                    "2025-02-20",
                    14.08
                ],
                [
                    "2025-02-21",
                    -1.31
                ],
                [
                    "2025-02-24",
                    5.07
                ],
                [
                    "2025-02-25",
                    3.45
                ],
                [
                    "2025-02-26",
                    -0.60
                ],
                [
                    "2025-02-28",
                    -0.61
                ],
                [
                    "2025-03-05",
                    8.46
                ],
                [
                    "2025-03-06",
                    -6.15
                ],
                [
                    "2025-03-07",
                    -0.45
                ],
                [
                    "2025-03-10",
                    4.20
                ],
                [
                    "2025-03-11",
                    -0.07
                ],
                [
                    "2025-03-13",
                    8.67
                ],
                [
                    "2025-03-14",
                    -1.08
                ],
                [
                    "2025-03-17",
                    9.04
                ],
                [
                    "2025-03-18",
                    -2.26
                ],
                [
                    "2025-03-19",
                    -2.86
                ],
                [
                    "2025-03-20",
                    -1.23
                ],
                [
                    "2025-03-27",
                    1.11
                ],
                [
                    "2025-03-28",
                    -6.22
                ],
                [
                    "2025-03-31",
                    3.52
                ],
                [
                    "2025-04-01",
                    7.05
                ],
                [
                    "2025-04-07",
                    11.08
                ],
                [
                    "2025-04-10",
                    1.10
                ],
                [
                    "2025-04-11",
                    8.99
                ],
                [
                    "2025-04-14",
                    1.11
                ],
                [
                    "2025-04-15",
                    1.62
                ],
                [
                    "2025-04-16",
                    4.29
                ],
                [
                    "2025-04-17",
                    13.62
                ],
                [
                    "2025-04-18",
                    12.53
                ],
                [
                    "2025-04-21",
                    5.48
                ],
                [
                    "2025-04-22",
                    10.57
                ],
                [
                    "2025-04-23",
                    5.97
                ],
                [
                    "2025-04-24",
                    7.14
                ],
                [
                    "2025-04-25",
                    8.21
                ],
                [
                    "2025-04-28",
                    -15.23
                ],
                [
                    "2025-04-29",
                    4.24
                ],
                [
                    "2025-04-30",
                    9.62
                ],
                [
                    "2025-05-06",
                    11.55
                ],
                [
                    "2025-05-07",
                    10.14
                ],
                [
                    "2025-05-08",
                    5.41
                ],
                [
                    "2025-05-09",
                    8.97
                ],
                [
                    "2025-05-12",
                    0.49
                ],
                [
                    "2025-05-13",
                    -7.60
                ],
                [
                    "2025-05-14",
                    -4.04
                ],
                [
                    "2025-05-15",
                    11.14
                ],
                [
                    "2025-05-16",
                    1.39
                ],
                [
                    "2025-05-20",
                    4.68
                ],
                [
                    "2025-05-21",
                    -1.48
                ],
                [
                    "2025-06-10",
                    11.81
                ],
                [
                    "2025-06-12",
                    2.56
                ],
                [
                    "2025-06-13",
                    3.07
                ],
                [
                    "2025-06-16",
                    -3.78
                ],
                [
                    "2025-06-18",
                    6.53
                ],
                [
                    "2025-06-19",
                    2.06
                ],
                [
                    "2025-06-20",
                    5.80
                ],
                [
                    "2025-06-23",
                    18.18
                ],
                [
                    "2025-06-24",
                    10.86
                ],
                [
                    "2025-06-25",
                    -3.60
                ],
                [
                    "2025-06-26",
                    -0.98
                ],
                [
                    "2025-06-27",
                    6.29
                ],
                [
                    "2025-07-02",
                    12.12
                ],
                [
                    "2025-07-03",
                    -11.41
                ],
                [
                    "2025-07-04",
                    7.85
                ],
                [
                    "2025-07-07",
                    10.37
                ],
                [
                    "2025-07-08",
                    7.37
                ],
                [
                    "2025-07-09",
                    5.09
                ],
                [
                    "2025-07-10",
                    -3.19
                ],
                [
                    "2025-07-11",
                    -7.06
                ],
                [
                    "2025-07-14",
                    5.04
                ],
                [
                    "2025-07-15",
                    6.28
                ],
                [
                    "2025-07-16",
                    -0.49
                ],
                [
                    "2025-07-17",
                    5.18
                ],
                [
                    "2025-07-18",
                    5.26
                ],
                [
                    "2025-07-21",
                    4.73
                ],
                [
                    "2025-07-22",
                    -9.81
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600880 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_fe4de5f8cef3432b96b4221326df6853.setOption(option_fe4de5f8cef3432b96b4221326df6853);
            window.addEventListener('resize', function(){
                chart_fe4de5f8cef3432b96b4221326df6853.resize();
            })
    </script>
</body>
</html>
