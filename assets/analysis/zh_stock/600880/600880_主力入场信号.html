<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="024e9b068ec742e0b9475bbb86179a6e" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_024e9b068ec742e0b9475bbb86179a6e = echarts.init(
            document.getElementById('024e9b068ec742e0b9475bbb86179a6e'), 'white', {renderer: 'canvas'});
        var option_024e9b068ec742e0b9475bbb86179a6e = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    4.6,
                    4.49,
                    4.47,
                    4.68
                ],
                [
                    4.57,
                    4.67,
                    4.56,
                    4.69
                ],
                [
                    4.69,
                    4.81,
                    4.63,
                    4.82
                ],
                [
                    4.81,
                    4.85,
                    4.76,
                    4.92
                ],
                [
                    4.87,
                    5.04,
                    4.85,
                    5.05
                ],
                [
                    5.04,
                    5.16,
                    4.96,
                    5.28
                ],
                [
                    5.14,
                    5.32,
                    5.06,
                    5.41
                ],
                [
                    5.33,
                    5.27,
                    5.17,
                    5.38
                ],
                [
                    5.27,
                    5.27,
                    5.1,
                    5.3
                ],
                [
                    5.27,
                    5.3,
                    5.25,
                    5.39
                ],
                [
                    5.25,
                    5.05,
                    5.02,
                    5.3
                ],
                [
                    5.05,
                    5.09,
                    5.0,
                    5.11
                ],
                [
                    5.08,
                    5.04,
                    5.0,
                    5.13
                ],
                [
                    5.07,
                    5.11,
                    5.0,
                    5.15
                ],
                [
                    5.1,
                    5.1,
                    5.0,
                    5.12
                ],
                [
                    5.04,
                    5.04,
                    4.98,
                    5.13
                ],
                [
                    5.04,
                    5.03,
                    5.0,
                    5.09
                ],
                [
                    5.06,
                    5.02,
                    4.93,
                    5.15
                ],
                [
                    5.0,
                    4.72,
                    4.72,
                    5.0
                ],
                [
                    4.75,
                    4.75,
                    4.71,
                    4.82
                ],
                [
                    4.73,
                    4.79,
                    4.71,
                    4.8
                ],
                [
                    4.78,
                    4.77,
                    4.67,
                    4.78
                ],
                [
                    4.79,
                    5.03,
                    4.78,
                    5.2
                ],
                [
                    4.96,
                    5.02,
                    4.95,
                    5.11
                ],
                [
                    5.05,
                    4.93,
                    4.89,
                    5.06
                ],
                [
                    4.86,
                    4.97,
                    4.83,
                    5.0
                ],
                [
                    5.02,
                    5.07,
                    4.98,
                    5.14
                ],
                [
                    5.05,
                    4.94,
                    4.88,
                    5.09
                ],
                [
                    4.94,
                    5.05,
                    4.92,
                    5.07
                ],
                [
                    5.08,
                    5.02,
                    5.01,
                    5.11
                ],
                [
                    5.03,
                    5.03,
                    4.99,
                    5.08
                ],
                [
                    5.01,
                    4.99,
                    4.94,
                    5.03
                ],
                [
                    4.99,
                    4.94,
                    4.92,
                    4.99
                ],
                [
                    4.92,
                    4.84,
                    4.84,
                    5.08
                ],
                [
                    4.85,
                    4.71,
                    4.59,
                    4.85
                ],
                [
                    4.69,
                    4.63,
                    4.58,
                    4.69
                ],
                [
                    4.63,
                    4.66,
                    4.59,
                    4.69
                ],
                [
                    4.66,
                    4.63,
                    4.57,
                    4.68
                ],
                [
                    4.64,
                    4.64,
                    4.58,
                    4.68
                ],
                [
                    4.61,
                    4.53,
                    4.46,
                    4.62
                ],
                [
                    4.54,
                    4.53,
                    4.53,
                    4.59
                ],
                [
                    4.52,
                    4.54,
                    4.52,
                    4.6
                ],
                [
                    4.49,
                    4.6,
                    4.49,
                    4.63
                ],
                [
                    4.38,
                    4.14,
                    4.14,
                    4.41
                ],
                [
                    4.0,
                    4.06,
                    3.91,
                    4.13
                ],
                [
                    3.98,
                    4.16,
                    3.78,
                    4.18
                ],
                [
                    4.22,
                    4.25,
                    4.19,
                    4.31
                ],
                [
                    4.2,
                    4.26,
                    4.2,
                    4.31
                ],
                [
                    4.33,
                    4.34,
                    4.3,
                    4.45
                ],
                [
                    4.34,
                    4.36,
                    4.32,
                    4.42
                ],
                [
                    4.33,
                    4.31,
                    4.23,
                    4.4
                ],
                [
                    4.25,
                    4.3,
                    4.25,
                    4.35
                ],
                [
                    4.3,
                    4.28,
                    4.25,
                    4.32
                ],
                [
                    4.29,
                    4.35,
                    4.25,
                    4.36
                ],
                [
                    4.35,
                    4.36,
                    4.31,
                    4.4
                ],
                [
                    4.38,
                    4.33,
                    4.33,
                    4.43
                ],
                [
                    4.33,
                    4.27,
                    4.23,
                    4.33
                ],
                [
                    4.27,
                    4.31,
                    4.25,
                    4.36
                ],
                [
                    4.32,
                    4.33,
                    4.27,
                    4.41
                ],
                [
                    4.32,
                    4.39,
                    4.28,
                    4.42
                ],
                [
                    4.41,
                    4.45,
                    4.4,
                    4.49
                ],
                [
                    4.45,
                    4.55,
                    4.45,
                    4.56
                ],
                [
                    4.6,
                    4.51,
                    4.47,
                    4.63
                ],
                [
                    4.49,
                    4.56,
                    4.48,
                    4.58
                ],
                [
                    4.57,
                    4.47,
                    4.46,
                    4.6
                ],
                [
                    4.52,
                    4.49,
                    4.46,
                    4.52
                ],
                [
                    4.52,
                    4.48,
                    4.47,
                    4.55
                ],
                [
                    4.48,
                    4.51,
                    4.46,
                    4.52
                ],
                [
                    4.5,
                    4.44,
                    4.43,
                    4.5
                ],
                [
                    4.43,
                    4.44,
                    4.42,
                    4.47
                ],
                [
                    4.45,
                    4.57,
                    4.43,
                    4.59
                ],
                [
                    4.59,
                    4.63,
                    4.55,
                    4.64
                ],
                [
                    4.61,
                    4.59,
                    4.56,
                    4.64
                ],
                [
                    4.58,
                    4.58,
                    4.53,
                    4.66
                ],
                [
                    4.56,
                    4.46,
                    4.46,
                    4.58
                ],
                [
                    4.46,
                    4.58,
                    4.46,
                    4.6
                ],
                [
                    4.6,
                    4.6,
                    4.52,
                    4.62
                ],
                [
                    4.6,
                    4.55,
                    4.5,
                    4.62
                ],
                [
                    4.53,
                    4.86,
                    4.53,
                    5.01
                ],
                [
                    4.86,
                    4.72,
                    4.71,
                    5.2
                ],
                [
                    4.87,
                    4.96,
                    4.85,
                    5.19
                ],
                [
                    4.86,
                    4.88,
                    4.84,
                    4.94
                ],
                [
                    4.99,
                    4.99,
                    4.9,
                    5.05
                ],
                [
                    4.96,
                    4.9,
                    4.87,
                    5.02
                ],
                [
                    4.93,
                    5.05,
                    4.92,
                    5.17
                ],
                [
                    5.0,
                    4.97,
                    4.89,
                    5.08
                ],
                [
                    4.96,
                    5.22,
                    4.96,
                    5.35
                ],
                [
                    5.2,
                    5.27,
                    5.17,
                    5.32
                ],
                [
                    5.26,
                    5.09,
                    5.07,
                    5.3
                ],
                [
                    5.05,
                    5.31,
                    5.04,
                    5.4
                ],
                [
                    5.26,
                    5.18,
                    5.15,
                    5.31
                ],
                [
                    5.17,
                    5.07,
                    4.99,
                    5.17
                ],
                [
                    5.1,
                    4.98,
                    4.92,
                    5.12
                ],
                [
                    5.0,
                    4.82,
                    4.8,
                    5.03
                ],
                [
                    4.76,
                    4.93,
                    4.75,
                    4.95
                ],
                [
                    4.92,
                    5.06,
                    4.9,
                    5.08
                ],
                [
                    5.06,
                    5.13,
                    4.99,
                    5.21
                ],
                [
                    5.1,
                    5.19,
                    5.03,
                    5.3
                ],
                [
                    5.18,
                    5.19,
                    5.13,
                    5.28
                ],
                [
                    5.18,
                    5.35,
                    5.14,
                    5.43
                ],
                [
                    5.31,
                    5.32,
                    5.27,
                    5.49
                ],
                [
                    5.33,
                    5.25,
                    5.19,
                    5.34
                ],
                [
                    5.28,
                    5.39,
                    5.21,
                    5.5
                ],
                [
                    5.3,
                    5.43,
                    5.26,
                    5.54
                ],
                [
                    5.36,
                    5.33,
                    5.31,
                    5.43
                ],
                [
                    5.32,
                    5.48,
                    5.29,
                    5.57
                ],
                [
                    5.58,
                    5.5,
                    5.47,
                    5.69
                ],
                [
                    5.45,
                    5.6,
                    5.45,
                    5.72
                ],
                [
                    5.72,
                    5.77,
                    5.48,
                    6.07
                ],
                [
                    5.66,
                    5.4,
                    5.33,
                    5.75
                ],
                [
                    5.37,
                    5.3,
                    5.25,
                    5.48
                ],
                [
                    5.37,
                    5.34,
                    5.3,
                    5.44
                ],
                [
                    5.3,
                    5.33,
                    5.28,
                    5.36
                ],
                [
                    5.3,
                    5.28,
                    5.26,
                    5.35
                ],
                [
                    5.3,
                    5.37,
                    5.28,
                    5.46
                ],
                [
                    5.41,
                    5.38,
                    5.29,
                    5.49
                ],
                [
                    5.41,
                    5.42,
                    5.36,
                    5.5
                ],
                [
                    5.49,
                    5.38,
                    5.35,
                    5.49
                ],
                [
                    5.33,
                    5.32,
                    5.14,
                    5.38
                ],
                [
                    5.32,
                    5.4,
                    5.25,
                    5.43
                ],
                [
                    5.39,
                    5.28,
                    5.21,
                    5.41
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#880000",
                "borderColor0": "#008F28"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5438\u7b79",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 18,
            "data": [
                [
                    "2025-02-11",
                    4.96
                ],
                [
                    "2025-02-27",
                    4.93
                ],
                [
                    "2025-03-28",
                    4.58
                ],
                [
                    "2025-04-02",
                    4.52
                ],
                [
                    "2025-04-03",
                    4.49
                ],
                [
                    "2025-04-28",
                    4.27
                ],
                [
                    "2025-05-13",
                    4.47
                ],
                [
                    "2025-05-16",
                    4.42
                ],
                [
                    "2025-05-21",
                    4.56
                ],
                [
                    "2025-05-22",
                    4.53
                ],
                [
                    "2025-06-03",
                    4.85
                ],
                [
                    "2025-06-05",
                    4.9
                ],
                [
                    "2025-06-25",
                    4.99
                ],
                [
                    "2025-06-26",
                    5.03
                ],
                [
                    "2025-07-03",
                    5.21
                ],
                [
                    "2025-07-11",
                    5.48
                ],
                [
                    "2025-07-22",
                    5.29
                ],
                [
                    "2025-07-23",
                    5.36
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#F4B400"
            }
        },
        {
            "type": "scatter",
            "name": "\u52a0\u4ed3",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 18,
            "data": [
                [
                    "2025-03-06",
                    4.78
                ],
                [
                    "2025-05-29",
                    4.53
                ],
                [
                    "2025-06-03",
                    4.85
                ],
                [
                    "2025-06-11",
                    4.96
                ],
                [
                    "2025-07-11",
                    5.48
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4285F4"
            }
        },
        {
            "type": "scatter",
            "name": "\u6d17\u76d8",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "rect",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#16A085"
            }
        },
        {
            "type": "scatter",
            "name": "\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "circle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#DB4437"
            }
        },
        {
            "type": "scatter",
            "name": "\u9690\u5f62\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "pin",
            "symbolSize": 18,
            "data": [
                [
                    "2025-06-18",
                    4.99
                ],
                [
                    "2025-06-19",
                    4.92
                ],
                [
                    "2025-06-23",
                    4.75
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#8E44AD"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5438\u7b79",
                "\u52a0\u4ed3",
                "\u6d17\u76d8",
                "\u51fa\u8d27",
                "\u9690\u5f62\u51fa\u8d27"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10,
            "top": "2%"
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "interval": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 0
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600880 \u4e3b\u529b\u4fe1\u53f7 (\u542b\u6d17\u76d8)",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "90%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_024e9b068ec742e0b9475bbb86179a6e.setOption(option_024e9b068ec742e0b9475bbb86179a6e);
            window.addEventListener('resize', function(){
                chart_024e9b068ec742e0b9475bbb86179a6e.resize();
            })
    </script>
</body>
</html>
