<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="67a759a5ce214433aabbb9c29eba1cc1" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_67a759a5ce214433aabbb9c29eba1cc1 = echarts.init(
            document.getElementById('67a759a5ce214433aabbb9c29eba1cc1'), 'white', {renderer: 'canvas'});
        var option_67a759a5ce214433aabbb9c29eba1cc1 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    3.82,
                    3.76,
                    3.75,
                    3.9
                ],
                [
                    3.78,
                    3.78,
                    3.74,
                    3.82
                ],
                [
                    3.76,
                    3.8,
                    3.66,
                    3.81
                ],
                [
                    3.8,
                    3.86,
                    3.77,
                    3.9
                ],
                [
                    3.86,
                    3.95,
                    3.86,
                    3.95
                ],
                [
                    4.02,
                    3.92,
                    3.89,
                    4.04
                ],
                [
                    3.93,
                    4.02,
                    3.9,
                    4.02
                ],
                [
                    3.98,
                    4.05,
                    3.97,
                    4.1
                ],
                [
                    4.05,
                    3.92,
                    3.9,
                    4.05
                ],
                [
                    3.91,
                    3.98,
                    3.89,
                    4.0
                ],
                [
                    3.96,
                    3.84,
                    3.82,
                    4.06
                ],
                [
                    3.82,
                    3.85,
                    3.79,
                    3.85
                ],
                [
                    3.84,
                    3.82,
                    3.78,
                    3.84
                ],
                [
                    3.84,
                    3.81,
                    3.76,
                    3.86
                ],
                [
                    3.8,
                    3.83,
                    3.79,
                    3.89
                ],
                [
                    3.8,
                    3.74,
                    3.74,
                    3.85
                ],
                [
                    3.76,
                    3.79,
                    3.76,
                    3.84
                ],
                [
                    3.79,
                    3.85,
                    3.79,
                    3.91
                ],
                [
                    3.86,
                    3.91,
                    3.8,
                    4.0
                ],
                [
                    3.93,
                    3.81,
                    3.78,
                    3.98
                ],
                [
                    3.77,
                    3.8,
                    3.74,
                    3.8
                ],
                [
                    3.78,
                    3.72,
                    3.69,
                    3.8
                ],
                [
                    3.75,
                    3.76,
                    3.71,
                    3.78
                ],
                [
                    3.74,
                    3.67,
                    3.65,
                    3.76
                ],
                [
                    3.67,
                    3.69,
                    3.65,
                    3.71
                ],
                [
                    3.66,
                    3.74,
                    3.63,
                    3.74
                ],
                [
                    3.75,
                    3.74,
                    3.71,
                    3.77
                ],
                [
                    3.74,
                    3.73,
                    3.68,
                    3.83
                ],
                [
                    3.74,
                    3.8,
                    3.72,
                    3.81
                ],
                [
                    3.81,
                    3.85,
                    3.81,
                    3.91
                ],
                [
                    3.85,
                    3.8,
                    3.77,
                    3.86
                ],
                [
                    3.79,
                    3.75,
                    3.74,
                    3.8
                ],
                [
                    3.76,
                    3.76,
                    3.75,
                    3.79
                ],
                [
                    3.74,
                    3.74,
                    3.71,
                    3.81
                ],
                [
                    3.73,
                    3.75,
                    3.67,
                    3.8
                ],
                [
                    3.74,
                    3.73,
                    3.69,
                    3.75
                ],
                [
                    3.72,
                    3.77,
                    3.7,
                    3.79
                ],
                [
                    3.75,
                    3.73,
                    3.68,
                    3.77
                ],
                [
                    3.72,
                    3.66,
                    3.65,
                    3.74
                ],
                [
                    3.63,
                    3.59,
                    3.56,
                    3.65
                ],
                [
                    3.61,
                    3.6,
                    3.58,
                    3.63
                ],
                [
                    3.59,
                    3.59,
                    3.57,
                    3.61
                ],
                [
                    3.57,
                    3.68,
                    3.56,
                    3.69
                ],
                [
                    3.55,
                    3.31,
                    3.31,
                    3.58
                ],
                [
                    3.28,
                    3.28,
                    3.19,
                    3.34
                ],
                [
                    3.24,
                    3.39,
                    3.09,
                    3.42
                ],
                [
                    3.38,
                    3.49,
                    3.38,
                    3.55
                ],
                [
                    3.47,
                    3.44,
                    3.43,
                    3.52
                ],
                [
                    3.46,
                    3.47,
                    3.44,
                    3.5
                ],
                [
                    3.46,
                    3.45,
                    3.43,
                    3.49
                ],
                [
                    3.46,
                    3.41,
                    3.35,
                    3.48
                ],
                [
                    3.39,
                    3.5,
                    3.38,
                    3.57
                ],
                [
                    3.48,
                    3.85,
                    3.48,
                    3.85
                ],
                [
                    3.99,
                    3.96,
                    3.83,
                    4.07
                ],
                [
                    3.83,
                    3.92,
                    3.81,
                    4.01
                ],
                [
                    3.93,
                    3.8,
                    3.79,
                    3.97
                ],
                [
                    3.79,
                    3.86,
                    3.79,
                    3.98
                ],
                [
                    3.85,
                    3.95,
                    3.85,
                    4.24
                ],
                [
                    3.83,
                    3.65,
                    3.65,
                    3.98
                ],
                [
                    3.69,
                    3.6,
                    3.57,
                    3.71
                ],
                [
                    3.61,
                    3.58,
                    3.58,
                    3.68
                ],
                [
                    3.58,
                    3.65,
                    3.58,
                    3.65
                ],
                [
                    3.75,
                    4.02,
                    3.75,
                    4.02
                ],
                [
                    4.02,
                    3.97,
                    3.9,
                    4.03
                ],
                [
                    3.95,
                    3.89,
                    3.87,
                    4.01
                ],
                [
                    3.91,
                    3.95,
                    3.87,
                    3.98
                ],
                [
                    4.06,
                    3.92,
                    3.91,
                    4.09
                ],
                [
                    3.94,
                    3.93,
                    3.87,
                    3.95
                ],
                [
                    3.95,
                    3.9,
                    3.9,
                    3.98
                ],
                [
                    3.92,
                    3.95,
                    3.88,
                    3.96
                ],
                [
                    3.91,
                    3.98,
                    3.9,
                    4.03
                ],
                [
                    3.98,
                    4.08,
                    3.93,
                    4.16
                ],
                [
                    4.1,
                    3.99,
                    3.98,
                    4.14
                ],
                [
                    3.81,
                    3.68,
                    3.67,
                    3.87
                ],
                [
                    3.69,
                    3.63,
                    3.63,
                    3.71
                ],
                [
                    3.63,
                    3.7,
                    3.62,
                    3.7
                ],
                [
                    3.7,
                    3.7,
                    3.65,
                    3.73
                ],
                [
                    3.7,
                    3.66,
                    3.63,
                    3.72
                ],
                [
                    3.65,
                    3.76,
                    3.63,
                    3.82
                ],
                [
                    3.71,
                    3.65,
                    3.65,
                    3.75
                ],
                [
                    3.63,
                    3.66,
                    3.61,
                    3.69
                ],
                [
                    3.68,
                    3.7,
                    3.64,
                    3.7
                ],
                [
                    3.68,
                    3.65,
                    3.63,
                    3.71
                ],
                [
                    3.65,
                    3.63,
                    3.61,
                    3.67
                ],
                [
                    3.63,
                    3.66,
                    3.61,
                    3.68
                ],
                [
                    3.65,
                    3.59,
                    3.54,
                    3.68
                ],
                [
                    3.59,
                    3.62,
                    3.58,
                    3.64
                ],
                [
                    3.61,
                    3.58,
                    3.56,
                    3.62
                ],
                [
                    3.57,
                    3.48,
                    3.46,
                    3.57
                ],
                [
                    3.46,
                    3.57,
                    3.46,
                    3.62
                ],
                [
                    3.56,
                    3.55,
                    3.52,
                    3.58
                ],
                [
                    3.54,
                    3.5,
                    3.48,
                    3.54
                ],
                [
                    3.48,
                    3.4,
                    3.4,
                    3.49
                ],
                [
                    3.4,
                    3.37,
                    3.37,
                    3.44
                ],
                [
                    3.36,
                    3.42,
                    3.35,
                    3.44
                ],
                [
                    3.41,
                    3.49,
                    3.41,
                    3.51
                ],
                [
                    3.48,
                    3.51,
                    3.46,
                    3.52
                ],
                [
                    3.5,
                    3.49,
                    3.47,
                    3.53
                ],
                [
                    3.48,
                    3.49,
                    3.48,
                    3.56
                ],
                [
                    3.5,
                    3.51,
                    3.48,
                    3.51
                ],
                [
                    3.51,
                    3.51,
                    3.46,
                    3.51
                ],
                [
                    3.51,
                    3.55,
                    3.49,
                    3.56
                ],
                [
                    3.55,
                    3.53,
                    3.52,
                    3.57
                ],
                [
                    3.53,
                    3.5,
                    3.49,
                    3.56
                ],
                [
                    3.5,
                    3.54,
                    3.47,
                    3.59
                ],
                [
                    3.53,
                    3.58,
                    3.5,
                    3.58
                ],
                [
                    3.57,
                    3.59,
                    3.56,
                    3.61
                ],
                [
                    3.59,
                    3.67,
                    3.57,
                    3.69
                ],
                [
                    3.69,
                    3.67,
                    3.65,
                    3.74
                ],
                [
                    3.64,
                    3.62,
                    3.58,
                    3.66
                ],
                [
                    3.62,
                    3.57,
                    3.49,
                    3.62
                ],
                [
                    3.56,
                    3.57,
                    3.55,
                    3.61
                ],
                [
                    3.58,
                    3.58,
                    3.55,
                    3.6
                ],
                [
                    3.59,
                    3.56,
                    3.54,
                    3.59
                ],
                [
                    3.56,
                    3.63,
                    3.55,
                    3.63
                ],
                [
                    3.63,
                    3.66,
                    3.58,
                    3.67
                ],
                [
                    3.67,
                    3.61,
                    3.6,
                    3.69
                ],
                [
                    3.6,
                    3.68,
                    3.59,
                    3.68
                ],
                [
                    3.68,
                    3.66,
                    3.64,
                    3.69
                ],
                [
                    3.66,
                    3.66,
                    3.62,
                    3.68
                ],
                [
                    3.66,
                    3.66,
                    3.62,
                    3.69
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#880000",
                "borderColor0": "#008F28"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5438\u7b79",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 18,
            "data": [
                [
                    "2025-02-28",
                    3.8
                ],
                [
                    "2025-03-11",
                    3.63
                ],
                [
                    "2025-03-13",
                    3.68
                ],
                [
                    "2025-03-21",
                    3.71
                ],
                [
                    "2025-03-24",
                    3.67
                ],
                [
                    "2025-03-25",
                    3.69
                ],
                [
                    "2025-05-23",
                    3.63
                ],
                [
                    "2025-06-04",
                    3.64
                ],
                [
                    "2025-06-09",
                    3.61
                ],
                [
                    "2025-07-01",
                    3.46
                ],
                [
                    "2025-07-02",
                    3.49
                ],
                [
                    "2025-07-17",
                    3.55
                ],
                [
                    "2025-07-22",
                    3.58
                ],
                [
                    "2025-07-25",
                    3.64
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#F4B400"
            }
        },
        {
            "type": "scatter",
            "name": "\u52a0\u4ed3",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 18,
            "data": [
                [
                    "2025-04-18",
                    3.48
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4285F4"
            }
        },
        {
            "type": "scatter",
            "name": "\u6d17\u76d8",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "rect",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#16A085"
            }
        },
        {
            "type": "scatter",
            "name": "\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "circle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#DB4437"
            }
        },
        {
            "type": "scatter",
            "name": "\u9690\u5f62\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "pin",
            "symbolSize": 18,
            "data": [
                [
                    "2025-05-15",
                    3.9
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#8E44AD"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5438\u7b79",
                "\u52a0\u4ed3",
                "\u6d17\u76d8",
                "\u51fa\u8d27",
                "\u9690\u5f62\u51fa\u8d27"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10,
            "top": "2%"
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "interval": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 0
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "000863 \u4e3b\u529b\u4fe1\u53f7 (\u542b\u6d17\u76d8)",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "90%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_67a759a5ce214433aabbb9c29eba1cc1.setOption(option_67a759a5ce214433aabbb9c29eba1cc1);
            window.addEventListener('resize', function(){
                chart_67a759a5ce214433aabbb9c29eba1cc1.resize();
            })
    </script>
</body>
</html>
