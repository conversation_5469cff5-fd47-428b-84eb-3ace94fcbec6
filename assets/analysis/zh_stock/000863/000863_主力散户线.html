<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="07d0cbda7c86414bad55bc2b447b288e" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_07d0cbda7c86414bad55bc2b447b288e = echarts.init(
            document.getElementById('07d0cbda7c86414bad55bc2b447b288e'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_07d0cbda7c86414bad55bc2b447b288e = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    3.82,
                    3.76,
                    3.75,
                    3.9
                ],
                [
                    3.78,
                    3.78,
                    3.74,
                    3.82
                ],
                [
                    3.76,
                    3.8,
                    3.66,
                    3.81
                ],
                [
                    3.8,
                    3.86,
                    3.77,
                    3.9
                ],
                [
                    3.86,
                    3.95,
                    3.86,
                    3.95
                ],
                [
                    4.02,
                    3.92,
                    3.89,
                    4.04
                ],
                [
                    3.93,
                    4.02,
                    3.9,
                    4.02
                ],
                [
                    3.98,
                    4.05,
                    3.97,
                    4.1
                ],
                [
                    4.05,
                    3.92,
                    3.9,
                    4.05
                ],
                [
                    3.91,
                    3.98,
                    3.89,
                    4.0
                ],
                [
                    3.96,
                    3.84,
                    3.82,
                    4.06
                ],
                [
                    3.82,
                    3.85,
                    3.79,
                    3.85
                ],
                [
                    3.84,
                    3.82,
                    3.78,
                    3.84
                ],
                [
                    3.84,
                    3.81,
                    3.76,
                    3.86
                ],
                [
                    3.8,
                    3.83,
                    3.79,
                    3.89
                ],
                [
                    3.8,
                    3.74,
                    3.74,
                    3.85
                ],
                [
                    3.76,
                    3.79,
                    3.76,
                    3.84
                ],
                [
                    3.79,
                    3.85,
                    3.79,
                    3.91
                ],
                [
                    3.86,
                    3.91,
                    3.8,
                    4.0
                ],
                [
                    3.93,
                    3.81,
                    3.78,
                    3.98
                ],
                [
                    3.77,
                    3.8,
                    3.74,
                    3.8
                ],
                [
                    3.78,
                    3.72,
                    3.69,
                    3.8
                ],
                [
                    3.75,
                    3.76,
                    3.71,
                    3.78
                ],
                [
                    3.74,
                    3.67,
                    3.65,
                    3.76
                ],
                [
                    3.67,
                    3.69,
                    3.65,
                    3.71
                ],
                [
                    3.66,
                    3.74,
                    3.63,
                    3.74
                ],
                [
                    3.75,
                    3.74,
                    3.71,
                    3.77
                ],
                [
                    3.74,
                    3.73,
                    3.68,
                    3.83
                ],
                [
                    3.74,
                    3.8,
                    3.72,
                    3.81
                ],
                [
                    3.81,
                    3.85,
                    3.81,
                    3.91
                ],
                [
                    3.85,
                    3.8,
                    3.77,
                    3.86
                ],
                [
                    3.79,
                    3.75,
                    3.74,
                    3.8
                ],
                [
                    3.76,
                    3.76,
                    3.75,
                    3.79
                ],
                [
                    3.74,
                    3.74,
                    3.71,
                    3.81
                ],
                [
                    3.73,
                    3.75,
                    3.67,
                    3.8
                ],
                [
                    3.74,
                    3.73,
                    3.69,
                    3.75
                ],
                [
                    3.72,
                    3.77,
                    3.7,
                    3.79
                ],
                [
                    3.75,
                    3.73,
                    3.68,
                    3.77
                ],
                [
                    3.72,
                    3.66,
                    3.65,
                    3.74
                ],
                [
                    3.63,
                    3.59,
                    3.56,
                    3.65
                ],
                [
                    3.61,
                    3.6,
                    3.58,
                    3.63
                ],
                [
                    3.59,
                    3.59,
                    3.57,
                    3.61
                ],
                [
                    3.57,
                    3.68,
                    3.56,
                    3.69
                ],
                [
                    3.55,
                    3.31,
                    3.31,
                    3.58
                ],
                [
                    3.28,
                    3.28,
                    3.19,
                    3.34
                ],
                [
                    3.24,
                    3.39,
                    3.09,
                    3.42
                ],
                [
                    3.38,
                    3.49,
                    3.38,
                    3.55
                ],
                [
                    3.47,
                    3.44,
                    3.43,
                    3.52
                ],
                [
                    3.46,
                    3.47,
                    3.44,
                    3.5
                ],
                [
                    3.46,
                    3.45,
                    3.43,
                    3.49
                ],
                [
                    3.46,
                    3.41,
                    3.35,
                    3.48
                ],
                [
                    3.39,
                    3.5,
                    3.38,
                    3.57
                ],
                [
                    3.48,
                    3.85,
                    3.48,
                    3.85
                ],
                [
                    3.99,
                    3.96,
                    3.83,
                    4.07
                ],
                [
                    3.83,
                    3.92,
                    3.81,
                    4.01
                ],
                [
                    3.93,
                    3.8,
                    3.79,
                    3.97
                ],
                [
                    3.79,
                    3.86,
                    3.79,
                    3.98
                ],
                [
                    3.85,
                    3.95,
                    3.85,
                    4.24
                ],
                [
                    3.83,
                    3.65,
                    3.65,
                    3.98
                ],
                [
                    3.69,
                    3.6,
                    3.57,
                    3.71
                ],
                [
                    3.61,
                    3.58,
                    3.58,
                    3.68
                ],
                [
                    3.58,
                    3.65,
                    3.58,
                    3.65
                ],
                [
                    3.75,
                    4.02,
                    3.75,
                    4.02
                ],
                [
                    4.02,
                    3.97,
                    3.9,
                    4.03
                ],
                [
                    3.95,
                    3.89,
                    3.87,
                    4.01
                ],
                [
                    3.91,
                    3.95,
                    3.87,
                    3.98
                ],
                [
                    4.06,
                    3.92,
                    3.91,
                    4.09
                ],
                [
                    3.94,
                    3.93,
                    3.87,
                    3.95
                ],
                [
                    3.95,
                    3.9,
                    3.9,
                    3.98
                ],
                [
                    3.92,
                    3.95,
                    3.88,
                    3.96
                ],
                [
                    3.91,
                    3.98,
                    3.9,
                    4.03
                ],
                [
                    3.98,
                    4.08,
                    3.93,
                    4.16
                ],
                [
                    4.1,
                    3.99,
                    3.98,
                    4.14
                ],
                [
                    3.81,
                    3.68,
                    3.67,
                    3.87
                ],
                [
                    3.69,
                    3.63,
                    3.63,
                    3.71
                ],
                [
                    3.63,
                    3.7,
                    3.62,
                    3.7
                ],
                [
                    3.7,
                    3.7,
                    3.65,
                    3.73
                ],
                [
                    3.7,
                    3.66,
                    3.63,
                    3.72
                ],
                [
                    3.65,
                    3.76,
                    3.63,
                    3.82
                ],
                [
                    3.71,
                    3.65,
                    3.65,
                    3.75
                ],
                [
                    3.63,
                    3.66,
                    3.61,
                    3.69
                ],
                [
                    3.68,
                    3.7,
                    3.64,
                    3.7
                ],
                [
                    3.68,
                    3.65,
                    3.63,
                    3.71
                ],
                [
                    3.65,
                    3.63,
                    3.61,
                    3.67
                ],
                [
                    3.63,
                    3.66,
                    3.61,
                    3.68
                ],
                [
                    3.65,
                    3.59,
                    3.54,
                    3.68
                ],
                [
                    3.59,
                    3.62,
                    3.58,
                    3.64
                ],
                [
                    3.61,
                    3.58,
                    3.56,
                    3.62
                ],
                [
                    3.57,
                    3.48,
                    3.46,
                    3.57
                ],
                [
                    3.46,
                    3.57,
                    3.46,
                    3.62
                ],
                [
                    3.56,
                    3.55,
                    3.52,
                    3.58
                ],
                [
                    3.54,
                    3.5,
                    3.48,
                    3.54
                ],
                [
                    3.48,
                    3.4,
                    3.4,
                    3.49
                ],
                [
                    3.4,
                    3.37,
                    3.37,
                    3.44
                ],
                [
                    3.36,
                    3.42,
                    3.35,
                    3.44
                ],
                [
                    3.41,
                    3.49,
                    3.41,
                    3.51
                ],
                [
                    3.48,
                    3.51,
                    3.46,
                    3.52
                ],
                [
                    3.5,
                    3.49,
                    3.47,
                    3.53
                ],
                [
                    3.48,
                    3.49,
                    3.48,
                    3.56
                ],
                [
                    3.5,
                    3.51,
                    3.48,
                    3.51
                ],
                [
                    3.51,
                    3.51,
                    3.46,
                    3.51
                ],
                [
                    3.51,
                    3.55,
                    3.49,
                    3.56
                ],
                [
                    3.55,
                    3.53,
                    3.52,
                    3.57
                ],
                [
                    3.53,
                    3.5,
                    3.49,
                    3.56
                ],
                [
                    3.5,
                    3.54,
                    3.47,
                    3.59
                ],
                [
                    3.53,
                    3.58,
                    3.5,
                    3.58
                ],
                [
                    3.57,
                    3.59,
                    3.56,
                    3.61
                ],
                [
                    3.59,
                    3.67,
                    3.57,
                    3.69
                ],
                [
                    3.69,
                    3.67,
                    3.65,
                    3.74
                ],
                [
                    3.64,
                    3.62,
                    3.58,
                    3.66
                ],
                [
                    3.62,
                    3.57,
                    3.49,
                    3.62
                ],
                [
                    3.56,
                    3.57,
                    3.55,
                    3.61
                ],
                [
                    3.58,
                    3.58,
                    3.55,
                    3.6
                ],
                [
                    3.59,
                    3.56,
                    3.54,
                    3.59
                ],
                [
                    3.56,
                    3.63,
                    3.55,
                    3.63
                ],
                [
                    3.63,
                    3.66,
                    3.58,
                    3.67
                ],
                [
                    3.67,
                    3.61,
                    3.6,
                    3.69
                ],
                [
                    3.6,
                    3.68,
                    3.59,
                    3.68
                ],
                [
                    3.68,
                    3.66,
                    3.64,
                    3.69
                ],
                [
                    3.66,
                    3.66,
                    3.62,
                    3.68
                ],
                [
                    3.66,
                    3.66,
                    3.62,
                    3.69
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -2.12
                ],
                [
                    "2025-02-05",
                    2.19
                ],
                [
                    "2025-02-06",
                    -0.24
                ],
                [
                    "2025-02-07",
                    -4.95
                ],
                [
                    "2025-02-10",
                    -3.44
                ],
                [
                    "2025-02-11",
                    -6.48
                ],
                [
                    "2025-02-12",
                    0.34
                ],
                [
                    "2025-02-13",
                    1.23
                ],
                [
                    "2025-02-14",
                    -3.97
                ],
                [
                    "2025-02-17",
                    0.84
                ],
                [
                    "2025-02-18",
                    0.18
                ],
                [
                    "2025-02-19",
                    -14.39
                ],
                [
                    "2025-02-20",
                    -10.28
                ],
                [
                    "2025-02-21",
                    -9.67
                ],
                [
                    "2025-02-24",
                    -4.36
                ],
                [
                    "2025-02-25",
                    -8.22
                ],
                [
                    "2025-02-26",
                    -4.06
                ],
                [
                    "2025-02-27",
                    8.93
                ],
                [
                    "2025-02-28",
                    14.45
                ],
                [
                    "2025-03-03",
                    -6.76
                ],
                [
                    "2025-03-04",
                    -2.50
                ],
                [
                    "2025-03-05",
                    -1.75
                ],
                [
                    "2025-03-06",
                    -7.51
                ],
                [
                    "2025-03-07",
                    -7.46
                ],
                [
                    "2025-03-10",
                    -13.04
                ],
                [
                    "2025-03-11",
                    4.27
                ],
                [
                    "2025-03-12",
                    -6.68
                ],
                [
                    "2025-03-13",
                    4.51
                ],
                [
                    "2025-03-14",
                    -12.72
                ],
                [
                    "2025-03-17",
                    -3.80
                ],
                [
                    "2025-03-18",
                    -15.32
                ],
                [
                    "2025-03-19",
                    -18.78
                ],
                [
                    "2025-03-20",
                    -9.10
                ],
                [
                    "2025-03-21",
                    10.11
                ],
                [
                    "2025-03-24",
                    11.17
                ],
                [
                    "2025-03-25",
                    6.07
                ],
                [
                    "2025-03-26",
                    -1.12
                ],
                [
                    "2025-03-27",
                    -12.96
                ],
                [
                    "2025-03-28",
                    -4.33
                ],
                [
                    "2025-03-31",
                    -13.54
                ],
                [
                    "2025-04-01",
                    -12.16
                ],
                [
                    "2025-04-02",
                    -6.46
                ],
                [
                    "2025-04-03",
                    10.89
                ],
                [
                    "2025-04-07",
                    -13.62
                ],
                [
                    "2025-04-08",
                    -1.95
                ],
                [
                    "2025-04-09",
                    -1.80
                ],
                [
                    "2025-04-10",
                    -3.62
                ],
                [
                    "2025-04-11",
                    -12.01
                ],
                [
                    "2025-04-14",
                    -2.59
                ],
                [
                    "2025-04-15",
                    -17.58
                ],
                [
                    "2025-04-16",
                    -15.81
                ],
                [
                    "2025-04-17",
                    1.37
                ],
                [
                    "2025-04-18",
                    48.68
                ],
                [
                    "2025-04-21",
                    -15.88
                ],
                [
                    "2025-04-22",
                    0.21
                ],
                [
                    "2025-04-23",
                    -7.84
                ],
                [
                    "2025-04-24",
                    1.71
                ],
                [
                    "2025-04-25",
                    -1.68
                ],
                [
                    "2025-04-28",
                    -9.92
                ],
                [
                    "2025-04-29",
                    -4.90
                ],
                [
                    "2025-04-30",
                    2.84
                ],
                [
                    "2025-05-06",
                    2.14
                ],
                [
                    "2025-05-07",
                    40.41
                ],
                [
                    "2025-05-08",
                    -1.34
                ],
                [
                    "2025-05-09",
                    -6.35
                ],
                [
                    "2025-05-12",
                    6.90
                ],
                [
                    "2025-05-13",
                    -10.81
                ],
                [
                    "2025-05-14",
                    -7.18
                ],
                [
                    "2025-05-15",
                    -4.16
                ],
                [
                    "2025-05-16",
                    0.37
                ],
                [
                    "2025-05-19",
                    -1.36
                ],
                [
                    "2025-05-20",
                    7.73
                ],
                [
                    "2025-05-21",
                    -12.21
                ],
                [
                    "2025-05-22",
                    -18.04
                ],
                [
                    "2025-05-23",
                    7.87
                ],
                [
                    "2025-05-26",
                    4.89
                ],
                [
                    "2025-05-27",
                    -14.41
                ],
                [
                    "2025-05-28",
                    -12.41
                ],
                [
                    "2025-05-29",
                    16.71
                ],
                [
                    "2025-05-30",
                    -11.72
                ],
                [
                    "2025-06-03",
                    -7.02
                ],
                [
                    "2025-06-04",
                    3.76
                ],
                [
                    "2025-06-05",
                    -12.09
                ],
                [
                    "2025-06-06",
                    -10.73
                ],
                [
                    "2025-06-09",
                    9.23
                ],
                [
                    "2025-06-10",
                    -8.23
                ],
                [
                    "2025-06-11",
                    0.34
                ],
                [
                    "2025-06-12",
                    -10.03
                ],
                [
                    "2025-06-13",
                    -10.79
                ],
                [
                    "2025-06-16",
                    11.05
                ],
                [
                    "2025-06-17",
                    -7.45
                ],
                [
                    "2025-06-18",
                    -1.38
                ],
                [
                    "2025-06-19",
                    -19.17
                ],
                [
                    "2025-06-20",
                    -8.38
                ],
                [
                    "2025-06-23",
                    -2.60
                ],
                [
                    "2025-06-24",
                    -6.90
                ],
                [
                    "2025-06-25",
                    -5.13
                ],
                [
                    "2025-06-26",
                    -9.43
                ],
                [
                    "2025-06-27",
                    0.92
                ],
                [
                    "2025-06-30",
                    2.93
                ],
                [
                    "2025-07-01",
                    9.50
                ],
                [
                    "2025-07-02",
                    8.71
                ],
                [
                    "2025-07-03",
                    -4.12
                ],
                [
                    "2025-07-04",
                    -8.41
                ],
                [
                    "2025-07-07",
                    -1.62
                ],
                [
                    "2025-07-08",
                    -6.82
                ],
                [
                    "2025-07-09",
                    -2.06
                ],
                [
                    "2025-07-10",
                    7.37
                ],
                [
                    "2025-07-11",
                    -13.11
                ],
                [
                    "2025-07-14",
                    -2.19
                ],
                [
                    "2025-07-15",
                    -3.93
                ],
                [
                    "2025-07-16",
                    -11.79
                ],
                [
                    "2025-07-17",
                    14.04
                ],
                [
                    "2025-07-18",
                    -5.08
                ],
                [
                    "2025-07-21",
                    -1.92
                ],
                [
                    "2025-07-22",
                    4.55
                ],
                [
                    "2025-07-23",
                    2.89
                ],
                [
                    "2025-07-24",
                    -0.89
                ],
                [
                    "2025-07-25",
                    11.12
                ],
                [
                    "2025-07-28",
                    -15.06
                ],
                [
                    "2025-07-29",
                    -0.76
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    0.21
                ],
                [
                    "2025-02-05",
                    -2.03
                ],
                [
                    "2025-02-06",
                    -2.46
                ],
                [
                    "2025-02-07",
                    1.36
                ],
                [
                    "2025-02-10",
                    -2.92
                ],
                [
                    "2025-02-11",
                    -4.21
                ],
                [
                    "2025-02-12",
                    -1.14
                ],
                [
                    "2025-02-13",
                    1.97
                ],
                [
                    "2025-02-14",
                    0.47
                ],
                [
                    "2025-02-17",
                    -6.94
                ],
                [
                    "2025-02-18",
                    1.76
                ],
                [
                    "2025-02-19",
                    3.27
                ],
                [
                    "2025-02-20",
                    0.89
                ],
                [
                    "2025-02-21",
                    1.68
                ],
                [
                    "2025-02-24",
                    -3.17
                ],
                [
                    "2025-02-25",
                    -1.02
                ],
                [
                    "2025-02-26",
                    -5.17
                ],
                [
                    "2025-02-27",
                    -1.53
                ],
                [
                    "2025-02-28",
                    1.29
                ],
                [
                    "2025-03-03",
                    3.97
                ],
                [
                    "2025-03-04",
                    -3.47
                ],
                [
                    "2025-03-05",
                    -2.01
                ],
                [
                    "2025-03-06",
                    -3.24
                ],
                [
                    "2025-03-07",
                    -5.12
                ],
                [
                    "2025-03-10",
                    3.46
                ],
                [
                    "2025-03-11",
                    1.98
                ],
                [
                    "2025-03-12",
                    -6.18
                ],
                [
                    "2025-03-13",
                    4.73
                ],
                [
                    "2025-03-14",
                    5.29
                ],
                [
                    "2025-03-17",
                    -2.57
                ],
                [
                    "2025-03-18",
                    6.59
                ],
                [
                    "2025-03-19",
                    6.93
                ],
                [
                    "2025-03-20",
                    -4.19
                ],
                [
                    "2025-03-21",
                    -4.74
                ],
                [
                    "2025-03-24",
                    2.93
                ],
                [
                    "2025-03-25",
                    -4.54
                ],
                [
                    "2025-03-26",
                    -4.66
                ],
                [
                    "2025-03-27",
                    7.54
                ],
                [
                    "2025-03-28",
                    3.29
                ],
                [
                    "2025-03-31",
                    3.88
                ],
                [
                    "2025-04-01",
                    -1.73
                ],
                [
                    "2025-04-02",
                    -4.45
                ],
                [
                    "2025-04-03",
                    -6.82
                ],
                [
                    "2025-04-07",
                    2.81
                ],
                [
                    "2025-04-08",
                    0.52
                ],
                [
                    "2025-04-09",
                    -0.83
                ],
                [
                    "2025-04-10",
                    0.41
                ],
                [
                    "2025-04-11",
                    1.14
                ],
                [
                    "2025-04-14",
                    -9.41
                ],
                [
                    "2025-04-15",
                    8.13
                ],
                [
                    "2025-04-16",
                    7.21
                ],
                [
                    "2025-04-17",
                    -6.28
                ],
                [
                    "2025-04-18",
                    -25.81
                ],
                [
                    "2025-04-21",
                    0.76
                ],
                [
                    "2025-04-22",
                    1.55
                ],
                [
                    "2025-04-23",
                    -0.49
                ],
                [
                    "2025-04-24",
                    0.97
                ],
                [
                    "2025-04-25",
                    -0.17
                ],
                [
                    "2025-04-28",
                    -0.05
                ],
                [
                    "2025-04-29",
                    -5.63
                ],
                [
                    "2025-04-30",
                    1.17
                ],
                [
                    "2025-05-06",
                    0.42
                ],
                [
                    "2025-05-07",
                    -21.86
                ],
                [
                    "2025-05-08",
                    -1.70
                ],
                [
                    "2025-05-09",
                    -5.70
                ],
                [
                    "2025-05-12",
                    -0.81
                ],
                [
                    "2025-05-13",
                    -0.17
                ],
                [
                    "2025-05-14",
                    0.55
                ],
                [
                    "2025-05-15",
                    5.87
                ],
                [
                    "2025-05-16",
                    -0.16
                ],
                [
                    "2025-05-19",
                    0.54
                ],
                [
                    "2025-05-20",
                    -1.02
                ],
                [
                    "2025-05-21",
                    5.24
                ],
                [
                    "2025-05-22",
                    -0.70
                ],
                [
                    "2025-05-23",
                    -8.03
                ],
                [
                    "2025-05-26",
                    -4.56
                ],
                [
                    "2025-05-27",
                    4.40
                ],
                [
                    "2025-05-28",
                    -5.18
                ],
                [
                    "2025-05-29",
                    -5.58
                ],
                [
                    "2025-05-30",
                    5.45
                ],
                [
                    "2025-06-03",
                    -1.60
                ],
                [
                    "2025-06-04",
                    -5.22
                ],
                [
                    "2025-06-05",
                    3.61
                ],
                [
                    "2025-06-06",
                    1.45
                ],
                [
                    "2025-06-09",
                    -5.33
                ],
                [
                    "2025-06-10",
                    -0.53
                ],
                [
                    "2025-06-11",
                    -6.53
                ],
                [
                    "2025-06-12",
                    1.73
                ],
                [
                    "2025-06-13",
                    -6.77
                ],
                [
                    "2025-06-16",
                    -4.11
                ],
                [
                    "2025-06-17",
                    -0.64
                ],
                [
                    "2025-06-18",
                    1.93
                ],
                [
                    "2025-06-19",
                    3.75
                ],
                [
                    "2025-06-20",
                    -0.74
                ],
                [
                    "2025-06-23",
                    -1.10
                ],
                [
                    "2025-06-24",
                    10.33
                ],
                [
                    "2025-06-25",
                    -1.78
                ],
                [
                    "2025-06-26",
                    3.31
                ],
                [
                    "2025-06-27",
                    -3.17
                ],
                [
                    "2025-06-30",
                    -1.41
                ],
                [
                    "2025-07-01",
                    -4.97
                ],
                [
                    "2025-07-02",
                    -3.16
                ],
                [
                    "2025-07-03",
                    4.05
                ],
                [
                    "2025-07-04",
                    1.34
                ],
                [
                    "2025-07-07",
                    -2.53
                ],
                [
                    "2025-07-08",
                    -4.72
                ],
                [
                    "2025-07-09",
                    -2.99
                ],
                [
                    "2025-07-10",
                    -5.43
                ],
                [
                    "2025-07-11",
                    0.73
                ],
                [
                    "2025-07-14",
                    -3.35
                ],
                [
                    "2025-07-15",
                    0.80
                ],
                [
                    "2025-07-16",
                    -1.33
                ],
                [
                    "2025-07-17",
                    -6.06
                ],
                [
                    "2025-07-18",
                    1.51
                ],
                [
                    "2025-07-21",
                    2.05
                ],
                [
                    "2025-07-22",
                    -1.83
                ],
                [
                    "2025-07-23",
                    -2.37
                ],
                [
                    "2025-07-24",
                    -0.77
                ],
                [
                    "2025-07-25",
                    -7.33
                ],
                [
                    "2025-07-28",
                    9.55
                ],
                [
                    "2025-07-29",
                    5.51
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    1.91
                ],
                [
                    "2025-02-05",
                    -0.16
                ],
                [
                    "2025-02-06",
                    2.71
                ],
                [
                    "2025-02-07",
                    3.58
                ],
                [
                    "2025-02-10",
                    6.36
                ],
                [
                    "2025-02-11",
                    10.69
                ],
                [
                    "2025-02-12",
                    0.80
                ],
                [
                    "2025-02-13",
                    -3.19
                ],
                [
                    "2025-02-14",
                    3.49
                ],
                [
                    "2025-02-17",
                    6.09
                ],
                [
                    "2025-02-18",
                    -1.94
                ],
                [
                    "2025-02-19",
                    11.12
                ],
                [
                    "2025-02-20",
                    9.40
                ],
                [
                    "2025-02-21",
                    8.00
                ],
                [
                    "2025-02-24",
                    7.54
                ],
                [
                    "2025-02-25",
                    9.25
                ],
                [
                    "2025-02-26",
                    9.22
                ],
                [
                    "2025-02-27",
                    -7.40
                ],
                [
                    "2025-02-28",
                    -15.74
                ],
                [
                    "2025-03-03",
                    2.78
                ],
                [
                    "2025-03-04",
                    5.97
                ],
                [
                    "2025-03-05",
                    3.76
                ],
                [
                    "2025-03-06",
                    10.75
                ],
                [
                    "2025-03-07",
                    12.59
                ],
                [
                    "2025-03-10",
                    9.58
                ],
                [
                    "2025-03-11",
                    -6.25
                ],
                [
                    "2025-03-12",
                    12.86
                ],
                [
                    "2025-03-13",
                    -9.25
                ],
                [
                    "2025-03-14",
                    7.43
                ],
                [
                    "2025-03-17",
                    6.37
                ],
                [
                    "2025-03-18",
                    8.72
                ],
                [
                    "2025-03-19",
                    11.84
                ],
                [
                    "2025-03-20",
                    13.29
                ],
                [
                    "2025-03-21",
                    -5.37
                ],
                [
                    "2025-03-24",
                    -14.10
                ],
                [
                    "2025-03-25",
                    -1.53
                ],
                [
                    "2025-03-26",
                    5.78
                ],
                [
                    "2025-03-27",
                    5.42
                ],
                [
                    "2025-03-28",
                    1.03
                ],
                [
                    "2025-03-31",
                    9.67
                ],
                [
                    "2025-04-01",
                    13.89
                ],
                [
                    "2025-04-02",
                    10.91
                ],
                [
                    "2025-04-03",
                    -4.06
                ],
                [
                    "2025-04-07",
                    10.81
                ],
                [
                    "2025-04-08",
                    1.44
                ],
                [
                    "2025-04-09",
                    2.62
                ],
                [
                    "2025-04-10",
                    3.21
                ],
                [
                    "2025-04-11",
                    10.87
                ],
                [
                    "2025-04-14",
                    12.00
                ],
                [
                    "2025-04-15",
                    9.45
                ],
                [
                    "2025-04-16",
                    8.59
                ],
                [
                    "2025-04-17",
                    4.91
                ],
                [
                    "2025-04-18",
                    -22.88
                ],
                [
                    "2025-04-21",
                    15.12
                ],
                [
                    "2025-04-22",
                    -1.76
                ],
                [
                    "2025-04-23",
                    8.33
                ],
                [
                    "2025-04-24",
                    -2.67
                ],
                [
                    "2025-04-25",
                    1.85
                ],
                [
                    "2025-04-28",
                    9.96
                ],
                [
                    "2025-04-29",
                    10.53
                ],
                [
                    "2025-04-30",
                    -4.02
                ],
                [
                    "2025-05-06",
                    -2.57
                ],
                [
                    "2025-05-07",
                    -18.55
                ],
                [
                    "2025-05-08",
                    3.05
                ],
                [
                    "2025-05-09",
                    12.05
                ],
                [
                    "2025-05-12",
                    -6.09
                ],
                [
                    "2025-05-13",
                    10.98
                ],
                [
                    "2025-05-14",
                    6.63
                ],
                [
                    "2025-05-15",
                    -1.71
                ],
                [
                    "2025-05-16",
                    -0.21
                ],
                [
                    "2025-05-19",
                    0.82
                ],
                [
                    "2025-05-20",
                    -6.72
                ],
                [
                    "2025-05-21",
                    6.98
                ],
                [
                    "2025-05-22",
                    18.74
                ],
                [
                    "2025-05-23",
                    0.16
                ],
                [
                    "2025-05-26",
                    -0.34
                ],
                [
                    "2025-05-27",
                    10.00
                ],
                [
                    "2025-05-28",
                    17.59
                ],
                [
                    "2025-05-29",
                    -11.13
                ],
                [
                    "2025-05-30",
                    6.27
                ],
                [
                    "2025-06-03",
                    8.62
                ],
                [
                    "2025-06-04",
                    1.46
                ],
                [
                    "2025-06-05",
                    8.48
                ],
                [
                    "2025-06-06",
                    9.28
                ],
                [
                    "2025-06-09",
                    -3.90
                ],
                [
                    "2025-06-10",
                    8.76
                ],
                [
                    "2025-06-11",
                    6.19
                ],
                [
                    "2025-06-12",
                    8.30
                ],
                [
                    "2025-06-13",
                    17.56
                ],
                [
                    "2025-06-16",
                    -6.94
                ],
                [
                    "2025-06-17",
                    8.09
                ],
                [
                    "2025-06-18",
                    -0.55
                ],
                [
                    "2025-06-19",
                    15.42
                ],
                [
                    "2025-06-20",
                    9.12
                ],
                [
                    "2025-06-23",
                    3.70
                ],
                [
                    "2025-06-24",
                    -3.43
                ],
                [
                    "2025-06-25",
                    6.90
                ],
                [
                    "2025-06-26",
                    6.12
                ],
                [
                    "2025-06-27",
                    2.26
                ],
                [
                    "2025-06-30",
                    -1.52
                ],
                [
                    "2025-07-01",
                    -4.53
                ],
                [
                    "2025-07-02",
                    -5.55
                ],
                [
                    "2025-07-03",
                    0.08
                ],
                [
                    "2025-07-04",
                    7.07
                ],
                [
                    "2025-07-07",
                    4.15
                ],
                [
                    "2025-07-08",
                    11.55
                ],
                [
                    "2025-07-09",
                    5.04
                ],
                [
                    "2025-07-10",
                    -1.94
                ],
                [
                    "2025-07-11",
                    12.38
                ],
                [
                    "2025-07-14",
                    5.55
                ],
                [
                    "2025-07-15",
                    3.13
                ],
                [
                    "2025-07-16",
                    13.12
                ],
                [
                    "2025-07-17",
                    -7.98
                ],
                [
                    "2025-07-18",
                    3.57
                ],
                [
                    "2025-07-21",
                    -0.13
                ],
                [
                    "2025-07-22",
                    -2.71
                ],
                [
                    "2025-07-23",
                    -0.52
                ],
                [
                    "2025-07-24",
                    1.66
                ],
                [
                    "2025-07-25",
                    -3.79
                ],
                [
                    "2025-07-28",
                    5.51
                ],
                [
                    "2025-07-29",
                    -4.75
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-03",
                    -6.76
                ],
                [
                    "2025-03-04",
                    -2.50
                ],
                [
                    "2025-03-05",
                    -1.75
                ],
                [
                    "2025-03-26",
                    -1.12
                ],
                [
                    "2025-03-27",
                    -12.96
                ],
                [
                    "2025-04-24",
                    1.71
                ],
                [
                    "2025-05-07",
                    40.41
                ],
                [
                    "2025-05-08",
                    -1.34
                ],
                [
                    "2025-05-09",
                    -6.35
                ],
                [
                    "2025-05-12",
                    6.90
                ],
                [
                    "2025-07-02",
                    8.71
                ],
                [
                    "2025-07-03",
                    -4.12
                ],
                [
                    "2025-07-04",
                    -8.41
                ],
                [
                    "2025-07-23",
                    2.89
                ],
                [
                    "2025-07-25",
                    11.12
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-28",
                    3.29
                ],
                [
                    "2025-05-20",
                    -1.02
                ],
                [
                    "2025-05-21",
                    5.24
                ],
                [
                    "2025-07-29",
                    5.51
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    6.36
                ],
                [
                    "2025-02-11",
                    10.69
                ],
                [
                    "2025-02-12",
                    0.80
                ],
                [
                    "2025-02-13",
                    -3.19
                ],
                [
                    "2025-02-14",
                    3.49
                ],
                [
                    "2025-02-17",
                    6.09
                ],
                [
                    "2025-02-18",
                    -1.94
                ],
                [
                    "2025-02-19",
                    11.12
                ],
                [
                    "2025-02-20",
                    9.40
                ],
                [
                    "2025-02-21",
                    8.00
                ],
                [
                    "2025-02-24",
                    7.54
                ],
                [
                    "2025-02-25",
                    9.25
                ],
                [
                    "2025-02-26",
                    9.22
                ],
                [
                    "2025-02-27",
                    -7.40
                ],
                [
                    "2025-03-06",
                    10.75
                ],
                [
                    "2025-03-07",
                    12.59
                ],
                [
                    "2025-03-10",
                    9.58
                ],
                [
                    "2025-03-11",
                    -6.25
                ],
                [
                    "2025-03-12",
                    12.86
                ],
                [
                    "2025-03-13",
                    -9.25
                ],
                [
                    "2025-03-14",
                    7.43
                ],
                [
                    "2025-03-17",
                    6.37
                ],
                [
                    "2025-03-18",
                    8.72
                ],
                [
                    "2025-03-19",
                    11.84
                ],
                [
                    "2025-03-20",
                    13.29
                ],
                [
                    "2025-03-21",
                    -5.37
                ],
                [
                    "2025-03-24",
                    -14.10
                ],
                [
                    "2025-03-25",
                    -1.53
                ],
                [
                    "2025-03-31",
                    9.67
                ],
                [
                    "2025-04-01",
                    13.89
                ],
                [
                    "2025-04-02",
                    10.91
                ],
                [
                    "2025-04-03",
                    -4.06
                ],
                [
                    "2025-04-07",
                    10.81
                ],
                [
                    "2025-04-08",
                    1.44
                ],
                [
                    "2025-04-09",
                    2.62
                ],
                [
                    "2025-04-10",
                    3.21
                ],
                [
                    "2025-04-11",
                    10.87
                ],
                [
                    "2025-04-14",
                    12.00
                ],
                [
                    "2025-04-15",
                    9.45
                ],
                [
                    "2025-04-16",
                    8.59
                ],
                [
                    "2025-04-17",
                    4.91
                ],
                [
                    "2025-04-25",
                    1.85
                ],
                [
                    "2025-04-28",
                    9.96
                ],
                [
                    "2025-04-29",
                    10.53
                ],
                [
                    "2025-04-30",
                    -4.02
                ],
                [
                    "2025-05-06",
                    -2.57
                ],
                [
                    "2025-05-14",
                    6.63
                ],
                [
                    "2025-05-15",
                    -1.71
                ],
                [
                    "2025-05-16",
                    -0.21
                ],
                [
                    "2025-05-19",
                    0.82
                ],
                [
                    "2025-05-22",
                    18.74
                ],
                [
                    "2025-05-23",
                    0.16
                ],
                [
                    "2025-05-26",
                    -0.34
                ],
                [
                    "2025-05-27",
                    10.00
                ],
                [
                    "2025-05-28",
                    17.59
                ],
                [
                    "2025-05-30",
                    6.27
                ],
                [
                    "2025-06-03",
                    8.62
                ],
                [
                    "2025-06-04",
                    1.46
                ],
                [
                    "2025-06-05",
                    8.48
                ],
                [
                    "2025-06-06",
                    9.28
                ],
                [
                    "2025-06-09",
                    -3.90
                ],
                [
                    "2025-06-10",
                    8.76
                ],
                [
                    "2025-06-11",
                    6.19
                ],
                [
                    "2025-06-12",
                    8.30
                ],
                [
                    "2025-06-13",
                    17.56
                ],
                [
                    "2025-06-16",
                    -6.94
                ],
                [
                    "2025-06-17",
                    8.09
                ],
                [
                    "2025-06-18",
                    -0.55
                ],
                [
                    "2025-06-19",
                    15.42
                ],
                [
                    "2025-06-20",
                    9.12
                ],
                [
                    "2025-06-23",
                    3.70
                ],
                [
                    "2025-06-24",
                    -3.43
                ],
                [
                    "2025-06-25",
                    6.90
                ],
                [
                    "2025-06-26",
                    6.12
                ],
                [
                    "2025-06-27",
                    2.26
                ],
                [
                    "2025-06-30",
                    -1.52
                ],
                [
                    "2025-07-01",
                    -4.53
                ],
                [
                    "2025-07-08",
                    11.55
                ],
                [
                    "2025-07-09",
                    5.04
                ],
                [
                    "2025-07-10",
                    -1.94
                ],
                [
                    "2025-07-11",
                    12.38
                ],
                [
                    "2025-07-14",
                    5.55
                ],
                [
                    "2025-07-15",
                    3.13
                ],
                [
                    "2025-07-16",
                    13.12
                ],
                [
                    "2025-07-17",
                    -7.98
                ],
                [
                    "2025-07-18",
                    3.57
                ],
                [
                    "2025-07-21",
                    -0.13
                ],
                [
                    "2025-07-22",
                    -2.71
                ],
                [
                    "2025-07-24",
                    1.66
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "000863 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_07d0cbda7c86414bad55bc2b447b288e.setOption(option_07d0cbda7c86414bad55bc2b447b288e);
            window.addEventListener('resize', function(){
                chart_07d0cbda7c86414bad55bc2b447b288e.resize();
            })
    </script>
</body>
</html>
