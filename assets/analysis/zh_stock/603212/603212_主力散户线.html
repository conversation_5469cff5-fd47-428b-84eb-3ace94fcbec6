<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="20febeaedd1e42028486fe5723752d27" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_20febeaedd1e42028486fe5723752d27 = echarts.init(
            document.getElementById('20febeaedd1e42028486fe5723752d27'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_20febeaedd1e42028486fe5723752d27 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    10.5,
                    10.13,
                    10.11,
                    10.6
                ],
                [
                    10.28,
                    10.19,
                    10.06,
                    10.29
                ],
                [
                    10.15,
                    10.58,
                    10.11,
                    10.58
                ],
                [
                    10.52,
                    10.78,
                    10.52,
                    10.89
                ],
                [
                    10.75,
                    10.8,
                    10.61,
                    10.8
                ],
                [
                    10.76,
                    10.66,
                    10.54,
                    10.78
                ],
                [
                    10.62,
                    10.7,
                    10.56,
                    10.73
                ],
                [
                    10.78,
                    10.6,
                    10.59,
                    10.87
                ],
                [
                    10.53,
                    10.75,
                    10.52,
                    10.85
                ],
                [
                    10.73,
                    10.77,
                    10.71,
                    10.88
                ],
                [
                    10.8,
                    10.52,
                    10.5,
                    10.98
                ],
                [
                    10.45,
                    10.84,
                    10.43,
                    10.85
                ],
                [
                    10.89,
                    10.81,
                    10.68,
                    10.92
                ],
                [
                    10.83,
                    10.75,
                    10.65,
                    10.86
                ],
                [
                    10.75,
                    10.92,
                    10.61,
                    10.93
                ],
                [
                    10.82,
                    10.96,
                    10.75,
                    11.06
                ],
                [
                    10.98,
                    11.26,
                    10.97,
                    11.27
                ],
                [
                    11.04,
                    10.94,
                    10.7,
                    11.15
                ],
                [
                    10.82,
                    10.67,
                    10.65,
                    11.07
                ],
                [
                    10.71,
                    10.95,
                    10.71,
                    11.24
                ],
                [
                    10.85,
                    10.86,
                    10.64,
                    10.89
                ],
                [
                    10.83,
                    10.56,
                    10.48,
                    10.89
                ],
                [
                    10.62,
                    10.7,
                    10.53,
                    10.79
                ],
                [
                    10.65,
                    10.62,
                    10.4,
                    10.75
                ],
                [
                    10.62,
                    10.95,
                    10.6,
                    11.18
                ],
                [
                    10.82,
                    10.98,
                    10.73,
                    11.06
                ],
                [
                    10.99,
                    10.9,
                    10.87,
                    11.1
                ],
                [
                    10.91,
                    10.61,
                    10.52,
                    10.96
                ],
                [
                    10.65,
                    10.86,
                    10.54,
                    10.92
                ],
                [
                    10.91,
                    10.88,
                    10.84,
                    11.07
                ],
                [
                    10.89,
                    11.05,
                    10.85,
                    11.09
                ],
                [
                    10.97,
                    10.91,
                    10.79,
                    11.05
                ],
                [
                    10.86,
                    10.83,
                    10.81,
                    10.96
                ],
                [
                    10.82,
                    10.65,
                    10.62,
                    10.87
                ],
                [
                    10.66,
                    10.36,
                    10.1,
                    10.7
                ],
                [
                    10.37,
                    10.48,
                    10.25,
                    10.57
                ],
                [
                    10.44,
                    10.8,
                    10.42,
                    10.96
                ],
                [
                    10.72,
                    10.51,
                    10.5,
                    10.77
                ],
                [
                    10.51,
                    10.37,
                    10.34,
                    10.84
                ],
                [
                    10.3,
                    10.31,
                    10.16,
                    10.77
                ],
                [
                    10.23,
                    10.49,
                    10.23,
                    10.65
                ],
                [
                    10.45,
                    10.78,
                    10.44,
                    11.2
                ],
                [
                    10.59,
                    10.55,
                    10.51,
                    10.79
                ],
                [
                    9.89,
                    9.5,
                    9.5,
                    10.07
                ],
                [
                    9.21,
                    9.28,
                    9.12,
                    9.6
                ],
                [
                    9.05,
                    9.28,
                    8.45,
                    9.29
                ],
                [
                    9.68,
                    9.43,
                    9.42,
                    9.71
                ],
                [
                    9.33,
                    9.46,
                    9.31,
                    9.59
                ],
                [
                    9.55,
                    9.62,
                    9.55,
                    9.77
                ],
                [
                    9.55,
                    9.61,
                    9.5,
                    9.68
                ],
                [
                    9.56,
                    9.39,
                    9.29,
                    9.58
                ],
                [
                    9.32,
                    9.41,
                    9.32,
                    9.51
                ],
                [
                    9.42,
                    9.3,
                    9.25,
                    9.44
                ],
                [
                    9.22,
                    9.37,
                    9.22,
                    9.39
                ],
                [
                    9.39,
                    9.55,
                    9.28,
                    9.65
                ],
                [
                    9.55,
                    9.49,
                    9.43,
                    9.63
                ],
                [
                    9.5,
                    9.33,
                    9.29,
                    9.5
                ],
                [
                    9.3,
                    9.31,
                    9.3,
                    9.43
                ],
                [
                    9.3,
                    9.02,
                    9.02,
                    9.32
                ],
                [
                    8.97,
                    9.05,
                    8.95,
                    9.23
                ],
                [
                    9.02,
                    9.27,
                    9.02,
                    9.38
                ],
                [
                    9.32,
                    9.59,
                    9.3,
                    9.59
                ],
                [
                    9.64,
                    9.61,
                    9.54,
                    9.74
                ],
                [
                    9.6,
                    9.76,
                    9.5,
                    9.78
                ],
                [
                    9.78,
                    9.58,
                    9.52,
                    9.78
                ],
                [
                    9.6,
                    9.78,
                    9.6,
                    9.78
                ],
                [
                    9.83,
                    9.89,
                    9.83,
                    10.26
                ],
                [
                    9.91,
                    9.84,
                    9.78,
                    9.93
                ],
                [
                    9.82,
                    9.68,
                    9.67,
                    9.85
                ],
                [
                    9.68,
                    9.74,
                    9.62,
                    9.84
                ],
                [
                    9.74,
                    9.78,
                    9.66,
                    9.79
                ],
                [
                    9.79,
                    9.83,
                    9.71,
                    9.85
                ],
                [
                    9.79,
                    9.76,
                    9.71,
                    9.87
                ],
                [
                    9.74,
                    9.45,
                    9.42,
                    9.8
                ],
                [
                    9.42,
                    9.4,
                    9.38,
                    9.61
                ],
                [
                    9.44,
                    9.45,
                    9.35,
                    9.53
                ],
                [
                    9.46,
                    9.48,
                    9.33,
                    9.5
                ],
                [
                    9.45,
                    9.34,
                    9.3,
                    9.53
                ],
                [
                    9.35,
                    9.5,
                    9.33,
                    9.52
                ],
                [
                    9.47,
                    9.36,
                    9.31,
                    9.52
                ],
                [
                    9.31,
                    9.43,
                    9.29,
                    9.52
                ],
                [
                    9.48,
                    9.57,
                    9.44,
                    9.58
                ],
                [
                    9.58,
                    9.58,
                    9.48,
                    9.61
                ],
                [
                    9.68,
                    9.63,
                    9.6,
                    9.79
                ],
                [
                    9.63,
                    9.83,
                    9.63,
                    9.88
                ],
                [
                    9.84,
                    9.71,
                    9.6,
                    9.87
                ],
                [
                    9.76,
                    9.71,
                    9.69,
                    9.89
                ],
                [
                    9.71,
                    9.69,
                    9.62,
                    9.74
                ],
                [
                    9.68,
                    9.38,
                    9.31,
                    9.68
                ],
                [
                    9.33,
                    9.57,
                    9.33,
                    9.75
                ],
                [
                    9.58,
                    9.6,
                    9.53,
                    9.66
                ],
                [
                    9.52,
                    9.59,
                    9.44,
                    9.6
                ],
                [
                    9.56,
                    9.66,
                    9.55,
                    9.92
                ],
                [
                    10.49,
                    10.63,
                    10.49,
                    10.63
                ],
                [
                    11.66,
                    10.86,
                    10.72,
                    11.66
                ],
                [
                    10.69,
                    11.95,
                    10.51,
                    11.95
                ],
                [
                    12.18,
                    12.39,
                    12.08,
                    13.15
                ],
                [
                    11.8,
                    12.0,
                    11.79,
                    12.99
                ],
                [
                    11.86,
                    11.35,
                    11.35,
                    12.1
                ],
                [
                    11.55,
                    12.49,
                    11.55,
                    12.49
                ],
                [
                    12.84,
                    12.87,
                    12.6,
                    13.29
                ],
                [
                    12.86,
                    14.16,
                    12.44,
                    14.16
                ],
                [
                    14.5,
                    13.45,
                    13.31,
                    14.58
                ],
                [
                    13.67,
                    12.9,
                    12.84,
                    14.15
                ],
                [
                    12.37,
                    12.61,
                    12.27,
                    12.98
                ],
                [
                    12.5,
                    13.08,
                    12.35,
                    13.44
                ],
                [
                    12.83,
                    12.41,
                    12.32,
                    12.92
                ],
                [
                    12.21,
                    12.12,
                    12.11,
                    12.65
                ],
                [
                    12.21,
                    12.18,
                    11.9,
                    12.28
                ],
                [
                    12.22,
                    12.35,
                    12.13,
                    12.38
                ],
                [
                    12.14,
                    11.48,
                    11.41,
                    12.15
                ],
                [
                    11.59,
                    11.58,
                    11.44,
                    11.65
                ],
                [
                    11.66,
                    11.69,
                    11.53,
                    11.82
                ],
                [
                    11.64,
                    11.65,
                    11.56,
                    11.84
                ],
                [
                    11.72,
                    11.75,
                    11.6,
                    11.75
                ],
                [
                    11.69,
                    11.81,
                    11.61,
                    11.94
                ],
                [
                    11.76,
                    11.46,
                    11.4,
                    11.85
                ],
                [
                    11.41,
                    11.73,
                    11.4,
                    11.74
                ],
                [
                    11.73,
                    11.59,
                    11.56,
                    11.76
                ],
                [
                    11.59,
                    11.66,
                    11.51,
                    11.67
                ],
                [
                    11.66,
                    11.75,
                    11.49,
                    11.8
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -13.21
                ],
                [
                    "2025-02-05",
                    -3.59
                ],
                [
                    "2025-02-06",
                    9.41
                ],
                [
                    "2025-02-07",
                    -2.70
                ],
                [
                    "2025-02-10",
                    -9.28
                ],
                [
                    "2025-02-11",
                    -11.08
                ],
                [
                    "2025-02-12",
                    -2.46
                ],
                [
                    "2025-02-13",
                    6.13
                ],
                [
                    "2025-02-14",
                    0.60
                ],
                [
                    "2025-02-17",
                    -0.62
                ],
                [
                    "2025-02-18",
                    0.12
                ],
                [
                    "2025-02-19",
                    5.58
                ],
                [
                    "2025-02-20",
                    -9.83
                ],
                [
                    "2025-02-21",
                    -17.38
                ],
                [
                    "2025-02-24",
                    -7.34
                ],
                [
                    "2025-02-25",
                    9.65
                ],
                [
                    "2025-02-26",
                    -1.00
                ],
                [
                    "2025-02-27",
                    -6.51
                ],
                [
                    "2025-02-28",
                    -1.20
                ],
                [
                    "2025-03-03",
                    1.32
                ],
                [
                    "2025-03-04",
                    -4.96
                ],
                [
                    "2025-03-05",
                    -11.32
                ],
                [
                    "2025-03-06",
                    -9.90
                ],
                [
                    "2025-03-07",
                    -1.85
                ],
                [
                    "2025-03-10",
                    -0.31
                ],
                [
                    "2025-03-11",
                    -2.94
                ],
                [
                    "2025-03-12",
                    -6.98
                ],
                [
                    "2025-03-13",
                    -14.07
                ],
                [
                    "2025-03-14",
                    0.34
                ],
                [
                    "2025-03-17",
                    -5.45
                ],
                [
                    "2025-03-18",
                    9.06
                ],
                [
                    "2025-03-19",
                    -5.60
                ],
                [
                    "2025-03-20",
                    0.12
                ],
                [
                    "2025-03-21",
                    -4.27
                ],
                [
                    "2025-03-24",
                    -5.52
                ],
                [
                    "2025-03-25",
                    -3.34
                ],
                [
                    "2025-03-26",
                    1.40
                ],
                [
                    "2025-03-27",
                    -5.70
                ],
                [
                    "2025-03-28",
                    5.48
                ],
                [
                    "2025-03-31",
                    10.55
                ],
                [
                    "2025-04-01",
                    2.77
                ],
                [
                    "2025-04-02",
                    -0.90
                ],
                [
                    "2025-04-03",
                    -3.42
                ],
                [
                    "2025-04-07",
                    -11.25
                ],
                [
                    "2025-04-08",
                    -7.84
                ],
                [
                    "2025-04-09",
                    -7.21
                ],
                [
                    "2025-04-10",
                    -0.56
                ],
                [
                    "2025-04-11",
                    -5.37
                ],
                [
                    "2025-04-14",
                    0.37
                ],
                [
                    "2025-04-15",
                    -5.38
                ],
                [
                    "2025-04-16",
                    -8.46
                ],
                [
                    "2025-04-17",
                    -6.92
                ],
                [
                    "2025-04-18",
                    -5.17
                ],
                [
                    "2025-04-21",
                    -3.55
                ],
                [
                    "2025-04-22",
                    0.53
                ],
                [
                    "2025-04-23",
                    -11.83
                ],
                [
                    "2025-04-24",
                    -1.67
                ],
                [
                    "2025-04-25",
                    -16.93
                ],
                [
                    "2025-04-28",
                    -11.10
                ],
                [
                    "2025-04-29",
                    -5.71
                ],
                [
                    "2025-04-30",
                    1.61
                ],
                [
                    "2025-05-06",
                    4.63
                ],
                [
                    "2025-05-07",
                    -20.91
                ],
                [
                    "2025-05-08",
                    -5.56
                ],
                [
                    "2025-05-09",
                    -10.47
                ],
                [
                    "2025-05-12",
                    -9.36
                ],
                [
                    "2025-05-13",
                    5.40
                ],
                [
                    "2025-05-14",
                    -11.15
                ],
                [
                    "2025-05-15",
                    -7.04
                ],
                [
                    "2025-05-16",
                    -3.85
                ],
                [
                    "2025-05-19",
                    -7.35
                ],
                [
                    "2025-05-20",
                    -2.58
                ],
                [
                    "2025-05-21",
                    -2.34
                ],
                [
                    "2025-05-22",
                    -14.46
                ],
                [
                    "2025-05-23",
                    -6.84
                ],
                [
                    "2025-05-26",
                    -5.21
                ],
                [
                    "2025-05-27",
                    -2.34
                ],
                [
                    "2025-05-28",
                    -5.74
                ],
                [
                    "2025-05-29",
                    -5.76
                ],
                [
                    "2025-05-30",
                    3.19
                ],
                [
                    "2025-06-03",
                    0.40
                ],
                [
                    "2025-06-04",
                    9.49
                ],
                [
                    "2025-06-05",
                    -3.82
                ],
                [
                    "2025-06-06",
                    -1.81
                ],
                [
                    "2025-06-09",
                    -3.08
                ],
                [
                    "2025-06-10",
                    -8.12
                ],
                [
                    "2025-06-11",
                    -0.27
                ],
                [
                    "2025-06-12",
                    -0.60
                ],
                [
                    "2025-06-13",
                    -12.46
                ],
                [
                    "2025-06-16",
                    6.26
                ],
                [
                    "2025-06-17",
                    -6.30
                ],
                [
                    "2025-06-18",
                    2.52
                ],
                [
                    "2025-06-19",
                    16.42
                ],
                [
                    "2025-06-20",
                    50.83
                ],
                [
                    "2025-06-23",
                    -18.01
                ],
                [
                    "2025-06-24",
                    5.25
                ],
                [
                    "2025-06-25",
                    -5.22
                ],
                [
                    "2025-06-26",
                    -4.60
                ],
                [
                    "2025-06-27",
                    -6.54
                ],
                [
                    "2025-06-30",
                    46.08
                ],
                [
                    "2025-07-01",
                    -10.65
                ],
                [
                    "2025-07-02",
                    13.86
                ],
                [
                    "2025-07-03",
                    -14.26
                ],
                [
                    "2025-07-04",
                    -4.42
                ],
                [
                    "2025-07-07",
                    0.52
                ],
                [
                    "2025-07-08",
                    -0.15
                ],
                [
                    "2025-07-09",
                    -8.25
                ],
                [
                    "2025-07-10",
                    -4.54
                ],
                [
                    "2025-07-11",
                    0.17
                ],
                [
                    "2025-07-14",
                    -2.89
                ],
                [
                    "2025-07-15",
                    -9.01
                ],
                [
                    "2025-07-16",
                    -4.73
                ],
                [
                    "2025-07-17",
                    -2.79
                ],
                [
                    "2025-07-18",
                    -8.29
                ],
                [
                    "2025-07-21",
                    -9.81
                ],
                [
                    "2025-07-22",
                    -1.87
                ],
                [
                    "2025-07-23",
                    -20.64
                ],
                [
                    "2025-07-24",
                    -4.95
                ],
                [
                    "2025-07-25",
                    -18.53
                ],
                [
                    "2025-07-28",
                    -2.52
                ],
                [
                    "2025-07-29",
                    -12.38
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    0.01
                ],
                [
                    "2025-02-05",
                    -5.89
                ],
                [
                    "2025-02-06",
                    -4.87
                ],
                [
                    "2025-02-07",
                    -3.51
                ],
                [
                    "2025-02-10",
                    -4.99
                ],
                [
                    "2025-02-11",
                    2.37
                ],
                [
                    "2025-02-12",
                    -3.05
                ],
                [
                    "2025-02-13",
                    -0.10
                ],
                [
                    "2025-02-14",
                    -0.86
                ],
                [
                    "2025-02-17",
                    1.53
                ],
                [
                    "2025-02-18",
                    4.00
                ],
                [
                    "2025-02-19",
                    -5.48
                ],
                [
                    "2025-02-20",
                    1.23
                ],
                [
                    "2025-02-21",
                    3.54
                ],
                [
                    "2025-02-24",
                    -1.08
                ],
                [
                    "2025-02-25",
                    2.51
                ],
                [
                    "2025-02-26",
                    -1.12
                ],
                [
                    "2025-02-27",
                    2.84
                ],
                [
                    "2025-02-28",
                    1.04
                ],
                [
                    "2025-03-03",
                    -1.66
                ],
                [
                    "2025-03-04",
                    -1.61
                ],
                [
                    "2025-03-05",
                    3.99
                ],
                [
                    "2025-03-06",
                    0.41
                ],
                [
                    "2025-03-07",
                    0.59
                ],
                [
                    "2025-03-10",
                    0.64
                ],
                [
                    "2025-03-11",
                    3.17
                ],
                [
                    "2025-03-12",
                    1.61
                ],
                [
                    "2025-03-13",
                    1.83
                ],
                [
                    "2025-03-14",
                    -0.87
                ],
                [
                    "2025-03-17",
                    0.04
                ],
                [
                    "2025-03-18",
                    -4.39
                ],
                [
                    "2025-03-19",
                    5.02
                ],
                [
                    "2025-03-20",
                    0.23
                ],
                [
                    "2025-03-21",
                    -2.52
                ],
                [
                    "2025-03-24",
                    5.43
                ],
                [
                    "2025-03-25",
                    1.94
                ],
                [
                    "2025-03-26",
                    -1.54
                ],
                [
                    "2025-03-27",
                    6.76
                ],
                [
                    "2025-03-28",
                    5.65
                ],
                [
                    "2025-03-31",
                    6.39
                ],
                [
                    "2025-04-01",
                    -0.97
                ],
                [
                    "2025-04-02",
                    3.38
                ],
                [
                    "2025-04-03",
                    0.61
                ],
                [
                    "2025-04-07",
                    3.29
                ],
                [
                    "2025-04-08",
                    6.70
                ],
                [
                    "2025-04-09",
                    2.81
                ],
                [
                    "2025-04-10",
                    0.18
                ],
                [
                    "2025-04-11",
                    0.87
                ],
                [
                    "2025-04-14",
                    -1.73
                ],
                [
                    "2025-04-15",
                    -3.04
                ],
                [
                    "2025-04-16",
                    -1.15
                ],
                [
                    "2025-04-17",
                    -4.68
                ],
                [
                    "2025-04-18",
                    -1.35
                ],
                [
                    "2025-04-21",
                    -6.01
                ],
                [
                    "2025-04-22",
                    2.14
                ],
                [
                    "2025-04-23",
                    7.96
                ],
                [
                    "2025-04-24",
                    0.06
                ],
                [
                    "2025-04-25",
                    5.42
                ],
                [
                    "2025-04-28",
                    -5.12
                ],
                [
                    "2025-04-29",
                    -6.04
                ],
                [
                    "2025-04-30",
                    -5.49
                ],
                [
                    "2025-05-06",
                    -4.35
                ],
                [
                    "2025-05-07",
                    2.07
                ],
                [
                    "2025-05-08",
                    -4.69
                ],
                [
                    "2025-05-09",
                    1.74
                ],
                [
                    "2025-05-12",
                    -10.12
                ],
                [
                    "2025-05-13",
                    7.04
                ],
                [
                    "2025-05-14",
                    -9.89
                ],
                [
                    "2025-05-15",
                    1.67
                ],
                [
                    "2025-05-16",
                    -5.64
                ],
                [
                    "2025-05-19",
                    -2.78
                ],
                [
                    "2025-05-20",
                    6.93
                ],
                [
                    "2025-05-21",
                    3.69
                ],
                [
                    "2025-05-22",
                    2.94
                ],
                [
                    "2025-05-23",
                    0.82
                ],
                [
                    "2025-05-26",
                    -3.07
                ],
                [
                    "2025-05-27",
                    -2.41
                ],
                [
                    "2025-05-28",
                    8.60
                ],
                [
                    "2025-05-29",
                    -2.95
                ],
                [
                    "2025-05-30",
                    8.81
                ],
                [
                    "2025-06-03",
                    3.33
                ],
                [
                    "2025-06-04",
                    -7.49
                ],
                [
                    "2025-06-05",
                    4.89
                ],
                [
                    "2025-06-06",
                    6.57
                ],
                [
                    "2025-06-09",
                    0.20
                ],
                [
                    "2025-06-10",
                    -7.17
                ],
                [
                    "2025-06-11",
                    -3.89
                ],
                [
                    "2025-06-12",
                    -6.26
                ],
                [
                    "2025-06-13",
                    -0.05
                ],
                [
                    "2025-06-16",
                    7.18
                ],
                [
                    "2025-06-17",
                    -2.87
                ],
                [
                    "2025-06-18",
                    4.52
                ],
                [
                    "2025-06-19",
                    4.11
                ],
                [
                    "2025-06-20",
                    -27.41
                ],
                [
                    "2025-06-23",
                    6.60
                ],
                [
                    "2025-06-24",
                    -0.15
                ],
                [
                    "2025-06-25",
                    -2.60
                ],
                [
                    "2025-06-26",
                    2.35
                ],
                [
                    "2025-06-27",
                    -3.33
                ],
                [
                    "2025-06-30",
                    -21.35
                ],
                [
                    "2025-07-01",
                    1.82
                ],
                [
                    "2025-07-02",
                    -6.07
                ],
                [
                    "2025-07-03",
                    1.42
                ],
                [
                    "2025-07-04",
                    -1.58
                ],
                [
                    "2025-07-07",
                    -1.80
                ],
                [
                    "2025-07-08",
                    -1.08
                ],
                [
                    "2025-07-09",
                    -1.71
                ],
                [
                    "2025-07-10",
                    -1.84
                ],
                [
                    "2025-07-11",
                    -1.90
                ],
                [
                    "2025-07-14",
                    0.45
                ],
                [
                    "2025-07-15",
                    -0.51
                ],
                [
                    "2025-07-16",
                    -7.12
                ],
                [
                    "2025-07-17",
                    -2.05
                ],
                [
                    "2025-07-18",
                    -6.20
                ],
                [
                    "2025-07-21",
                    2.60
                ],
                [
                    "2025-07-22",
                    4.86
                ],
                [
                    "2025-07-23",
                    -1.74
                ],
                [
                    "2025-07-24",
                    -4.98
                ],
                [
                    "2025-07-25",
                    1.55
                ],
                [
                    "2025-07-28",
                    -5.49
                ],
                [
                    "2025-07-29",
                    3.12
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    13.21
                ],
                [
                    "2025-02-05",
                    9.48
                ],
                [
                    "2025-02-06",
                    -4.54
                ],
                [
                    "2025-02-07",
                    6.20
                ],
                [
                    "2025-02-10",
                    14.27
                ],
                [
                    "2025-02-11",
                    8.71
                ],
                [
                    "2025-02-12",
                    5.50
                ],
                [
                    "2025-02-13",
                    -6.04
                ],
                [
                    "2025-02-14",
                    0.26
                ],
                [
                    "2025-02-17",
                    -0.90
                ],
                [
                    "2025-02-18",
                    -4.13
                ],
                [
                    "2025-02-19",
                    -0.10
                ],
                [
                    "2025-02-20",
                    8.60
                ],
                [
                    "2025-02-21",
                    13.84
                ],
                [
                    "2025-02-24",
                    8.42
                ],
                [
                    "2025-02-25",
                    -12.16
                ],
                [
                    "2025-02-26",
                    2.12
                ],
                [
                    "2025-02-27",
                    3.66
                ],
                [
                    "2025-02-28",
                    0.17
                ],
                [
                    "2025-03-03",
                    0.34
                ],
                [
                    "2025-03-04",
                    6.58
                ],
                [
                    "2025-03-05",
                    7.34
                ],
                [
                    "2025-03-06",
                    9.49
                ],
                [
                    "2025-03-07",
                    1.26
                ],
                [
                    "2025-03-10",
                    -0.33
                ],
                [
                    "2025-03-11",
                    -0.22
                ],
                [
                    "2025-03-12",
                    5.37
                ],
                [
                    "2025-03-13",
                    12.24
                ],
                [
                    "2025-03-14",
                    0.53
                ],
                [
                    "2025-03-17",
                    5.42
                ],
                [
                    "2025-03-18",
                    -4.68
                ],
                [
                    "2025-03-19",
                    0.59
                ],
                [
                    "2025-03-20",
                    -0.34
                ],
                [
                    "2025-03-21",
                    6.79
                ],
                [
                    "2025-03-24",
                    0.10
                ],
                [
                    "2025-03-25",
                    1.40
                ],
                [
                    "2025-03-26",
                    0.14
                ],
                [
                    "2025-03-27",
                    -1.06
                ],
                [
                    "2025-03-28",
                    -11.13
                ],
                [
                    "2025-03-31",
                    -16.94
                ],
                [
                    "2025-04-01",
                    -1.80
                ],
                [
                    "2025-04-02",
                    -2.48
                ],
                [
                    "2025-04-03",
                    2.80
                ],
                [
                    "2025-04-07",
                    7.95
                ],
                [
                    "2025-04-08",
                    1.15
                ],
                [
                    "2025-04-09",
                    4.40
                ],
                [
                    "2025-04-10",
                    0.38
                ],
                [
                    "2025-04-11",
                    4.50
                ],
                [
                    "2025-04-14",
                    1.36
                ],
                [
                    "2025-04-15",
                    8.41
                ],
                [
                    "2025-04-16",
                    9.62
                ],
                [
                    "2025-04-17",
                    11.60
                ],
                [
                    "2025-04-18",
                    6.52
                ],
                [
                    "2025-04-21",
                    9.56
                ],
                [
                    "2025-04-22",
                    -2.66
                ],
                [
                    "2025-04-23",
                    3.87
                ],
                [
                    "2025-04-24",
                    1.61
                ],
                [
                    "2025-04-25",
                    11.50
                ],
                [
                    "2025-04-28",
                    16.22
                ],
                [
                    "2025-04-29",
                    11.75
                ],
                [
                    "2025-04-30",
                    3.88
                ],
                [
                    "2025-05-06",
                    -0.28
                ],
                [
                    "2025-05-07",
                    18.84
                ],
                [
                    "2025-05-08",
                    10.25
                ],
                [
                    "2025-05-09",
                    8.73
                ],
                [
                    "2025-05-12",
                    19.47
                ],
                [
                    "2025-05-13",
                    -12.44
                ],
                [
                    "2025-05-14",
                    21.05
                ],
                [
                    "2025-05-15",
                    5.37
                ],
                [
                    "2025-05-16",
                    9.49
                ],
                [
                    "2025-05-19",
                    10.13
                ],
                [
                    "2025-05-20",
                    -4.35
                ],
                [
                    "2025-05-21",
                    -1.34
                ],
                [
                    "2025-05-22",
                    11.52
                ],
                [
                    "2025-05-23",
                    6.02
                ],
                [
                    "2025-05-26",
                    8.27
                ],
                [
                    "2025-05-27",
                    4.74
                ],
                [
                    "2025-05-28",
                    -2.85
                ],
                [
                    "2025-05-29",
                    8.71
                ],
                [
                    "2025-05-30",
                    -12.00
                ],
                [
                    "2025-06-03",
                    -3.73
                ],
                [
                    "2025-06-04",
                    -2.01
                ],
                [
                    "2025-06-05",
                    -1.07
                ],
                [
                    "2025-06-06",
                    -4.75
                ],
                [
                    "2025-06-09",
                    2.88
                ],
                [
                    "2025-06-10",
                    15.29
                ],
                [
                    "2025-06-11",
                    4.17
                ],
                [
                    "2025-06-12",
                    6.86
                ],
                [
                    "2025-06-13",
                    12.50
                ],
                [
                    "2025-06-16",
                    -13.43
                ],
                [
                    "2025-06-17",
                    9.18
                ],
                [
                    "2025-06-18",
                    -7.05
                ],
                [
                    "2025-06-19",
                    -20.52
                ],
                [
                    "2025-06-20",
                    -23.42
                ],
                [
                    "2025-06-23",
                    11.40
                ],
                [
                    "2025-06-24",
                    -5.11
                ],
                [
                    "2025-06-25",
                    7.82
                ],
                [
                    "2025-06-26",
                    2.25
                ],
                [
                    "2025-06-27",
                    9.87
                ],
                [
                    "2025-06-30",
                    -24.74
                ],
                [
                    "2025-07-01",
                    8.82
                ],
                [
                    "2025-07-02",
                    -7.80
                ],
                [
                    "2025-07-03",
                    12.83
                ],
                [
                    "2025-07-04",
                    5.99
                ],
                [
                    "2025-07-07",
                    1.28
                ],
                [
                    "2025-07-08",
                    1.24
                ],
                [
                    "2025-07-09",
                    9.96
                ],
                [
                    "2025-07-10",
                    6.38
                ],
                [
                    "2025-07-11",
                    1.72
                ],
                [
                    "2025-07-14",
                    2.44
                ],
                [
                    "2025-07-15",
                    9.53
                ],
                [
                    "2025-07-16",
                    11.85
                ],
                [
                    "2025-07-17",
                    4.84
                ],
                [
                    "2025-07-18",
                    14.50
                ],
                [
                    "2025-07-21",
                    7.20
                ],
                [
                    "2025-07-22",
                    -2.99
                ],
                [
                    "2025-07-23",
                    22.39
                ],
                [
                    "2025-07-24",
                    9.92
                ],
                [
                    "2025-07-25",
                    16.98
                ],
                [
                    "2025-07-28",
                    8.01
                ],
                [
                    "2025-07-29",
                    9.26
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-18",
                    0.12
                ],
                [
                    "2025-02-19",
                    5.58
                ],
                [
                    "2025-03-03",
                    1.32
                ],
                [
                    "2025-03-31",
                    10.55
                ],
                [
                    "2025-04-01",
                    2.77
                ],
                [
                    "2025-04-02",
                    -0.90
                ],
                [
                    "2025-04-03",
                    -3.42
                ],
                [
                    "2025-06-04",
                    9.49
                ],
                [
                    "2025-06-05",
                    -3.82
                ],
                [
                    "2025-06-06",
                    -1.81
                ],
                [
                    "2025-06-09",
                    -3.08
                ],
                [
                    "2025-06-19",
                    16.42
                ],
                [
                    "2025-06-20",
                    50.83
                ],
                [
                    "2025-06-23",
                    -18.01
                ],
                [
                    "2025-06-24",
                    5.25
                ],
                [
                    "2025-06-25",
                    -5.22
                ],
                [
                    "2025-06-26",
                    -4.60
                ],
                [
                    "2025-06-30",
                    46.08
                ],
                [
                    "2025-07-02",
                    13.86
                ],
                [
                    "2025-07-03",
                    -14.26
                ],
                [
                    "2025-07-04",
                    -4.42
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-18",
                    4.00
                ],
                [
                    "2025-03-03",
                    -1.66
                ],
                [
                    "2025-03-28",
                    5.65
                ],
                [
                    "2025-03-31",
                    6.39
                ],
                [
                    "2025-04-01",
                    -0.97
                ],
                [
                    "2025-04-02",
                    3.38
                ],
                [
                    "2025-04-03",
                    0.61
                ],
                [
                    "2025-04-07",
                    3.29
                ],
                [
                    "2025-06-03",
                    3.33
                ],
                [
                    "2025-06-04",
                    -7.49
                ],
                [
                    "2025-06-05",
                    4.89
                ],
                [
                    "2025-06-06",
                    6.57
                ],
                [
                    "2025-06-09",
                    0.20
                ],
                [
                    "2025-06-19",
                    4.11
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    14.27
                ],
                [
                    "2025-02-11",
                    8.71
                ],
                [
                    "2025-02-12",
                    5.50
                ],
                [
                    "2025-02-13",
                    -6.04
                ],
                [
                    "2025-02-14",
                    0.26
                ],
                [
                    "2025-02-17",
                    -0.90
                ],
                [
                    "2025-02-20",
                    8.60
                ],
                [
                    "2025-02-21",
                    13.84
                ],
                [
                    "2025-02-24",
                    8.42
                ],
                [
                    "2025-02-25",
                    -12.16
                ],
                [
                    "2025-02-26",
                    2.12
                ],
                [
                    "2025-02-27",
                    3.66
                ],
                [
                    "2025-02-28",
                    0.17
                ],
                [
                    "2025-03-04",
                    6.58
                ],
                [
                    "2025-03-05",
                    7.34
                ],
                [
                    "2025-03-06",
                    9.49
                ],
                [
                    "2025-03-07",
                    1.26
                ],
                [
                    "2025-03-10",
                    -0.33
                ],
                [
                    "2025-03-11",
                    -0.22
                ],
                [
                    "2025-03-12",
                    5.37
                ],
                [
                    "2025-03-13",
                    12.24
                ],
                [
                    "2025-03-14",
                    0.53
                ],
                [
                    "2025-03-17",
                    5.42
                ],
                [
                    "2025-03-18",
                    -4.68
                ],
                [
                    "2025-03-19",
                    0.59
                ],
                [
                    "2025-03-20",
                    -0.34
                ],
                [
                    "2025-03-21",
                    6.79
                ],
                [
                    "2025-03-24",
                    0.10
                ],
                [
                    "2025-03-25",
                    1.40
                ],
                [
                    "2025-03-26",
                    0.14
                ],
                [
                    "2025-03-27",
                    -1.06
                ],
                [
                    "2025-04-08",
                    1.15
                ],
                [
                    "2025-04-09",
                    4.40
                ],
                [
                    "2025-04-10",
                    0.38
                ],
                [
                    "2025-04-11",
                    4.50
                ],
                [
                    "2025-04-14",
                    1.36
                ],
                [
                    "2025-04-15",
                    8.41
                ],
                [
                    "2025-04-16",
                    9.62
                ],
                [
                    "2025-04-17",
                    11.60
                ],
                [
                    "2025-04-18",
                    6.52
                ],
                [
                    "2025-04-21",
                    9.56
                ],
                [
                    "2025-04-22",
                    -2.66
                ],
                [
                    "2025-04-23",
                    3.87
                ],
                [
                    "2025-04-24",
                    1.61
                ],
                [
                    "2025-04-25",
                    11.50
                ],
                [
                    "2025-04-28",
                    16.22
                ],
                [
                    "2025-04-29",
                    11.75
                ],
                [
                    "2025-04-30",
                    3.88
                ],
                [
                    "2025-05-06",
                    -0.28
                ],
                [
                    "2025-05-07",
                    18.84
                ],
                [
                    "2025-05-08",
                    10.25
                ],
                [
                    "2025-05-09",
                    8.73
                ],
                [
                    "2025-05-12",
                    19.47
                ],
                [
                    "2025-05-13",
                    -12.44
                ],
                [
                    "2025-05-14",
                    21.05
                ],
                [
                    "2025-05-15",
                    5.37
                ],
                [
                    "2025-05-16",
                    9.49
                ],
                [
                    "2025-05-19",
                    10.13
                ],
                [
                    "2025-05-20",
                    -4.35
                ],
                [
                    "2025-05-21",
                    -1.34
                ],
                [
                    "2025-05-22",
                    11.52
                ],
                [
                    "2025-05-23",
                    6.02
                ],
                [
                    "2025-05-26",
                    8.27
                ],
                [
                    "2025-05-27",
                    4.74
                ],
                [
                    "2025-05-28",
                    -2.85
                ],
                [
                    "2025-05-29",
                    8.71
                ],
                [
                    "2025-05-30",
                    -12.00
                ],
                [
                    "2025-06-10",
                    15.29
                ],
                [
                    "2025-06-11",
                    4.17
                ],
                [
                    "2025-06-12",
                    6.86
                ],
                [
                    "2025-06-13",
                    12.50
                ],
                [
                    "2025-06-16",
                    -13.43
                ],
                [
                    "2025-06-17",
                    9.18
                ],
                [
                    "2025-06-18",
                    -7.05
                ],
                [
                    "2025-06-27",
                    9.87
                ],
                [
                    "2025-07-07",
                    1.28
                ],
                [
                    "2025-07-08",
                    1.24
                ],
                [
                    "2025-07-09",
                    9.96
                ],
                [
                    "2025-07-10",
                    6.38
                ],
                [
                    "2025-07-11",
                    1.72
                ],
                [
                    "2025-07-14",
                    2.44
                ],
                [
                    "2025-07-15",
                    9.53
                ],
                [
                    "2025-07-16",
                    11.85
                ],
                [
                    "2025-07-17",
                    4.84
                ],
                [
                    "2025-07-18",
                    14.50
                ],
                [
                    "2025-07-21",
                    7.20
                ],
                [
                    "2025-07-22",
                    -2.99
                ],
                [
                    "2025-07-23",
                    22.39
                ],
                [
                    "2025-07-24",
                    9.92
                ],
                [
                    "2025-07-25",
                    16.98
                ],
                [
                    "2025-07-28",
                    8.01
                ],
                [
                    "2025-07-29",
                    9.26
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603212 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_20febeaedd1e42028486fe5723752d27.setOption(option_20febeaedd1e42028486fe5723752d27);
            window.addEventListener('resize', function(){
                chart_20febeaedd1e42028486fe5723752d27.resize();
            })
    </script>
</body>
</html>
