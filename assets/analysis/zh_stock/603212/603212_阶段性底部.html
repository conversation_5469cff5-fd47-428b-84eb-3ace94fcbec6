<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="aba6f73e79f84a8bb84da61808bb3ce9" class="chart-container" style="width:100%; height:500px; "></div>
    <script>
        var chart_aba6f73e79f84a8bb84da61808bb3ce9 = echarts.init(
            document.getElementById('aba6f73e79f84a8bb84da61808bb3ce9'), 'white', {renderer: 'canvas'});
        var option_aba6f73e79f84a8bb84da61808bb3ce9 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    10.5,
                    10.13,
                    10.11,
                    10.6
                ],
                [
                    10.28,
                    10.19,
                    10.06,
                    10.29
                ],
                [
                    10.15,
                    10.58,
                    10.11,
                    10.58
                ],
                [
                    10.52,
                    10.78,
                    10.52,
                    10.89
                ],
                [
                    10.75,
                    10.8,
                    10.61,
                    10.8
                ],
                [
                    10.76,
                    10.66,
                    10.54,
                    10.78
                ],
                [
                    10.62,
                    10.7,
                    10.56,
                    10.73
                ],
                [
                    10.78,
                    10.6,
                    10.59,
                    10.87
                ],
                [
                    10.53,
                    10.75,
                    10.52,
                    10.85
                ],
                [
                    10.73,
                    10.77,
                    10.71,
                    10.88
                ],
                [
                    10.8,
                    10.52,
                    10.5,
                    10.98
                ],
                [
                    10.45,
                    10.84,
                    10.43,
                    10.85
                ],
                [
                    10.89,
                    10.81,
                    10.68,
                    10.92
                ],
                [
                    10.83,
                    10.75,
                    10.65,
                    10.86
                ],
                [
                    10.75,
                    10.92,
                    10.61,
                    10.93
                ],
                [
                    10.82,
                    10.96,
                    10.75,
                    11.06
                ],
                [
                    10.98,
                    11.26,
                    10.97,
                    11.27
                ],
                [
                    11.04,
                    10.94,
                    10.7,
                    11.15
                ],
                [
                    10.82,
                    10.67,
                    10.65,
                    11.07
                ],
                [
                    10.71,
                    10.95,
                    10.71,
                    11.24
                ],
                [
                    10.85,
                    10.86,
                    10.64,
                    10.89
                ],
                [
                    10.83,
                    10.56,
                    10.48,
                    10.89
                ],
                [
                    10.62,
                    10.7,
                    10.53,
                    10.79
                ],
                [
                    10.65,
                    10.62,
                    10.4,
                    10.75
                ],
                [
                    10.62,
                    10.95,
                    10.6,
                    11.18
                ],
                [
                    10.82,
                    10.98,
                    10.73,
                    11.06
                ],
                [
                    10.99,
                    10.9,
                    10.87,
                    11.1
                ],
                [
                    10.91,
                    10.61,
                    10.52,
                    10.96
                ],
                [
                    10.65,
                    10.86,
                    10.54,
                    10.92
                ],
                [
                    10.91,
                    10.88,
                    10.84,
                    11.07
                ],
                [
                    10.89,
                    11.05,
                    10.85,
                    11.09
                ],
                [
                    10.97,
                    10.91,
                    10.79,
                    11.05
                ],
                [
                    10.86,
                    10.83,
                    10.81,
                    10.96
                ],
                [
                    10.82,
                    10.65,
                    10.62,
                    10.87
                ],
                [
                    10.66,
                    10.36,
                    10.1,
                    10.7
                ],
                [
                    10.37,
                    10.48,
                    10.25,
                    10.57
                ],
                [
                    10.44,
                    10.8,
                    10.42,
                    10.96
                ],
                [
                    10.72,
                    10.51,
                    10.5,
                    10.77
                ],
                [
                    10.51,
                    10.37,
                    10.34,
                    10.84
                ],
                [
                    10.3,
                    10.31,
                    10.16,
                    10.77
                ],
                [
                    10.23,
                    10.49,
                    10.23,
                    10.65
                ],
                [
                    10.45,
                    10.78,
                    10.44,
                    11.2
                ],
                [
                    10.59,
                    10.55,
                    10.51,
                    10.79
                ],
                [
                    9.89,
                    9.5,
                    9.5,
                    10.07
                ],
                [
                    9.21,
                    9.28,
                    9.12,
                    9.6
                ],
                [
                    9.05,
                    9.28,
                    8.45,
                    9.29
                ],
                [
                    9.68,
                    9.43,
                    9.42,
                    9.71
                ],
                [
                    9.33,
                    9.46,
                    9.31,
                    9.59
                ],
                [
                    9.55,
                    9.62,
                    9.55,
                    9.77
                ],
                [
                    9.55,
                    9.61,
                    9.5,
                    9.68
                ],
                [
                    9.56,
                    9.39,
                    9.29,
                    9.58
                ],
                [
                    9.32,
                    9.41,
                    9.32,
                    9.51
                ],
                [
                    9.42,
                    9.3,
                    9.25,
                    9.44
                ],
                [
                    9.22,
                    9.37,
                    9.22,
                    9.39
                ],
                [
                    9.39,
                    9.55,
                    9.28,
                    9.65
                ],
                [
                    9.55,
                    9.49,
                    9.43,
                    9.63
                ],
                [
                    9.5,
                    9.33,
                    9.29,
                    9.5
                ],
                [
                    9.3,
                    9.31,
                    9.3,
                    9.43
                ],
                [
                    9.3,
                    9.02,
                    9.02,
                    9.32
                ],
                [
                    8.97,
                    9.05,
                    8.95,
                    9.23
                ],
                [
                    9.02,
                    9.27,
                    9.02,
                    9.38
                ],
                [
                    9.32,
                    9.59,
                    9.3,
                    9.59
                ],
                [
                    9.64,
                    9.61,
                    9.54,
                    9.74
                ],
                [
                    9.6,
                    9.76,
                    9.5,
                    9.78
                ],
                [
                    9.78,
                    9.58,
                    9.52,
                    9.78
                ],
                [
                    9.6,
                    9.78,
                    9.6,
                    9.78
                ],
                [
                    9.83,
                    9.89,
                    9.83,
                    10.26
                ],
                [
                    9.91,
                    9.84,
                    9.78,
                    9.93
                ],
                [
                    9.82,
                    9.68,
                    9.67,
                    9.85
                ],
                [
                    9.68,
                    9.74,
                    9.62,
                    9.84
                ],
                [
                    9.74,
                    9.78,
                    9.66,
                    9.79
                ],
                [
                    9.79,
                    9.83,
                    9.71,
                    9.85
                ],
                [
                    9.79,
                    9.76,
                    9.71,
                    9.87
                ],
                [
                    9.74,
                    9.45,
                    9.42,
                    9.8
                ],
                [
                    9.42,
                    9.4,
                    9.38,
                    9.61
                ],
                [
                    9.44,
                    9.45,
                    9.35,
                    9.53
                ],
                [
                    9.46,
                    9.48,
                    9.33,
                    9.5
                ],
                [
                    9.45,
                    9.34,
                    9.3,
                    9.53
                ],
                [
                    9.35,
                    9.5,
                    9.33,
                    9.52
                ],
                [
                    9.47,
                    9.36,
                    9.31,
                    9.52
                ],
                [
                    9.31,
                    9.43,
                    9.29,
                    9.52
                ],
                [
                    9.48,
                    9.57,
                    9.44,
                    9.58
                ],
                [
                    9.58,
                    9.58,
                    9.48,
                    9.61
                ],
                [
                    9.68,
                    9.63,
                    9.6,
                    9.79
                ],
                [
                    9.63,
                    9.83,
                    9.63,
                    9.88
                ],
                [
                    9.84,
                    9.71,
                    9.6,
                    9.87
                ],
                [
                    9.76,
                    9.71,
                    9.69,
                    9.89
                ],
                [
                    9.71,
                    9.69,
                    9.62,
                    9.74
                ],
                [
                    9.68,
                    9.38,
                    9.31,
                    9.68
                ],
                [
                    9.33,
                    9.57,
                    9.33,
                    9.75
                ],
                [
                    9.58,
                    9.6,
                    9.53,
                    9.66
                ],
                [
                    9.52,
                    9.59,
                    9.44,
                    9.6
                ],
                [
                    9.56,
                    9.66,
                    9.55,
                    9.92
                ],
                [
                    10.49,
                    10.63,
                    10.49,
                    10.63
                ],
                [
                    11.66,
                    10.86,
                    10.72,
                    11.66
                ],
                [
                    10.69,
                    11.95,
                    10.51,
                    11.95
                ],
                [
                    12.18,
                    12.39,
                    12.08,
                    13.15
                ],
                [
                    11.8,
                    12.0,
                    11.79,
                    12.99
                ],
                [
                    11.86,
                    11.35,
                    11.35,
                    12.1
                ],
                [
                    11.55,
                    12.49,
                    11.55,
                    12.49
                ],
                [
                    12.84,
                    12.87,
                    12.6,
                    13.29
                ],
                [
                    12.86,
                    14.16,
                    12.44,
                    14.16
                ],
                [
                    14.5,
                    13.45,
                    13.31,
                    14.58
                ],
                [
                    13.67,
                    12.9,
                    12.84,
                    14.15
                ],
                [
                    12.37,
                    12.61,
                    12.27,
                    12.98
                ],
                [
                    12.5,
                    13.08,
                    12.35,
                    13.44
                ],
                [
                    12.83,
                    12.41,
                    12.32,
                    12.92
                ],
                [
                    12.21,
                    12.12,
                    12.11,
                    12.65
                ],
                [
                    12.21,
                    12.18,
                    11.9,
                    12.28
                ],
                [
                    12.22,
                    12.35,
                    12.13,
                    12.38
                ],
                [
                    12.14,
                    11.48,
                    11.41,
                    12.15
                ],
                [
                    11.59,
                    11.58,
                    11.44,
                    11.65
                ],
                [
                    11.66,
                    11.69,
                    11.53,
                    11.82
                ],
                [
                    11.64,
                    11.65,
                    11.56,
                    11.84
                ],
                [
                    11.72,
                    11.75,
                    11.6,
                    11.75
                ],
                [
                    11.69,
                    11.81,
                    11.61,
                    11.94
                ],
                [
                    11.76,
                    11.46,
                    11.4,
                    11.85
                ],
                [
                    11.41,
                    11.73,
                    11.4,
                    11.74
                ],
                [
                    11.73,
                    11.59,
                    11.56,
                    11.76
                ],
                [
                    11.59,
                    11.66,
                    11.51,
                    11.67
                ],
                [
                    11.66,
                    11.75,
                    11.49,
                    11.8
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u9636\u6bb5\u6027\u5e95\u90e8",
            "symbol": "triangle",
            "symbolSize": 20,
            "data": [
                [
                    "2025-03-21",
                    10.41
                ],
                [
                    "2025-03-24",
                    9.9
                ],
                [
                    "2025-03-25",
                    10.04
                ],
                [
                    "2025-03-28",
                    10.13
                ],
                [
                    "2025-03-31",
                    9.96
                ],
                [
                    "2025-04-07",
                    9.31
                ],
                [
                    "2025-04-08",
                    8.94
                ],
                [
                    "2025-05-22",
                    9.23
                ],
                [
                    "2025-05-23",
                    9.19
                ],
                [
                    "2025-05-26",
                    9.16
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#FF6B35",
                "borderColor": "#CC5529",
                "borderWidth": 2,
                "opacity": 0.9
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf",
                "\u9636\u6bb5\u6027\u5e95\u90e8"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603212 \u9636\u6bb5\u6027\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_aba6f73e79f84a8bb84da61808bb3ce9.setOption(option_aba6f73e79f84a8bb84da61808bb3ce9);
            window.addEventListener('resize', function(){
                chart_aba6f73e79f84a8bb84da61808bb3ce9.resize();
            })
    </script>
</body>
</html>
