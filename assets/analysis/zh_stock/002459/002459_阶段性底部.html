<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="e33313b3842441d998164f92f67d190c" class="chart-container" style="width:100%; height:500px; "></div>
    <script>
        var chart_e33313b3842441d998164f92f67d190c = echarts.init(
            document.getElementById('e33313b3842441d998164f92f67d190c'), 'white', {renderer: 'canvas'});
        var option_e33313b3842441d998164f92f67d190c = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    12.67,
                    12.46,
                    12.28,
                    12.84
                ],
                [
                    12.49,
                    12.51,
                    12.25,
                    12.76
                ],
                [
                    12.42,
                    13.01,
                    12.18,
                    13.05
                ],
                [
                    12.98,
                    13.75,
                    12.9,
                    14.08
                ],
                [
                    13.68,
                    13.42,
                    13.29,
                    13.73
                ],
                [
                    13.42,
                    12.9,
                    12.85,
                    13.46
                ],
                [
                    12.83,
                    13.02,
                    12.66,
                    13.06
                ],
                [
                    12.95,
                    12.88,
                    12.88,
                    13.13
                ],
                [
                    12.86,
                    12.83,
                    12.78,
                    13.2
                ],
                [
                    12.78,
                    13.1,
                    12.74,
                    13.17
                ],
                [
                    13.19,
                    12.46,
                    12.45,
                    13.19
                ],
                [
                    12.41,
                    12.8,
                    12.38,
                    13.02
                ],
                [
                    12.8,
                    12.4,
                    12.33,
                    12.81
                ],
                [
                    12.41,
                    12.45,
                    12.25,
                    12.6
                ],
                [
                    12.46,
                    12.74,
                    12.4,
                    13.1
                ],
                [
                    12.55,
                    12.88,
                    12.49,
                    13.03
                ],
                [
                    13.02,
                    13.53,
                    12.87,
                    13.54
                ],
                [
                    13.43,
                    13.1,
                    12.95,
                    13.44
                ],
                [
                    13.1,
                    12.73,
                    12.72,
                    13.34
                ],
                [
                    12.74,
                    12.85,
                    12.72,
                    13.14
                ],
                [
                    12.73,
                    12.88,
                    12.55,
                    12.9
                ],
                [
                    12.8,
                    12.51,
                    12.38,
                    12.81
                ],
                [
                    12.52,
                    12.59,
                    12.38,
                    12.7
                ],
                [
                    12.51,
                    12.24,
                    12.2,
                    12.58
                ],
                [
                    12.26,
                    12.54,
                    12.26,
                    12.55
                ],
                [
                    12.36,
                    12.6,
                    12.21,
                    12.6
                ],
                [
                    12.51,
                    12.45,
                    12.43,
                    12.68
                ],
                [
                    12.41,
                    12.18,
                    12.04,
                    12.48
                ],
                [
                    12.18,
                    12.53,
                    12.18,
                    12.6
                ],
                [
                    12.65,
                    12.77,
                    12.62,
                    13.15
                ],
                [
                    12.82,
                    12.92,
                    12.67,
                    12.96
                ],
                [
                    12.87,
                    12.81,
                    12.68,
                    12.98
                ],
                [
                    12.78,
                    12.66,
                    12.63,
                    12.9
                ],
                [
                    12.65,
                    12.82,
                    12.57,
                    13.02
                ],
                [
                    12.82,
                    12.44,
                    12.28,
                    13.08
                ],
                [
                    12.39,
                    12.4,
                    12.31,
                    12.65
                ],
                [
                    12.41,
                    12.89,
                    12.33,
                    13.1
                ],
                [
                    12.8,
                    12.36,
                    12.36,
                    12.91
                ],
                [
                    12.36,
                    12.21,
                    12.2,
                    12.67
                ],
                [
                    12.06,
                    11.53,
                    11.5,
                    12.17
                ],
                [
                    11.58,
                    11.52,
                    11.31,
                    11.65
                ],
                [
                    11.49,
                    11.43,
                    11.38,
                    11.58
                ],
                [
                    11.21,
                    11.17,
                    11.16,
                    11.5
                ],
                [
                    10.5,
                    10.05,
                    10.05,
                    10.5
                ],
                [
                    9.92,
                    9.99,
                    9.68,
                    10.1
                ],
                [
                    9.73,
                    9.9,
                    9.44,
                    9.98
                ],
                [
                    10.2,
                    10.22,
                    10.1,
                    10.35
                ],
                [
                    10.05,
                    10.15,
                    10.03,
                    10.3
                ],
                [
                    10.32,
                    10.16,
                    10.14,
                    10.35
                ],
                [
                    10.17,
                    9.96,
                    9.87,
                    10.2
                ],
                [
                    9.92,
                    9.78,
                    9.61,
                    9.94
                ],
                [
                    9.7,
                    9.7,
                    9.66,
                    9.8
                ],
                [
                    9.69,
                    9.68,
                    9.56,
                    9.72
                ],
                [
                    9.7,
                    9.79,
                    9.6,
                    9.79
                ],
                [
                    9.75,
                    9.72,
                    9.68,
                    9.86
                ],
                [
                    9.74,
                    9.84,
                    9.73,
                    10.05
                ],
                [
                    9.85,
                    9.6,
                    9.6,
                    9.95
                ],
                [
                    9.62,
                    9.64,
                    9.57,
                    9.77
                ],
                [
                    9.6,
                    9.56,
                    9.47,
                    9.69
                ],
                [
                    9.54,
                    9.44,
                    9.41,
                    9.66
                ],
                [
                    9.37,
                    9.55,
                    9.35,
                    9.97
                ],
                [
                    9.65,
                    9.8,
                    9.58,
                    9.81
                ],
                [
                    9.96,
                    9.87,
                    9.78,
                    10.04
                ],
                [
                    9.83,
                    10.01,
                    9.79,
                    10.13
                ],
                [
                    10.0,
                    9.71,
                    9.7,
                    10.0
                ],
                [
                    9.86,
                    10.01,
                    9.79,
                    10.02
                ],
                [
                    10.12,
                    10.17,
                    10.11,
                    10.68
                ],
                [
                    10.06,
                    9.99,
                    9.82,
                    10.15
                ],
                [
                    9.93,
                    9.67,
                    9.67,
                    9.94
                ],
                [
                    9.67,
                    9.67,
                    9.64,
                    9.86
                ],
                [
                    9.63,
                    9.55,
                    9.47,
                    9.67
                ],
                [
                    9.57,
                    9.59,
                    9.47,
                    9.6
                ],
                [
                    9.6,
                    9.66,
                    9.53,
                    9.69
                ],
                [
                    9.6,
                    9.47,
                    9.46,
                    9.68
                ],
                [
                    9.44,
                    9.67,
                    9.41,
                    9.98
                ],
                [
                    9.6,
                    9.48,
                    9.46,
                    9.72
                ],
                [
                    9.5,
                    9.28,
                    9.19,
                    9.52
                ],
                [
                    9.26,
                    9.16,
                    9.1,
                    9.31
                ],
                [
                    9.19,
                    9.33,
                    9.17,
                    9.34
                ],
                [
                    9.27,
                    9.14,
                    9.13,
                    9.27
                ],
                [
                    9.09,
                    9.19,
                    9.05,
                    9.21
                ],
                [
                    9.23,
                    9.38,
                    9.2,
                    9.41
                ],
                [
                    9.35,
                    9.38,
                    9.26,
                    9.4
                ],
                [
                    9.4,
                    9.34,
                    9.29,
                    9.41
                ],
                [
                    9.33,
                    9.75,
                    9.29,
                    9.82
                ],
                [
                    9.71,
                    9.75,
                    9.69,
                    9.95
                ],
                [
                    9.79,
                    9.91,
                    9.77,
                    10.1
                ],
                [
                    9.9,
                    9.91,
                    9.79,
                    9.99
                ],
                [
                    9.9,
                    9.55,
                    9.53,
                    9.9
                ],
                [
                    9.53,
                    9.63,
                    9.5,
                    9.73
                ],
                [
                    9.63,
                    9.64,
                    9.52,
                    9.74
                ],
                [
                    9.63,
                    9.69,
                    9.53,
                    9.78
                ],
                [
                    9.63,
                    9.36,
                    9.36,
                    9.73
                ],
                [
                    9.46,
                    9.41,
                    9.36,
                    9.94
                ],
                [
                    9.29,
                    9.42,
                    9.2,
                    9.49
                ],
                [
                    9.48,
                    9.61,
                    9.42,
                    9.64
                ],
                [
                    9.61,
                    9.7,
                    9.47,
                    9.72
                ],
                [
                    9.67,
                    9.52,
                    9.51,
                    9.82
                ],
                [
                    9.56,
                    9.73,
                    9.54,
                    10.01
                ],
                [
                    9.91,
                    9.98,
                    9.81,
                    10.13
                ],
                [
                    9.93,
                    9.82,
                    9.73,
                    9.94
                ],
                [
                    9.88,
                    10.55,
                    9.82,
                    10.76
                ],
                [
                    10.57,
                    10.54,
                    10.31,
                    10.61
                ],
                [
                    10.71,
                    10.43,
                    10.33,
                    10.75
                ],
                [
                    10.35,
                    10.34,
                    10.25,
                    10.49
                ],
                [
                    10.33,
                    10.93,
                    10.3,
                    10.99
                ],
                [
                    10.85,
                    10.74,
                    10.71,
                    11.05
                ],
                [
                    10.75,
                    11.81,
                    10.75,
                    11.81
                ],
                [
                    12.09,
                    11.74,
                    11.6,
                    12.09
                ],
                [
                    11.62,
                    11.57,
                    11.53,
                    11.87
                ],
                [
                    11.44,
                    11.24,
                    11.12,
                    11.56
                ],
                [
                    11.25,
                    11.26,
                    11.13,
                    11.3
                ],
                [
                    11.56,
                    11.64,
                    11.35,
                    11.8
                ],
                [
                    11.49,
                    11.45,
                    11.38,
                    11.85
                ],
                [
                    11.46,
                    11.68,
                    11.38,
                    11.8
                ],
                [
                    11.74,
                    12.11,
                    11.45,
                    12.11
                ],
                [
                    12.12,
                    11.65,
                    11.62,
                    12.27
                ],
                [
                    11.61,
                    11.92,
                    11.52,
                    12.05
                ],
                [
                    11.91,
                    11.65,
                    11.61,
                    11.98
                ],
                [
                    11.55,
                    11.63,
                    11.33,
                    11.8
                ],
                [
                    11.6,
                    11.86,
                    11.43,
                    11.87
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u9636\u6bb5\u6027\u5e95\u90e8",
            "symbol": "triangle",
            "symbolSize": 20,
            "data": [
                [
                    "2025-03-05",
                    12.13
                ],
                [
                    "2025-03-06",
                    12.13
                ],
                [
                    "2025-03-27",
                    12.11
                ],
                [
                    "2025-03-28",
                    11.96
                ],
                [
                    "2025-03-31",
                    11.27
                ],
                [
                    "2025-04-24",
                    9.41
                ],
                [
                    "2025-04-28",
                    9.28
                ],
                [
                    "2025-04-29",
                    9.22
                ],
                [
                    "2025-05-21",
                    9.34
                ],
                [
                    "2025-05-22",
                    9.27
                ],
                [
                    "2025-05-26",
                    9.27
                ],
                [
                    "2025-05-27",
                    9.01
                ],
                [
                    "2025-05-28",
                    8.92
                ],
                [
                    "2025-05-30",
                    8.95
                ],
                [
                    "2025-06-03",
                    8.87
                ],
                [
                    "2025-06-20",
                    9.17
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#FF6B35",
                "borderColor": "#CC5529",
                "borderWidth": 2,
                "opacity": 0.9
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf",
                "\u9636\u6bb5\u6027\u5e95\u90e8"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002459 \u9636\u6bb5\u6027\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_e33313b3842441d998164f92f67d190c.setOption(option_e33313b3842441d998164f92f67d190c);
            window.addEventListener('resize', function(){
                chart_e33313b3842441d998164f92f67d190c.resize();
            })
    </script>
</body>
</html>
