<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="c8a887edc5b64ba5b3bc834c0959d7df" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_c8a887edc5b64ba5b3bc834c0959d7df = echarts.init(
            document.getElementById('c8a887edc5b64ba5b3bc834c0959d7df'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_c8a887edc5b64ba5b3bc834c0959d7df = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    12.67,
                    12.46,
                    12.28,
                    12.84
                ],
                [
                    12.49,
                    12.51,
                    12.25,
                    12.76
                ],
                [
                    12.42,
                    13.01,
                    12.18,
                    13.05
                ],
                [
                    12.98,
                    13.75,
                    12.9,
                    14.08
                ],
                [
                    13.68,
                    13.42,
                    13.29,
                    13.73
                ],
                [
                    13.42,
                    12.9,
                    12.85,
                    13.46
                ],
                [
                    12.83,
                    13.02,
                    12.66,
                    13.06
                ],
                [
                    12.95,
                    12.88,
                    12.88,
                    13.13
                ],
                [
                    12.86,
                    12.83,
                    12.78,
                    13.2
                ],
                [
                    12.78,
                    13.1,
                    12.74,
                    13.17
                ],
                [
                    13.19,
                    12.46,
                    12.45,
                    13.19
                ],
                [
                    12.41,
                    12.8,
                    12.38,
                    13.02
                ],
                [
                    12.8,
                    12.4,
                    12.33,
                    12.81
                ],
                [
                    12.41,
                    12.45,
                    12.25,
                    12.6
                ],
                [
                    12.46,
                    12.74,
                    12.4,
                    13.1
                ],
                [
                    12.55,
                    12.88,
                    12.49,
                    13.03
                ],
                [
                    13.02,
                    13.53,
                    12.87,
                    13.54
                ],
                [
                    13.43,
                    13.1,
                    12.95,
                    13.44
                ],
                [
                    13.1,
                    12.73,
                    12.72,
                    13.34
                ],
                [
                    12.74,
                    12.85,
                    12.72,
                    13.14
                ],
                [
                    12.73,
                    12.88,
                    12.55,
                    12.9
                ],
                [
                    12.8,
                    12.51,
                    12.38,
                    12.81
                ],
                [
                    12.52,
                    12.59,
                    12.38,
                    12.7
                ],
                [
                    12.51,
                    12.24,
                    12.2,
                    12.58
                ],
                [
                    12.26,
                    12.54,
                    12.26,
                    12.55
                ],
                [
                    12.36,
                    12.6,
                    12.21,
                    12.6
                ],
                [
                    12.51,
                    12.45,
                    12.43,
                    12.68
                ],
                [
                    12.41,
                    12.18,
                    12.04,
                    12.48
                ],
                [
                    12.18,
                    12.53,
                    12.18,
                    12.6
                ],
                [
                    12.65,
                    12.77,
                    12.62,
                    13.15
                ],
                [
                    12.82,
                    12.92,
                    12.67,
                    12.96
                ],
                [
                    12.87,
                    12.81,
                    12.68,
                    12.98
                ],
                [
                    12.78,
                    12.66,
                    12.63,
                    12.9
                ],
                [
                    12.65,
                    12.82,
                    12.57,
                    13.02
                ],
                [
                    12.82,
                    12.44,
                    12.28,
                    13.08
                ],
                [
                    12.39,
                    12.4,
                    12.31,
                    12.65
                ],
                [
                    12.41,
                    12.89,
                    12.33,
                    13.1
                ],
                [
                    12.8,
                    12.36,
                    12.36,
                    12.91
                ],
                [
                    12.36,
                    12.21,
                    12.2,
                    12.67
                ],
                [
                    12.06,
                    11.53,
                    11.5,
                    12.17
                ],
                [
                    11.58,
                    11.52,
                    11.31,
                    11.65
                ],
                [
                    11.49,
                    11.43,
                    11.38,
                    11.58
                ],
                [
                    11.21,
                    11.17,
                    11.16,
                    11.5
                ],
                [
                    10.5,
                    10.05,
                    10.05,
                    10.5
                ],
                [
                    9.92,
                    9.99,
                    9.68,
                    10.1
                ],
                [
                    9.73,
                    9.9,
                    9.44,
                    9.98
                ],
                [
                    10.2,
                    10.22,
                    10.1,
                    10.35
                ],
                [
                    10.05,
                    10.15,
                    10.03,
                    10.3
                ],
                [
                    10.32,
                    10.16,
                    10.14,
                    10.35
                ],
                [
                    10.17,
                    9.96,
                    9.87,
                    10.2
                ],
                [
                    9.92,
                    9.78,
                    9.61,
                    9.94
                ],
                [
                    9.7,
                    9.7,
                    9.66,
                    9.8
                ],
                [
                    9.69,
                    9.68,
                    9.56,
                    9.72
                ],
                [
                    9.7,
                    9.79,
                    9.6,
                    9.79
                ],
                [
                    9.75,
                    9.72,
                    9.68,
                    9.86
                ],
                [
                    9.74,
                    9.84,
                    9.73,
                    10.05
                ],
                [
                    9.85,
                    9.6,
                    9.6,
                    9.95
                ],
                [
                    9.62,
                    9.64,
                    9.57,
                    9.77
                ],
                [
                    9.6,
                    9.56,
                    9.47,
                    9.69
                ],
                [
                    9.54,
                    9.44,
                    9.41,
                    9.66
                ],
                [
                    9.37,
                    9.55,
                    9.35,
                    9.97
                ],
                [
                    9.65,
                    9.8,
                    9.58,
                    9.81
                ],
                [
                    9.96,
                    9.87,
                    9.78,
                    10.04
                ],
                [
                    9.83,
                    10.01,
                    9.79,
                    10.13
                ],
                [
                    10.0,
                    9.71,
                    9.7,
                    10.0
                ],
                [
                    9.86,
                    10.01,
                    9.79,
                    10.02
                ],
                [
                    10.12,
                    10.17,
                    10.11,
                    10.68
                ],
                [
                    10.06,
                    9.99,
                    9.82,
                    10.15
                ],
                [
                    9.93,
                    9.67,
                    9.67,
                    9.94
                ],
                [
                    9.67,
                    9.67,
                    9.64,
                    9.86
                ],
                [
                    9.63,
                    9.55,
                    9.47,
                    9.67
                ],
                [
                    9.57,
                    9.59,
                    9.47,
                    9.6
                ],
                [
                    9.6,
                    9.66,
                    9.53,
                    9.69
                ],
                [
                    9.6,
                    9.47,
                    9.46,
                    9.68
                ],
                [
                    9.44,
                    9.67,
                    9.41,
                    9.98
                ],
                [
                    9.6,
                    9.48,
                    9.46,
                    9.72
                ],
                [
                    9.5,
                    9.28,
                    9.19,
                    9.52
                ],
                [
                    9.26,
                    9.16,
                    9.1,
                    9.31
                ],
                [
                    9.19,
                    9.33,
                    9.17,
                    9.34
                ],
                [
                    9.27,
                    9.14,
                    9.13,
                    9.27
                ],
                [
                    9.09,
                    9.19,
                    9.05,
                    9.21
                ],
                [
                    9.23,
                    9.38,
                    9.2,
                    9.41
                ],
                [
                    9.35,
                    9.38,
                    9.26,
                    9.4
                ],
                [
                    9.4,
                    9.34,
                    9.29,
                    9.41
                ],
                [
                    9.33,
                    9.75,
                    9.29,
                    9.82
                ],
                [
                    9.71,
                    9.75,
                    9.69,
                    9.95
                ],
                [
                    9.79,
                    9.91,
                    9.77,
                    10.1
                ],
                [
                    9.9,
                    9.91,
                    9.79,
                    9.99
                ],
                [
                    9.9,
                    9.55,
                    9.53,
                    9.9
                ],
                [
                    9.53,
                    9.63,
                    9.5,
                    9.73
                ],
                [
                    9.63,
                    9.64,
                    9.52,
                    9.74
                ],
                [
                    9.63,
                    9.69,
                    9.53,
                    9.78
                ],
                [
                    9.63,
                    9.36,
                    9.36,
                    9.73
                ],
                [
                    9.46,
                    9.41,
                    9.36,
                    9.94
                ],
                [
                    9.29,
                    9.42,
                    9.2,
                    9.49
                ],
                [
                    9.48,
                    9.61,
                    9.42,
                    9.64
                ],
                [
                    9.61,
                    9.7,
                    9.47,
                    9.72
                ],
                [
                    9.67,
                    9.52,
                    9.51,
                    9.82
                ],
                [
                    9.56,
                    9.73,
                    9.54,
                    10.01
                ],
                [
                    9.91,
                    9.98,
                    9.81,
                    10.13
                ],
                [
                    9.93,
                    9.82,
                    9.73,
                    9.94
                ],
                [
                    9.88,
                    10.55,
                    9.82,
                    10.76
                ],
                [
                    10.57,
                    10.54,
                    10.31,
                    10.61
                ],
                [
                    10.71,
                    10.43,
                    10.33,
                    10.75
                ],
                [
                    10.35,
                    10.34,
                    10.25,
                    10.49
                ],
                [
                    10.33,
                    10.93,
                    10.3,
                    10.99
                ],
                [
                    10.85,
                    10.74,
                    10.71,
                    11.05
                ],
                [
                    10.75,
                    11.81,
                    10.75,
                    11.81
                ],
                [
                    12.09,
                    11.74,
                    11.6,
                    12.09
                ],
                [
                    11.62,
                    11.57,
                    11.53,
                    11.87
                ],
                [
                    11.44,
                    11.24,
                    11.12,
                    11.56
                ],
                [
                    11.25,
                    11.26,
                    11.13,
                    11.3
                ],
                [
                    11.56,
                    11.64,
                    11.35,
                    11.8
                ],
                [
                    11.49,
                    11.45,
                    11.38,
                    11.85
                ],
                [
                    11.46,
                    11.68,
                    11.38,
                    11.8
                ],
                [
                    11.74,
                    12.11,
                    11.45,
                    12.11
                ],
                [
                    12.12,
                    11.65,
                    11.62,
                    12.27
                ],
                [
                    11.61,
                    11.92,
                    11.52,
                    12.05
                ],
                [
                    11.91,
                    11.65,
                    11.61,
                    11.98
                ],
                [
                    11.55,
                    11.63,
                    11.33,
                    11.8
                ],
                [
                    11.6,
                    11.86,
                    11.43,
                    11.87
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    7.19
                ],
                [
                    "2025-02-05",
                    6.31
                ],
                [
                    "2025-02-06",
                    11.43
                ],
                [
                    "2025-02-07",
                    2.20
                ],
                [
                    "2025-02-10",
                    -3.14
                ],
                [
                    "2025-02-11",
                    -8.01
                ],
                [
                    "2025-02-12",
                    -1.64
                ],
                [
                    "2025-02-13",
                    -4.27
                ],
                [
                    "2025-02-14",
                    -4.43
                ],
                [
                    "2025-02-17",
                    11.92
                ],
                [
                    "2025-02-18",
                    -7.59
                ],
                [
                    "2025-02-19",
                    2.38
                ],
                [
                    "2025-02-20",
                    -12.97
                ],
                [
                    "2025-02-21",
                    3.41
                ],
                [
                    "2025-02-24",
                    3.45
                ],
                [
                    "2025-02-25",
                    1.23
                ],
                [
                    "2025-02-26",
                    7.67
                ],
                [
                    "2025-02-27",
                    -6.01
                ],
                [
                    "2025-02-28",
                    -0.29
                ],
                [
                    "2025-03-03",
                    2.28
                ],
                [
                    "2025-03-04",
                    5.35
                ],
                [
                    "2025-03-05",
                    -8.62
                ],
                [
                    "2025-03-06",
                    -4.56
                ],
                [
                    "2025-03-07",
                    -15.22
                ],
                [
                    "2025-03-10",
                    1.65
                ],
                [
                    "2025-03-11",
                    -3.66
                ],
                [
                    "2025-03-12",
                    -9.76
                ],
                [
                    "2025-03-13",
                    -12.76
                ],
                [
                    "2025-03-14",
                    3.77
                ],
                [
                    "2025-03-17",
                    -0.58
                ],
                [
                    "2025-03-18",
                    8.85
                ],
                [
                    "2025-03-19",
                    -3.43
                ],
                [
                    "2025-03-20",
                    -6.34
                ],
                [
                    "2025-03-21",
                    7.64
                ],
                [
                    "2025-03-24",
                    -14.46
                ],
                [
                    "2025-03-25",
                    -1.25
                ],
                [
                    "2025-03-26",
                    11.92
                ],
                [
                    "2025-03-27",
                    -11.40
                ],
                [
                    "2025-03-28",
                    0.39
                ],
                [
                    "2025-03-31",
                    -8.32
                ],
                [
                    "2025-04-01",
                    -11.33
                ],
                [
                    "2025-04-02",
                    -10.11
                ],
                [
                    "2025-04-03",
                    -14.21
                ],
                [
                    "2025-04-07",
                    -20.03
                ],
                [
                    "2025-04-08",
                    -5.77
                ],
                [
                    "2025-04-09",
                    -0.89
                ],
                [
                    "2025-04-10",
                    2.33
                ],
                [
                    "2025-04-11",
                    4.03
                ],
                [
                    "2025-04-14",
                    -9.29
                ],
                [
                    "2025-04-15",
                    -17.98
                ],
                [
                    "2025-04-16",
                    -18.01
                ],
                [
                    "2025-04-17",
                    -14.66
                ],
                [
                    "2025-04-18",
                    -10.68
                ],
                [
                    "2025-04-21",
                    0.32
                ],
                [
                    "2025-04-22",
                    -6.85
                ],
                [
                    "2025-04-23",
                    3.37
                ],
                [
                    "2025-04-24",
                    -6.14
                ],
                [
                    "2025-04-25",
                    -1.50
                ],
                [
                    "2025-04-28",
                    -1.00
                ],
                [
                    "2025-04-29",
                    -5.61
                ],
                [
                    "2025-04-30",
                    0.90
                ],
                [
                    "2025-05-06",
                    -2.84
                ],
                [
                    "2025-05-07",
                    1.25
                ],
                [
                    "2025-05-08",
                    0.01
                ],
                [
                    "2025-05-09",
                    -4.71
                ],
                [
                    "2025-05-12",
                    1.46
                ],
                [
                    "2025-05-13",
                    -6.58
                ],
                [
                    "2025-05-14",
                    -8.11
                ],
                [
                    "2025-05-15",
                    -14.43
                ],
                [
                    "2025-05-16",
                    3.52
                ],
                [
                    "2025-05-19",
                    -10.24
                ],
                [
                    "2025-05-20",
                    -2.06
                ],
                [
                    "2025-05-21",
                    6.54
                ],
                [
                    "2025-05-22",
                    -12.69
                ],
                [
                    "2025-05-23",
                    11.26
                ],
                [
                    "2025-05-26",
                    -5.06
                ],
                [
                    "2025-05-27",
                    -13.79
                ],
                [
                    "2025-05-28",
                    -5.56
                ],
                [
                    "2025-05-29",
                    9.07
                ],
                [
                    "2025-05-30",
                    -6.93
                ],
                [
                    "2025-06-03",
                    3.24
                ],
                [
                    "2025-06-04",
                    10.72
                ],
                [
                    "2025-06-05",
                    -0.28
                ],
                [
                    "2025-06-06",
                    -10.17
                ],
                [
                    "2025-06-09",
                    6.15
                ],
                [
                    "2025-06-10",
                    -3.55
                ],
                [
                    "2025-06-11",
                    3.80
                ],
                [
                    "2025-06-12",
                    6.01
                ],
                [
                    "2025-06-13",
                    -13.36
                ],
                [
                    "2025-06-16",
                    -4.87
                ],
                [
                    "2025-06-17",
                    5.22
                ],
                [
                    "2025-06-18",
                    10.38
                ],
                [
                    "2025-06-19",
                    -8.36
                ],
                [
                    "2025-06-20",
                    3.82
                ],
                [
                    "2025-06-23",
                    -3.67
                ],
                [
                    "2025-06-24",
                    2.59
                ],
                [
                    "2025-06-25",
                    1.58
                ],
                [
                    "2025-06-26",
                    -7.01
                ],
                [
                    "2025-06-27",
                    5.19
                ],
                [
                    "2025-06-30",
                    1.69
                ],
                [
                    "2025-07-01",
                    -4.43
                ],
                [
                    "2025-07-02",
                    13.76
                ],
                [
                    "2025-07-03",
                    -2.61
                ],
                [
                    "2025-07-04",
                    -1.54
                ],
                [
                    "2025-07-07",
                    1.15
                ],
                [
                    "2025-07-08",
                    6.57
                ],
                [
                    "2025-07-09",
                    -16.60
                ],
                [
                    "2025-07-10",
                    18.89
                ],
                [
                    "2025-07-11",
                    -7.41
                ],
                [
                    "2025-07-14",
                    -0.15
                ],
                [
                    "2025-07-15",
                    -9.71
                ],
                [
                    "2025-07-16",
                    1.20
                ],
                [
                    "2025-07-17",
                    8.44
                ],
                [
                    "2025-07-18",
                    -5.22
                ],
                [
                    "2025-07-21",
                    1.01
                ],
                [
                    "2025-07-22",
                    2.73
                ],
                [
                    "2025-07-23",
                    -10.94
                ],
                [
                    "2025-07-24",
                    -0.20
                ],
                [
                    "2025-07-25",
                    -15.90
                ],
                [
                    "2025-07-28",
                    2.87
                ],
                [
                    "2025-07-29",
                    4.74
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    2.54
                ],
                [
                    "2025-02-05",
                    -2.05
                ],
                [
                    "2025-02-06",
                    -6.62
                ],
                [
                    "2025-02-07",
                    2.50
                ],
                [
                    "2025-02-10",
                    1.08
                ],
                [
                    "2025-02-11",
                    2.01
                ],
                [
                    "2025-02-12",
                    1.85
                ],
                [
                    "2025-02-13",
                    6.00
                ],
                [
                    "2025-02-14",
                    5.17
                ],
                [
                    "2025-02-17",
                    -7.85
                ],
                [
                    "2025-02-18",
                    1.31
                ],
                [
                    "2025-02-19",
                    -5.63
                ],
                [
                    "2025-02-20",
                    1.61
                ],
                [
                    "2025-02-21",
                    -6.93
                ],
                [
                    "2025-02-24",
                    0.62
                ],
                [
                    "2025-02-25",
                    -4.78
                ],
                [
                    "2025-02-26",
                    -3.00
                ],
                [
                    "2025-02-27",
                    3.01
                ],
                [
                    "2025-02-28",
                    0.86
                ],
                [
                    "2025-03-03",
                    -4.06
                ],
                [
                    "2025-03-04",
                    -3.18
                ],
                [
                    "2025-03-05",
                    4.97
                ],
                [
                    "2025-03-06",
                    3.23
                ],
                [
                    "2025-03-07",
                    8.73
                ],
                [
                    "2025-03-10",
                    0.55
                ],
                [
                    "2025-03-11",
                    4.33
                ],
                [
                    "2025-03-12",
                    1.42
                ],
                [
                    "2025-03-13",
                    3.69
                ],
                [
                    "2025-03-14",
                    -2.30
                ],
                [
                    "2025-03-17",
                    -4.45
                ],
                [
                    "2025-03-18",
                    -4.20
                ],
                [
                    "2025-03-19",
                    -2.18
                ],
                [
                    "2025-03-20",
                    -0.23
                ],
                [
                    "2025-03-21",
                    1.39
                ],
                [
                    "2025-03-24",
                    3.28
                ],
                [
                    "2025-03-25",
                    1.47
                ],
                [
                    "2025-03-26",
                    -4.28
                ],
                [
                    "2025-03-27",
                    8.31
                ],
                [
                    "2025-03-28",
                    2.79
                ],
                [
                    "2025-03-31",
                    -3.53
                ],
                [
                    "2025-04-01",
                    -1.96
                ],
                [
                    "2025-04-02",
                    3.65
                ],
                [
                    "2025-04-03",
                    0.66
                ],
                [
                    "2025-04-07",
                    -2.64
                ],
                [
                    "2025-04-08",
                    -3.31
                ],
                [
                    "2025-04-09",
                    -1.91
                ],
                [
                    "2025-04-10",
                    -1.56
                ],
                [
                    "2025-04-11",
                    -12.46
                ],
                [
                    "2025-04-14",
                    1.31
                ],
                [
                    "2025-04-15",
                    -3.21
                ],
                [
                    "2025-04-16",
                    -1.19
                ],
                [
                    "2025-04-17",
                    8.62
                ],
                [
                    "2025-04-18",
                    1.64
                ],
                [
                    "2025-04-21",
                    -2.45
                ],
                [
                    "2025-04-22",
                    3.14
                ],
                [
                    "2025-04-23",
                    1.30
                ],
                [
                    "2025-04-24",
                    2.19
                ],
                [
                    "2025-04-25",
                    -6.54
                ],
                [
                    "2025-04-28",
                    -7.78
                ],
                [
                    "2025-04-29",
                    1.45
                ],
                [
                    "2025-04-30",
                    -0.02
                ],
                [
                    "2025-05-06",
                    -4.67
                ],
                [
                    "2025-05-07",
                    -1.86
                ],
                [
                    "2025-05-08",
                    -1.10
                ],
                [
                    "2025-05-09",
                    0.14
                ],
                [
                    "2025-05-12",
                    -1.70
                ],
                [
                    "2025-05-13",
                    1.63
                ],
                [
                    "2025-05-14",
                    0.04
                ],
                [
                    "2025-05-15",
                    4.81
                ],
                [
                    "2025-05-16",
                    -0.97
                ],
                [
                    "2025-05-19",
                    1.19
                ],
                [
                    "2025-05-20",
                    1.18
                ],
                [
                    "2025-05-21",
                    1.11
                ],
                [
                    "2025-05-22",
                    4.52
                ],
                [
                    "2025-05-23",
                    -4.63
                ],
                [
                    "2025-05-26",
                    2.78
                ],
                [
                    "2025-05-27",
                    3.30
                ],
                [
                    "2025-05-28",
                    1.01
                ],
                [
                    "2025-05-29",
                    -6.36
                ],
                [
                    "2025-05-30",
                    2.98
                ],
                [
                    "2025-06-03",
                    2.62
                ],
                [
                    "2025-06-04",
                    -8.05
                ],
                [
                    "2025-06-05",
                    -2.05
                ],
                [
                    "2025-06-06",
                    9.23
                ],
                [
                    "2025-06-09",
                    -2.39
                ],
                [
                    "2025-06-10",
                    -2.56
                ],
                [
                    "2025-06-11",
                    0.52
                ],
                [
                    "2025-06-12",
                    -4.95
                ],
                [
                    "2025-06-13",
                    -1.06
                ],
                [
                    "2025-06-16",
                    4.52
                ],
                [
                    "2025-06-17",
                    0.31
                ],
                [
                    "2025-06-18",
                    3.21
                ],
                [
                    "2025-06-19",
                    -1.64
                ],
                [
                    "2025-06-20",
                    2.00
                ],
                [
                    "2025-06-23",
                    -4.55
                ],
                [
                    "2025-06-24",
                    -5.13
                ],
                [
                    "2025-06-25",
                    -1.39
                ],
                [
                    "2025-06-26",
                    1.17
                ],
                [
                    "2025-06-27",
                    -1.19
                ],
                [
                    "2025-06-30",
                    -2.69
                ],
                [
                    "2025-07-01",
                    1.14
                ],
                [
                    "2025-07-02",
                    -3.24
                ],
                [
                    "2025-07-03",
                    0.35
                ],
                [
                    "2025-07-04",
                    -1.02
                ],
                [
                    "2025-07-07",
                    -1.94
                ],
                [
                    "2025-07-08",
                    -0.03
                ],
                [
                    "2025-07-09",
                    2.91
                ],
                [
                    "2025-07-10",
                    -7.36
                ],
                [
                    "2025-07-11",
                    0.03
                ],
                [
                    "2025-07-14",
                    3.53
                ],
                [
                    "2025-07-15",
                    0.36
                ],
                [
                    "2025-07-16",
                    1.90
                ],
                [
                    "2025-07-17",
                    -1.11
                ],
                [
                    "2025-07-18",
                    1.55
                ],
                [
                    "2025-07-21",
                    2.93
                ],
                [
                    "2025-07-22",
                    -0.02
                ],
                [
                    "2025-07-23",
                    1.83
                ],
                [
                    "2025-07-24",
                    0.05
                ],
                [
                    "2025-07-25",
                    1.08
                ],
                [
                    "2025-07-28",
                    -3.30
                ],
                [
                    "2025-07-29",
                    -2.02
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -9.72
                ],
                [
                    "2025-02-05",
                    -4.27
                ],
                [
                    "2025-02-06",
                    -4.82
                ],
                [
                    "2025-02-07",
                    -4.70
                ],
                [
                    "2025-02-10",
                    2.07
                ],
                [
                    "2025-02-11",
                    6.00
                ],
                [
                    "2025-02-12",
                    -0.21
                ],
                [
                    "2025-02-13",
                    -1.73
                ],
                [
                    "2025-02-14",
                    -0.73
                ],
                [
                    "2025-02-17",
                    -4.08
                ],
                [
                    "2025-02-18",
                    6.28
                ],
                [
                    "2025-02-19",
                    3.24
                ],
                [
                    "2025-02-20",
                    11.36
                ],
                [
                    "2025-02-21",
                    3.52
                ],
                [
                    "2025-02-24",
                    -4.07
                ],
                [
                    "2025-02-25",
                    3.56
                ],
                [
                    "2025-02-26",
                    -4.68
                ],
                [
                    "2025-02-27",
                    3.00
                ],
                [
                    "2025-02-28",
                    -0.57
                ],
                [
                    "2025-03-03",
                    1.78
                ],
                [
                    "2025-03-04",
                    -2.17
                ],
                [
                    "2025-03-05",
                    3.65
                ],
                [
                    "2025-03-06",
                    1.34
                ],
                [
                    "2025-03-07",
                    6.50
                ],
                [
                    "2025-03-10",
                    -2.19
                ],
                [
                    "2025-03-11",
                    -0.67
                ],
                [
                    "2025-03-12",
                    8.34
                ],
                [
                    "2025-03-13",
                    9.07
                ],
                [
                    "2025-03-14",
                    -1.46
                ],
                [
                    "2025-03-17",
                    5.03
                ],
                [
                    "2025-03-18",
                    -4.66
                ],
                [
                    "2025-03-19",
                    5.62
                ],
                [
                    "2025-03-20",
                    6.58
                ],
                [
                    "2025-03-21",
                    -9.03
                ],
                [
                    "2025-03-24",
                    11.17
                ],
                [
                    "2025-03-25",
                    -0.22
                ],
                [
                    "2025-03-26",
                    -7.63
                ],
                [
                    "2025-03-27",
                    3.09
                ],
                [
                    "2025-03-28",
                    -3.18
                ],
                [
                    "2025-03-31",
                    11.85
                ],
                [
                    "2025-04-01",
                    13.28
                ],
                [
                    "2025-04-02",
                    6.46
                ],
                [
                    "2025-04-03",
                    13.55
                ],
                [
                    "2025-04-07",
                    22.67
                ],
                [
                    "2025-04-08",
                    9.08
                ],
                [
                    "2025-04-09",
                    2.80
                ],
                [
                    "2025-04-10",
                    -0.77
                ],
                [
                    "2025-04-11",
                    8.43
                ],
                [
                    "2025-04-14",
                    7.98
                ],
                [
                    "2025-04-15",
                    21.19
                ],
                [
                    "2025-04-16",
                    19.20
                ],
                [
                    "2025-04-17",
                    6.04
                ],
                [
                    "2025-04-18",
                    9.04
                ],
                [
                    "2025-04-21",
                    2.14
                ],
                [
                    "2025-04-22",
                    3.71
                ],
                [
                    "2025-04-23",
                    -4.67
                ],
                [
                    "2025-04-24",
                    3.95
                ],
                [
                    "2025-04-25",
                    8.04
                ],
                [
                    "2025-04-28",
                    8.79
                ],
                [
                    "2025-04-29",
                    4.16
                ],
                [
                    "2025-04-30",
                    -0.89
                ],
                [
                    "2025-05-06",
                    7.51
                ],
                [
                    "2025-05-07",
                    0.61
                ],
                [
                    "2025-05-08",
                    1.10
                ],
                [
                    "2025-05-09",
                    4.56
                ],
                [
                    "2025-05-12",
                    0.24
                ],
                [
                    "2025-05-13",
                    4.96
                ],
                [
                    "2025-05-14",
                    8.08
                ],
                [
                    "2025-05-15",
                    9.61
                ],
                [
                    "2025-05-16",
                    -2.55
                ],
                [
                    "2025-05-19",
                    9.06
                ],
                [
                    "2025-05-20",
                    0.87
                ],
                [
                    "2025-05-21",
                    -7.65
                ],
                [
                    "2025-05-22",
                    8.18
                ],
                [
                    "2025-05-23",
                    -6.64
                ],
                [
                    "2025-05-26",
                    2.28
                ],
                [
                    "2025-05-27",
                    10.50
                ],
                [
                    "2025-05-28",
                    4.55
                ],
                [
                    "2025-05-29",
                    -2.72
                ],
                [
                    "2025-05-30",
                    3.95
                ],
                [
                    "2025-06-03",
                    -5.86
                ],
                [
                    "2025-06-04",
                    -2.67
                ],
                [
                    "2025-06-05",
                    2.33
                ],
                [
                    "2025-06-06",
                    0.94
                ],
                [
                    "2025-06-09",
                    -3.76
                ],
                [
                    "2025-06-10",
                    6.11
                ],
                [
                    "2025-06-11",
                    -4.32
                ],
                [
                    "2025-06-12",
                    -1.06
                ],
                [
                    "2025-06-13",
                    14.42
                ],
                [
                    "2025-06-16",
                    0.35
                ],
                [
                    "2025-06-17",
                    -5.54
                ],
                [
                    "2025-06-18",
                    -13.59
                ],
                [
                    "2025-06-19",
                    10.01
                ],
                [
                    "2025-06-20",
                    -5.82
                ],
                [
                    "2025-06-23",
                    8.23
                ],
                [
                    "2025-06-24",
                    2.54
                ],
                [
                    "2025-06-25",
                    -0.18
                ],
                [
                    "2025-06-26",
                    5.84
                ],
                [
                    "2025-06-27",
                    -4.00
                ],
                [
                    "2025-06-30",
                    0.99
                ],
                [
                    "2025-07-01",
                    3.29
                ],
                [
                    "2025-07-02",
                    -10.51
                ],
                [
                    "2025-07-03",
                    2.26
                ],
                [
                    "2025-07-04",
                    2.56
                ],
                [
                    "2025-07-07",
                    0.80
                ],
                [
                    "2025-07-08",
                    -6.54
                ],
                [
                    "2025-07-09",
                    13.69
                ],
                [
                    "2025-07-10",
                    -11.53
                ],
                [
                    "2025-07-11",
                    7.38
                ],
                [
                    "2025-07-14",
                    -3.38
                ],
                [
                    "2025-07-15",
                    9.35
                ],
                [
                    "2025-07-16",
                    -3.10
                ],
                [
                    "2025-07-17",
                    -7.33
                ],
                [
                    "2025-07-18",
                    3.66
                ],
                [
                    "2025-07-21",
                    -3.94
                ],
                [
                    "2025-07-22",
                    -2.71
                ],
                [
                    "2025-07-23",
                    9.12
                ],
                [
                    "2025-07-24",
                    0.15
                ],
                [
                    "2025-07-25",
                    14.82
                ],
                [
                    "2025-07-28",
                    0.43
                ],
                [
                    "2025-07-29",
                    -2.72
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    -3.14
                ],
                [
                    "2025-02-11",
                    -8.01
                ],
                [
                    "2025-02-12",
                    -1.64
                ],
                [
                    "2025-02-28",
                    -0.29
                ],
                [
                    "2025-03-04",
                    5.35
                ],
                [
                    "2025-06-04",
                    10.72
                ],
                [
                    "2025-06-05",
                    -0.28
                ],
                [
                    "2025-06-09",
                    6.15
                ],
                [
                    "2025-06-12",
                    6.01
                ],
                [
                    "2025-06-18",
                    10.38
                ],
                [
                    "2025-06-20",
                    3.82
                ],
                [
                    "2025-06-23",
                    -3.67
                ],
                [
                    "2025-07-02",
                    13.76
                ],
                [
                    "2025-07-03",
                    -2.61
                ],
                [
                    "2025-07-04",
                    -1.54
                ],
                [
                    "2025-07-07",
                    1.15
                ],
                [
                    "2025-07-08",
                    6.57
                ],
                [
                    "2025-07-10",
                    18.89
                ],
                [
                    "2025-07-14",
                    -0.15
                ],
                [
                    "2025-07-16",
                    1.20
                ],
                [
                    "2025-07-22",
                    2.73
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-12",
                    1.85
                ],
                [
                    "2025-02-17",
                    -7.85
                ],
                [
                    "2025-02-18",
                    1.31
                ],
                [
                    "2025-03-27",
                    8.31
                ],
                [
                    "2025-05-26",
                    2.78
                ],
                [
                    "2025-06-06",
                    9.23
                ],
                [
                    "2025-06-18",
                    3.21
                ],
                [
                    "2025-06-20",
                    2.00
                ],
                [
                    "2025-07-18",
                    1.55
                ],
                [
                    "2025-07-21",
                    2.93
                ],
                [
                    "2025-07-22",
                    -0.02
                ],
                [
                    "2025-07-23",
                    1.83
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-13",
                    -1.73
                ],
                [
                    "2025-02-14",
                    -0.73
                ],
                [
                    "2025-02-19",
                    3.24
                ],
                [
                    "2025-02-20",
                    11.36
                ],
                [
                    "2025-02-21",
                    3.52
                ],
                [
                    "2025-02-24",
                    -4.07
                ],
                [
                    "2025-02-25",
                    3.56
                ],
                [
                    "2025-03-05",
                    3.65
                ],
                [
                    "2025-03-06",
                    1.34
                ],
                [
                    "2025-03-07",
                    6.50
                ],
                [
                    "2025-03-10",
                    -2.19
                ],
                [
                    "2025-03-11",
                    -0.67
                ],
                [
                    "2025-03-12",
                    8.34
                ],
                [
                    "2025-03-13",
                    9.07
                ],
                [
                    "2025-03-14",
                    -1.46
                ],
                [
                    "2025-03-17",
                    5.03
                ],
                [
                    "2025-03-18",
                    -4.66
                ],
                [
                    "2025-03-19",
                    5.62
                ],
                [
                    "2025-03-24",
                    11.17
                ],
                [
                    "2025-03-25",
                    -0.22
                ],
                [
                    "2025-03-26",
                    -7.63
                ],
                [
                    "2025-03-28",
                    -3.18
                ],
                [
                    "2025-03-31",
                    11.85
                ],
                [
                    "2025-04-01",
                    13.28
                ],
                [
                    "2025-04-02",
                    6.46
                ],
                [
                    "2025-04-03",
                    13.55
                ],
                [
                    "2025-04-07",
                    22.67
                ],
                [
                    "2025-04-08",
                    9.08
                ],
                [
                    "2025-04-09",
                    2.80
                ],
                [
                    "2025-04-10",
                    -0.77
                ],
                [
                    "2025-04-11",
                    8.43
                ],
                [
                    "2025-04-14",
                    7.98
                ],
                [
                    "2025-04-15",
                    21.19
                ],
                [
                    "2025-04-16",
                    19.20
                ],
                [
                    "2025-04-17",
                    6.04
                ],
                [
                    "2025-04-18",
                    9.04
                ],
                [
                    "2025-04-21",
                    2.14
                ],
                [
                    "2025-04-22",
                    3.71
                ],
                [
                    "2025-04-23",
                    -4.67
                ],
                [
                    "2025-04-24",
                    3.95
                ],
                [
                    "2025-04-25",
                    8.04
                ],
                [
                    "2025-04-28",
                    8.79
                ],
                [
                    "2025-04-29",
                    4.16
                ],
                [
                    "2025-04-30",
                    -0.89
                ],
                [
                    "2025-05-06",
                    7.51
                ],
                [
                    "2025-05-07",
                    0.61
                ],
                [
                    "2025-05-08",
                    1.10
                ],
                [
                    "2025-05-09",
                    4.56
                ],
                [
                    "2025-05-12",
                    0.24
                ],
                [
                    "2025-05-13",
                    4.96
                ],
                [
                    "2025-05-14",
                    8.08
                ],
                [
                    "2025-05-15",
                    9.61
                ],
                [
                    "2025-05-16",
                    -2.55
                ],
                [
                    "2025-05-19",
                    9.06
                ],
                [
                    "2025-05-20",
                    0.87
                ],
                [
                    "2025-05-21",
                    -7.65
                ],
                [
                    "2025-05-22",
                    8.18
                ],
                [
                    "2025-05-23",
                    -6.64
                ],
                [
                    "2025-05-27",
                    10.50
                ],
                [
                    "2025-05-28",
                    4.55
                ],
                [
                    "2025-05-29",
                    -2.72
                ],
                [
                    "2025-05-30",
                    3.95
                ],
                [
                    "2025-06-03",
                    -5.86
                ],
                [
                    "2025-06-11",
                    -4.32
                ],
                [
                    "2025-06-13",
                    14.42
                ],
                [
                    "2025-06-16",
                    0.35
                ],
                [
                    "2025-06-17",
                    -5.54
                ],
                [
                    "2025-06-19",
                    10.01
                ],
                [
                    "2025-06-25",
                    -0.18
                ],
                [
                    "2025-06-26",
                    5.84
                ],
                [
                    "2025-06-27",
                    -4.00
                ],
                [
                    "2025-07-01",
                    3.29
                ],
                [
                    "2025-07-09",
                    13.69
                ],
                [
                    "2025-07-15",
                    9.35
                ],
                [
                    "2025-07-17",
                    -7.33
                ],
                [
                    "2025-07-24",
                    0.15
                ],
                [
                    "2025-07-25",
                    14.82
                ],
                [
                    "2025-07-28",
                    0.43
                ],
                [
                    "2025-07-29",
                    -2.72
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002459 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_c8a887edc5b64ba5b3bc834c0959d7df.setOption(option_c8a887edc5b64ba5b3bc834c0959d7df);
            window.addEventListener('resize', function(){
                chart_c8a887edc5b64ba5b3bc834c0959d7df.resize();
            })
    </script>
</body>
</html>
