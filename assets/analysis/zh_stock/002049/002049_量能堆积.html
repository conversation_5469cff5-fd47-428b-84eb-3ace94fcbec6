<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="be6c62a592e74f89b2e35d34eca9f333" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_be6c62a592e74f89b2e35d34eca9f333 = echarts.init(
            document.getElementById('be6c62a592e74f89b2e35d34eca9f333'), 'white', {renderer: 'canvas'});
        var option_be6c62a592e74f89b2e35d34eca9f333 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    60.20,
                    59.12,
                    59.09,
                    60.38
                ],
                [
                    60.01,
                    60.80,
                    59.60,
                    61.18
                ],
                [
                    60.40,
                    62.16,
                    60.24,
                    62.21
                ],
                [
                    62.18,
                    63.27,
                    61.67,
                    64.64
                ],
                [
                    63.63,
                    63.99,
                    63.22,
                    64.50
                ],
                [
                    63.70,
                    63.83,
                    63.03,
                    64.99
                ],
                [
                    63.60,
                    65.63,
                    63.23,
                    65.88
                ],
                [
                    65.27,
                    63.68,
                    63.63,
                    65.48
                ],
                [
                    63.53,
                    64.71,
                    62.94,
                    65.21
                ],
                [
                    64.78,
                    65.26,
                    64.77,
                    66.52
                ],
                [
                    65.00,
                    62.23,
                    61.67,
                    65.27
                ],
                [
                    62.22,
                    64.11,
                    62.20,
                    64.13
                ],
                [
                    64.08,
                    63.56,
                    62.90,
                    64.14
                ],
                [
                    63.82,
                    65.93,
                    63.50,
                    66.15
                ],
                [
                    66.00,
                    65.30,
                    64.78,
                    66.25
                ],
                [
                    64.30,
                    64.74,
                    64.08,
                    65.93
                ],
                [
                    64.83,
                    65.95,
                    63.84,
                    66.36
                ],
                [
                    66.31,
                    64.83,
                    63.56,
                    66.50
                ],
                [
                    64.22,
                    61.65,
                    61.58,
                    64.45
                ],
                [
                    61.67,
                    61.80,
                    61.14,
                    63.11
                ],
                [
                    61.39,
                    67.98,
                    61.28,
                    67.98
                ],
                [
                    70.06,
                    68.70,
                    68.20,
                    70.88
                ],
                [
                    69.26,
                    69.97,
                    68.65,
                    70.50
                ],
                [
                    69.50,
                    69.58,
                    69.18,
                    72.23
                ],
                [
                    69.82,
                    70.57,
                    69.25,
                    71.28
                ],
                [
                    69.11,
                    71.58,
                    68.88,
                    73.00
                ],
                [
                    71.65,
                    70.70,
                    70.66,
                    72.20
                ],
                [
                    71.10,
                    69.30,
                    68.80,
                    71.28
                ],
                [
                    69.06,
                    69.90,
                    68.92,
                    70.25
                ],
                [
                    69.66,
                    68.66,
                    68.34,
                    69.80
                ],
                [
                    68.80,
                    68.95,
                    68.70,
                    70.66
                ],
                [
                    68.45,
                    67.91,
                    67.66,
                    69.27
                ],
                [
                    67.84,
                    68.72,
                    67.65,
                    71.00
                ],
                [
                    68.12,
                    66.20,
                    66.00,
                    69.40
                ],
                [
                    66.02,
                    66.10,
                    64.56,
                    66.56
                ],
                [
                    66.03,
                    68.39,
                    65.55,
                    69.18
                ],
                [
                    67.80,
                    69.07,
                    67.50,
                    69.87
                ],
                [
                    68.50,
                    68.10,
                    67.75,
                    69.68
                ],
                [
                    68.00,
                    66.67,
                    66.15,
                    68.01
                ],
                [
                    66.03,
                    65.74,
                    64.91,
                    66.63
                ],
                [
                    66.00,
                    66.32,
                    65.75,
                    67.48
                ],
                [
                    66.20,
                    67.53,
                    65.90,
                    69.00
                ],
                [
                    66.88,
                    67.86,
                    66.81,
                    69.20
                ],
                [
                    64.50,
                    63.11,
                    61.07,
                    67.45
                ],
                [
                    63.99,
                    62.78,
                    61.41,
                    64.70
                ],
                [
                    62.00,
                    69.06,
                    61.37,
                    69.06
                ],
                [
                    69.49,
                    69.31,
                    68.10,
                    70.99
                ],
                [
                    68.80,
                    71.70,
                    68.70,
                    73.18
                ],
                [
                    72.15,
                    70.59,
                    70.30,
                    72.15
                ],
                [
                    70.44,
                    69.39,
                    68.45,
                    70.74
                ],
                [
                    69.09,
                    69.09,
                    68.01,
                    69.80
                ],
                [
                    68.86,
                    68.47,
                    68.44,
                    69.99
                ],
                [
                    68.16,
                    67.57,
                    66.88,
                    68.47
                ],
                [
                    67.70,
                    68.25,
                    67.29,
                    68.50
                ],
                [
                    68.00,
                    67.68,
                    67.51,
                    68.91
                ],
                [
                    67.80,
                    66.70,
                    66.53,
                    68.11
                ],
                [
                    66.80,
                    65.45,
                    65.27,
                    67.33
                ],
                [
                    65.70,
                    65.94,
                    65.00,
                    66.33
                ],
                [
                    65.97,
                    65.45,
                    65.16,
                    66.33
                ],
                [
                    63.51,
                    64.39,
                    62.83,
                    64.76
                ],
                [
                    64.30,
                    63.98,
                    63.90,
                    64.97
                ],
                [
                    64.30,
                    65.80,
                    64.20,
                    65.80
                ],
                [
                    66.42,
                    65.82,
                    65.38,
                    66.80
                ],
                [
                    65.72,
                    66.18,
                    65.30,
                    66.47
                ],
                [
                    66.18,
                    64.84,
                    64.72,
                    66.18
                ],
                [
                    65.20,
                    67.05,
                    65.20,
                    67.65
                ],
                [
                    67.10,
                    66.11,
                    66.03,
                    67.14
                ],
                [
                    66.11,
                    66.15,
                    65.70,
                    66.44
                ],
                [
                    66.09,
                    64.31,
                    64.31,
                    66.09
                ],
                [
                    64.20,
                    64.35,
                    63.76,
                    64.82
                ],
                [
                    64.02,
                    64.68,
                    63.83,
                    64.91
                ],
                [
                    64.70,
                    64.63,
                    64.31,
                    64.97
                ],
                [
                    64.62,
                    64.39,
                    64.32,
                    64.95
                ],
                [
                    64.09,
                    64.57,
                    64.06,
                    65.45
                ],
                [
                    64.50,
                    63.08,
                    63.03,
                    64.73
                ],
                [
                    63.31,
                    64.46,
                    63.10,
                    64.50
                ],
                [
                    64.42,
                    64.15,
                    63.89,
                    65.07
                ],
                [
                    64.21,
                    63.40,
                    63.22,
                    64.41
                ],
                [
                    63.76,
                    64.00,
                    63.50,
                    64.50
                ],
                [
                    63.97,
                    63.97,
                    63.00,
                    64.25
                ],
                [
                    63.40,
                    64.24,
                    63.17,
                    64.44
                ],
                [
                    64.20,
                    64.25,
                    64.04,
                    64.50
                ],
                [
                    64.50,
                    64.84,
                    64.06,
                    65.10
                ],
                [
                    65.01,
                    64.45,
                    64.38,
                    65.73
                ],
                [
                    64.45,
                    64.54,
                    64.23,
                    64.85
                ],
                [
                    64.54,
                    63.19,
                    62.84,
                    64.54
                ],
                [
                    63.20,
                    63.19,
                    63.11,
                    63.65
                ],
                [
                    63.00,
                    62.91,
                    62.84,
                    63.20
                ],
                [
                    62.93,
                    61.97,
                    61.89,
                    62.99
                ],
                [
                    61.99,
                    62.67,
                    61.95,
                    62.93
                ],
                [
                    62.60,
                    62.52,
                    62.07,
                    62.70
                ],
                [
                    62.45,
                    62.90,
                    62.33,
                    63.08
                ],
                [
                    62.90,
                    62.17,
                    61.95,
                    63.04
                ],
                [
                    62.13,
                    61.90,
                    61.76,
                    62.46
                ],
                [
                    61.66,
                    62.65,
                    61.52,
                    62.96
                ],
                [
                    62.65,
                    63.25,
                    62.52,
                    63.33
                ],
                [
                    63.16,
                    64.50,
                    63.06,
                    65.33
                ],
                [
                    64.40,
                    63.69,
                    63.69,
                    64.55
                ],
                [
                    64.09,
                    63.87,
                    63.65,
                    64.53
                ],
                [
                    64.10,
                    65.86,
                    64.10,
                    66.42
                ],
                [
                    65.60,
                    65.52,
                    65.15,
                    66.20
                ],
                [
                    65.33,
                    64.91,
                    64.55,
                    65.33
                ],
                [
                    64.71,
                    64.78,
                    64.50,
                    65.20
                ],
                [
                    64.85,
                    64.73,
                    64.40,
                    65.28
                ],
                [
                    64.72,
                    64.24,
                    64.01,
                    64.95
                ],
                [
                    64.24,
                    65.50,
                    64.24,
                    65.60
                ],
                [
                    65.23,
                    65.19,
                    65.08,
                    65.72
                ],
                [
                    65.18,
                    65.82,
                    65.00,
                    66.08
                ],
                [
                    65.82,
                    66.11,
                    65.44,
                    66.42
                ],
                [
                    66.02,
                    66.62,
                    65.90,
                    66.79
                ],
                [
                    66.49,
                    66.09,
                    65.70,
                    67.25
                ],
                [
                    66.10,
                    66.37,
                    66.00,
                    67.79
                ],
                [
                    66.25,
                    67.90,
                    65.89,
                    67.97
                ],
                [
                    67.91,
                    67.65,
                    67.22,
                    68.19
                ],
                [
                    67.33,
                    67.57,
                    67.23,
                    68.00
                ],
                [
                    67.56,
                    67.26,
                    66.70,
                    67.83
                ],
                [
                    66.91,
                    67.40,
                    66.84,
                    68.24
                ],
                [
                    67.40,
                    67.98,
                    67.26,
                    68.50
                ],
                [
                    68.00,
                    68.49,
                    67.60,
                    68.59
                ],
                [
                    68.69,
                    68.48,
                    68.22,
                    69.25
                ],
                [
                    68.30,
                    73.20,
                    68.18,
                    73.50
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u7a81\u7834",
            "symbol": "pin",
            "symbolSize": 16,
            "data": [
                [
                    "2025-04-11",
                    73.18
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#1f77b4"
            }
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "symbol": "diamond",
            "symbolSize": 16,
            "data": [
                [
                    "2025-03-05",
                    70.88
                ],
                [
                    "2025-04-10",
                    70.99
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u7a81\u7834",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002049 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_be6c62a592e74f89b2e35d34eca9f333.setOption(option_be6c62a592e74f89b2e35d34eca9f333);
            window.addEventListener('resize', function(){
                chart_be6c62a592e74f89b2e35d34eca9f333.resize();
            })
    </script>
</body>
</html>
