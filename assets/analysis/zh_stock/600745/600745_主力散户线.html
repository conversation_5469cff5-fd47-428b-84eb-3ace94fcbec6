<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="c95ad042229f46808cbbbff5ba3f8a53" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_c95ad042229f46808cbbbff5ba3f8a53 = echarts.init(
            document.getElementById('c95ad042229f46808cbbbff5ba3f8a53'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_c95ad042229f46808cbbbff5ba3f8a53 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    34.88,
                    33.66,
                    33.65,
                    35.07
                ],
                [
                    34.05,
                    34.32,
                    33.7,
                    34.96
                ],
                [
                    34.12,
                    35.13,
                    33.89,
                    35.2
                ],
                [
                    35.08,
                    36.04,
                    34.91,
                    36.75
                ],
                [
                    36.05,
                    36.0,
                    35.62,
                    36.18
                ],
                [
                    35.99,
                    35.62,
                    35.3,
                    36.05
                ],
                [
                    35.65,
                    36.67,
                    35.1,
                    36.68
                ],
                [
                    36.49,
                    35.64,
                    35.6,
                    36.5
                ],
                [
                    35.52,
                    35.72,
                    35.35,
                    35.87
                ],
                [
                    35.72,
                    35.6,
                    35.33,
                    36.08
                ],
                [
                    35.55,
                    34.32,
                    34.2,
                    35.57
                ],
                [
                    34.38,
                    35.53,
                    34.35,
                    35.56
                ],
                [
                    35.5,
                    36.9,
                    35.26,
                    37.77
                ],
                [
                    36.5,
                    37.33,
                    36.49,
                    37.68
                ],
                [
                    37.28,
                    37.63,
                    36.7,
                    37.98
                ],
                [
                    37.16,
                    37.32,
                    36.83,
                    37.95
                ],
                [
                    37.28,
                    37.11,
                    36.85,
                    37.46
                ],
                [
                    37.15,
                    36.51,
                    35.88,
                    37.38
                ],
                [
                    36.29,
                    34.65,
                    34.55,
                    36.42
                ],
                [
                    34.73,
                    35.1,
                    34.26,
                    36.1
                ],
                [
                    34.9,
                    35.8,
                    34.71,
                    36.29
                ],
                [
                    35.8,
                    35.49,
                    35.13,
                    36.15
                ],
                [
                    35.75,
                    36.28,
                    35.66,
                    36.4
                ],
                [
                    36.28,
                    36.2,
                    35.96,
                    36.69
                ],
                [
                    36.32,
                    36.01,
                    35.59,
                    36.45
                ],
                [
                    35.51,
                    36.36,
                    35.51,
                    36.91
                ],
                [
                    36.3,
                    36.17,
                    36.1,
                    36.78
                ],
                [
                    36.1,
                    35.28,
                    35.0,
                    36.1
                ],
                [
                    35.35,
                    35.78,
                    35.11,
                    35.98
                ],
                [
                    35.81,
                    35.57,
                    35.51,
                    35.94
                ],
                [
                    35.7,
                    35.8,
                    35.6,
                    36.43
                ],
                [
                    35.66,
                    35.19,
                    35.12,
                    35.73
                ],
                [
                    35.06,
                    34.69,
                    34.66,
                    35.24
                ],
                [
                    35.45,
                    34.89,
                    34.51,
                    36.66
                ],
                [
                    34.43,
                    33.95,
                    33.15,
                    34.74
                ],
                [
                    33.9,
                    33.98,
                    33.8,
                    34.55
                ],
                [
                    33.8,
                    33.58,
                    33.57,
                    34.11
                ],
                [
                    33.6,
                    33.59,
                    33.26,
                    34.09
                ],
                [
                    33.25,
                    33.37,
                    33.25,
                    33.82
                ],
                [
                    33.01,
                    32.58,
                    32.25,
                    33.25
                ],
                [
                    32.5,
                    32.59,
                    32.44,
                    32.92
                ],
                [
                    32.6,
                    32.69,
                    32.42,
                    32.83
                ],
                [
                    32.33,
                    32.0,
                    31.88,
                    32.78
                ],
                [
                    30.42,
                    28.8,
                    28.8,
                    30.92
                ],
                [
                    28.82,
                    28.61,
                    27.92,
                    29.45
                ],
                [
                    28.08,
                    29.35,
                    27.52,
                    29.58
                ],
                [
                    30.2,
                    30.24,
                    29.93,
                    30.77
                ],
                [
                    30.1,
                    31.37,
                    29.95,
                    31.8
                ],
                [
                    32.47,
                    31.8,
                    31.55,
                    32.48
                ],
                [
                    31.61,
                    31.93,
                    31.31,
                    32.0
                ],
                [
                    31.78,
                    32.1,
                    31.52,
                    33.15
                ],
                [
                    31.94,
                    31.6,
                    31.57,
                    32.4
                ],
                [
                    31.48,
                    31.31,
                    31.16,
                    31.75
                ],
                [
                    31.45,
                    31.9,
                    31.2,
                    32.6
                ],
                [
                    31.8,
                    31.9,
                    31.7,
                    32.05
                ],
                [
                    32.1,
                    32.13,
                    31.7,
                    32.44
                ],
                [
                    32.15,
                    31.83,
                    31.8,
                    32.76
                ],
                [
                    31.8,
                    32.43,
                    31.77,
                    33.28
                ],
                [
                    33.28,
                    33.64,
                    32.97,
                    33.99
                ],
                [
                    33.51,
                    34.51,
                    33.28,
                    35.4
                ],
                [
                    34.67,
                    34.67,
                    34.41,
                    35.16
                ],
                [
                    34.75,
                    34.95,
                    34.5,
                    35.26
                ],
                [
                    35.0,
                    34.55,
                    34.29,
                    35.2
                ],
                [
                    34.54,
                    34.32,
                    34.18,
                    34.58
                ],
                [
                    34.18,
                    34.13,
                    33.76,
                    34.5
                ],
                [
                    34.48,
                    34.61,
                    34.07,
                    34.69
                ],
                [
                    34.98,
                    35.05,
                    34.06,
                    35.61
                ],
                [
                    34.88,
                    34.79,
                    34.53,
                    35.22
                ],
                [
                    34.6,
                    34.16,
                    34.05,
                    34.6
                ],
                [
                    34.05,
                    34.22,
                    33.89,
                    34.69
                ],
                [
                    34.69,
                    34.0,
                    33.96,
                    35.36
                ],
                [
                    33.71,
                    35.13,
                    33.68,
                    36.18
                ],
                [
                    35.01,
                    34.63,
                    34.57,
                    35.22
                ],
                [
                    34.68,
                    35.81,
                    34.68,
                    36.37
                ],
                [
                    35.6,
                    35.08,
                    35.03,
                    36.17
                ],
                [
                    34.99,
                    34.59,
                    34.34,
                    35.14
                ],
                [
                    34.49,
                    34.21,
                    33.95,
                    34.5
                ],
                [
                    34.2,
                    33.77,
                    33.68,
                    34.35
                ],
                [
                    33.85,
                    34.64,
                    33.85,
                    34.76
                ],
                [
                    34.36,
                    33.96,
                    33.85,
                    34.36
                ],
                [
                    33.3,
                    32.96,
                    32.51,
                    33.49
                ],
                [
                    32.91,
                    33.0,
                    32.72,
                    33.2
                ],
                [
                    32.9,
                    33.33,
                    32.8,
                    33.6
                ],
                [
                    32.68,
                    32.41,
                    32.3,
                    32.86
                ],
                [
                    32.42,
                    32.73,
                    32.34,
                    33.13
                ],
                [
                    32.85,
                    32.67,
                    32.6,
                    33.63
                ],
                [
                    32.67,
                    32.65,
                    32.6,
                    33.18
                ],
                [
                    32.5,
                    32.18,
                    32.1,
                    32.53
                ],
                [
                    32.06,
                    31.23,
                    31.12,
                    32.15
                ],
                [
                    31.1,
                    31.25,
                    30.9,
                    31.45
                ],
                [
                    31.32,
                    31.38,
                    31.09,
                    31.74
                ],
                [
                    31.06,
                    31.42,
                    30.91,
                    31.5
                ],
                [
                    31.32,
                    31.33,
                    31.28,
                    32.0
                ],
                [
                    31.43,
                    31.43,
                    31.38,
                    32.08
                ],
                [
                    31.12,
                    31.81,
                    31.0,
                    31.9
                ],
                [
                    31.95,
                    32.56,
                    31.86,
                    32.6
                ],
                [
                    32.7,
                    33.69,
                    32.69,
                    33.78
                ],
                [
                    33.72,
                    33.13,
                    33.12,
                    33.75
                ],
                [
                    33.39,
                    33.24,
                    33.05,
                    33.63
                ],
                [
                    33.45,
                    33.53,
                    33.32,
                    33.66
                ],
                [
                    33.53,
                    33.46,
                    33.21,
                    33.75
                ],
                [
                    33.34,
                    33.05,
                    32.8,
                    33.43
                ],
                [
                    33.13,
                    33.6,
                    33.02,
                    33.75
                ],
                [
                    33.5,
                    33.03,
                    33.0,
                    33.55
                ],
                [
                    33.07,
                    33.17,
                    32.91,
                    33.5
                ],
                [
                    33.17,
                    34.11,
                    33.16,
                    34.38
                ],
                [
                    34.0,
                    34.55,
                    34.0,
                    34.74
                ],
                [
                    34.3,
                    34.39,
                    34.11,
                    34.66
                ],
                [
                    34.35,
                    34.4,
                    34.1,
                    34.45
                ],
                [
                    34.53,
                    34.14,
                    33.76,
                    34.6
                ],
                [
                    34.52,
                    34.71,
                    33.9,
                    35.23
                ],
                [
                    34.72,
                    34.55,
                    34.36,
                    35.25
                ],
                [
                    34.4,
                    35.05,
                    34.2,
                    35.2
                ],
                [
                    35.1,
                    36.99,
                    35.09,
                    37.28
                ],
                [
                    37.05,
                    37.12,
                    36.8,
                    37.49
                ],
                [
                    36.93,
                    36.58,
                    36.43,
                    37.05
                ],
                [
                    36.57,
                    36.23,
                    36.08,
                    36.9
                ],
                [
                    36.25,
                    36.48,
                    36.15,
                    36.57
                ],
                [
                    36.0,
                    37.11,
                    35.76,
                    37.15
                ],
                [
                    37.13,
                    37.57,
                    36.81,
                    37.93
                ],
                [
                    37.4,
                    38.02,
                    37.31,
                    38.07
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    0.21
                ],
                [
                    "2025-02-05",
                    4.71
                ],
                [
                    "2025-02-06",
                    5.94
                ],
                [
                    "2025-02-07",
                    6.29
                ],
                [
                    "2025-02-10",
                    -5.46
                ],
                [
                    "2025-02-11",
                    -8.74
                ],
                [
                    "2025-02-12",
                    5.21
                ],
                [
                    "2025-02-13",
                    -11.34
                ],
                [
                    "2025-02-14",
                    -5.58
                ],
                [
                    "2025-02-17",
                    -5.02
                ],
                [
                    "2025-02-18",
                    -12.82
                ],
                [
                    "2025-02-19",
                    4.08
                ],
                [
                    "2025-02-20",
                    7.25
                ],
                [
                    "2025-02-21",
                    -2.81
                ],
                [
                    "2025-02-24",
                    6.09
                ],
                [
                    "2025-02-25",
                    -5.67
                ],
                [
                    "2025-02-26",
                    -13.70
                ],
                [
                    "2025-02-27",
                    -11.82
                ],
                [
                    "2025-02-28",
                    -8.79
                ],
                [
                    "2025-03-03",
                    2.19
                ],
                [
                    "2025-03-04",
                    5.35
                ],
                [
                    "2025-03-05",
                    -7.26
                ],
                [
                    "2025-03-06",
                    7.76
                ],
                [
                    "2025-03-07",
                    1.45
                ],
                [
                    "2025-03-10",
                    -0.23
                ],
                [
                    "2025-03-11",
                    4.28
                ],
                [
                    "2025-03-12",
                    2.31
                ],
                [
                    "2025-03-13",
                    -6.48
                ],
                [
                    "2025-03-14",
                    0.68
                ],
                [
                    "2025-03-17",
                    -4.91
                ],
                [
                    "2025-03-18",
                    0.76
                ],
                [
                    "2025-03-19",
                    -3.42
                ],
                [
                    "2025-03-20",
                    -10.17
                ],
                [
                    "2025-03-21",
                    5.14
                ],
                [
                    "2025-03-24",
                    -1.37
                ],
                [
                    "2025-03-25",
                    1.65
                ],
                [
                    "2025-03-26",
                    -7.47
                ],
                [
                    "2025-03-27",
                    5.20
                ],
                [
                    "2025-03-28",
                    2.40
                ],
                [
                    "2025-03-31",
                    -8.69
                ],
                [
                    "2025-04-01",
                    -0.49
                ],
                [
                    "2025-04-02",
                    2.01
                ],
                [
                    "2025-04-03",
                    -10.55
                ],
                [
                    "2025-04-07",
                    -15.05
                ],
                [
                    "2025-04-08",
                    -13.39
                ],
                [
                    "2025-04-09",
                    2.98
                ],
                [
                    "2025-04-10",
                    1.94
                ],
                [
                    "2025-04-11",
                    5.96
                ],
                [
                    "2025-04-14",
                    1.45
                ],
                [
                    "2025-04-15",
                    8.55
                ],
                [
                    "2025-04-16",
                    9.48
                ],
                [
                    "2025-04-17",
                    -4.90
                ],
                [
                    "2025-04-18",
                    -13.38
                ],
                [
                    "2025-04-21",
                    5.51
                ],
                [
                    "2025-04-22",
                    -5.99
                ],
                [
                    "2025-04-23",
                    5.95
                ],
                [
                    "2025-04-24",
                    3.98
                ],
                [
                    "2025-04-25",
                    13.32
                ],
                [
                    "2025-04-28",
                    11.87
                ],
                [
                    "2025-04-29",
                    11.53
                ],
                [
                    "2025-04-30",
                    5.20
                ],
                [
                    "2025-05-06",
                    4.74
                ],
                [
                    "2025-05-07",
                    -5.62
                ],
                [
                    "2025-05-08",
                    -15.68
                ],
                [
                    "2025-05-09",
                    -6.21
                ],
                [
                    "2025-05-12",
                    -5.32
                ],
                [
                    "2025-05-13",
                    6.30
                ],
                [
                    "2025-05-14",
                    2.52
                ],
                [
                    "2025-05-15",
                    -7.78
                ],
                [
                    "2025-05-16",
                    -10.74
                ],
                [
                    "2025-05-19",
                    -4.34
                ],
                [
                    "2025-05-20",
                    11.85
                ],
                [
                    "2025-05-21",
                    -8.85
                ],
                [
                    "2025-05-22",
                    12.82
                ],
                [
                    "2025-05-23",
                    -6.06
                ],
                [
                    "2025-05-26",
                    -9.86
                ],
                [
                    "2025-05-27",
                    -10.40
                ],
                [
                    "2025-05-28",
                    -13.36
                ],
                [
                    "2025-05-29",
                    0.76
                ],
                [
                    "2025-05-30",
                    -7.29
                ],
                [
                    "2025-06-03",
                    -14.96
                ],
                [
                    "2025-06-04",
                    -15.27
                ],
                [
                    "2025-06-05",
                    0.19
                ],
                [
                    "2025-06-06",
                    -12.33
                ],
                [
                    "2025-06-09",
                    -0.91
                ],
                [
                    "2025-06-10",
                    1.90
                ],
                [
                    "2025-06-11",
                    -5.90
                ],
                [
                    "2025-06-12",
                    -7.72
                ],
                [
                    "2025-06-13",
                    -12.87
                ],
                [
                    "2025-06-16",
                    -7.55
                ],
                [
                    "2025-06-17",
                    0.60
                ],
                [
                    "2025-06-18",
                    -7.20
                ],
                [
                    "2025-06-19",
                    5.80
                ],
                [
                    "2025-06-20",
                    8.76
                ],
                [
                    "2025-06-23",
                    0.72
                ],
                [
                    "2025-06-24",
                    4.48
                ],
                [
                    "2025-06-25",
                    3.46
                ],
                [
                    "2025-06-26",
                    -5.86
                ],
                [
                    "2025-06-27",
                    -1.77
                ],
                [
                    "2025-06-30",
                    -6.09
                ],
                [
                    "2025-07-01",
                    -0.21
                ],
                [
                    "2025-07-02",
                    -16.29
                ],
                [
                    "2025-07-03",
                    5.09
                ],
                [
                    "2025-07-04",
                    -3.91
                ],
                [
                    "2025-07-07",
                    5.76
                ],
                [
                    "2025-07-08",
                    2.43
                ],
                [
                    "2025-07-09",
                    2.91
                ],
                [
                    "2025-07-10",
                    -4.13
                ],
                [
                    "2025-07-11",
                    6.21
                ],
                [
                    "2025-07-14",
                    -6.79
                ],
                [
                    "2025-07-15",
                    0.54
                ],
                [
                    "2025-07-16",
                    -3.98
                ],
                [
                    "2025-07-17",
                    4.43
                ],
                [
                    "2025-07-18",
                    12.54
                ],
                [
                    "2025-07-21",
                    0.18
                ],
                [
                    "2025-07-22",
                    -12.65
                ],
                [
                    "2025-07-23",
                    -15.52
                ],
                [
                    "2025-07-24",
                    -1.29
                ],
                [
                    "2025-07-25",
                    6.91
                ],
                [
                    "2025-07-28",
                    3.31
                ],
                [
                    "2025-07-29",
                    3.95
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -0.62
                ],
                [
                    "2025-02-05",
                    -3.35
                ],
                [
                    "2025-02-06",
                    -1.25
                ],
                [
                    "2025-02-07",
                    -2.06
                ],
                [
                    "2025-02-10",
                    1.35
                ],
                [
                    "2025-02-11",
                    1.16
                ],
                [
                    "2025-02-12",
                    -1.68
                ],
                [
                    "2025-02-13",
                    3.65
                ],
                [
                    "2025-02-14",
                    -1.23
                ],
                [
                    "2025-02-17",
                    1.57
                ],
                [
                    "2025-02-18",
                    1.28
                ],
                [
                    "2025-02-19",
                    -4.60
                ],
                [
                    "2025-02-20",
                    -0.57
                ],
                [
                    "2025-02-21",
                    2.82
                ],
                [
                    "2025-02-24",
                    1.87
                ],
                [
                    "2025-02-25",
                    1.80
                ],
                [
                    "2025-02-26",
                    3.43
                ],
                [
                    "2025-02-27",
                    -1.33
                ],
                [
                    "2025-02-28",
                    -1.02
                ],
                [
                    "2025-03-03",
                    -1.45
                ],
                [
                    "2025-03-04",
                    -0.10
                ],
                [
                    "2025-03-05",
                    1.88
                ],
                [
                    "2025-03-06",
                    -5.42
                ],
                [
                    "2025-03-07",
                    3.18
                ],
                [
                    "2025-03-10",
                    -1.21
                ],
                [
                    "2025-03-11",
                    0.64
                ],
                [
                    "2025-03-12",
                    1.15
                ],
                [
                    "2025-03-13",
                    -0.07
                ],
                [
                    "2025-03-14",
                    -0.47
                ],
                [
                    "2025-03-17",
                    3.43
                ],
                [
                    "2025-03-18",
                    -2.34
                ],
                [
                    "2025-03-19",
                    0.18
                ],
                [
                    "2025-03-20",
                    0.43
                ],
                [
                    "2025-03-21",
                    0.30
                ],
                [
                    "2025-03-24",
                    -2.18
                ],
                [
                    "2025-03-25",
                    -2.78
                ],
                [
                    "2025-03-26",
                    -4.19
                ],
                [
                    "2025-03-27",
                    -2.00
                ],
                [
                    "2025-03-28",
                    -5.50
                ],
                [
                    "2025-03-31",
                    -3.92
                ],
                [
                    "2025-04-01",
                    -6.45
                ],
                [
                    "2025-04-02",
                    -6.15
                ],
                [
                    "2025-04-03",
                    -0.17
                ],
                [
                    "2025-04-07",
                    -3.41
                ],
                [
                    "2025-04-08",
                    -3.48
                ],
                [
                    "2025-04-09",
                    -5.16
                ],
                [
                    "2025-04-10",
                    0.76
                ],
                [
                    "2025-04-11",
                    -2.14
                ],
                [
                    "2025-04-14",
                    -1.62
                ],
                [
                    "2025-04-15",
                    -8.39
                ],
                [
                    "2025-04-16",
                    -4.28
                ],
                [
                    "2025-04-17",
                    -2.81
                ],
                [
                    "2025-04-18",
                    -4.84
                ],
                [
                    "2025-04-21",
                    1.17
                ],
                [
                    "2025-04-22",
                    0.97
                ],
                [
                    "2025-04-23",
                    -0.81
                ],
                [
                    "2025-04-24",
                    -1.49
                ],
                [
                    "2025-04-25",
                    -6.52
                ],
                [
                    "2025-04-28",
                    -2.63
                ],
                [
                    "2025-04-29",
                    -2.26
                ],
                [
                    "2025-04-30",
                    -2.57
                ],
                [
                    "2025-05-06",
                    -0.26
                ],
                [
                    "2025-05-07",
                    -2.69
                ],
                [
                    "2025-05-08",
                    -1.94
                ],
                [
                    "2025-05-09",
                    3.12
                ],
                [
                    "2025-05-12",
                    5.66
                ],
                [
                    "2025-05-13",
                    1.38
                ],
                [
                    "2025-05-14",
                    -2.28
                ],
                [
                    "2025-05-15",
                    0.39
                ],
                [
                    "2025-05-16",
                    3.41
                ],
                [
                    "2025-05-19",
                    4.83
                ],
                [
                    "2025-05-20",
                    -2.59
                ],
                [
                    "2025-05-21",
                    2.09
                ],
                [
                    "2025-05-22",
                    -3.07
                ],
                [
                    "2025-05-23",
                    1.71
                ],
                [
                    "2025-05-26",
                    0.31
                ],
                [
                    "2025-05-27",
                    0.78
                ],
                [
                    "2025-05-28",
                    2.34
                ],
                [
                    "2025-05-29",
                    -7.30
                ],
                [
                    "2025-05-30",
                    0.17
                ],
                [
                    "2025-06-03",
                    3.86
                ],
                [
                    "2025-06-04",
                    1.59
                ],
                [
                    "2025-06-05",
                    0.30
                ],
                [
                    "2025-06-06",
                    -3.45
                ],
                [
                    "2025-06-09",
                    -5.12
                ],
                [
                    "2025-06-10",
                    1.98
                ],
                [
                    "2025-06-11",
                    -5.32
                ],
                [
                    "2025-06-12",
                    -3.07
                ],
                [
                    "2025-06-13",
                    1.95
                ],
                [
                    "2025-06-16",
                    -2.79
                ],
                [
                    "2025-06-17",
                    -4.46
                ],
                [
                    "2025-06-18",
                    2.01
                ],
                [
                    "2025-06-19",
                    -2.44
                ],
                [
                    "2025-06-20",
                    2.65
                ],
                [
                    "2025-06-23",
                    0.95
                ],
                [
                    "2025-06-24",
                    1.53
                ],
                [
                    "2025-06-25",
                    -2.97
                ],
                [
                    "2025-06-26",
                    -1.13
                ],
                [
                    "2025-06-27",
                    2.67
                ],
                [
                    "2025-06-30",
                    1.04
                ],
                [
                    "2025-07-01",
                    -4.92
                ],
                [
                    "2025-07-02",
                    -2.16
                ],
                [
                    "2025-07-03",
                    -6.81
                ],
                [
                    "2025-07-04",
                    5.54
                ],
                [
                    "2025-07-07",
                    -1.28
                ],
                [
                    "2025-07-08",
                    0.58
                ],
                [
                    "2025-07-09",
                    2.44
                ],
                [
                    "2025-07-10",
                    -1.99
                ],
                [
                    "2025-07-11",
                    1.74
                ],
                [
                    "2025-07-14",
                    3.93
                ],
                [
                    "2025-07-15",
                    4.29
                ],
                [
                    "2025-07-16",
                    -0.21
                ],
                [
                    "2025-07-17",
                    -3.01
                ],
                [
                    "2025-07-18",
                    -4.98
                ],
                [
                    "2025-07-21",
                    -3.35
                ],
                [
                    "2025-07-22",
                    -0.89
                ],
                [
                    "2025-07-23",
                    2.21
                ],
                [
                    "2025-07-24",
                    -0.15
                ],
                [
                    "2025-07-25",
                    -2.08
                ],
                [
                    "2025-07-28",
                    -0.55
                ],
                [
                    "2025-07-29",
                    0.14
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    0.41
                ],
                [
                    "2025-02-05",
                    -1.37
                ],
                [
                    "2025-02-06",
                    -4.69
                ],
                [
                    "2025-02-07",
                    -4.23
                ],
                [
                    "2025-02-10",
                    4.12
                ],
                [
                    "2025-02-11",
                    7.58
                ],
                [
                    "2025-02-12",
                    -3.53
                ],
                [
                    "2025-02-13",
                    7.70
                ],
                [
                    "2025-02-14",
                    6.81
                ],
                [
                    "2025-02-17",
                    3.46
                ],
                [
                    "2025-02-18",
                    11.55
                ],
                [
                    "2025-02-19",
                    0.52
                ],
                [
                    "2025-02-20",
                    -6.68
                ],
                [
                    "2025-02-21",
                    -0.01
                ],
                [
                    "2025-02-24",
                    -7.96
                ],
                [
                    "2025-02-25",
                    3.87
                ],
                [
                    "2025-02-26",
                    10.27
                ],
                [
                    "2025-02-27",
                    13.15
                ],
                [
                    "2025-02-28",
                    9.81
                ],
                [
                    "2025-03-03",
                    -0.75
                ],
                [
                    "2025-03-04",
                    -5.25
                ],
                [
                    "2025-03-05",
                    5.39
                ],
                [
                    "2025-03-06",
                    -2.35
                ],
                [
                    "2025-03-07",
                    -4.64
                ],
                [
                    "2025-03-10",
                    1.44
                ],
                [
                    "2025-03-11",
                    -4.92
                ],
                [
                    "2025-03-12",
                    -3.46
                ],
                [
                    "2025-03-13",
                    6.54
                ],
                [
                    "2025-03-14",
                    -0.21
                ],
                [
                    "2025-03-17",
                    1.48
                ],
                [
                    "2025-03-18",
                    1.58
                ],
                [
                    "2025-03-19",
                    3.24
                ],
                [
                    "2025-03-20",
                    9.74
                ],
                [
                    "2025-03-21",
                    -5.44
                ],
                [
                    "2025-03-24",
                    3.55
                ],
                [
                    "2025-03-25",
                    1.12
                ],
                [
                    "2025-03-26",
                    11.67
                ],
                [
                    "2025-03-27",
                    -3.19
                ],
                [
                    "2025-03-28",
                    3.10
                ],
                [
                    "2025-03-31",
                    12.60
                ],
                [
                    "2025-04-01",
                    6.94
                ],
                [
                    "2025-04-02",
                    4.14
                ],
                [
                    "2025-04-03",
                    10.73
                ],
                [
                    "2025-04-07",
                    18.45
                ],
                [
                    "2025-04-08",
                    16.87
                ],
                [
                    "2025-04-09",
                    2.18
                ],
                [
                    "2025-04-10",
                    -2.69
                ],
                [
                    "2025-04-11",
                    -3.82
                ],
                [
                    "2025-04-14",
                    0.16
                ],
                [
                    "2025-04-15",
                    -0.17
                ],
                [
                    "2025-04-16",
                    -5.19
                ],
                [
                    "2025-04-17",
                    7.72
                ],
                [
                    "2025-04-18",
                    18.23
                ],
                [
                    "2025-04-21",
                    -6.68
                ],
                [
                    "2025-04-22",
                    5.02
                ],
                [
                    "2025-04-23",
                    -5.14
                ],
                [
                    "2025-04-24",
                    -2.49
                ],
                [
                    "2025-04-25",
                    -6.80
                ],
                [
                    "2025-04-28",
                    -9.24
                ],
                [
                    "2025-04-29",
                    -9.27
                ],
                [
                    "2025-04-30",
                    -2.63
                ],
                [
                    "2025-05-06",
                    -4.49
                ],
                [
                    "2025-05-07",
                    8.31
                ],
                [
                    "2025-05-08",
                    17.62
                ],
                [
                    "2025-05-09",
                    3.09
                ],
                [
                    "2025-05-12",
                    -0.34
                ],
                [
                    "2025-05-13",
                    -7.69
                ],
                [
                    "2025-05-14",
                    -0.24
                ],
                [
                    "2025-05-15",
                    7.39
                ],
                [
                    "2025-05-16",
                    7.33
                ],
                [
                    "2025-05-19",
                    -0.48
                ],
                [
                    "2025-05-20",
                    -9.26
                ],
                [
                    "2025-05-21",
                    6.76
                ],
                [
                    "2025-05-22",
                    -9.75
                ],
                [
                    "2025-05-23",
                    4.36
                ],
                [
                    "2025-05-26",
                    9.55
                ],
                [
                    "2025-05-27",
                    9.62
                ],
                [
                    "2025-05-28",
                    11.02
                ],
                [
                    "2025-05-29",
                    6.54
                ],
                [
                    "2025-05-30",
                    7.12
                ],
                [
                    "2025-06-03",
                    11.11
                ],
                [
                    "2025-06-04",
                    13.69
                ],
                [
                    "2025-06-05",
                    -0.49
                ],
                [
                    "2025-06-06",
                    15.78
                ],
                [
                    "2025-06-09",
                    6.03
                ],
                [
                    "2025-06-10",
                    -3.89
                ],
                [
                    "2025-06-11",
                    11.23
                ],
                [
                    "2025-06-12",
                    10.79
                ],
                [
                    "2025-06-13",
                    10.93
                ],
                [
                    "2025-06-16",
                    10.35
                ],
                [
                    "2025-06-17",
                    3.86
                ],
                [
                    "2025-06-18",
                    5.19
                ],
                [
                    "2025-06-19",
                    -3.36
                ],
                [
                    "2025-06-20",
                    -11.41
                ],
                [
                    "2025-06-23",
                    -1.66
                ],
                [
                    "2025-06-24",
                    -6.01
                ],
                [
                    "2025-06-25",
                    -0.50
                ],
                [
                    "2025-06-26",
                    6.98
                ],
                [
                    "2025-06-27",
                    -0.90
                ],
                [
                    "2025-06-30",
                    5.05
                ],
                [
                    "2025-07-01",
                    5.13
                ],
                [
                    "2025-07-02",
                    18.45
                ],
                [
                    "2025-07-03",
                    1.72
                ],
                [
                    "2025-07-04",
                    -1.62
                ],
                [
                    "2025-07-07",
                    -4.47
                ],
                [
                    "2025-07-08",
                    -3.01
                ],
                [
                    "2025-07-09",
                    -5.35
                ],
                [
                    "2025-07-10",
                    6.12
                ],
                [
                    "2025-07-11",
                    -7.95
                ],
                [
                    "2025-07-14",
                    2.85
                ],
                [
                    "2025-07-15",
                    -4.83
                ],
                [
                    "2025-07-16",
                    4.18
                ],
                [
                    "2025-07-17",
                    -1.42
                ],
                [
                    "2025-07-18",
                    -7.57
                ],
                [
                    "2025-07-21",
                    3.16
                ],
                [
                    "2025-07-22",
                    13.54
                ],
                [
                    "2025-07-23",
                    13.31
                ],
                [
                    "2025-07-24",
                    1.45
                ],
                [
                    "2025-07-25",
                    -4.83
                ],
                [
                    "2025-07-28",
                    -2.77
                ],
                [
                    "2025-07-29",
                    -4.09
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    -5.46
                ],
                [
                    "2025-02-12",
                    5.21
                ],
                [
                    "2025-02-24",
                    6.09
                ],
                [
                    "2025-02-25",
                    -5.67
                ],
                [
                    "2025-03-07",
                    1.45
                ],
                [
                    "2025-03-10",
                    -0.23
                ],
                [
                    "2025-03-11",
                    4.28
                ],
                [
                    "2025-03-12",
                    2.31
                ],
                [
                    "2025-03-13",
                    -6.48
                ],
                [
                    "2025-03-14",
                    0.68
                ],
                [
                    "2025-04-15",
                    8.55
                ],
                [
                    "2025-04-16",
                    9.48
                ],
                [
                    "2025-04-17",
                    -4.90
                ],
                [
                    "2025-04-25",
                    13.32
                ],
                [
                    "2025-04-28",
                    11.87
                ],
                [
                    "2025-04-29",
                    11.53
                ],
                [
                    "2025-04-30",
                    5.20
                ],
                [
                    "2025-05-06",
                    4.74
                ],
                [
                    "2025-05-07",
                    -5.62
                ],
                [
                    "2025-05-22",
                    12.82
                ],
                [
                    "2025-05-23",
                    -6.06
                ],
                [
                    "2025-06-23",
                    0.72
                ],
                [
                    "2025-06-24",
                    4.48
                ],
                [
                    "2025-06-25",
                    3.46
                ],
                [
                    "2025-06-26",
                    -5.86
                ],
                [
                    "2025-06-27",
                    -1.77
                ],
                [
                    "2025-07-09",
                    2.91
                ],
                [
                    "2025-07-10",
                    -4.13
                ],
                [
                    "2025-07-11",
                    6.21
                ],
                [
                    "2025-07-14",
                    -6.79
                ],
                [
                    "2025-07-17",
                    4.43
                ],
                [
                    "2025-07-18",
                    12.54
                ],
                [
                    "2025-07-21",
                    0.18
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-24",
                    1.87
                ],
                [
                    "2025-02-25",
                    1.80
                ],
                [
                    "2025-02-26",
                    3.43
                ],
                [
                    "2025-03-13",
                    -0.07
                ],
                [
                    "2025-03-14",
                    -0.47
                ],
                [
                    "2025-03-17",
                    3.43
                ],
                [
                    "2025-05-22",
                    -3.07
                ],
                [
                    "2025-05-23",
                    1.71
                ],
                [
                    "2025-06-24",
                    1.53
                ],
                [
                    "2025-06-26",
                    -1.13
                ],
                [
                    "2025-06-27",
                    2.67
                ],
                [
                    "2025-07-09",
                    2.44
                ],
                [
                    "2025-07-10",
                    -1.99
                ],
                [
                    "2025-07-11",
                    1.74
                ],
                [
                    "2025-07-14",
                    3.93
                ],
                [
                    "2025-07-15",
                    4.29
                ],
                [
                    "2025-07-17",
                    -3.01
                ],
                [
                    "2025-07-18",
                    -4.98
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-13",
                    7.70
                ],
                [
                    "2025-02-14",
                    6.81
                ],
                [
                    "2025-02-17",
                    3.46
                ],
                [
                    "2025-02-18",
                    11.55
                ],
                [
                    "2025-02-19",
                    0.52
                ],
                [
                    "2025-02-20",
                    -6.68
                ],
                [
                    "2025-02-21",
                    -0.01
                ],
                [
                    "2025-02-27",
                    13.15
                ],
                [
                    "2025-02-28",
                    9.81
                ],
                [
                    "2025-03-03",
                    -0.75
                ],
                [
                    "2025-03-04",
                    -5.25
                ],
                [
                    "2025-03-05",
                    5.39
                ],
                [
                    "2025-03-06",
                    -2.35
                ],
                [
                    "2025-03-18",
                    1.58
                ],
                [
                    "2025-03-19",
                    3.24
                ],
                [
                    "2025-03-20",
                    9.74
                ],
                [
                    "2025-03-21",
                    -5.44
                ],
                [
                    "2025-03-24",
                    3.55
                ],
                [
                    "2025-03-25",
                    1.12
                ],
                [
                    "2025-03-26",
                    11.67
                ],
                [
                    "2025-03-31",
                    12.60
                ],
                [
                    "2025-04-01",
                    6.94
                ],
                [
                    "2025-04-03",
                    10.73
                ],
                [
                    "2025-04-07",
                    18.45
                ],
                [
                    "2025-04-08",
                    16.87
                ],
                [
                    "2025-04-09",
                    2.18
                ],
                [
                    "2025-04-10",
                    -2.69
                ],
                [
                    "2025-04-11",
                    -3.82
                ],
                [
                    "2025-04-14",
                    0.16
                ],
                [
                    "2025-04-22",
                    5.02
                ],
                [
                    "2025-04-23",
                    -5.14
                ],
                [
                    "2025-04-24",
                    -2.49
                ],
                [
                    "2025-05-09",
                    3.09
                ],
                [
                    "2025-05-12",
                    -0.34
                ],
                [
                    "2025-05-13",
                    -7.69
                ],
                [
                    "2025-05-14",
                    -0.24
                ],
                [
                    "2025-05-15",
                    7.39
                ],
                [
                    "2025-05-16",
                    7.33
                ],
                [
                    "2025-05-19",
                    -0.48
                ],
                [
                    "2025-05-20",
                    -9.26
                ],
                [
                    "2025-05-21",
                    6.76
                ],
                [
                    "2025-05-26",
                    9.55
                ],
                [
                    "2025-05-27",
                    9.62
                ],
                [
                    "2025-05-28",
                    11.02
                ],
                [
                    "2025-05-29",
                    6.54
                ],
                [
                    "2025-05-30",
                    7.12
                ],
                [
                    "2025-06-03",
                    11.11
                ],
                [
                    "2025-06-04",
                    13.69
                ],
                [
                    "2025-06-05",
                    -0.49
                ],
                [
                    "2025-06-06",
                    15.78
                ],
                [
                    "2025-06-09",
                    6.03
                ],
                [
                    "2025-06-10",
                    -3.89
                ],
                [
                    "2025-06-11",
                    11.23
                ],
                [
                    "2025-06-12",
                    10.79
                ],
                [
                    "2025-06-13",
                    10.93
                ],
                [
                    "2025-06-16",
                    10.35
                ],
                [
                    "2025-06-17",
                    3.86
                ],
                [
                    "2025-06-18",
                    5.19
                ],
                [
                    "2025-06-19",
                    -3.36
                ],
                [
                    "2025-06-30",
                    5.05
                ],
                [
                    "2025-07-01",
                    5.13
                ],
                [
                    "2025-07-02",
                    18.45
                ],
                [
                    "2025-07-03",
                    1.72
                ],
                [
                    "2025-07-04",
                    -1.62
                ],
                [
                    "2025-07-07",
                    -4.47
                ],
                [
                    "2025-07-08",
                    -3.01
                ],
                [
                    "2025-07-16",
                    4.18
                ],
                [
                    "2025-07-23",
                    13.31
                ],
                [
                    "2025-07-24",
                    1.45
                ],
                [
                    "2025-07-25",
                    -4.83
                ],
                [
                    "2025-07-28",
                    -2.77
                ],
                [
                    "2025-07-29",
                    -4.09
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600745 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_c95ad042229f46808cbbbff5ba3f8a53.setOption(option_c95ad042229f46808cbbbff5ba3f8a53);
            window.addEventListener('resize', function(){
                chart_c95ad042229f46808cbbbff5ba3f8a53.resize();
            })
    </script>
</body>
</html>
