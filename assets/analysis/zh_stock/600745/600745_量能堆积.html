<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="ac8feb5c572142b2a2b6b6092cba886c" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_ac8feb5c572142b2a2b6b6092cba886c = echarts.init(
            document.getElementById('ac8feb5c572142b2a2b6b6092cba886c'), 'white', {renderer: 'canvas'});
        var option_ac8feb5c572142b2a2b6b6092cba886c = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    34.88,
                    33.66,
                    33.65,
                    35.07
                ],
                [
                    34.05,
                    34.32,
                    33.70,
                    34.96
                ],
                [
                    34.12,
                    35.13,
                    33.89,
                    35.20
                ],
                [
                    35.08,
                    36.04,
                    34.91,
                    36.75
                ],
                [
                    36.05,
                    36.00,
                    35.62,
                    36.18
                ],
                [
                    35.99,
                    35.62,
                    35.30,
                    36.05
                ],
                [
                    35.65,
                    36.67,
                    35.10,
                    36.68
                ],
                [
                    36.49,
                    35.64,
                    35.60,
                    36.50
                ],
                [
                    35.52,
                    35.72,
                    35.35,
                    35.87
                ],
                [
                    35.72,
                    35.60,
                    35.33,
                    36.08
                ],
                [
                    35.55,
                    34.32,
                    34.20,
                    35.57
                ],
                [
                    34.38,
                    35.53,
                    34.35,
                    35.56
                ],
                [
                    35.50,
                    36.90,
                    35.26,
                    37.77
                ],
                [
                    36.50,
                    37.33,
                    36.49,
                    37.68
                ],
                [
                    37.28,
                    37.63,
                    36.70,
                    37.98
                ],
                [
                    37.16,
                    37.32,
                    36.83,
                    37.95
                ],
                [
                    37.28,
                    37.11,
                    36.85,
                    37.46
                ],
                [
                    37.15,
                    36.51,
                    35.88,
                    37.38
                ],
                [
                    36.29,
                    34.65,
                    34.55,
                    36.42
                ],
                [
                    34.73,
                    35.10,
                    34.26,
                    36.10
                ],
                [
                    34.90,
                    35.80,
                    34.71,
                    36.29
                ],
                [
                    35.80,
                    35.49,
                    35.13,
                    36.15
                ],
                [
                    35.75,
                    36.28,
                    35.66,
                    36.40
                ],
                [
                    36.28,
                    36.20,
                    35.96,
                    36.69
                ],
                [
                    36.32,
                    36.01,
                    35.59,
                    36.45
                ],
                [
                    35.51,
                    36.36,
                    35.51,
                    36.91
                ],
                [
                    36.30,
                    36.17,
                    36.10,
                    36.78
                ],
                [
                    36.10,
                    35.28,
                    35.00,
                    36.10
                ],
                [
                    35.35,
                    35.78,
                    35.11,
                    35.98
                ],
                [
                    35.81,
                    35.57,
                    35.51,
                    35.94
                ],
                [
                    35.70,
                    35.80,
                    35.60,
                    36.43
                ],
                [
                    35.66,
                    35.19,
                    35.12,
                    35.73
                ],
                [
                    35.06,
                    34.69,
                    34.66,
                    35.24
                ],
                [
                    35.45,
                    34.89,
                    34.51,
                    36.66
                ],
                [
                    34.43,
                    33.95,
                    33.15,
                    34.74
                ],
                [
                    33.90,
                    33.98,
                    33.80,
                    34.55
                ],
                [
                    33.80,
                    33.58,
                    33.57,
                    34.11
                ],
                [
                    33.60,
                    33.59,
                    33.26,
                    34.09
                ],
                [
                    33.25,
                    33.37,
                    33.25,
                    33.82
                ],
                [
                    33.01,
                    32.58,
                    32.25,
                    33.25
                ],
                [
                    32.50,
                    32.59,
                    32.44,
                    32.92
                ],
                [
                    32.60,
                    32.69,
                    32.42,
                    32.83
                ],
                [
                    32.33,
                    32.00,
                    31.88,
                    32.78
                ],
                [
                    30.42,
                    28.80,
                    28.80,
                    30.92
                ],
                [
                    28.82,
                    28.61,
                    27.92,
                    29.45
                ],
                [
                    28.08,
                    29.35,
                    27.52,
                    29.58
                ],
                [
                    30.20,
                    30.24,
                    29.93,
                    30.77
                ],
                [
                    30.10,
                    31.37,
                    29.95,
                    31.80
                ],
                [
                    32.47,
                    31.80,
                    31.55,
                    32.48
                ],
                [
                    31.61,
                    31.93,
                    31.31,
                    32.00
                ],
                [
                    31.78,
                    32.10,
                    31.52,
                    33.15
                ],
                [
                    31.94,
                    31.60,
                    31.57,
                    32.40
                ],
                [
                    31.48,
                    31.31,
                    31.16,
                    31.75
                ],
                [
                    31.45,
                    31.90,
                    31.20,
                    32.60
                ],
                [
                    31.80,
                    31.90,
                    31.70,
                    32.05
                ],
                [
                    32.10,
                    32.13,
                    31.70,
                    32.44
                ],
                [
                    32.15,
                    31.83,
                    31.80,
                    32.76
                ],
                [
                    31.80,
                    32.43,
                    31.77,
                    33.28
                ],
                [
                    33.28,
                    33.64,
                    32.97,
                    33.99
                ],
                [
                    33.51,
                    34.51,
                    33.28,
                    35.40
                ],
                [
                    34.67,
                    34.67,
                    34.41,
                    35.16
                ],
                [
                    34.75,
                    34.95,
                    34.50,
                    35.26
                ],
                [
                    35.00,
                    34.55,
                    34.29,
                    35.20
                ],
                [
                    34.54,
                    34.32,
                    34.18,
                    34.58
                ],
                [
                    34.18,
                    34.13,
                    33.76,
                    34.50
                ],
                [
                    34.48,
                    34.61,
                    34.07,
                    34.69
                ],
                [
                    34.98,
                    35.05,
                    34.06,
                    35.61
                ],
                [
                    34.88,
                    34.79,
                    34.53,
                    35.22
                ],
                [
                    34.60,
                    34.16,
                    34.05,
                    34.60
                ],
                [
                    34.05,
                    34.22,
                    33.89,
                    34.69
                ],
                [
                    34.69,
                    34.00,
                    33.96,
                    35.36
                ],
                [
                    33.71,
                    35.13,
                    33.68,
                    36.18
                ],
                [
                    35.01,
                    34.63,
                    34.57,
                    35.22
                ],
                [
                    34.68,
                    35.81,
                    34.68,
                    36.37
                ],
                [
                    35.60,
                    35.08,
                    35.03,
                    36.17
                ],
                [
                    34.99,
                    34.59,
                    34.34,
                    35.14
                ],
                [
                    34.49,
                    34.21,
                    33.95,
                    34.50
                ],
                [
                    34.20,
                    33.77,
                    33.68,
                    34.35
                ],
                [
                    33.85,
                    34.64,
                    33.85,
                    34.76
                ],
                [
                    34.36,
                    33.96,
                    33.85,
                    34.36
                ],
                [
                    33.30,
                    32.96,
                    32.51,
                    33.49
                ],
                [
                    32.91,
                    33.00,
                    32.72,
                    33.20
                ],
                [
                    32.90,
                    33.33,
                    32.80,
                    33.60
                ],
                [
                    32.68,
                    32.41,
                    32.30,
                    32.86
                ],
                [
                    32.42,
                    32.73,
                    32.34,
                    33.13
                ],
                [
                    32.85,
                    32.67,
                    32.60,
                    33.63
                ],
                [
                    32.67,
                    32.65,
                    32.60,
                    33.18
                ],
                [
                    32.50,
                    32.18,
                    32.10,
                    32.53
                ],
                [
                    32.06,
                    31.23,
                    31.12,
                    32.15
                ],
                [
                    31.10,
                    31.25,
                    30.90,
                    31.45
                ],
                [
                    31.32,
                    31.38,
                    31.09,
                    31.74
                ],
                [
                    31.06,
                    31.42,
                    30.91,
                    31.50
                ],
                [
                    31.32,
                    31.33,
                    31.28,
                    32.00
                ],
                [
                    31.43,
                    31.43,
                    31.38,
                    32.08
                ],
                [
                    31.12,
                    31.81,
                    31.00,
                    31.90
                ],
                [
                    31.95,
                    32.56,
                    31.86,
                    32.60
                ],
                [
                    32.70,
                    33.69,
                    32.69,
                    33.78
                ],
                [
                    33.72,
                    33.13,
                    33.12,
                    33.75
                ],
                [
                    33.39,
                    33.24,
                    33.05,
                    33.63
                ],
                [
                    33.45,
                    33.53,
                    33.32,
                    33.66
                ],
                [
                    33.53,
                    33.46,
                    33.21,
                    33.75
                ],
                [
                    33.34,
                    33.05,
                    32.80,
                    33.43
                ],
                [
                    33.13,
                    33.60,
                    33.02,
                    33.75
                ],
                [
                    33.50,
                    33.03,
                    33.00,
                    33.55
                ],
                [
                    33.07,
                    33.17,
                    32.91,
                    33.50
                ],
                [
                    33.17,
                    34.11,
                    33.16,
                    34.38
                ],
                [
                    34.00,
                    34.55,
                    34.00,
                    34.74
                ],
                [
                    34.30,
                    34.39,
                    34.11,
                    34.66
                ],
                [
                    34.35,
                    34.40,
                    34.10,
                    34.45
                ],
                [
                    34.53,
                    34.14,
                    33.76,
                    34.60
                ],
                [
                    34.52,
                    34.71,
                    33.90,
                    35.23
                ],
                [
                    34.72,
                    34.55,
                    34.36,
                    35.25
                ],
                [
                    34.40,
                    35.05,
                    34.20,
                    35.20
                ],
                [
                    35.10,
                    36.99,
                    35.09,
                    37.28
                ],
                [
                    37.05,
                    37.12,
                    36.80,
                    37.49
                ],
                [
                    36.93,
                    36.58,
                    36.43,
                    37.05
                ],
                [
                    36.57,
                    36.23,
                    36.08,
                    36.90
                ],
                [
                    36.25,
                    36.48,
                    36.15,
                    36.57
                ],
                [
                    36.00,
                    37.11,
                    35.76,
                    37.15
                ],
                [
                    37.13,
                    37.57,
                    36.81,
                    37.93
                ],
                [
                    37.40,
                    38.02,
                    37.31,
                    38.07
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "symbol": "diamond",
            "symbolSize": 16,
            "data": [
                [
                    "2025-04-07",
                    30.92
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600745 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_ac8feb5c572142b2a2b6b6092cba886c.setOption(option_ac8feb5c572142b2a2b6b6092cba886c);
            window.addEventListener('resize', function(){
                chart_ac8feb5c572142b2a2b6b6092cba886c.resize();
            })
    </script>
</body>
</html>
