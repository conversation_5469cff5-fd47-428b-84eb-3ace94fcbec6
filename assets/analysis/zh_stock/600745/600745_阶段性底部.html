<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="d436cfe199f84c8da419790fde22154a" class="chart-container" style="width:100%; height:500px; "></div>
    <script>
        var chart_d436cfe199f84c8da419790fde22154a = echarts.init(
            document.getElementById('d436cfe199f84c8da419790fde22154a'), 'white', {renderer: 'canvas'});
        var option_d436cfe199f84c8da419790fde22154a = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    34.88,
                    33.66,
                    33.65,
                    35.07
                ],
                [
                    34.05,
                    34.32,
                    33.7,
                    34.96
                ],
                [
                    34.12,
                    35.13,
                    33.89,
                    35.2
                ],
                [
                    35.08,
                    36.04,
                    34.91,
                    36.75
                ],
                [
                    36.05,
                    36.0,
                    35.62,
                    36.18
                ],
                [
                    35.99,
                    35.62,
                    35.3,
                    36.05
                ],
                [
                    35.65,
                    36.67,
                    35.1,
                    36.68
                ],
                [
                    36.49,
                    35.64,
                    35.6,
                    36.5
                ],
                [
                    35.52,
                    35.72,
                    35.35,
                    35.87
                ],
                [
                    35.72,
                    35.6,
                    35.33,
                    36.08
                ],
                [
                    35.55,
                    34.32,
                    34.2,
                    35.57
                ],
                [
                    34.38,
                    35.53,
                    34.35,
                    35.56
                ],
                [
                    35.5,
                    36.9,
                    35.26,
                    37.77
                ],
                [
                    36.5,
                    37.33,
                    36.49,
                    37.68
                ],
                [
                    37.28,
                    37.63,
                    36.7,
                    37.98
                ],
                [
                    37.16,
                    37.32,
                    36.83,
                    37.95
                ],
                [
                    37.28,
                    37.11,
                    36.85,
                    37.46
                ],
                [
                    37.15,
                    36.51,
                    35.88,
                    37.38
                ],
                [
                    36.29,
                    34.65,
                    34.55,
                    36.42
                ],
                [
                    34.73,
                    35.1,
                    34.26,
                    36.1
                ],
                [
                    34.9,
                    35.8,
                    34.71,
                    36.29
                ],
                [
                    35.8,
                    35.49,
                    35.13,
                    36.15
                ],
                [
                    35.75,
                    36.28,
                    35.66,
                    36.4
                ],
                [
                    36.28,
                    36.2,
                    35.96,
                    36.69
                ],
                [
                    36.32,
                    36.01,
                    35.59,
                    36.45
                ],
                [
                    35.51,
                    36.36,
                    35.51,
                    36.91
                ],
                [
                    36.3,
                    36.17,
                    36.1,
                    36.78
                ],
                [
                    36.1,
                    35.28,
                    35.0,
                    36.1
                ],
                [
                    35.35,
                    35.78,
                    35.11,
                    35.98
                ],
                [
                    35.81,
                    35.57,
                    35.51,
                    35.94
                ],
                [
                    35.7,
                    35.8,
                    35.6,
                    36.43
                ],
                [
                    35.66,
                    35.19,
                    35.12,
                    35.73
                ],
                [
                    35.06,
                    34.69,
                    34.66,
                    35.24
                ],
                [
                    35.45,
                    34.89,
                    34.51,
                    36.66
                ],
                [
                    34.43,
                    33.95,
                    33.15,
                    34.74
                ],
                [
                    33.9,
                    33.98,
                    33.8,
                    34.55
                ],
                [
                    33.8,
                    33.58,
                    33.57,
                    34.11
                ],
                [
                    33.6,
                    33.59,
                    33.26,
                    34.09
                ],
                [
                    33.25,
                    33.37,
                    33.25,
                    33.82
                ],
                [
                    33.01,
                    32.58,
                    32.25,
                    33.25
                ],
                [
                    32.5,
                    32.59,
                    32.44,
                    32.92
                ],
                [
                    32.6,
                    32.69,
                    32.42,
                    32.83
                ],
                [
                    32.33,
                    32.0,
                    31.88,
                    32.78
                ],
                [
                    30.42,
                    28.8,
                    28.8,
                    30.92
                ],
                [
                    28.82,
                    28.61,
                    27.92,
                    29.45
                ],
                [
                    28.08,
                    29.35,
                    27.52,
                    29.58
                ],
                [
                    30.2,
                    30.24,
                    29.93,
                    30.77
                ],
                [
                    30.1,
                    31.37,
                    29.95,
                    31.8
                ],
                [
                    32.47,
                    31.8,
                    31.55,
                    32.48
                ],
                [
                    31.61,
                    31.93,
                    31.31,
                    32.0
                ],
                [
                    31.78,
                    32.1,
                    31.52,
                    33.15
                ],
                [
                    31.94,
                    31.6,
                    31.57,
                    32.4
                ],
                [
                    31.48,
                    31.31,
                    31.16,
                    31.75
                ],
                [
                    31.45,
                    31.9,
                    31.2,
                    32.6
                ],
                [
                    31.8,
                    31.9,
                    31.7,
                    32.05
                ],
                [
                    32.1,
                    32.13,
                    31.7,
                    32.44
                ],
                [
                    32.15,
                    31.83,
                    31.8,
                    32.76
                ],
                [
                    31.8,
                    32.43,
                    31.77,
                    33.28
                ],
                [
                    33.28,
                    33.64,
                    32.97,
                    33.99
                ],
                [
                    33.51,
                    34.51,
                    33.28,
                    35.4
                ],
                [
                    34.67,
                    34.67,
                    34.41,
                    35.16
                ],
                [
                    34.75,
                    34.95,
                    34.5,
                    35.26
                ],
                [
                    35.0,
                    34.55,
                    34.29,
                    35.2
                ],
                [
                    34.54,
                    34.32,
                    34.18,
                    34.58
                ],
                [
                    34.18,
                    34.13,
                    33.76,
                    34.5
                ],
                [
                    34.48,
                    34.61,
                    34.07,
                    34.69
                ],
                [
                    34.98,
                    35.05,
                    34.06,
                    35.61
                ],
                [
                    34.88,
                    34.79,
                    34.53,
                    35.22
                ],
                [
                    34.6,
                    34.16,
                    34.05,
                    34.6
                ],
                [
                    34.05,
                    34.22,
                    33.89,
                    34.69
                ],
                [
                    34.69,
                    34.0,
                    33.96,
                    35.36
                ],
                [
                    33.71,
                    35.13,
                    33.68,
                    36.18
                ],
                [
                    35.01,
                    34.63,
                    34.57,
                    35.22
                ],
                [
                    34.68,
                    35.81,
                    34.68,
                    36.37
                ],
                [
                    35.6,
                    35.08,
                    35.03,
                    36.17
                ],
                [
                    34.99,
                    34.59,
                    34.34,
                    35.14
                ],
                [
                    34.49,
                    34.21,
                    33.95,
                    34.5
                ],
                [
                    34.2,
                    33.77,
                    33.68,
                    34.35
                ],
                [
                    33.85,
                    34.64,
                    33.85,
                    34.76
                ],
                [
                    34.36,
                    33.96,
                    33.85,
                    34.36
                ],
                [
                    33.3,
                    32.96,
                    32.51,
                    33.49
                ],
                [
                    32.91,
                    33.0,
                    32.72,
                    33.2
                ],
                [
                    32.9,
                    33.33,
                    32.8,
                    33.6
                ],
                [
                    32.68,
                    32.41,
                    32.3,
                    32.86
                ],
                [
                    32.42,
                    32.73,
                    32.34,
                    33.13
                ],
                [
                    32.85,
                    32.67,
                    32.6,
                    33.63
                ],
                [
                    32.67,
                    32.65,
                    32.6,
                    33.18
                ],
                [
                    32.5,
                    32.18,
                    32.1,
                    32.53
                ],
                [
                    32.06,
                    31.23,
                    31.12,
                    32.15
                ],
                [
                    31.1,
                    31.25,
                    30.9,
                    31.45
                ],
                [
                    31.32,
                    31.38,
                    31.09,
                    31.74
                ],
                [
                    31.06,
                    31.42,
                    30.91,
                    31.5
                ],
                [
                    31.32,
                    31.33,
                    31.28,
                    32.0
                ],
                [
                    31.43,
                    31.43,
                    31.38,
                    32.08
                ],
                [
                    31.12,
                    31.81,
                    31.0,
                    31.9
                ],
                [
                    31.95,
                    32.56,
                    31.86,
                    32.6
                ],
                [
                    32.7,
                    33.69,
                    32.69,
                    33.78
                ],
                [
                    33.72,
                    33.13,
                    33.12,
                    33.75
                ],
                [
                    33.39,
                    33.24,
                    33.05,
                    33.63
                ],
                [
                    33.45,
                    33.53,
                    33.32,
                    33.66
                ],
                [
                    33.53,
                    33.46,
                    33.21,
                    33.75
                ],
                [
                    33.34,
                    33.05,
                    32.8,
                    33.43
                ],
                [
                    33.13,
                    33.6,
                    33.02,
                    33.75
                ],
                [
                    33.5,
                    33.03,
                    33.0,
                    33.55
                ],
                [
                    33.07,
                    33.17,
                    32.91,
                    33.5
                ],
                [
                    33.17,
                    34.11,
                    33.16,
                    34.38
                ],
                [
                    34.0,
                    34.55,
                    34.0,
                    34.74
                ],
                [
                    34.3,
                    34.39,
                    34.11,
                    34.66
                ],
                [
                    34.35,
                    34.4,
                    34.1,
                    34.45
                ],
                [
                    34.53,
                    34.14,
                    33.76,
                    34.6
                ],
                [
                    34.52,
                    34.71,
                    33.9,
                    35.23
                ],
                [
                    34.72,
                    34.55,
                    34.36,
                    35.25
                ],
                [
                    34.4,
                    35.05,
                    34.2,
                    35.2
                ],
                [
                    35.1,
                    36.99,
                    35.09,
                    37.28
                ],
                [
                    37.05,
                    37.12,
                    36.8,
                    37.49
                ],
                [
                    36.93,
                    36.58,
                    36.43,
                    37.05
                ],
                [
                    36.57,
                    36.23,
                    36.08,
                    36.9
                ],
                [
                    36.25,
                    36.48,
                    36.15,
                    36.57
                ],
                [
                    36.0,
                    37.11,
                    35.76,
                    37.15
                ],
                [
                    37.13,
                    37.57,
                    36.81,
                    37.93
                ],
                [
                    37.4,
                    38.02,
                    37.31,
                    38.07
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u9636\u6bb5\u6027\u5e95\u90e8",
            "symbol": "triangle",
            "symbolSize": 20,
            "data": [
                [
                    "2025-03-19",
                    34.42
                ],
                [
                    "2025-06-19",
                    30.65
                ],
                [
                    "2025-06-20",
                    30.75
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#FF6B35",
                "borderColor": "#CC5529",
                "borderWidth": 2,
                "opacity": 0.9
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf",
                "\u9636\u6bb5\u6027\u5e95\u90e8"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600745 \u9636\u6bb5\u6027\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_d436cfe199f84c8da419790fde22154a.setOption(option_d436cfe199f84c8da419790fde22154a);
            window.addEventListener('resize', function(){
                chart_d436cfe199f84c8da419790fde22154a.resize();
            })
    </script>
</body>
</html>
