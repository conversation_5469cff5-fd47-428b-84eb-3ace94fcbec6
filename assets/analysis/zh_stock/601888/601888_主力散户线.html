<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="dbe1626eb84b48c5865feb6c110d4db0" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_dbe1626eb84b48c5865feb6c110d4db0 = echarts.init(
            document.getElementById('dbe1626eb84b48c5865feb6c110d4db0'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_dbe1626eb84b48c5865feb6c110d4db0 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    61.4,
                    60.7,
                    60.68,
                    62.17
                ],
                [
                    61.2,
                    60.22,
                    60.18,
                    61.36
                ],
                [
                    60.13,
                    60.95,
                    59.15,
                    60.96
                ],
                [
                    60.76,
                    61.64,
                    60.61,
                    62.23
                ],
                [
                    61.65,
                    62.05,
                    61.32,
                    62.33
                ],
                [
                    62.1,
                    60.97,
                    60.89,
                    62.33
                ],
                [
                    60.87,
                    61.48,
                    60.4,
                    61.5
                ],
                [
                    61.2,
                    61.99,
                    61.12,
                    62.44
                ],
                [
                    61.8,
                    62.09,
                    61.68,
                    62.6
                ],
                [
                    62.2,
                    61.75,
                    61.51,
                    62.99
                ],
                [
                    61.55,
                    60.31,
                    60.27,
                    61.69
                ],
                [
                    60.16,
                    60.86,
                    59.91,
                    60.95
                ],
                [
                    60.6,
                    60.47,
                    60.34,
                    61.24
                ],
                [
                    60.57,
                    61.05,
                    60.56,
                    62.11
                ],
                [
                    61.05,
                    60.72,
                    60.46,
                    61.37
                ],
                [
                    60.22,
                    60.05,
                    60.0,
                    60.49
                ],
                [
                    60.11,
                    61.04,
                    59.86,
                    61.08
                ],
                [
                    61.0,
                    62.67,
                    60.85,
                    63.5
                ],
                [
                    62.8,
                    61.17,
                    61.09,
                    63.8
                ],
                [
                    61.19,
                    61.11,
                    60.68,
                    62.26
                ],
                [
                    60.67,
                    61.13,
                    60.22,
                    61.15
                ],
                [
                    61.43,
                    61.23,
                    60.59,
                    61.69
                ],
                [
                    61.58,
                    62.34,
                    61.2,
                    63.37
                ],
                [
                    61.85,
                    61.92,
                    61.41,
                    62.84
                ],
                [
                    61.84,
                    61.27,
                    60.99,
                    62.03
                ],
                [
                    60.5,
                    62.2,
                    60.48,
                    62.2
                ],
                [
                    62.4,
                    61.72,
                    61.67,
                    63.11
                ],
                [
                    61.61,
                    62.12,
                    61.52,
                    63.12
                ],
                [
                    62.6,
                    64.08,
                    62.44,
                    64.77
                ],
                [
                    65.22,
                    64.12,
                    64.05,
                    65.22
                ],
                [
                    64.32,
                    63.59,
                    63.42,
                    64.49
                ],
                [
                    63.39,
                    63.34,
                    63.14,
                    63.98
                ],
                [
                    63.3,
                    62.72,
                    62.65,
                    63.4
                ],
                [
                    62.52,
                    61.82,
                    61.41,
                    62.98
                ],
                [
                    61.85,
                    62.02,
                    61.46,
                    62.39
                ],
                [
                    62.03,
                    62.15,
                    61.37,
                    62.21
                ],
                [
                    62.62,
                    62.38,
                    62.17,
                    62.7
                ],
                [
                    62.9,
                    62.39,
                    62.33,
                    63.2
                ],
                [
                    62.03,
                    61.61,
                    61.52,
                    62.25
                ],
                [
                    61.0,
                    60.2,
                    60.06,
                    61.0
                ],
                [
                    60.22,
                    60.43,
                    60.09,
                    60.69
                ],
                [
                    60.4,
                    60.42,
                    60.18,
                    60.52
                ],
                [
                    60.0,
                    60.6,
                    59.8,
                    60.81
                ],
                [
                    59.0,
                    56.57,
                    54.75,
                    59.0
                ],
                [
                    56.57,
                    60.79,
                    56.56,
                    61.35
                ],
                [
                    60.8,
                    66.87,
                    59.6,
                    66.87
                ],
                [
                    69.0,
                    70.21,
                    67.89,
                    73.46
                ],
                [
                    69.89,
                    67.95,
                    67.39,
                    70.55
                ],
                [
                    67.61,
                    67.98,
                    67.08,
                    69.55
                ],
                [
                    67.21,
                    65.68,
                    65.24,
                    67.94
                ],
                [
                    65.75,
                    67.4,
                    64.31,
                    67.89
                ],
                [
                    66.66,
                    66.02,
                    65.79,
                    66.77
                ],
                [
                    66.25,
                    64.87,
                    64.35,
                    66.25
                ],
                [
                    64.45,
                    64.65,
                    64.01,
                    65.19
                ],
                [
                    64.6,
                    64.32,
                    64.21,
                    65.17
                ],
                [
                    64.34,
                    62.96,
                    62.89,
                    64.57
                ],
                [
                    62.97,
                    62.66,
                    62.41,
                    63.2
                ],
                [
                    62.68,
                    63.0,
                    62.68,
                    63.38
                ],
                [
                    64.9,
                    63.05,
                    63.05,
                    64.9
                ],
                [
                    62.87,
                    62.32,
                    62.06,
                    62.88
                ],
                [
                    62.2,
                    63.4,
                    62.2,
                    63.5
                ],
                [
                    63.48,
                    63.56,
                    62.68,
                    63.6
                ],
                [
                    64.28,
                    63.4,
                    62.9,
                    64.36
                ],
                [
                    63.0,
                    63.27,
                    62.78,
                    63.42
                ],
                [
                    63.0,
                    62.72,
                    62.63,
                    63.15
                ],
                [
                    62.75,
                    62.57,
                    62.36,
                    62.9
                ],
                [
                    62.9,
                    62.51,
                    62.48,
                    63.1
                ],
                [
                    62.55,
                    62.58,
                    61.44,
                    62.96
                ],
                [
                    62.34,
                    61.73,
                    61.73,
                    62.57
                ],
                [
                    61.73,
                    62.87,
                    61.5,
                    63.1
                ],
                [
                    62.38,
                    61.88,
                    61.84,
                    62.47
                ],
                [
                    61.9,
                    61.91,
                    61.76,
                    62.12
                ],
                [
                    61.78,
                    61.9,
                    61.7,
                    62.2
                ],
                [
                    61.8,
                    61.24,
                    61.24,
                    62.0
                ],
                [
                    61.24,
                    60.88,
                    60.81,
                    61.63
                ],
                [
                    60.81,
                    61.15,
                    60.74,
                    61.16
                ],
                [
                    61.0,
                    61.18,
                    60.95,
                    61.33
                ],
                [
                    61.18,
                    61.23,
                    60.88,
                    61.45
                ],
                [
                    61.1,
                    61.63,
                    60.95,
                    61.7
                ],
                [
                    61.56,
                    60.59,
                    60.59,
                    61.56
                ],
                [
                    60.2,
                    60.92,
                    60.2,
                    61.09
                ],
                [
                    60.93,
                    61.64,
                    60.72,
                    61.78
                ],
                [
                    61.47,
                    61.35,
                    61.25,
                    61.62
                ],
                [
                    61.3,
                    61.61,
                    61.03,
                    61.95
                ],
                [
                    61.61,
                    61.66,
                    61.33,
                    61.88
                ],
                [
                    61.66,
                    61.16,
                    60.65,
                    61.69
                ],
                [
                    61.02,
                    61.38,
                    61.02,
                    61.77
                ],
                [
                    61.4,
                    61.18,
                    61.06,
                    61.45
                ],
                [
                    61.2,
                    60.4,
                    60.35,
                    61.44
                ],
                [
                    60.23,
                    60.45,
                    60.11,
                    60.6
                ],
                [
                    60.51,
                    60.63,
                    60.5,
                    60.95
                ],
                [
                    60.61,
                    60.66,
                    60.35,
                    60.84
                ],
                [
                    60.46,
                    60.81,
                    60.46,
                    61.15
                ],
                [
                    59.78,
                    60.18,
                    59.5,
                    61.09
                ],
                [
                    59.87,
                    59.71,
                    58.79,
                    59.87
                ],
                [
                    59.94,
                    60.88,
                    59.93,
                    60.89
                ],
                [
                    60.9,
                    61.3,
                    60.51,
                    61.35
                ],
                [
                    61.0,
                    61.1,
                    60.82,
                    61.62
                ],
                [
                    61.02,
                    60.59,
                    60.58,
                    61.32
                ],
                [
                    60.59,
                    60.97,
                    60.57,
                    61.07
                ],
                [
                    60.96,
                    60.61,
                    60.56,
                    61.06
                ],
                [
                    60.65,
                    60.68,
                    60.4,
                    60.92
                ],
                [
                    60.63,
                    60.73,
                    60.47,
                    60.92
                ],
                [
                    60.75,
                    61.03,
                    60.69,
                    61.55
                ],
                [
                    60.86,
                    60.8,
                    60.64,
                    61.03
                ],
                [
                    60.8,
                    61.34,
                    60.66,
                    61.4
                ],
                [
                    61.25,
                    61.18,
                    61.13,
                    61.91
                ],
                [
                    61.18,
                    61.86,
                    61.1,
                    62.5
                ],
                [
                    61.92,
                    62.25,
                    61.74,
                    62.63
                ],
                [
                    61.99,
                    61.6,
                    61.6,
                    62.16
                ],
                [
                    61.58,
                    61.08,
                    60.8,
                    61.66
                ],
                [
                    61.1,
                    61.15,
                    61.0,
                    61.55
                ],
                [
                    61.22,
                    61.65,
                    61.22,
                    62.14
                ],
                [
                    61.72,
                    62.77,
                    61.71,
                    63.2
                ],
                [
                    62.67,
                    63.78,
                    62.41,
                    64.24
                ],
                [
                    65.0,
                    65.43,
                    63.63,
                    65.49
                ],
                [
                    66.0,
                    64.4,
                    64.4,
                    66.68
                ],
                [
                    64.89,
                    70.84,
                    64.89,
                    70.84
                ],
                [
                    72.22,
                    68.05,
                    67.7,
                    72.22
                ],
                [
                    67.2,
                    66.37,
                    66.1,
                    68.45
                ],
                [
                    66.37,
                    66.23,
                    65.75,
                    67.09
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -1.75
                ],
                [
                    "2025-02-05",
                    -9.61
                ],
                [
                    "2025-02-06",
                    -10.42
                ],
                [
                    "2025-02-07",
                    7.03
                ],
                [
                    "2025-02-10",
                    -1.60
                ],
                [
                    "2025-02-11",
                    -10.58
                ],
                [
                    "2025-02-12",
                    -12.97
                ],
                [
                    "2025-02-13",
                    6.97
                ],
                [
                    "2025-02-14",
                    -2.06
                ],
                [
                    "2025-02-17",
                    1.16
                ],
                [
                    "2025-02-18",
                    -11.94
                ],
                [
                    "2025-02-19",
                    -3.60
                ],
                [
                    "2025-02-20",
                    -7.42
                ],
                [
                    "2025-02-21",
                    -0.99
                ],
                [
                    "2025-02-24",
                    -4.50
                ],
                [
                    "2025-02-25",
                    -13.96
                ],
                [
                    "2025-02-26",
                    -1.21
                ],
                [
                    "2025-02-27",
                    5.43
                ],
                [
                    "2025-02-28",
                    0.79
                ],
                [
                    "2025-03-03",
                    -2.13
                ],
                [
                    "2025-03-04",
                    2.89
                ],
                [
                    "2025-03-05",
                    0.44
                ],
                [
                    "2025-03-06",
                    7.41
                ],
                [
                    "2025-03-07",
                    -6.83
                ],
                [
                    "2025-03-10",
                    -8.96
                ],
                [
                    "2025-03-11",
                    -2.51
                ],
                [
                    "2025-03-12",
                    -1.22
                ],
                [
                    "2025-03-13",
                    6.43
                ],
                [
                    "2025-03-14",
                    13.33
                ],
                [
                    "2025-03-17",
                    -7.30
                ],
                [
                    "2025-03-18",
                    -10.64
                ],
                [
                    "2025-03-19",
                    -12.12
                ],
                [
                    "2025-03-20",
                    -13.91
                ],
                [
                    "2025-03-21",
                    -15.68
                ],
                [
                    "2025-03-24",
                    -3.61
                ],
                [
                    "2025-03-25",
                    -2.08
                ],
                [
                    "2025-03-26",
                    -0.01
                ],
                [
                    "2025-03-27",
                    -5.26
                ],
                [
                    "2025-03-28",
                    -22.02
                ],
                [
                    "2025-03-31",
                    -12.80
                ],
                [
                    "2025-04-01",
                    -7.95
                ],
                [
                    "2025-04-02",
                    -5.99
                ],
                [
                    "2025-04-03",
                    1.89
                ],
                [
                    "2025-04-07",
                    -19.73
                ],
                [
                    "2025-04-08",
                    -1.18
                ],
                [
                    "2025-04-09",
                    15.33
                ],
                [
                    "2025-04-10",
                    -1.70
                ],
                [
                    "2025-04-11",
                    -10.17
                ],
                [
                    "2025-04-14",
                    -2.26
                ],
                [
                    "2025-04-15",
                    -13.84
                ],
                [
                    "2025-04-16",
                    -2.47
                ],
                [
                    "2025-04-17",
                    -11.22
                ],
                [
                    "2025-04-18",
                    -14.23
                ],
                [
                    "2025-04-21",
                    -11.54
                ],
                [
                    "2025-04-22",
                    -2.90
                ],
                [
                    "2025-04-23",
                    -18.54
                ],
                [
                    "2025-04-24",
                    -9.42
                ],
                [
                    "2025-04-25",
                    -0.62
                ],
                [
                    "2025-04-28",
                    -4.25
                ],
                [
                    "2025-04-29",
                    -8.62
                ],
                [
                    "2025-04-30",
                    6.65
                ],
                [
                    "2025-05-06",
                    -0.91
                ],
                [
                    "2025-05-07",
                    -8.02
                ],
                [
                    "2025-05-08",
                    -1.06
                ],
                [
                    "2025-05-09",
                    -6.05
                ],
                [
                    "2025-05-12",
                    -11.95
                ],
                [
                    "2025-05-13",
                    -4.26
                ],
                [
                    "2025-05-14",
                    -4.56
                ],
                [
                    "2025-05-15",
                    -11.98
                ],
                [
                    "2025-05-16",
                    4.65
                ],
                [
                    "2025-05-19",
                    -12.36
                ],
                [
                    "2025-05-20",
                    -7.76
                ],
                [
                    "2025-05-21",
                    -6.79
                ],
                [
                    "2025-05-22",
                    -13.69
                ],
                [
                    "2025-05-23",
                    -7.56
                ],
                [
                    "2025-05-26",
                    -7.67
                ],
                [
                    "2025-05-27",
                    1.33
                ],
                [
                    "2025-05-28",
                    10.25
                ],
                [
                    "2025-05-29",
                    -0.31
                ],
                [
                    "2025-05-30",
                    -9.44
                ],
                [
                    "2025-06-03",
                    -7.77
                ],
                [
                    "2025-06-04",
                    9.78
                ],
                [
                    "2025-06-05",
                    2.94
                ],
                [
                    "2025-06-06",
                    15.93
                ],
                [
                    "2025-06-09",
                    -0.07
                ],
                [
                    "2025-06-10",
                    -11.07
                ],
                [
                    "2025-06-11",
                    -2.16
                ],
                [
                    "2025-06-12",
                    -13.97
                ],
                [
                    "2025-06-13",
                    -14.39
                ],
                [
                    "2025-06-16",
                    -8.16
                ],
                [
                    "2025-06-17",
                    -7.18
                ],
                [
                    "2025-06-18",
                    -1.46
                ],
                [
                    "2025-06-19",
                    0.23
                ],
                [
                    "2025-06-20",
                    1.04
                ],
                [
                    "2025-06-23",
                    -10.27
                ],
                [
                    "2025-06-24",
                    4.48
                ],
                [
                    "2025-06-25",
                    6.93
                ],
                [
                    "2025-06-26",
                    1.85
                ],
                [
                    "2025-06-27",
                    -9.15
                ],
                [
                    "2025-06-30",
                    -2.52
                ],
                [
                    "2025-07-01",
                    -12.44
                ],
                [
                    "2025-07-02",
                    1.84
                ],
                [
                    "2025-07-03",
                    -0.90
                ],
                [
                    "2025-07-04",
                    8.58
                ],
                [
                    "2025-07-07",
                    -0.75
                ],
                [
                    "2025-07-08",
                    -0.22
                ],
                [
                    "2025-07-09",
                    -0.83
                ],
                [
                    "2025-07-10",
                    7.13
                ],
                [
                    "2025-07-11",
                    -1.27
                ],
                [
                    "2025-07-14",
                    -7.75
                ],
                [
                    "2025-07-15",
                    -16.62
                ],
                [
                    "2025-07-16",
                    1.14
                ],
                [
                    "2025-07-17",
                    -1.22
                ],
                [
                    "2025-07-18",
                    6.21
                ],
                [
                    "2025-07-21",
                    12.73
                ],
                [
                    "2025-07-22",
                    5.06
                ],
                [
                    "2025-07-23",
                    -5.02
                ],
                [
                    "2025-07-24",
                    15.40
                ],
                [
                    "2025-07-25",
                    -9.33
                ],
                [
                    "2025-07-28",
                    -9.49
                ],
                [
                    "2025-07-29",
                    -9.51
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    0.95
                ],
                [
                    "2025-02-05",
                    3.50
                ],
                [
                    "2025-02-06",
                    2.32
                ],
                [
                    "2025-02-07",
                    -4.57
                ],
                [
                    "2025-02-10",
                    0.99
                ],
                [
                    "2025-02-11",
                    6.29
                ],
                [
                    "2025-02-12",
                    7.37
                ],
                [
                    "2025-02-13",
                    -2.25
                ],
                [
                    "2025-02-14",
                    -0.27
                ],
                [
                    "2025-02-17",
                    -0.36
                ],
                [
                    "2025-02-18",
                    6.87
                ],
                [
                    "2025-02-19",
                    0.05
                ],
                [
                    "2025-02-20",
                    2.14
                ],
                [
                    "2025-02-21",
                    2.62
                ],
                [
                    "2025-02-24",
                    1.62
                ],
                [
                    "2025-02-25",
                    6.41
                ],
                [
                    "2025-02-26",
                    0.53
                ],
                [
                    "2025-02-27",
                    -1.51
                ],
                [
                    "2025-02-28",
                    1.64
                ],
                [
                    "2025-03-03",
                    4.03
                ],
                [
                    "2025-03-04",
                    -1.64
                ],
                [
                    "2025-03-05",
                    2.35
                ],
                [
                    "2025-03-06",
                    -2.74
                ],
                [
                    "2025-03-07",
                    4.51
                ],
                [
                    "2025-03-10",
                    6.71
                ],
                [
                    "2025-03-11",
                    -0.66
                ],
                [
                    "2025-03-12",
                    1.91
                ],
                [
                    "2025-03-13",
                    -1.11
                ],
                [
                    "2025-03-14",
                    -3.92
                ],
                [
                    "2025-03-17",
                    7.61
                ],
                [
                    "2025-03-18",
                    5.13
                ],
                [
                    "2025-03-19",
                    8.13
                ],
                [
                    "2025-03-20",
                    7.01
                ],
                [
                    "2025-03-21",
                    7.51
                ],
                [
                    "2025-03-24",
                    -0.55
                ],
                [
                    "2025-03-25",
                    -1.84
                ],
                [
                    "2025-03-26",
                    1.90
                ],
                [
                    "2025-03-27",
                    2.09
                ],
                [
                    "2025-03-28",
                    6.09
                ],
                [
                    "2025-03-31",
                    0.84
                ],
                [
                    "2025-04-01",
                    1.33
                ],
                [
                    "2025-04-02",
                    -0.38
                ],
                [
                    "2025-04-03",
                    -1.11
                ],
                [
                    "2025-04-07",
                    1.72
                ],
                [
                    "2025-04-08",
                    -4.04
                ],
                [
                    "2025-04-09",
                    -6.43
                ],
                [
                    "2025-04-10",
                    1.14
                ],
                [
                    "2025-04-11",
                    3.60
                ],
                [
                    "2025-04-14",
                    1.56
                ],
                [
                    "2025-04-15",
                    1.04
                ],
                [
                    "2025-04-16",
                    1.32
                ],
                [
                    "2025-04-17",
                    5.90
                ],
                [
                    "2025-04-18",
                    1.86
                ],
                [
                    "2025-04-21",
                    3.37
                ],
                [
                    "2025-04-22",
                    1.00
                ],
                [
                    "2025-04-23",
                    4.23
                ],
                [
                    "2025-04-24",
                    0.91
                ],
                [
                    "2025-04-25",
                    1.98
                ],
                [
                    "2025-04-28",
                    2.44
                ],
                [
                    "2025-04-29",
                    0.15
                ],
                [
                    "2025-04-30",
                    -3.70
                ],
                [
                    "2025-05-06",
                    0.01
                ],
                [
                    "2025-05-07",
                    3.17
                ],
                [
                    "2025-05-08",
                    -1.14
                ],
                [
                    "2025-05-09",
                    -0.41
                ],
                [
                    "2025-05-12",
                    3.01
                ],
                [
                    "2025-05-13",
                    1.85
                ],
                [
                    "2025-05-14",
                    -1.37
                ],
                [
                    "2025-05-15",
                    5.29
                ],
                [
                    "2025-05-16",
                    -2.41
                ],
                [
                    "2025-05-19",
                    5.05
                ],
                [
                    "2025-05-20",
                    0.20
                ],
                [
                    "2025-05-21",
                    3.51
                ],
                [
                    "2025-05-22",
                    0.28
                ],
                [
                    "2025-05-23",
                    0.90
                ],
                [
                    "2025-05-26",
                    -3.03
                ],
                [
                    "2025-05-27",
                    -0.30
                ],
                [
                    "2025-05-28",
                    -1.63
                ],
                [
                    "2025-05-29",
                    -2.12
                ],
                [
                    "2025-05-30",
                    4.65
                ],
                [
                    "2025-06-03",
                    -2.19
                ],
                [
                    "2025-06-04",
                    -2.51
                ],
                [
                    "2025-06-05",
                    6.67
                ],
                [
                    "2025-06-06",
                    -2.49
                ],
                [
                    "2025-06-09",
                    -2.95
                ],
                [
                    "2025-06-10",
                    3.29
                ],
                [
                    "2025-06-11",
                    -4.05
                ],
                [
                    "2025-06-12",
                    2.75
                ],
                [
                    "2025-06-13",
                    6.27
                ],
                [
                    "2025-06-16",
                    4.88
                ],
                [
                    "2025-06-17",
                    2.57
                ],
                [
                    "2025-06-18",
                    -1.19
                ],
                [
                    "2025-06-19",
                    5.61
                ],
                [
                    "2025-06-20",
                    4.15
                ],
                [
                    "2025-06-23",
                    3.91
                ],
                [
                    "2025-06-24",
                    -9.56
                ],
                [
                    "2025-06-25",
                    -3.94
                ],
                [
                    "2025-06-26",
                    5.30
                ],
                [
                    "2025-06-27",
                    4.10
                ],
                [
                    "2025-06-30",
                    -0.09
                ],
                [
                    "2025-07-01",
                    9.39
                ],
                [
                    "2025-07-02",
                    -3.87
                ],
                [
                    "2025-07-03",
                    -0.33
                ],
                [
                    "2025-07-04",
                    0.17
                ],
                [
                    "2025-07-07",
                    1.28
                ],
                [
                    "2025-07-08",
                    -1.20
                ],
                [
                    "2025-07-09",
                    -0.26
                ],
                [
                    "2025-07-10",
                    0.90
                ],
                [
                    "2025-07-11",
                    3.96
                ],
                [
                    "2025-07-14",
                    11.59
                ],
                [
                    "2025-07-15",
                    10.27
                ],
                [
                    "2025-07-16",
                    -1.56
                ],
                [
                    "2025-07-17",
                    4.30
                ],
                [
                    "2025-07-18",
                    -0.66
                ],
                [
                    "2025-07-21",
                    -3.14
                ],
                [
                    "2025-07-22",
                    1.40
                ],
                [
                    "2025-07-23",
                    3.82
                ],
                [
                    "2025-07-24",
                    -6.44
                ],
                [
                    "2025-07-25",
                    1.84
                ],
                [
                    "2025-07-28",
                    -0.09
                ],
                [
                    "2025-07-29",
                    3.11
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    0.79
                ],
                [
                    "2025-02-05",
                    6.11
                ],
                [
                    "2025-02-06",
                    8.10
                ],
                [
                    "2025-02-07",
                    -2.46
                ],
                [
                    "2025-02-10",
                    0.61
                ],
                [
                    "2025-02-11",
                    4.29
                ],
                [
                    "2025-02-12",
                    5.61
                ],
                [
                    "2025-02-13",
                    -4.72
                ],
                [
                    "2025-02-14",
                    2.33
                ],
                [
                    "2025-02-17",
                    -0.81
                ],
                [
                    "2025-02-18",
                    5.06
                ],
                [
                    "2025-02-19",
                    3.55
                ],
                [
                    "2025-02-20",
                    5.28
                ],
                [
                    "2025-02-21",
                    -1.63
                ],
                [
                    "2025-02-24",
                    2.88
                ],
                [
                    "2025-02-25",
                    7.55
                ],
                [
                    "2025-02-26",
                    0.68
                ],
                [
                    "2025-02-27",
                    -3.93
                ],
                [
                    "2025-02-28",
                    -2.42
                ],
                [
                    "2025-03-03",
                    -1.90
                ],
                [
                    "2025-03-04",
                    -1.24
                ],
                [
                    "2025-03-05",
                    -2.79
                ],
                [
                    "2025-03-06",
                    -4.67
                ],
                [
                    "2025-03-07",
                    2.32
                ],
                [
                    "2025-03-10",
                    2.25
                ],
                [
                    "2025-03-11",
                    3.16
                ],
                [
                    "2025-03-12",
                    -0.69
                ],
                [
                    "2025-03-13",
                    -5.31
                ],
                [
                    "2025-03-14",
                    -9.42
                ],
                [
                    "2025-03-17",
                    -0.31
                ],
                [
                    "2025-03-18",
                    5.51
                ],
                [
                    "2025-03-19",
                    3.99
                ],
                [
                    "2025-03-20",
                    6.90
                ],
                [
                    "2025-03-21",
                    8.17
                ],
                [
                    "2025-03-24",
                    4.16
                ],
                [
                    "2025-03-25",
                    3.92
                ],
                [
                    "2025-03-26",
                    -1.88
                ],
                [
                    "2025-03-27",
                    3.17
                ],
                [
                    "2025-03-28",
                    15.93
                ],
                [
                    "2025-03-31",
                    11.95
                ],
                [
                    "2025-04-01",
                    6.63
                ],
                [
                    "2025-04-02",
                    6.38
                ],
                [
                    "2025-04-03",
                    -0.78
                ],
                [
                    "2025-04-07",
                    18.00
                ],
                [
                    "2025-04-08",
                    5.22
                ],
                [
                    "2025-04-09",
                    -8.91
                ],
                [
                    "2025-04-10",
                    0.56
                ],
                [
                    "2025-04-11",
                    6.57
                ],
                [
                    "2025-04-14",
                    0.70
                ],
                [
                    "2025-04-15",
                    12.81
                ],
                [
                    "2025-04-16",
                    1.15
                ],
                [
                    "2025-04-17",
                    5.32
                ],
                [
                    "2025-04-18",
                    12.37
                ],
                [
                    "2025-04-21",
                    8.18
                ],
                [
                    "2025-04-22",
                    1.90
                ],
                [
                    "2025-04-23",
                    14.32
                ],
                [
                    "2025-04-24",
                    8.51
                ],
                [
                    "2025-04-25",
                    -1.36
                ],
                [
                    "2025-04-28",
                    1.81
                ],
                [
                    "2025-04-29",
                    8.47
                ],
                [
                    "2025-04-30",
                    -2.95
                ],
                [
                    "2025-05-06",
                    0.89
                ],
                [
                    "2025-05-07",
                    4.86
                ],
                [
                    "2025-05-08",
                    2.19
                ],
                [
                    "2025-05-09",
                    6.46
                ],
                [
                    "2025-05-12",
                    8.95
                ],
                [
                    "2025-05-13",
                    2.41
                ],
                [
                    "2025-05-14",
                    5.93
                ],
                [
                    "2025-05-15",
                    6.69
                ],
                [
                    "2025-05-16",
                    -2.23
                ],
                [
                    "2025-05-19",
                    7.32
                ],
                [
                    "2025-05-20",
                    7.57
                ],
                [
                    "2025-05-21",
                    3.27
                ],
                [
                    "2025-05-22",
                    13.41
                ],
                [
                    "2025-05-23",
                    6.65
                ],
                [
                    "2025-05-26",
                    10.70
                ],
                [
                    "2025-05-27",
                    -1.03
                ],
                [
                    "2025-05-28",
                    -8.62
                ],
                [
                    "2025-05-29",
                    2.43
                ],
                [
                    "2025-05-30",
                    4.79
                ],
                [
                    "2025-06-03",
                    9.96
                ],
                [
                    "2025-06-04",
                    -7.26
                ],
                [
                    "2025-06-05",
                    -9.61
                ],
                [
                    "2025-06-06",
                    -13.45
                ],
                [
                    "2025-06-09",
                    3.02
                ],
                [
                    "2025-06-10",
                    7.78
                ],
                [
                    "2025-06-11",
                    6.21
                ],
                [
                    "2025-06-12",
                    11.22
                ],
                [
                    "2025-06-13",
                    8.13
                ],
                [
                    "2025-06-16",
                    3.27
                ],
                [
                    "2025-06-17",
                    4.61
                ],
                [
                    "2025-06-18",
                    2.65
                ],
                [
                    "2025-06-19",
                    -5.84
                ],
                [
                    "2025-06-20",
                    -5.19
                ],
                [
                    "2025-06-23",
                    6.36
                ],
                [
                    "2025-06-24",
                    5.07
                ],
                [
                    "2025-06-25",
                    -2.99
                ],
                [
                    "2025-06-26",
                    -7.14
                ],
                [
                    "2025-06-27",
                    5.05
                ],
                [
                    "2025-06-30",
                    2.60
                ],
                [
                    "2025-07-01",
                    3.05
                ],
                [
                    "2025-07-02",
                    2.04
                ],
                [
                    "2025-07-03",
                    1.23
                ],
                [
                    "2025-07-04",
                    -8.75
                ],
                [
                    "2025-07-07",
                    -0.54
                ],
                [
                    "2025-07-08",
                    1.42
                ],
                [
                    "2025-07-09",
                    1.10
                ],
                [
                    "2025-07-10",
                    -8.04
                ],
                [
                    "2025-07-11",
                    -2.69
                ],
                [
                    "2025-07-14",
                    -3.84
                ],
                [
                    "2025-07-15",
                    6.34
                ],
                [
                    "2025-07-16",
                    0.41
                ],
                [
                    "2025-07-17",
                    -3.09
                ],
                [
                    "2025-07-18",
                    -5.55
                ],
                [
                    "2025-07-21",
                    -9.60
                ],
                [
                    "2025-07-22",
                    -6.46
                ],
                [
                    "2025-07-23",
                    1.20
                ],
                [
                    "2025-07-24",
                    -8.96
                ],
                [
                    "2025-07-25",
                    7.49
                ],
                [
                    "2025-07-28",
                    9.57
                ],
                [
                    "2025-07-29",
                    6.40
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-04",
                    2.89
                ],
                [
                    "2025-03-05",
                    0.44
                ],
                [
                    "2025-03-06",
                    7.41
                ],
                [
                    "2025-03-07",
                    -6.83
                ],
                [
                    "2025-03-14",
                    13.33
                ],
                [
                    "2025-03-17",
                    -7.30
                ],
                [
                    "2025-03-18",
                    -10.64
                ],
                [
                    "2025-06-06",
                    15.93
                ],
                [
                    "2025-06-09",
                    -0.07
                ],
                [
                    "2025-06-10",
                    -11.07
                ],
                [
                    "2025-06-11",
                    -2.16
                ],
                [
                    "2025-06-25",
                    6.93
                ],
                [
                    "2025-06-26",
                    1.85
                ],
                [
                    "2025-07-08",
                    -0.22
                ],
                [
                    "2025-07-09",
                    -0.83
                ],
                [
                    "2025-07-10",
                    7.13
                ],
                [
                    "2025-07-11",
                    -1.27
                ],
                [
                    "2025-07-21",
                    12.73
                ],
                [
                    "2025-07-22",
                    5.06
                ],
                [
                    "2025-07-23",
                    -5.02
                ],
                [
                    "2025-07-24",
                    15.40
                ],
                [
                    "2025-07-25",
                    -9.33
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-03",
                    4.03
                ],
                [
                    "2025-03-04",
                    -1.64
                ],
                [
                    "2025-03-05",
                    2.35
                ],
                [
                    "2025-03-06",
                    -2.74
                ],
                [
                    "2025-03-07",
                    4.51
                ],
                [
                    "2025-03-10",
                    6.71
                ],
                [
                    "2025-03-14",
                    -3.92
                ],
                [
                    "2025-03-17",
                    7.61
                ],
                [
                    "2025-03-18",
                    5.13
                ],
                [
                    "2025-03-19",
                    8.13
                ],
                [
                    "2025-06-06",
                    -2.49
                ],
                [
                    "2025-06-10",
                    3.29
                ],
                [
                    "2025-06-11",
                    -4.05
                ],
                [
                    "2025-06-20",
                    4.15
                ],
                [
                    "2025-06-25",
                    -3.94
                ],
                [
                    "2025-07-07",
                    1.28
                ],
                [
                    "2025-07-10",
                    0.90
                ],
                [
                    "2025-07-11",
                    3.96
                ],
                [
                    "2025-07-14",
                    11.59
                ],
                [
                    "2025-07-15",
                    10.27
                ],
                [
                    "2025-07-16",
                    -1.56
                ],
                [
                    "2025-07-17",
                    4.30
                ],
                [
                    "2025-07-18",
                    -0.66
                ],
                [
                    "2025-07-21",
                    -3.14
                ],
                [
                    "2025-07-22",
                    1.40
                ],
                [
                    "2025-07-23",
                    3.82
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    0.61
                ],
                [
                    "2025-02-11",
                    4.29
                ],
                [
                    "2025-02-12",
                    5.61
                ],
                [
                    "2025-02-13",
                    -4.72
                ],
                [
                    "2025-02-14",
                    2.33
                ],
                [
                    "2025-02-17",
                    -0.81
                ],
                [
                    "2025-02-18",
                    5.06
                ],
                [
                    "2025-02-19",
                    3.55
                ],
                [
                    "2025-02-20",
                    5.28
                ],
                [
                    "2025-02-21",
                    -1.63
                ],
                [
                    "2025-02-24",
                    2.88
                ],
                [
                    "2025-02-25",
                    7.55
                ],
                [
                    "2025-02-26",
                    0.68
                ],
                [
                    "2025-02-27",
                    -3.93
                ],
                [
                    "2025-02-28",
                    -2.42
                ],
                [
                    "2025-03-11",
                    3.16
                ],
                [
                    "2025-03-12",
                    -0.69
                ],
                [
                    "2025-03-13",
                    -5.31
                ],
                [
                    "2025-03-20",
                    6.90
                ],
                [
                    "2025-03-21",
                    8.17
                ],
                [
                    "2025-03-24",
                    4.16
                ],
                [
                    "2025-03-25",
                    3.92
                ],
                [
                    "2025-03-26",
                    -1.88
                ],
                [
                    "2025-03-27",
                    3.17
                ],
                [
                    "2025-03-28",
                    15.93
                ],
                [
                    "2025-03-31",
                    11.95
                ],
                [
                    "2025-04-01",
                    6.63
                ],
                [
                    "2025-04-02",
                    6.38
                ],
                [
                    "2025-04-03",
                    -0.78
                ],
                [
                    "2025-04-07",
                    18.00
                ],
                [
                    "2025-04-08",
                    5.22
                ],
                [
                    "2025-04-09",
                    -8.91
                ],
                [
                    "2025-04-10",
                    0.56
                ],
                [
                    "2025-04-11",
                    6.57
                ],
                [
                    "2025-04-15",
                    12.81
                ],
                [
                    "2025-04-16",
                    1.15
                ],
                [
                    "2025-04-17",
                    5.32
                ],
                [
                    "2025-04-18",
                    12.37
                ],
                [
                    "2025-04-21",
                    8.18
                ],
                [
                    "2025-04-22",
                    1.90
                ],
                [
                    "2025-04-23",
                    14.32
                ],
                [
                    "2025-04-24",
                    8.51
                ],
                [
                    "2025-04-25",
                    -1.36
                ],
                [
                    "2025-04-28",
                    1.81
                ],
                [
                    "2025-04-29",
                    8.47
                ],
                [
                    "2025-04-30",
                    -2.95
                ],
                [
                    "2025-05-06",
                    0.89
                ],
                [
                    "2025-05-07",
                    4.86
                ],
                [
                    "2025-05-08",
                    2.19
                ],
                [
                    "2025-05-09",
                    6.46
                ],
                [
                    "2025-05-12",
                    8.95
                ],
                [
                    "2025-05-13",
                    2.41
                ],
                [
                    "2025-05-14",
                    5.93
                ],
                [
                    "2025-05-15",
                    6.69
                ],
                [
                    "2025-05-16",
                    -2.23
                ],
                [
                    "2025-05-19",
                    7.32
                ],
                [
                    "2025-05-20",
                    7.57
                ],
                [
                    "2025-05-21",
                    3.27
                ],
                [
                    "2025-05-22",
                    13.41
                ],
                [
                    "2025-05-23",
                    6.65
                ],
                [
                    "2025-05-26",
                    10.70
                ],
                [
                    "2025-05-27",
                    -1.03
                ],
                [
                    "2025-05-28",
                    -8.62
                ],
                [
                    "2025-05-29",
                    2.43
                ],
                [
                    "2025-05-30",
                    4.79
                ],
                [
                    "2025-06-03",
                    9.96
                ],
                [
                    "2025-06-05",
                    -9.61
                ],
                [
                    "2025-06-12",
                    11.22
                ],
                [
                    "2025-06-13",
                    8.13
                ],
                [
                    "2025-06-16",
                    3.27
                ],
                [
                    "2025-06-17",
                    4.61
                ],
                [
                    "2025-06-18",
                    2.65
                ],
                [
                    "2025-06-19",
                    -5.84
                ],
                [
                    "2025-06-23",
                    6.36
                ],
                [
                    "2025-06-24",
                    5.07
                ],
                [
                    "2025-06-27",
                    5.05
                ],
                [
                    "2025-07-01",
                    3.05
                ],
                [
                    "2025-07-02",
                    2.04
                ],
                [
                    "2025-07-03",
                    1.23
                ],
                [
                    "2025-07-04",
                    -8.75
                ],
                [
                    "2025-07-28",
                    9.57
                ],
                [
                    "2025-07-29",
                    6.40
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "601888 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_dbe1626eb84b48c5865feb6c110d4db0.setOption(option_dbe1626eb84b48c5865feb6c110d4db0);
            window.addEventListener('resize', function(){
                chart_dbe1626eb84b48c5865feb6c110d4db0.resize();
            })
    </script>
</body>
</html>
