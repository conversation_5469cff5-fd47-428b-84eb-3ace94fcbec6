<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="e9ed3dd905ca43808256633a288f575e" class="chart-container" style="width:100%; height:500px; "></div>
    <script>
        var chart_e9ed3dd905ca43808256633a288f575e = echarts.init(
            document.getElementById('e9ed3dd905ca43808256633a288f575e'), 'white', {renderer: 'canvas'});
        var option_e9ed3dd905ca43808256633a288f575e = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    61.4,
                    60.7,
                    60.68,
                    62.17
                ],
                [
                    61.2,
                    60.22,
                    60.18,
                    61.36
                ],
                [
                    60.13,
                    60.95,
                    59.15,
                    60.96
                ],
                [
                    60.76,
                    61.64,
                    60.61,
                    62.23
                ],
                [
                    61.65,
                    62.05,
                    61.32,
                    62.33
                ],
                [
                    62.1,
                    60.97,
                    60.89,
                    62.33
                ],
                [
                    60.87,
                    61.48,
                    60.4,
                    61.5
                ],
                [
                    61.2,
                    61.99,
                    61.12,
                    62.44
                ],
                [
                    61.8,
                    62.09,
                    61.68,
                    62.6
                ],
                [
                    62.2,
                    61.75,
                    61.51,
                    62.99
                ],
                [
                    61.55,
                    60.31,
                    60.27,
                    61.69
                ],
                [
                    60.16,
                    60.86,
                    59.91,
                    60.95
                ],
                [
                    60.6,
                    60.47,
                    60.34,
                    61.24
                ],
                [
                    60.57,
                    61.05,
                    60.56,
                    62.11
                ],
                [
                    61.05,
                    60.72,
                    60.46,
                    61.37
                ],
                [
                    60.22,
                    60.05,
                    60.0,
                    60.49
                ],
                [
                    60.11,
                    61.04,
                    59.86,
                    61.08
                ],
                [
                    61.0,
                    62.67,
                    60.85,
                    63.5
                ],
                [
                    62.8,
                    61.17,
                    61.09,
                    63.8
                ],
                [
                    61.19,
                    61.11,
                    60.68,
                    62.26
                ],
                [
                    60.67,
                    61.13,
                    60.22,
                    61.15
                ],
                [
                    61.43,
                    61.23,
                    60.59,
                    61.69
                ],
                [
                    61.58,
                    62.34,
                    61.2,
                    63.37
                ],
                [
                    61.85,
                    61.92,
                    61.41,
                    62.84
                ],
                [
                    61.84,
                    61.27,
                    60.99,
                    62.03
                ],
                [
                    60.5,
                    62.2,
                    60.48,
                    62.2
                ],
                [
                    62.4,
                    61.72,
                    61.67,
                    63.11
                ],
                [
                    61.61,
                    62.12,
                    61.52,
                    63.12
                ],
                [
                    62.6,
                    64.08,
                    62.44,
                    64.77
                ],
                [
                    65.22,
                    64.12,
                    64.05,
                    65.22
                ],
                [
                    64.32,
                    63.59,
                    63.42,
                    64.49
                ],
                [
                    63.39,
                    63.34,
                    63.14,
                    63.98
                ],
                [
                    63.3,
                    62.72,
                    62.65,
                    63.4
                ],
                [
                    62.52,
                    61.82,
                    61.41,
                    62.98
                ],
                [
                    61.85,
                    62.02,
                    61.46,
                    62.39
                ],
                [
                    62.03,
                    62.15,
                    61.37,
                    62.21
                ],
                [
                    62.62,
                    62.38,
                    62.17,
                    62.7
                ],
                [
                    62.9,
                    62.39,
                    62.33,
                    63.2
                ],
                [
                    62.03,
                    61.61,
                    61.52,
                    62.25
                ],
                [
                    61.0,
                    60.2,
                    60.06,
                    61.0
                ],
                [
                    60.22,
                    60.43,
                    60.09,
                    60.69
                ],
                [
                    60.4,
                    60.42,
                    60.18,
                    60.52
                ],
                [
                    60.0,
                    60.6,
                    59.8,
                    60.81
                ],
                [
                    59.0,
                    56.57,
                    54.75,
                    59.0
                ],
                [
                    56.57,
                    60.79,
                    56.56,
                    61.35
                ],
                [
                    60.8,
                    66.87,
                    59.6,
                    66.87
                ],
                [
                    69.0,
                    70.21,
                    67.89,
                    73.46
                ],
                [
                    69.89,
                    67.95,
                    67.39,
                    70.55
                ],
                [
                    67.61,
                    67.98,
                    67.08,
                    69.55
                ],
                [
                    67.21,
                    65.68,
                    65.24,
                    67.94
                ],
                [
                    65.75,
                    67.4,
                    64.31,
                    67.89
                ],
                [
                    66.66,
                    66.02,
                    65.79,
                    66.77
                ],
                [
                    66.25,
                    64.87,
                    64.35,
                    66.25
                ],
                [
                    64.45,
                    64.65,
                    64.01,
                    65.19
                ],
                [
                    64.6,
                    64.32,
                    64.21,
                    65.17
                ],
                [
                    64.34,
                    62.96,
                    62.89,
                    64.57
                ],
                [
                    62.97,
                    62.66,
                    62.41,
                    63.2
                ],
                [
                    62.68,
                    63.0,
                    62.68,
                    63.38
                ],
                [
                    64.9,
                    63.05,
                    63.05,
                    64.9
                ],
                [
                    62.87,
                    62.32,
                    62.06,
                    62.88
                ],
                [
                    62.2,
                    63.4,
                    62.2,
                    63.5
                ],
                [
                    63.48,
                    63.56,
                    62.68,
                    63.6
                ],
                [
                    64.28,
                    63.4,
                    62.9,
                    64.36
                ],
                [
                    63.0,
                    63.27,
                    62.78,
                    63.42
                ],
                [
                    63.0,
                    62.72,
                    62.63,
                    63.15
                ],
                [
                    62.75,
                    62.57,
                    62.36,
                    62.9
                ],
                [
                    62.9,
                    62.51,
                    62.48,
                    63.1
                ],
                [
                    62.55,
                    62.58,
                    61.44,
                    62.96
                ],
                [
                    62.34,
                    61.73,
                    61.73,
                    62.57
                ],
                [
                    61.73,
                    62.87,
                    61.5,
                    63.1
                ],
                [
                    62.38,
                    61.88,
                    61.84,
                    62.47
                ],
                [
                    61.9,
                    61.91,
                    61.76,
                    62.12
                ],
                [
                    61.78,
                    61.9,
                    61.7,
                    62.2
                ],
                [
                    61.8,
                    61.24,
                    61.24,
                    62.0
                ],
                [
                    61.24,
                    60.88,
                    60.81,
                    61.63
                ],
                [
                    60.81,
                    61.15,
                    60.74,
                    61.16
                ],
                [
                    61.0,
                    61.18,
                    60.95,
                    61.33
                ],
                [
                    61.18,
                    61.23,
                    60.88,
                    61.45
                ],
                [
                    61.1,
                    61.63,
                    60.95,
                    61.7
                ],
                [
                    61.56,
                    60.59,
                    60.59,
                    61.56
                ],
                [
                    60.2,
                    60.92,
                    60.2,
                    61.09
                ],
                [
                    60.93,
                    61.64,
                    60.72,
                    61.78
                ],
                [
                    61.47,
                    61.35,
                    61.25,
                    61.62
                ],
                [
                    61.3,
                    61.61,
                    61.03,
                    61.95
                ],
                [
                    61.61,
                    61.66,
                    61.33,
                    61.88
                ],
                [
                    61.66,
                    61.16,
                    60.65,
                    61.69
                ],
                [
                    61.02,
                    61.38,
                    61.02,
                    61.77
                ],
                [
                    61.4,
                    61.18,
                    61.06,
                    61.45
                ],
                [
                    61.2,
                    60.4,
                    60.35,
                    61.44
                ],
                [
                    60.23,
                    60.45,
                    60.11,
                    60.6
                ],
                [
                    60.51,
                    60.63,
                    60.5,
                    60.95
                ],
                [
                    60.61,
                    60.66,
                    60.35,
                    60.84
                ],
                [
                    60.46,
                    60.81,
                    60.46,
                    61.15
                ],
                [
                    59.78,
                    60.18,
                    59.5,
                    61.09
                ],
                [
                    59.87,
                    59.71,
                    58.79,
                    59.87
                ],
                [
                    59.94,
                    60.88,
                    59.93,
                    60.89
                ],
                [
                    60.9,
                    61.3,
                    60.51,
                    61.35
                ],
                [
                    61.0,
                    61.1,
                    60.82,
                    61.62
                ],
                [
                    61.02,
                    60.59,
                    60.58,
                    61.32
                ],
                [
                    60.59,
                    60.97,
                    60.57,
                    61.07
                ],
                [
                    60.96,
                    60.61,
                    60.56,
                    61.06
                ],
                [
                    60.65,
                    60.68,
                    60.4,
                    60.92
                ],
                [
                    60.63,
                    60.73,
                    60.47,
                    60.92
                ],
                [
                    60.75,
                    61.03,
                    60.69,
                    61.55
                ],
                [
                    60.86,
                    60.8,
                    60.64,
                    61.03
                ],
                [
                    60.8,
                    61.34,
                    60.66,
                    61.4
                ],
                [
                    61.25,
                    61.18,
                    61.13,
                    61.91
                ],
                [
                    61.18,
                    61.86,
                    61.1,
                    62.5
                ],
                [
                    61.92,
                    62.25,
                    61.74,
                    62.63
                ],
                [
                    61.99,
                    61.6,
                    61.6,
                    62.16
                ],
                [
                    61.58,
                    61.08,
                    60.8,
                    61.66
                ],
                [
                    61.1,
                    61.15,
                    61.0,
                    61.55
                ],
                [
                    61.22,
                    61.65,
                    61.22,
                    62.14
                ],
                [
                    61.72,
                    62.77,
                    61.71,
                    63.2
                ],
                [
                    62.67,
                    63.78,
                    62.41,
                    64.24
                ],
                [
                    65.0,
                    65.43,
                    63.63,
                    65.49
                ],
                [
                    66.0,
                    64.4,
                    64.4,
                    66.68
                ],
                [
                    64.89,
                    70.84,
                    64.89,
                    70.84
                ],
                [
                    72.22,
                    68.05,
                    67.7,
                    72.22
                ],
                [
                    67.2,
                    66.37,
                    66.1,
                    68.45
                ],
                [
                    66.37,
                    66.23,
                    65.75,
                    67.09
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u9636\u6bb5\u6027\u5e95\u90e8",
            "symbol": "triangle",
            "symbolSize": 20,
            "data": [
                [
                    "2025-05-15",
                    60.5
                ],
                [
                    "2025-05-28",
                    59.66
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#FF6B35",
                "borderColor": "#CC5529",
                "borderWidth": 2,
                "opacity": 0.9
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf",
                "\u9636\u6bb5\u6027\u5e95\u90e8"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "601888 \u9636\u6bb5\u6027\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_e9ed3dd905ca43808256633a288f575e.setOption(option_e9ed3dd905ca43808256633a288f575e);
            window.addEventListener('resize', function(){
                chart_e9ed3dd905ca43808256633a288f575e.resize();
            })
    </script>
</body>
</html>
