<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="52184d64f29a42babfb884649541328d" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_52184d64f29a42babfb884649541328d = echarts.init(
            document.getElementById('52184d64f29a42babfb884649541328d'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_52184d64f29a42babfb884649541328d = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    10.09,
                    10.02,
                    9.92,
                    10.23
                ],
                [
                    10.06,
                    9.8,
                    9.74,
                    10.08
                ],
                [
                    9.82,
                    9.85,
                    9.6,
                    9.85
                ],
                [
                    9.82,
                    9.84,
                    9.71,
                    9.98
                ],
                [
                    9.89,
                    10.57,
                    9.85,
                    10.66
                ],
                [
                    10.62,
                    10.58,
                    10.28,
                    10.67
                ],
                [
                    10.63,
                    10.77,
                    10.44,
                    10.79
                ],
                [
                    10.76,
                    10.61,
                    10.58,
                    10.82
                ],
                [
                    10.55,
                    10.49,
                    10.4,
                    10.68
                ],
                [
                    10.47,
                    10.48,
                    10.25,
                    10.58
                ],
                [
                    10.44,
                    9.87,
                    9.83,
                    10.45
                ],
                [
                    9.85,
                    10.04,
                    9.75,
                    10.06
                ],
                [
                    10.04,
                    10.19,
                    9.97,
                    10.24
                ],
                [
                    10.21,
                    10.0,
                    9.89,
                    10.22
                ],
                [
                    9.99,
                    10.05,
                    9.95,
                    10.14
                ],
                [
                    9.98,
                    9.98,
                    9.92,
                    10.19
                ],
                [
                    9.97,
                    10.14,
                    9.95,
                    10.17
                ],
                [
                    10.12,
                    10.41,
                    10.11,
                    10.45
                ],
                [
                    10.41,
                    10.06,
                    10.04,
                    10.46
                ],
                [
                    10.15,
                    10.03,
                    9.97,
                    10.27
                ],
                [
                    9.98,
                    10.05,
                    9.92,
                    10.07
                ],
                [
                    10.01,
                    9.98,
                    9.82,
                    10.04
                ],
                [
                    9.96,
                    10.1,
                    9.9,
                    10.14
                ],
                [
                    10.06,
                    10.0,
                    9.95,
                    10.14
                ],
                [
                    9.96,
                    10.03,
                    9.94,
                    10.1
                ],
                [
                    9.92,
                    10.14,
                    9.88,
                    10.14
                ],
                [
                    10.15,
                    10.06,
                    10.06,
                    10.26
                ],
                [
                    10.02,
                    9.98,
                    9.87,
                    10.08
                ],
                [
                    10.02,
                    10.4,
                    10.02,
                    10.4
                ],
                [
                    10.6,
                    10.42,
                    10.39,
                    10.72
                ],
                [
                    10.38,
                    10.35,
                    10.25,
                    10.53
                ],
                [
                    10.36,
                    10.24,
                    10.2,
                    10.39
                ],
                [
                    10.25,
                    10.14,
                    10.1,
                    10.28
                ],
                [
                    10.06,
                    9.97,
                    9.92,
                    10.16
                ],
                [
                    10.01,
                    10.06,
                    9.81,
                    10.11
                ],
                [
                    10.1,
                    10.1,
                    9.94,
                    10.13
                ],
                [
                    10.06,
                    10.13,
                    10.01,
                    10.16
                ],
                [
                    10.12,
                    10.14,
                    10.0,
                    10.2
                ],
                [
                    10.05,
                    9.97,
                    9.93,
                    10.14
                ],
                [
                    9.91,
                    9.79,
                    9.77,
                    9.97
                ],
                [
                    9.78,
                    9.94,
                    9.75,
                    9.95
                ],
                [
                    9.91,
                    9.99,
                    9.86,
                    10.0
                ],
                [
                    9.91,
                    10.28,
                    9.87,
                    10.32
                ],
                [
                    10.12,
                    9.25,
                    9.25,
                    10.12
                ],
                [
                    9.25,
                    10.18,
                    9.22,
                    10.18
                ],
                [
                    10.25,
                    10.67,
                    9.79,
                    10.93
                ],
                [
                    10.66,
                    10.88,
                    10.39,
                    11.15
                ],
                [
                    10.73,
                    10.97,
                    10.72,
                    11.22
                ],
                [
                    10.95,
                    11.42,
                    10.85,
                    11.6
                ],
                [
                    11.42,
                    11.11,
                    11.0,
                    11.6
                ],
                [
                    11.0,
                    10.94,
                    10.78,
                    11.18
                ],
                [
                    10.87,
                    11.07,
                    10.8,
                    11.21
                ],
                [
                    10.97,
                    10.99,
                    10.81,
                    11.15
                ],
                [
                    10.85,
                    11.37,
                    10.85,
                    11.49
                ],
                [
                    11.21,
                    11.56,
                    11.09,
                    11.73
                ],
                [
                    11.49,
                    12.16,
                    10.98,
                    12.6
                ],
                [
                    11.73,
                    11.38,
                    11.3,
                    12.0
                ],
                [
                    11.38,
                    11.4,
                    11.12,
                    11.55
                ],
                [
                    11.37,
                    10.71,
                    10.68,
                    11.39
                ],
                [
                    10.69,
                    10.92,
                    10.53,
                    11.26
                ],
                [
                    10.67,
                    10.56,
                    10.5,
                    10.98
                ],
                [
                    10.56,
                    10.77,
                    10.55,
                    10.79
                ],
                [
                    10.8,
                    10.85,
                    10.7,
                    11.05
                ],
                [
                    10.8,
                    10.92,
                    10.66,
                    11.0
                ],
                [
                    10.92,
                    10.76,
                    10.68,
                    10.93
                ],
                [
                    10.79,
                    10.68,
                    10.61,
                    10.87
                ],
                [
                    10.75,
                    10.61,
                    10.54,
                    10.84
                ],
                [
                    10.67,
                    10.86,
                    10.46,
                    10.92
                ],
                [
                    10.76,
                    10.87,
                    10.75,
                    10.93
                ],
                [
                    10.92,
                    11.08,
                    10.72,
                    11.21
                ],
                [
                    11.03,
                    11.26,
                    10.98,
                    11.27
                ],
                [
                    11.2,
                    11.22,
                    11.09,
                    11.45
                ],
                [
                    11.19,
                    11.08,
                    10.9,
                    11.32
                ],
                [
                    10.96,
                    10.76,
                    10.75,
                    11.13
                ],
                [
                    10.77,
                    10.58,
                    10.54,
                    10.83
                ],
                [
                    10.57,
                    10.63,
                    10.48,
                    10.66
                ],
                [
                    10.62,
                    10.73,
                    10.6,
                    10.76
                ],
                [
                    10.73,
                    10.97,
                    10.63,
                    11.1
                ],
                [
                    10.9,
                    10.89,
                    10.68,
                    11.0
                ],
                [
                    10.82,
                    10.72,
                    10.69,
                    10.96
                ],
                [
                    10.64,
                    10.93,
                    10.64,
                    10.96
                ],
                [
                    10.93,
                    11.02,
                    10.86,
                    11.04
                ],
                [
                    11.02,
                    10.9,
                    10.86,
                    11.08
                ],
                [
                    10.9,
                    10.89,
                    10.74,
                    10.92
                ],
                [
                    10.92,
                    10.86,
                    10.79,
                    10.99
                ],
                [
                    10.77,
                    10.49,
                    10.39,
                    10.84
                ],
                [
                    10.5,
                    10.58,
                    10.47,
                    10.65
                ],
                [
                    10.52,
                    10.44,
                    10.39,
                    10.57
                ],
                [
                    10.41,
                    10.23,
                    10.15,
                    10.44
                ],
                [
                    10.2,
                    10.25,
                    10.15,
                    10.33
                ],
                [
                    10.22,
                    10.2,
                    10.15,
                    10.3
                ],
                [
                    10.16,
                    10.13,
                    10.04,
                    10.21
                ],
                [
                    10.12,
                    10.05,
                    9.97,
                    10.15
                ],
                [
                    10.08,
                    10.06,
                    10.03,
                    10.14
                ],
                [
                    10.1,
                    10.14,
                    9.97,
                    10.15
                ],
                [
                    10.17,
                    10.24,
                    10.15,
                    10.26
                ],
                [
                    10.28,
                    10.28,
                    10.18,
                    10.38
                ],
                [
                    10.24,
                    10.19,
                    10.16,
                    10.31
                ],
                [
                    10.19,
                    10.15,
                    10.13,
                    10.24
                ],
                [
                    10.18,
                    10.2,
                    10.1,
                    10.23
                ],
                [
                    10.24,
                    10.28,
                    10.22,
                    10.31
                ],
                [
                    10.29,
                    10.27,
                    10.22,
                    10.31
                ],
                [
                    10.28,
                    10.29,
                    10.25,
                    10.33
                ],
                [
                    10.29,
                    10.17,
                    10.16,
                    10.3
                ],
                [
                    10.19,
                    10.26,
                    10.15,
                    10.29
                ],
                [
                    10.24,
                    10.31,
                    10.24,
                    10.34
                ],
                [
                    10.3,
                    10.33,
                    10.28,
                    10.39
                ],
                [
                    10.3,
                    10.37,
                    10.28,
                    10.42
                ],
                [
                    10.35,
                    10.42,
                    10.31,
                    10.44
                ],
                [
                    10.42,
                    10.33,
                    10.31,
                    10.42
                ],
                [
                    10.26,
                    10.13,
                    10.07,
                    10.27
                ],
                [
                    10.14,
                    10.25,
                    10.14,
                    10.31
                ],
                [
                    10.28,
                    10.25,
                    10.23,
                    10.43
                ],
                [
                    10.27,
                    10.34,
                    10.18,
                    10.38
                ],
                [
                    10.34,
                    10.4,
                    10.28,
                    10.4
                ],
                [
                    10.4,
                    10.43,
                    10.34,
                    10.43
                ],
                [
                    10.43,
                    10.46,
                    10.41,
                    10.53
                ],
                [
                    10.5,
                    10.65,
                    10.44,
                    10.67
                ],
                [
                    10.7,
                    10.6,
                    10.59,
                    10.77
                ],
                [
                    10.65,
                    10.59,
                    10.54,
                    10.65
                ],
                [
                    10.64,
                    10.58,
                    10.48,
                    10.65
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -13.02
                ],
                [
                    "2025-02-05",
                    -10.16
                ],
                [
                    "2025-02-06",
                    -5.86
                ],
                [
                    "2025-02-07",
                    1.43
                ],
                [
                    "2025-02-10",
                    2.12
                ],
                [
                    "2025-02-11",
                    -4.52
                ],
                [
                    "2025-02-12",
                    1.45
                ],
                [
                    "2025-02-13",
                    0.60
                ],
                [
                    "2025-02-14",
                    -10.87
                ],
                [
                    "2025-02-17",
                    -3.34
                ],
                [
                    "2025-02-18",
                    -1.98
                ],
                [
                    "2025-02-19",
                    -2.12
                ],
                [
                    "2025-02-20",
                    4.18
                ],
                [
                    "2025-02-21",
                    -9.02
                ],
                [
                    "2025-02-24",
                    -5.87
                ],
                [
                    "2025-02-25",
                    7.21
                ],
                [
                    "2025-02-26",
                    3.73
                ],
                [
                    "2025-02-27",
                    4.22
                ],
                [
                    "2025-02-28",
                    -2.66
                ],
                [
                    "2025-03-03",
                    -14.06
                ],
                [
                    "2025-03-04",
                    -1.01
                ],
                [
                    "2025-03-05",
                    3.63
                ],
                [
                    "2025-03-06",
                    -9.73
                ],
                [
                    "2025-03-07",
                    -3.72
                ],
                [
                    "2025-03-10",
                    -4.78
                ],
                [
                    "2025-03-11",
                    -3.67
                ],
                [
                    "2025-03-12",
                    -4.70
                ],
                [
                    "2025-03-13",
                    -4.40
                ],
                [
                    "2025-03-14",
                    5.70
                ],
                [
                    "2025-03-17",
                    -13.67
                ],
                [
                    "2025-03-18",
                    1.27
                ],
                [
                    "2025-03-19",
                    -8.44
                ],
                [
                    "2025-03-20",
                    -12.41
                ],
                [
                    "2025-03-21",
                    -1.08
                ],
                [
                    "2025-03-24",
                    1.50
                ],
                [
                    "2025-03-25",
                    5.63
                ],
                [
                    "2025-03-26",
                    -3.60
                ],
                [
                    "2025-03-27",
                    -7.80
                ],
                [
                    "2025-03-28",
                    -1.45
                ],
                [
                    "2025-03-31",
                    -3.73
                ],
                [
                    "2025-04-01",
                    1.16
                ],
                [
                    "2025-04-02",
                    4.04
                ],
                [
                    "2025-04-03",
                    4.23
                ],
                [
                    "2025-04-07",
                    1.83
                ],
                [
                    "2025-04-08",
                    4.28
                ],
                [
                    "2025-04-09",
                    -0.07
                ],
                [
                    "2025-04-10",
                    1.39
                ],
                [
                    "2025-04-11",
                    2.95
                ],
                [
                    "2025-04-14",
                    0.02
                ],
                [
                    "2025-04-15",
                    -4.91
                ],
                [
                    "2025-04-16",
                    -8.60
                ],
                [
                    "2025-04-17",
                    2.49
                ],
                [
                    "2025-04-18",
                    -7.42
                ],
                [
                    "2025-04-21",
                    12.66
                ],
                [
                    "2025-04-22",
                    -2.07
                ],
                [
                    "2025-04-23",
                    5.30
                ],
                [
                    "2025-04-24",
                    -6.76
                ],
                [
                    "2025-04-25",
                    -4.79
                ],
                [
                    "2025-04-28",
                    1.33
                ],
                [
                    "2025-04-29",
                    2.44
                ],
                [
                    "2025-04-30",
                    -15.04
                ],
                [
                    "2025-05-06",
                    -4.86
                ],
                [
                    "2025-05-07",
                    -9.71
                ],
                [
                    "2025-05-08",
                    15.03
                ],
                [
                    "2025-05-09",
                    -3.71
                ],
                [
                    "2025-05-12",
                    -21.50
                ],
                [
                    "2025-05-13",
                    -0.66
                ],
                [
                    "2025-05-14",
                    3.29
                ],
                [
                    "2025-05-15",
                    0.23
                ],
                [
                    "2025-05-16",
                    13.23
                ],
                [
                    "2025-05-19",
                    -3.89
                ],
                [
                    "2025-05-20",
                    -2.87
                ],
                [
                    "2025-05-21",
                    -16.41
                ],
                [
                    "2025-05-22",
                    -13.74
                ],
                [
                    "2025-05-23",
                    4.62
                ],
                [
                    "2025-05-26",
                    -5.82
                ],
                [
                    "2025-05-27",
                    -9.83
                ],
                [
                    "2025-05-28",
                    16.20
                ],
                [
                    "2025-05-29",
                    -9.51
                ],
                [
                    "2025-05-30",
                    -1.26
                ],
                [
                    "2025-06-03",
                    -3.24
                ],
                [
                    "2025-06-04",
                    -7.06
                ],
                [
                    "2025-06-05",
                    4.31
                ],
                [
                    "2025-06-06",
                    -1.12
                ],
                [
                    "2025-06-09",
                    -3.29
                ],
                [
                    "2025-06-10",
                    -9.57
                ],
                [
                    "2025-06-11",
                    -4.99
                ],
                [
                    "2025-06-12",
                    -25.24
                ],
                [
                    "2025-06-13",
                    -8.01
                ],
                [
                    "2025-06-16",
                    5.27
                ],
                [
                    "2025-06-17",
                    -17.37
                ],
                [
                    "2025-06-18",
                    5.22
                ],
                [
                    "2025-06-19",
                    -1.14
                ],
                [
                    "2025-06-20",
                    10.19
                ],
                [
                    "2025-06-23",
                    8.53
                ],
                [
                    "2025-06-24",
                    8.80
                ],
                [
                    "2025-06-25",
                    1.70
                ],
                [
                    "2025-06-26",
                    -0.01
                ],
                [
                    "2025-06-27",
                    0.65
                ],
                [
                    "2025-06-30",
                    5.95
                ],
                [
                    "2025-07-01",
                    -11.97
                ],
                [
                    "2025-07-02",
                    -11.75
                ],
                [
                    "2025-07-03",
                    -8.75
                ],
                [
                    "2025-07-04",
                    -10.16
                ],
                [
                    "2025-07-07",
                    -9.21
                ],
                [
                    "2025-07-08",
                    -3.07
                ],
                [
                    "2025-07-09",
                    -1.23
                ],
                [
                    "2025-07-10",
                    -5.94
                ],
                [
                    "2025-07-11",
                    -1.00
                ],
                [
                    "2025-07-14",
                    -6.95
                ],
                [
                    "2025-07-15",
                    -11.98
                ],
                [
                    "2025-07-16",
                    -13.64
                ],
                [
                    "2025-07-17",
                    0.23
                ],
                [
                    "2025-07-18",
                    6.66
                ],
                [
                    "2025-07-21",
                    0.15
                ],
                [
                    "2025-07-22",
                    -4.60
                ],
                [
                    "2025-07-23",
                    7.41
                ],
                [
                    "2025-07-24",
                    -4.73
                ],
                [
                    "2025-07-25",
                    -0.21
                ],
                [
                    "2025-07-28",
                    -10.21
                ],
                [
                    "2025-07-29",
                    4.89
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    4.44
                ],
                [
                    "2025-02-05",
                    -2.07
                ],
                [
                    "2025-02-06",
                    1.13
                ],
                [
                    "2025-02-07",
                    11.96
                ],
                [
                    "2025-02-10",
                    6.67
                ],
                [
                    "2025-02-11",
                    0.19
                ],
                [
                    "2025-02-12",
                    0.73
                ],
                [
                    "2025-02-13",
                    -2.94
                ],
                [
                    "2025-02-14",
                    -1.31
                ],
                [
                    "2025-02-17",
                    2.14
                ],
                [
                    "2025-02-18",
                    -1.91
                ],
                [
                    "2025-02-19",
                    0.73
                ],
                [
                    "2025-02-20",
                    -1.44
                ],
                [
                    "2025-02-21",
                    1.93
                ],
                [
                    "2025-02-24",
                    -0.98
                ],
                [
                    "2025-02-25",
                    4.64
                ],
                [
                    "2025-02-26",
                    -4.10
                ],
                [
                    "2025-02-27",
                    0.49
                ],
                [
                    "2025-02-28",
                    -0.10
                ],
                [
                    "2025-03-03",
                    3.96
                ],
                [
                    "2025-03-04",
                    -2.53
                ],
                [
                    "2025-03-05",
                    -2.08
                ],
                [
                    "2025-03-06",
                    -3.29
                ],
                [
                    "2025-03-07",
                    -0.96
                ],
                [
                    "2025-03-10",
                    2.85
                ],
                [
                    "2025-03-11",
                    -6.17
                ],
                [
                    "2025-03-12",
                    0.39
                ],
                [
                    "2025-03-13",
                    7.97
                ],
                [
                    "2025-03-14",
                    -1.29
                ],
                [
                    "2025-03-17",
                    1.28
                ],
                [
                    "2025-03-18",
                    -6.14
                ],
                [
                    "2025-03-19",
                    1.80
                ],
                [
                    "2025-03-20",
                    -2.17
                ],
                [
                    "2025-03-21",
                    7.94
                ],
                [
                    "2025-03-24",
                    0.08
                ],
                [
                    "2025-03-25",
                    -1.98
                ],
                [
                    "2025-03-26",
                    1.23
                ],
                [
                    "2025-03-27",
                    -2.52
                ],
                [
                    "2025-03-28",
                    6.17
                ],
                [
                    "2025-03-31",
                    4.02
                ],
                [
                    "2025-04-01",
                    -3.68
                ],
                [
                    "2025-04-02",
                    -8.66
                ],
                [
                    "2025-04-03",
                    -4.14
                ],
                [
                    "2025-04-07",
                    7.66
                ],
                [
                    "2025-04-08",
                    -0.39
                ],
                [
                    "2025-04-09",
                    6.09
                ],
                [
                    "2025-04-10",
                    1.42
                ],
                [
                    "2025-04-11",
                    -1.37
                ],
                [
                    "2025-04-14",
                    -1.47
                ],
                [
                    "2025-04-15",
                    0.49
                ],
                [
                    "2025-04-16",
                    0.93
                ],
                [
                    "2025-04-17",
                    -3.84
                ],
                [
                    "2025-04-18",
                    1.51
                ],
                [
                    "2025-04-21",
                    -4.71
                ],
                [
                    "2025-04-22",
                    -2.62
                ],
                [
                    "2025-04-23",
                    -3.53
                ],
                [
                    "2025-04-24",
                    3.80
                ],
                [
                    "2025-04-25",
                    -1.73
                ],
                [
                    "2025-04-28",
                    2.35
                ],
                [
                    "2025-04-29",
                    -1.19
                ],
                [
                    "2025-04-30",
                    6.46
                ],
                [
                    "2025-05-06",
                    -6.04
                ],
                [
                    "2025-05-07",
                    1.13
                ],
                [
                    "2025-05-08",
                    0.91
                ],
                [
                    "2025-05-09",
                    -5.48
                ],
                [
                    "2025-05-12",
                    6.03
                ],
                [
                    "2025-05-13",
                    1.18
                ],
                [
                    "2025-05-14",
                    -3.02
                ],
                [
                    "2025-05-15",
                    -1.08
                ],
                [
                    "2025-05-16",
                    -7.24
                ],
                [
                    "2025-05-19",
                    2.04
                ],
                [
                    "2025-05-20",
                    -3.47
                ],
                [
                    "2025-05-21",
                    -6.83
                ],
                [
                    "2025-05-22",
                    -3.35
                ],
                [
                    "2025-05-23",
                    -5.33
                ],
                [
                    "2025-05-26",
                    7.22
                ],
                [
                    "2025-05-27",
                    3.70
                ],
                [
                    "2025-05-28",
                    -4.87
                ],
                [
                    "2025-05-29",
                    0.17
                ],
                [
                    "2025-05-30",
                    -2.55
                ],
                [
                    "2025-06-03",
                    7.46
                ],
                [
                    "2025-06-04",
                    -5.45
                ],
                [
                    "2025-06-05",
                    6.71
                ],
                [
                    "2025-06-06",
                    18.40
                ],
                [
                    "2025-06-09",
                    5.34
                ],
                [
                    "2025-06-10",
                    5.20
                ],
                [
                    "2025-06-11",
                    6.95
                ],
                [
                    "2025-06-12",
                    13.25
                ],
                [
                    "2025-06-13",
                    4.00
                ],
                [
                    "2025-06-16",
                    7.20
                ],
                [
                    "2025-06-17",
                    -0.38
                ],
                [
                    "2025-06-18",
                    5.34
                ],
                [
                    "2025-06-19",
                    9.14
                ],
                [
                    "2025-06-20",
                    10.25
                ],
                [
                    "2025-06-23",
                    9.49
                ],
                [
                    "2025-06-24",
                    3.33
                ],
                [
                    "2025-06-25",
                    0.45
                ],
                [
                    "2025-06-26",
                    9.04
                ],
                [
                    "2025-06-27",
                    -3.70
                ],
                [
                    "2025-06-30",
                    8.86
                ],
                [
                    "2025-07-01",
                    -10.35
                ],
                [
                    "2025-07-02",
                    6.26
                ],
                [
                    "2025-07-03",
                    -0.50
                ],
                [
                    "2025-07-04",
                    3.00
                ],
                [
                    "2025-07-07",
                    1.29
                ],
                [
                    "2025-07-08",
                    -8.99
                ],
                [
                    "2025-07-09",
                    -9.34
                ],
                [
                    "2025-07-10",
                    -8.74
                ],
                [
                    "2025-07-11",
                    -7.56
                ],
                [
                    "2025-07-14",
                    1.38
                ],
                [
                    "2025-07-15",
                    2.82
                ],
                [
                    "2025-07-16",
                    4.52
                ],
                [
                    "2025-07-17",
                    5.66
                ],
                [
                    "2025-07-18",
                    -0.96
                ],
                [
                    "2025-07-21",
                    -2.59
                ],
                [
                    "2025-07-22",
                    -3.55
                ],
                [
                    "2025-07-23",
                    -4.09
                ],
                [
                    "2025-07-24",
                    11.76
                ],
                [
                    "2025-07-25",
                    -5.50
                ],
                [
                    "2025-07-28",
                    10.55
                ],
                [
                    "2025-07-29",
                    -6.80
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    8.58
                ],
                [
                    "2025-02-05",
                    12.24
                ],
                [
                    "2025-02-06",
                    4.73
                ],
                [
                    "2025-02-07",
                    -13.39
                ],
                [
                    "2025-02-10",
                    -8.79
                ],
                [
                    "2025-02-11",
                    4.33
                ],
                [
                    "2025-02-12",
                    -2.18
                ],
                [
                    "2025-02-13",
                    2.33
                ],
                [
                    "2025-02-14",
                    12.18
                ],
                [
                    "2025-02-17",
                    1.20
                ],
                [
                    "2025-02-18",
                    3.88
                ],
                [
                    "2025-02-19",
                    1.39
                ],
                [
                    "2025-02-20",
                    -2.74
                ],
                [
                    "2025-02-21",
                    7.09
                ],
                [
                    "2025-02-24",
                    6.85
                ],
                [
                    "2025-02-25",
                    -11.85
                ],
                [
                    "2025-02-26",
                    0.36
                ],
                [
                    "2025-02-27",
                    -4.71
                ],
                [
                    "2025-02-28",
                    2.77
                ],
                [
                    "2025-03-03",
                    10.11
                ],
                [
                    "2025-03-04",
                    3.53
                ],
                [
                    "2025-03-05",
                    -1.56
                ],
                [
                    "2025-03-06",
                    13.03
                ],
                [
                    "2025-03-07",
                    4.67
                ],
                [
                    "2025-03-10",
                    1.94
                ],
                [
                    "2025-03-11",
                    9.83
                ],
                [
                    "2025-03-12",
                    4.31
                ],
                [
                    "2025-03-13",
                    -3.56
                ],
                [
                    "2025-03-14",
                    -4.41
                ],
                [
                    "2025-03-17",
                    12.39
                ],
                [
                    "2025-03-18",
                    4.87
                ],
                [
                    "2025-03-19",
                    6.63
                ],
                [
                    "2025-03-20",
                    14.57
                ],
                [
                    "2025-03-21",
                    -6.86
                ],
                [
                    "2025-03-24",
                    -1.58
                ],
                [
                    "2025-03-25",
                    -3.65
                ],
                [
                    "2025-03-26",
                    2.38
                ],
                [
                    "2025-03-27",
                    10.32
                ],
                [
                    "2025-03-28",
                    -4.71
                ],
                [
                    "2025-03-31",
                    -0.29
                ],
                [
                    "2025-04-01",
                    2.53
                ],
                [
                    "2025-04-02",
                    4.61
                ],
                [
                    "2025-04-03",
                    -0.10
                ],
                [
                    "2025-04-07",
                    -9.49
                ],
                [
                    "2025-04-08",
                    -3.89
                ],
                [
                    "2025-04-09",
                    -6.02
                ],
                [
                    "2025-04-10",
                    -2.80
                ],
                [
                    "2025-04-11",
                    -1.57
                ],
                [
                    "2025-04-14",
                    1.45
                ],
                [
                    "2025-04-15",
                    4.41
                ],
                [
                    "2025-04-16",
                    7.67
                ],
                [
                    "2025-04-17",
                    1.36
                ],
                [
                    "2025-04-18",
                    5.91
                ],
                [
                    "2025-04-21",
                    -7.95
                ],
                [
                    "2025-04-22",
                    4.70
                ],
                [
                    "2025-04-23",
                    -1.77
                ],
                [
                    "2025-04-24",
                    2.95
                ],
                [
                    "2025-04-25",
                    6.52
                ],
                [
                    "2025-04-28",
                    -3.68
                ],
                [
                    "2025-04-29",
                    -1.26
                ],
                [
                    "2025-04-30",
                    8.58
                ],
                [
                    "2025-05-06",
                    10.90
                ],
                [
                    "2025-05-07",
                    8.58
                ],
                [
                    "2025-05-08",
                    -15.95
                ],
                [
                    "2025-05-09",
                    9.19
                ],
                [
                    "2025-05-12",
                    15.47
                ],
                [
                    "2025-05-13",
                    -0.52
                ],
                [
                    "2025-05-14",
                    -0.27
                ],
                [
                    "2025-05-15",
                    0.85
                ],
                [
                    "2025-05-16",
                    -6.00
                ],
                [
                    "2025-05-19",
                    1.85
                ],
                [
                    "2025-05-20",
                    6.34
                ],
                [
                    "2025-05-21",
                    23.24
                ],
                [
                    "2025-05-22",
                    17.10
                ],
                [
                    "2025-05-23",
                    0.70
                ],
                [
                    "2025-05-26",
                    -1.40
                ],
                [
                    "2025-05-27",
                    6.12
                ],
                [
                    "2025-05-28",
                    -11.34
                ],
                [
                    "2025-05-29",
                    9.35
                ],
                [
                    "2025-05-30",
                    3.80
                ],
                [
                    "2025-06-03",
                    -4.22
                ],
                [
                    "2025-06-04",
                    12.51
                ],
                [
                    "2025-06-05",
                    -11.01
                ],
                [
                    "2025-06-06",
                    -17.28
                ],
                [
                    "2025-06-09",
                    -2.06
                ],
                [
                    "2025-06-10",
                    4.36
                ],
                [
                    "2025-06-11",
                    -1.96
                ],
                [
                    "2025-06-12",
                    11.99
                ],
                [
                    "2025-06-13",
                    4.00
                ],
                [
                    "2025-06-16",
                    -12.47
                ],
                [
                    "2025-06-17",
                    17.76
                ],
                [
                    "2025-06-18",
                    -10.56
                ],
                [
                    "2025-06-19",
                    -8.00
                ],
                [
                    "2025-06-20",
                    -20.43
                ],
                [
                    "2025-06-23",
                    -18.02
                ],
                [
                    "2025-06-24",
                    -12.12
                ],
                [
                    "2025-06-25",
                    -2.16
                ],
                [
                    "2025-06-26",
                    -9.03
                ],
                [
                    "2025-06-27",
                    3.05
                ],
                [
                    "2025-06-30",
                    -14.80
                ],
                [
                    "2025-07-01",
                    22.31
                ],
                [
                    "2025-07-02",
                    5.49
                ],
                [
                    "2025-07-03",
                    9.26
                ],
                [
                    "2025-07-04",
                    7.16
                ],
                [
                    "2025-07-07",
                    7.92
                ],
                [
                    "2025-07-08",
                    12.07
                ],
                [
                    "2025-07-09",
                    10.58
                ],
                [
                    "2025-07-10",
                    14.68
                ],
                [
                    "2025-07-11",
                    8.56
                ],
                [
                    "2025-07-14",
                    5.56
                ],
                [
                    "2025-07-15",
                    9.16
                ],
                [
                    "2025-07-16",
                    9.11
                ],
                [
                    "2025-07-17",
                    -5.89
                ],
                [
                    "2025-07-18",
                    -5.70
                ],
                [
                    "2025-07-21",
                    2.43
                ],
                [
                    "2025-07-22",
                    8.14
                ],
                [
                    "2025-07-23",
                    -3.32
                ],
                [
                    "2025-07-24",
                    -7.02
                ],
                [
                    "2025-07-25",
                    5.71
                ],
                [
                    "2025-07-28",
                    -0.34
                ],
                [
                    "2025-07-29",
                    1.91
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-13",
                    0.60
                ],
                [
                    "2025-02-26",
                    3.73
                ],
                [
                    "2025-02-27",
                    4.22
                ],
                [
                    "2025-02-28",
                    -2.66
                ],
                [
                    "2025-04-07",
                    1.83
                ],
                [
                    "2025-04-08",
                    4.28
                ],
                [
                    "2025-04-09",
                    -0.07
                ],
                [
                    "2025-04-10",
                    1.39
                ],
                [
                    "2025-04-11",
                    2.95
                ],
                [
                    "2025-04-14",
                    0.02
                ],
                [
                    "2025-05-19",
                    -3.89
                ],
                [
                    "2025-06-20",
                    10.19
                ],
                [
                    "2025-06-23",
                    8.53
                ],
                [
                    "2025-06-24",
                    8.80
                ],
                [
                    "2025-06-25",
                    1.70
                ],
                [
                    "2025-06-26",
                    -0.01
                ],
                [
                    "2025-06-27",
                    0.65
                ],
                [
                    "2025-06-30",
                    5.95
                ],
                [
                    "2025-07-23",
                    7.41
                ],
                [
                    "2025-07-24",
                    -4.73
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-11",
                    0.19
                ],
                [
                    "2025-02-12",
                    0.73
                ],
                [
                    "2025-02-13",
                    -2.94
                ],
                [
                    "2025-02-26",
                    -4.10
                ],
                [
                    "2025-02-27",
                    0.49
                ],
                [
                    "2025-03-03",
                    3.96
                ],
                [
                    "2025-04-09",
                    6.09
                ],
                [
                    "2025-04-10",
                    1.42
                ],
                [
                    "2025-04-11",
                    -1.37
                ],
                [
                    "2025-04-14",
                    -1.47
                ],
                [
                    "2025-04-15",
                    0.49
                ],
                [
                    "2025-06-06",
                    18.40
                ],
                [
                    "2025-06-09",
                    5.34
                ],
                [
                    "2025-06-10",
                    5.20
                ],
                [
                    "2025-06-11",
                    6.95
                ],
                [
                    "2025-06-12",
                    13.25
                ],
                [
                    "2025-06-19",
                    9.14
                ],
                [
                    "2025-06-20",
                    10.25
                ],
                [
                    "2025-06-23",
                    9.49
                ],
                [
                    "2025-06-24",
                    3.33
                ],
                [
                    "2025-06-25",
                    0.45
                ],
                [
                    "2025-06-26",
                    9.04
                ],
                [
                    "2025-06-27",
                    -3.70
                ],
                [
                    "2025-06-30",
                    8.86
                ],
                [
                    "2025-07-01",
                    -10.35
                ],
                [
                    "2025-07-24",
                    11.76
                ],
                [
                    "2025-07-29",
                    -6.80
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    -8.79
                ],
                [
                    "2025-02-14",
                    12.18
                ],
                [
                    "2025-02-17",
                    1.20
                ],
                [
                    "2025-02-18",
                    3.88
                ],
                [
                    "2025-02-19",
                    1.39
                ],
                [
                    "2025-02-20",
                    -2.74
                ],
                [
                    "2025-02-21",
                    7.09
                ],
                [
                    "2025-02-24",
                    6.85
                ],
                [
                    "2025-02-25",
                    -11.85
                ],
                [
                    "2025-03-04",
                    3.53
                ],
                [
                    "2025-03-05",
                    -1.56
                ],
                [
                    "2025-03-06",
                    13.03
                ],
                [
                    "2025-03-07",
                    4.67
                ],
                [
                    "2025-03-10",
                    1.94
                ],
                [
                    "2025-03-11",
                    9.83
                ],
                [
                    "2025-03-12",
                    4.31
                ],
                [
                    "2025-03-13",
                    -3.56
                ],
                [
                    "2025-03-14",
                    -4.41
                ],
                [
                    "2025-03-17",
                    12.39
                ],
                [
                    "2025-03-18",
                    4.87
                ],
                [
                    "2025-03-19",
                    6.63
                ],
                [
                    "2025-03-20",
                    14.57
                ],
                [
                    "2025-03-21",
                    -6.86
                ],
                [
                    "2025-03-24",
                    -1.58
                ],
                [
                    "2025-03-25",
                    -3.65
                ],
                [
                    "2025-03-26",
                    2.38
                ],
                [
                    "2025-03-27",
                    10.32
                ],
                [
                    "2025-03-28",
                    -4.71
                ],
                [
                    "2025-03-31",
                    -0.29
                ],
                [
                    "2025-04-01",
                    2.53
                ],
                [
                    "2025-04-02",
                    4.61
                ],
                [
                    "2025-04-16",
                    7.67
                ],
                [
                    "2025-04-17",
                    1.36
                ],
                [
                    "2025-04-18",
                    5.91
                ],
                [
                    "2025-04-21",
                    -7.95
                ],
                [
                    "2025-04-22",
                    4.70
                ],
                [
                    "2025-04-28",
                    -3.68
                ],
                [
                    "2025-04-29",
                    -1.26
                ],
                [
                    "2025-04-30",
                    8.58
                ],
                [
                    "2025-05-06",
                    10.90
                ],
                [
                    "2025-05-07",
                    8.58
                ],
                [
                    "2025-05-08",
                    -15.95
                ],
                [
                    "2025-05-09",
                    9.19
                ],
                [
                    "2025-05-12",
                    15.47
                ],
                [
                    "2025-05-13",
                    -0.52
                ],
                [
                    "2025-05-14",
                    -0.27
                ],
                [
                    "2025-05-15",
                    0.85
                ],
                [
                    "2025-05-16",
                    -6.00
                ],
                [
                    "2025-05-21",
                    23.24
                ],
                [
                    "2025-05-22",
                    17.10
                ],
                [
                    "2025-05-23",
                    0.70
                ],
                [
                    "2025-05-26",
                    -1.40
                ],
                [
                    "2025-05-27",
                    6.12
                ],
                [
                    "2025-05-28",
                    -11.34
                ],
                [
                    "2025-05-29",
                    9.35
                ],
                [
                    "2025-05-30",
                    3.80
                ],
                [
                    "2025-06-03",
                    -4.22
                ],
                [
                    "2025-06-04",
                    12.51
                ],
                [
                    "2025-06-05",
                    -11.01
                ],
                [
                    "2025-06-13",
                    4.00
                ],
                [
                    "2025-06-16",
                    -12.47
                ],
                [
                    "2025-06-17",
                    17.76
                ],
                [
                    "2025-06-18",
                    -10.56
                ],
                [
                    "2025-07-02",
                    5.49
                ],
                [
                    "2025-07-03",
                    9.26
                ],
                [
                    "2025-07-04",
                    7.16
                ],
                [
                    "2025-07-07",
                    7.92
                ],
                [
                    "2025-07-08",
                    12.07
                ],
                [
                    "2025-07-09",
                    10.58
                ],
                [
                    "2025-07-10",
                    14.68
                ],
                [
                    "2025-07-11",
                    8.56
                ],
                [
                    "2025-07-14",
                    5.56
                ],
                [
                    "2025-07-15",
                    9.16
                ],
                [
                    "2025-07-16",
                    9.11
                ],
                [
                    "2025-07-17",
                    -5.89
                ],
                [
                    "2025-07-18",
                    -5.70
                ],
                [
                    "2025-07-21",
                    2.43
                ],
                [
                    "2025-07-22",
                    8.14
                ],
                [
                    "2025-07-25",
                    5.71
                ],
                [
                    "2025-07-28",
                    -0.34
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603708 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_52184d64f29a42babfb884649541328d.setOption(option_52184d64f29a42babfb884649541328d);
            window.addEventListener('resize', function(){
                chart_52184d64f29a42babfb884649541328d.resize();
            })
    </script>
</body>
</html>
