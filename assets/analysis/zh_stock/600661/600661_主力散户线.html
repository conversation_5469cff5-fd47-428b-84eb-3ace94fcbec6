<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="ab37b2f1b6604e7e9d8b1b4dbfa2e584" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_ab37b2f1b6604e7e9d8b1b4dbfa2e584 = echarts.init(
            document.getElementById('ab37b2f1b6604e7e9d8b1b4dbfa2e584'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_ab37b2f1b6604e7e9d8b1b4dbfa2e584 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    10.72,
                    10.57,
                    10.55,
                    10.8
                ],
                [
                    10.61,
                    10.93,
                    10.59,
                    11.0
                ],
                [
                    10.75,
                    11.14,
                    10.68,
                    11.14
                ],
                [
                    11.08,
                    11.37,
                    11.07,
                    11.43
                ],
                [
                    11.39,
                    11.72,
                    11.38,
                    11.82
                ],
                [
                    11.74,
                    11.64,
                    11.5,
                    11.9
                ],
                [
                    11.7,
                    11.6,
                    11.54,
                    11.88
                ],
                [
                    11.66,
                    11.5,
                    11.44,
                    11.77
                ],
                [
                    11.49,
                    11.73,
                    11.31,
                    11.98
                ],
                [
                    12.12,
                    12.3,
                    12.02,
                    12.9
                ],
                [
                    12.0,
                    11.23,
                    11.2,
                    12.11
                ],
                [
                    11.22,
                    11.37,
                    11.22,
                    11.55
                ],
                [
                    11.33,
                    11.68,
                    11.28,
                    11.88
                ],
                [
                    11.61,
                    11.47,
                    11.18,
                    11.62
                ],
                [
                    11.37,
                    11.29,
                    11.15,
                    11.4
                ],
                [
                    11.16,
                    11.13,
                    11.05,
                    11.33
                ],
                [
                    11.14,
                    11.23,
                    11.06,
                    11.28
                ],
                [
                    11.23,
                    11.06,
                    10.86,
                    11.43
                ],
                [
                    11.0,
                    10.68,
                    10.63,
                    11.14
                ],
                [
                    10.84,
                    10.69,
                    10.63,
                    10.99
                ],
                [
                    10.63,
                    10.94,
                    10.55,
                    10.95
                ],
                [
                    10.91,
                    10.92,
                    10.78,
                    11.04
                ],
                [
                    10.98,
                    11.18,
                    10.91,
                    11.19
                ],
                [
                    11.19,
                    11.03,
                    10.97,
                    11.28
                ],
                [
                    11.04,
                    10.96,
                    10.9,
                    11.14
                ],
                [
                    10.78,
                    10.96,
                    10.72,
                    10.97
                ],
                [
                    10.99,
                    10.91,
                    10.91,
                    11.06
                ],
                [
                    10.86,
                    10.76,
                    10.6,
                    10.99
                ],
                [
                    10.82,
                    11.2,
                    10.68,
                    11.2
                ],
                [
                    11.28,
                    11.28,
                    11.24,
                    11.55
                ],
                [
                    11.38,
                    11.27,
                    11.23,
                    11.57
                ],
                [
                    11.27,
                    11.07,
                    11.04,
                    11.32
                ],
                [
                    11.05,
                    10.95,
                    10.91,
                    11.12
                ],
                [
                    10.9,
                    10.75,
                    10.68,
                    10.94
                ],
                [
                    10.72,
                    10.67,
                    10.46,
                    10.82
                ],
                [
                    10.66,
                    10.58,
                    10.4,
                    10.66
                ],
                [
                    10.54,
                    10.67,
                    10.45,
                    10.71
                ],
                [
                    10.68,
                    10.54,
                    10.36,
                    10.69
                ],
                [
                    10.52,
                    10.49,
                    10.44,
                    10.58
                ],
                [
                    10.52,
                    10.18,
                    10.08,
                    10.53
                ],
                [
                    10.18,
                    10.26,
                    10.18,
                    10.37
                ],
                [
                    10.27,
                    10.23,
                    10.21,
                    10.33
                ],
                [
                    10.17,
                    10.36,
                    10.13,
                    10.39
                ],
                [
                    9.61,
                    9.32,
                    9.32,
                    9.75
                ],
                [
                    9.28,
                    9.1,
                    8.92,
                    9.43
                ],
                [
                    9.0,
                    9.44,
                    8.43,
                    9.49
                ],
                [
                    9.48,
                    9.55,
                    9.46,
                    9.69
                ],
                [
                    9.44,
                    9.57,
                    9.4,
                    9.68
                ],
                [
                    9.59,
                    9.74,
                    9.59,
                    9.85
                ],
                [
                    9.8,
                    9.71,
                    9.64,
                    9.94
                ],
                [
                    9.64,
                    9.63,
                    9.47,
                    9.88
                ],
                [
                    9.44,
                    9.82,
                    9.44,
                    9.89
                ],
                [
                    9.72,
                    10.01,
                    9.65,
                    10.12
                ],
                [
                    9.99,
                    10.26,
                    9.95,
                    10.43
                ],
                [
                    10.25,
                    10.67,
                    10.25,
                    11.04
                ],
                [
                    10.6,
                    10.42,
                    10.4,
                    10.78
                ],
                [
                    10.49,
                    10.45,
                    10.33,
                    10.68
                ],
                [
                    10.35,
                    10.25,
                    10.24,
                    10.53
                ],
                [
                    10.23,
                    10.28,
                    10.04,
                    10.34
                ],
                [
                    10.26,
                    10.01,
                    9.94,
                    10.26
                ],
                [
                    10.11,
                    10.16,
                    10.0,
                    10.31
                ],
                [
                    10.32,
                    10.46,
                    10.22,
                    10.47
                ],
                [
                    10.51,
                    10.36,
                    10.27,
                    10.59
                ],
                [
                    10.34,
                    10.46,
                    10.28,
                    10.51
                ],
                [
                    10.5,
                    10.29,
                    10.27,
                    10.5
                ],
                [
                    10.34,
                    10.28,
                    10.11,
                    10.4
                ],
                [
                    10.39,
                    10.3,
                    10.18,
                    10.49
                ],
                [
                    10.3,
                    10.16,
                    10.11,
                    10.3
                ],
                [
                    10.13,
                    9.96,
                    9.88,
                    10.15
                ],
                [
                    9.94,
                    9.93,
                    9.87,
                    10.01
                ],
                [
                    9.93,
                    10.11,
                    9.85,
                    10.15
                ],
                [
                    10.08,
                    10.4,
                    9.98,
                    10.44
                ],
                [
                    10.4,
                    10.3,
                    10.16,
                    10.4
                ],
                [
                    10.22,
                    10.18,
                    10.12,
                    10.65
                ],
                [
                    10.18,
                    9.95,
                    9.94,
                    10.37
                ],
                [
                    10.06,
                    10.33,
                    9.99,
                    10.43
                ],
                [
                    10.3,
                    10.26,
                    10.08,
                    10.33
                ],
                [
                    10.21,
                    9.84,
                    9.79,
                    10.39
                ],
                [
                    9.8,
                    10.03,
                    9.66,
                    10.06
                ],
                [
                    10.04,
                    9.9,
                    9.87,
                    10.15
                ],
                [
                    9.99,
                    10.31,
                    9.91,
                    10.55
                ],
                [
                    10.29,
                    10.42,
                    10.15,
                    10.53
                ],
                [
                    10.48,
                    10.28,
                    10.21,
                    10.5
                ],
                [
                    10.33,
                    10.39,
                    10.23,
                    10.41
                ],
                [
                    10.51,
                    10.66,
                    10.37,
                    10.8
                ],
                [
                    10.64,
                    10.56,
                    10.37,
                    10.7
                ],
                [
                    10.52,
                    10.55,
                    10.49,
                    10.66
                ],
                [
                    10.5,
                    10.61,
                    10.41,
                    10.74
                ],
                [
                    10.58,
                    10.31,
                    10.28,
                    10.62
                ],
                [
                    10.27,
                    10.34,
                    10.26,
                    10.44
                ],
                [
                    10.42,
                    10.27,
                    10.19,
                    10.42
                ],
                [
                    10.18,
                    10.19,
                    10.13,
                    10.42
                ],
                [
                    10.17,
                    10.05,
                    10.0,
                    10.27
                ],
                [
                    10.05,
                    9.98,
                    9.94,
                    10.15
                ],
                [
                    9.9,
                    10.16,
                    9.86,
                    10.18
                ],
                [
                    10.18,
                    10.53,
                    10.16,
                    10.74
                ],
                [
                    10.53,
                    10.64,
                    10.5,
                    10.73
                ],
                [
                    10.62,
                    10.57,
                    10.5,
                    10.72
                ],
                [
                    10.54,
                    10.57,
                    10.49,
                    10.61
                ],
                [
                    10.56,
                    10.65,
                    10.51,
                    10.67
                ],
                [
                    10.67,
                    10.86,
                    10.67,
                    11.06
                ],
                [
                    10.86,
                    10.66,
                    10.58,
                    10.86
                ],
                [
                    10.65,
                    10.76,
                    10.62,
                    10.83
                ],
                [
                    10.77,
                    10.81,
                    10.71,
                    11.1
                ],
                [
                    10.77,
                    10.77,
                    10.64,
                    10.81
                ],
                [
                    10.76,
                    10.88,
                    10.7,
                    10.92
                ],
                [
                    11.09,
                    10.97,
                    10.88,
                    11.3
                ],
                [
                    10.92,
                    11.29,
                    10.92,
                    11.69
                ],
                [
                    11.29,
                    11.1,
                    11.01,
                    11.45
                ],
                [
                    11.22,
                    11.15,
                    10.95,
                    11.25
                ],
                [
                    11.18,
                    10.94,
                    10.82,
                    11.25
                ],
                [
                    10.94,
                    11.03,
                    10.75,
                    11.03
                ],
                [
                    10.98,
                    10.88,
                    10.81,
                    11.04
                ],
                [
                    10.88,
                    11.44,
                    10.84,
                    11.78
                ],
                [
                    11.37,
                    11.26,
                    11.18,
                    11.53
                ],
                [
                    11.25,
                    11.03,
                    10.98,
                    11.28
                ],
                [
                    11.0,
                    11.01,
                    10.93,
                    11.25
                ],
                [
                    11.0,
                    11.13,
                    10.95,
                    11.2
                ],
                [
                    11.2,
                    11.52,
                    11.13,
                    11.8
                ],
                [
                    11.85,
                    11.39,
                    11.31,
                    11.85
                ],
                [
                    11.68,
                    11.27,
                    11.11,
                    11.83
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -3.93
                ],
                [
                    "2025-02-05",
                    -9.69
                ],
                [
                    "2025-02-06",
                    0.71
                ],
                [
                    "2025-02-07",
                    -1.25
                ],
                [
                    "2025-02-10",
                    -9.22
                ],
                [
                    "2025-02-11",
                    -7.13
                ],
                [
                    "2025-02-12",
                    -3.86
                ],
                [
                    "2025-02-13",
                    -1.97
                ],
                [
                    "2025-02-14",
                    8.32
                ],
                [
                    "2025-02-17",
                    10.60
                ],
                [
                    "2025-02-18",
                    -17.22
                ],
                [
                    "2025-02-19",
                    -9.22
                ],
                [
                    "2025-02-20",
                    10.63
                ],
                [
                    "2025-02-21",
                    -9.99
                ],
                [
                    "2025-02-24",
                    -15.91
                ],
                [
                    "2025-02-25",
                    2.13
                ],
                [
                    "2025-02-26",
                    0.35
                ],
                [
                    "2025-02-27",
                    -6.15
                ],
                [
                    "2025-02-28",
                    -12.42
                ],
                [
                    "2025-03-03",
                    -0.69
                ],
                [
                    "2025-03-04",
                    1.09
                ],
                [
                    "2025-03-05",
                    -4.84
                ],
                [
                    "2025-03-06",
                    -7.33
                ],
                [
                    "2025-03-07",
                    -7.80
                ],
                [
                    "2025-03-10",
                    -14.61
                ],
                [
                    "2025-03-11",
                    -5.26
                ],
                [
                    "2025-03-12",
                    -5.75
                ],
                [
                    "2025-03-13",
                    -10.57
                ],
                [
                    "2025-03-14",
                    8.02
                ],
                [
                    "2025-03-17",
                    5.88
                ],
                [
                    "2025-03-18",
                    -0.77
                ],
                [
                    "2025-03-19",
                    -1.55
                ],
                [
                    "2025-03-20",
                    -4.83
                ],
                [
                    "2025-03-21",
                    -11.47
                ],
                [
                    "2025-03-24",
                    -8.63
                ],
                [
                    "2025-03-25",
                    -13.49
                ],
                [
                    "2025-03-26",
                    -4.93
                ],
                [
                    "2025-03-27",
                    -4.12
                ],
                [
                    "2025-03-28",
                    1.38
                ],
                [
                    "2025-03-31",
                    -18.53
                ],
                [
                    "2025-04-01",
                    -10.50
                ],
                [
                    "2025-04-02",
                    -2.24
                ],
                [
                    "2025-04-03",
                    2.25
                ],
                [
                    "2025-04-07",
                    15.14
                ],
                [
                    "2025-04-08",
                    -6.22
                ],
                [
                    "2025-04-09",
                    -3.98
                ],
                [
                    "2025-04-10",
                    -4.41
                ],
                [
                    "2025-04-11",
                    -5.65
                ],
                [
                    "2025-04-14",
                    0.13
                ],
                [
                    "2025-04-15",
                    -0.55
                ],
                [
                    "2025-04-16",
                    7.04
                ],
                [
                    "2025-04-17",
                    7.82
                ],
                [
                    "2025-04-18",
                    0.40
                ],
                [
                    "2025-04-21",
                    2.42
                ],
                [
                    "2025-04-22",
                    3.25
                ],
                [
                    "2025-04-23",
                    -13.05
                ],
                [
                    "2025-04-24",
                    8.07
                ],
                [
                    "2025-04-25",
                    -7.59
                ],
                [
                    "2025-04-28",
                    -7.10
                ],
                [
                    "2025-04-29",
                    -0.85
                ],
                [
                    "2025-04-30",
                    -10.44
                ],
                [
                    "2025-05-06",
                    -5.13
                ],
                [
                    "2025-05-07",
                    -5.30
                ],
                [
                    "2025-05-08",
                    -5.47
                ],
                [
                    "2025-05-09",
                    2.79
                ],
                [
                    "2025-05-12",
                    -2.85
                ],
                [
                    "2025-05-13",
                    9.11
                ],
                [
                    "2025-05-14",
                    -17.35
                ],
                [
                    "2025-05-15",
                    -12.45
                ],
                [
                    "2025-05-16",
                    -7.18
                ],
                [
                    "2025-05-19",
                    -0.37
                ],
                [
                    "2025-05-20",
                    -10.38
                ],
                [
                    "2025-05-21",
                    0.50
                ],
                [
                    "2025-05-22",
                    3.95
                ],
                [
                    "2025-05-23",
                    -1.01
                ],
                [
                    "2025-05-26",
                    1.05
                ],
                [
                    "2025-05-27",
                    -1.18
                ],
                [
                    "2025-05-28",
                    -1.61
                ],
                [
                    "2025-05-29",
                    0.25
                ],
                [
                    "2025-05-30",
                    -7.14
                ],
                [
                    "2025-06-03",
                    -4.78
                ],
                [
                    "2025-06-04",
                    1.32
                ],
                [
                    "2025-06-05",
                    -2.59
                ],
                [
                    "2025-06-06",
                    6.97
                ],
                [
                    "2025-06-09",
                    10.20
                ],
                [
                    "2025-06-10",
                    -3.62
                ],
                [
                    "2025-06-11",
                    -14.70
                ],
                [
                    "2025-06-12",
                    0.22
                ],
                [
                    "2025-06-13",
                    -9.83
                ],
                [
                    "2025-06-16",
                    -5.78
                ],
                [
                    "2025-06-17",
                    -0.54
                ],
                [
                    "2025-06-18",
                    8.50
                ],
                [
                    "2025-06-19",
                    -0.04
                ],
                [
                    "2025-06-20",
                    -9.62
                ],
                [
                    "2025-06-23",
                    -6.16
                ],
                [
                    "2025-06-24",
                    2.90
                ],
                [
                    "2025-06-25",
                    -3.10
                ],
                [
                    "2025-06-26",
                    -10.09
                ],
                [
                    "2025-06-27",
                    -11.28
                ],
                [
                    "2025-06-30",
                    -1.95
                ],
                [
                    "2025-07-01",
                    0.34
                ],
                [
                    "2025-07-02",
                    -8.71
                ],
                [
                    "2025-07-03",
                    8.98
                ],
                [
                    "2025-07-04",
                    4.97
                ],
                [
                    "2025-07-07",
                    -9.19
                ],
                [
                    "2025-07-08",
                    4.09
                ],
                [
                    "2025-07-09",
                    -3.02
                ],
                [
                    "2025-07-10",
                    16.56
                ],
                [
                    "2025-07-11",
                    -11.78
                ],
                [
                    "2025-07-14",
                    2.06
                ],
                [
                    "2025-07-15",
                    -17.78
                ],
                [
                    "2025-07-16",
                    -10.10
                ],
                [
                    "2025-07-17",
                    -28.59
                ],
                [
                    "2025-07-18",
                    10.03
                ],
                [
                    "2025-07-21",
                    -8.84
                ],
                [
                    "2025-07-22",
                    -6.23
                ],
                [
                    "2025-07-23",
                    10.20
                ],
                [
                    "2025-07-24",
                    -3.37
                ],
                [
                    "2025-07-25",
                    -5.94
                ],
                [
                    "2025-07-28",
                    -12.93
                ],
                [
                    "2025-07-29",
                    -2.14
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    5.38
                ],
                [
                    "2025-02-05",
                    1.69
                ],
                [
                    "2025-02-06",
                    1.53
                ],
                [
                    "2025-02-07",
                    -4.81
                ],
                [
                    "2025-02-10",
                    -0.36
                ],
                [
                    "2025-02-11",
                    -3.26
                ],
                [
                    "2025-02-12",
                    -17.65
                ],
                [
                    "2025-02-13",
                    -9.28
                ],
                [
                    "2025-02-14",
                    -9.75
                ],
                [
                    "2025-02-17",
                    0.94
                ],
                [
                    "2025-02-18",
                    3.61
                ],
                [
                    "2025-02-19",
                    0.29
                ],
                [
                    "2025-02-20",
                    -1.19
                ],
                [
                    "2025-02-21",
                    4.31
                ],
                [
                    "2025-02-24",
                    4.55
                ],
                [
                    "2025-02-25",
                    0.44
                ],
                [
                    "2025-02-26",
                    3.74
                ],
                [
                    "2025-02-27",
                    1.74
                ],
                [
                    "2025-02-28",
                    6.56
                ],
                [
                    "2025-03-03",
                    0.61
                ],
                [
                    "2025-03-04",
                    -5.39
                ],
                [
                    "2025-03-05",
                    7.31
                ],
                [
                    "2025-03-06",
                    -1.58
                ],
                [
                    "2025-03-07",
                    2.05
                ],
                [
                    "2025-03-10",
                    2.78
                ],
                [
                    "2025-03-11",
                    1.67
                ],
                [
                    "2025-03-12",
                    6.76
                ],
                [
                    "2025-03-13",
                    2.74
                ],
                [
                    "2025-03-14",
                    -7.83
                ],
                [
                    "2025-03-17",
                    -4.91
                ],
                [
                    "2025-03-18",
                    -5.47
                ],
                [
                    "2025-03-19",
                    -5.89
                ],
                [
                    "2025-03-20",
                    3.88
                ],
                [
                    "2025-03-21",
                    4.68
                ],
                [
                    "2025-03-24",
                    16.05
                ],
                [
                    "2025-03-25",
                    5.52
                ],
                [
                    "2025-03-26",
                    1.85
                ],
                [
                    "2025-03-27",
                    3.97
                ],
                [
                    "2025-03-28",
                    2.93
                ],
                [
                    "2025-03-31",
                    5.49
                ],
                [
                    "2025-04-01",
                    -4.25
                ],
                [
                    "2025-04-02",
                    9.66
                ],
                [
                    "2025-04-03",
                    3.11
                ],
                [
                    "2025-04-07",
                    -2.00
                ],
                [
                    "2025-04-08",
                    7.33
                ],
                [
                    "2025-04-09",
                    -6.71
                ],
                [
                    "2025-04-10",
                    -1.43
                ],
                [
                    "2025-04-11",
                    -0.99
                ],
                [
                    "2025-04-14",
                    -5.74
                ],
                [
                    "2025-04-15",
                    4.07
                ],
                [
                    "2025-04-16",
                    -1.01
                ],
                [
                    "2025-04-17",
                    -18.41
                ],
                [
                    "2025-04-18",
                    -4.93
                ],
                [
                    "2025-04-21",
                    7.09
                ],
                [
                    "2025-04-22",
                    -4.30
                ],
                [
                    "2025-04-23",
                    -4.72
                ],
                [
                    "2025-04-24",
                    -2.91
                ],
                [
                    "2025-04-25",
                    3.77
                ],
                [
                    "2025-04-28",
                    -0.02
                ],
                [
                    "2025-04-29",
                    -9.14
                ],
                [
                    "2025-04-30",
                    1.55
                ],
                [
                    "2025-05-06",
                    6.34
                ],
                [
                    "2025-05-07",
                    0.65
                ],
                [
                    "2025-05-08",
                    0.25
                ],
                [
                    "2025-05-09",
                    -0.14
                ],
                [
                    "2025-05-12",
                    1.34
                ],
                [
                    "2025-05-13",
                    1.09
                ],
                [
                    "2025-05-14",
                    11.27
                ],
                [
                    "2025-05-15",
                    -0.99
                ],
                [
                    "2025-05-16",
                    -15.59
                ],
                [
                    "2025-05-19",
                    1.09
                ],
                [
                    "2025-05-20",
                    6.49
                ],
                [
                    "2025-05-21",
                    8.62
                ],
                [
                    "2025-05-22",
                    0.38
                ],
                [
                    "2025-05-23",
                    0.37
                ],
                [
                    "2025-05-26",
                    -3.91
                ],
                [
                    "2025-05-27",
                    -3.19
                ],
                [
                    "2025-05-28",
                    6.08
                ],
                [
                    "2025-05-29",
                    4.90
                ],
                [
                    "2025-05-30",
                    2.63
                ],
                [
                    "2025-06-03",
                    4.08
                ],
                [
                    "2025-06-04",
                    4.11
                ],
                [
                    "2025-06-05",
                    -0.07
                ],
                [
                    "2025-06-06",
                    1.66
                ],
                [
                    "2025-06-09",
                    0.74
                ],
                [
                    "2025-06-10",
                    -4.79
                ],
                [
                    "2025-06-11",
                    0.18
                ],
                [
                    "2025-06-12",
                    -2.73
                ],
                [
                    "2025-06-13",
                    1.38
                ],
                [
                    "2025-06-16",
                    -2.83
                ],
                [
                    "2025-06-17",
                    -5.70
                ],
                [
                    "2025-06-18",
                    -1.93
                ],
                [
                    "2025-06-19",
                    -7.62
                ],
                [
                    "2025-06-20",
                    0.29
                ],
                [
                    "2025-06-23",
                    -2.27
                ],
                [
                    "2025-06-24",
                    -3.70
                ],
                [
                    "2025-06-25",
                    -4.24
                ],
                [
                    "2025-06-26",
                    -4.12
                ],
                [
                    "2025-06-27",
                    4.23
                ],
                [
                    "2025-06-30",
                    -6.44
                ],
                [
                    "2025-07-01",
                    3.28
                ],
                [
                    "2025-07-02",
                    -2.93
                ],
                [
                    "2025-07-03",
                    -9.81
                ],
                [
                    "2025-07-04",
                    5.19
                ],
                [
                    "2025-07-07",
                    3.89
                ],
                [
                    "2025-07-08",
                    -6.54
                ],
                [
                    "2025-07-09",
                    5.14
                ],
                [
                    "2025-07-10",
                    -1.57
                ],
                [
                    "2025-07-11",
                    1.67
                ],
                [
                    "2025-07-14",
                    -0.82
                ],
                [
                    "2025-07-15",
                    5.49
                ],
                [
                    "2025-07-16",
                    -3.07
                ],
                [
                    "2025-07-17",
                    9.15
                ],
                [
                    "2025-07-18",
                    -1.00
                ],
                [
                    "2025-07-21",
                    1.37
                ],
                [
                    "2025-07-22",
                    10.58
                ],
                [
                    "2025-07-23",
                    4.05
                ],
                [
                    "2025-07-24",
                    12.56
                ],
                [
                    "2025-07-25",
                    -1.94
                ],
                [
                    "2025-07-28",
                    5.22
                ],
                [
                    "2025-07-29",
                    7.28
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -1.44
                ],
                [
                    "2025-02-05",
                    8.01
                ],
                [
                    "2025-02-06",
                    -2.25
                ],
                [
                    "2025-02-07",
                    6.06
                ],
                [
                    "2025-02-10",
                    9.57
                ],
                [
                    "2025-02-11",
                    10.40
                ],
                [
                    "2025-02-12",
                    21.52
                ],
                [
                    "2025-02-13",
                    11.24
                ],
                [
                    "2025-02-14",
                    1.43
                ],
                [
                    "2025-02-17",
                    -11.54
                ],
                [
                    "2025-02-18",
                    13.62
                ],
                [
                    "2025-02-19",
                    8.93
                ],
                [
                    "2025-02-20",
                    -9.44
                ],
                [
                    "2025-02-21",
                    5.68
                ],
                [
                    "2025-02-24",
                    11.37
                ],
                [
                    "2025-02-25",
                    -2.58
                ],
                [
                    "2025-02-26",
                    -4.09
                ],
                [
                    "2025-02-27",
                    4.41
                ],
                [
                    "2025-02-28",
                    5.87
                ],
                [
                    "2025-03-03",
                    0.08
                ],
                [
                    "2025-03-04",
                    4.30
                ],
                [
                    "2025-03-05",
                    -2.48
                ],
                [
                    "2025-03-06",
                    8.90
                ],
                [
                    "2025-03-07",
                    5.75
                ],
                [
                    "2025-03-10",
                    11.83
                ],
                [
                    "2025-03-11",
                    3.59
                ],
                [
                    "2025-03-12",
                    -1.01
                ],
                [
                    "2025-03-13",
                    7.83
                ],
                [
                    "2025-03-14",
                    -0.19
                ],
                [
                    "2025-03-17",
                    -0.97
                ],
                [
                    "2025-03-18",
                    6.24
                ],
                [
                    "2025-03-19",
                    7.44
                ],
                [
                    "2025-03-20",
                    0.95
                ],
                [
                    "2025-03-21",
                    6.79
                ],
                [
                    "2025-03-24",
                    -7.42
                ],
                [
                    "2025-03-25",
                    7.97
                ],
                [
                    "2025-03-26",
                    3.08
                ],
                [
                    "2025-03-27",
                    0.15
                ],
                [
                    "2025-03-28",
                    -4.30
                ],
                [
                    "2025-03-31",
                    13.04
                ],
                [
                    "2025-04-01",
                    14.75
                ],
                [
                    "2025-04-02",
                    -7.43
                ],
                [
                    "2025-04-03",
                    -5.36
                ],
                [
                    "2025-04-07",
                    -13.14
                ],
                [
                    "2025-04-08",
                    -1.11
                ],
                [
                    "2025-04-09",
                    10.69
                ],
                [
                    "2025-04-10",
                    5.84
                ],
                [
                    "2025-04-11",
                    6.64
                ],
                [
                    "2025-04-14",
                    5.61
                ],
                [
                    "2025-04-15",
                    -3.52
                ],
                [
                    "2025-04-16",
                    -6.03
                ],
                [
                    "2025-04-17",
                    10.59
                ],
                [
                    "2025-04-18",
                    4.52
                ],
                [
                    "2025-04-21",
                    -9.50
                ],
                [
                    "2025-04-22",
                    1.05
                ],
                [
                    "2025-04-23",
                    17.77
                ],
                [
                    "2025-04-24",
                    -5.16
                ],
                [
                    "2025-04-25",
                    3.82
                ],
                [
                    "2025-04-28",
                    7.11
                ],
                [
                    "2025-04-29",
                    9.99
                ],
                [
                    "2025-04-30",
                    8.89
                ],
                [
                    "2025-05-06",
                    -1.21
                ],
                [
                    "2025-05-07",
                    4.65
                ],
                [
                    "2025-05-08",
                    5.22
                ],
                [
                    "2025-05-09",
                    -2.65
                ],
                [
                    "2025-05-12",
                    1.51
                ],
                [
                    "2025-05-13",
                    -10.20
                ],
                [
                    "2025-05-14",
                    6.08
                ],
                [
                    "2025-05-15",
                    13.44
                ],
                [
                    "2025-05-16",
                    22.77
                ],
                [
                    "2025-05-19",
                    -0.73
                ],
                [
                    "2025-05-20",
                    3.89
                ],
                [
                    "2025-05-21",
                    -9.12
                ],
                [
                    "2025-05-22",
                    -4.33
                ],
                [
                    "2025-05-23",
                    0.65
                ],
                [
                    "2025-05-26",
                    2.86
                ],
                [
                    "2025-05-27",
                    4.38
                ],
                [
                    "2025-05-28",
                    -4.47
                ],
                [
                    "2025-05-29",
                    -5.16
                ],
                [
                    "2025-05-30",
                    4.52
                ],
                [
                    "2025-06-03",
                    0.70
                ],
                [
                    "2025-06-04",
                    -5.43
                ],
                [
                    "2025-06-05",
                    2.65
                ],
                [
                    "2025-06-06",
                    -8.63
                ],
                [
                    "2025-06-09",
                    -10.94
                ],
                [
                    "2025-06-10",
                    8.41
                ],
                [
                    "2025-06-11",
                    14.53
                ],
                [
                    "2025-06-12",
                    2.51
                ],
                [
                    "2025-06-13",
                    8.45
                ],
                [
                    "2025-06-16",
                    8.60
                ],
                [
                    "2025-06-17",
                    6.24
                ],
                [
                    "2025-06-18",
                    -6.57
                ],
                [
                    "2025-06-19",
                    7.66
                ],
                [
                    "2025-06-20",
                    9.33
                ],
                [
                    "2025-06-23",
                    8.43
                ],
                [
                    "2025-06-24",
                    0.79
                ],
                [
                    "2025-06-25",
                    7.34
                ],
                [
                    "2025-06-26",
                    14.21
                ],
                [
                    "2025-06-27",
                    7.05
                ],
                [
                    "2025-06-30",
                    8.38
                ],
                [
                    "2025-07-01",
                    -3.62
                ],
                [
                    "2025-07-02",
                    11.64
                ],
                [
                    "2025-07-03",
                    0.83
                ],
                [
                    "2025-07-04",
                    -10.15
                ],
                [
                    "2025-07-07",
                    5.30
                ],
                [
                    "2025-07-08",
                    2.44
                ],
                [
                    "2025-07-09",
                    -2.13
                ],
                [
                    "2025-07-10",
                    -15.00
                ],
                [
                    "2025-07-11",
                    10.11
                ],
                [
                    "2025-07-14",
                    -1.24
                ],
                [
                    "2025-07-15",
                    12.28
                ],
                [
                    "2025-07-16",
                    13.18
                ],
                [
                    "2025-07-17",
                    19.44
                ],
                [
                    "2025-07-18",
                    -9.03
                ],
                [
                    "2025-07-21",
                    7.47
                ],
                [
                    "2025-07-22",
                    -4.35
                ],
                [
                    "2025-07-23",
                    -14.26
                ],
                [
                    "2025-07-24",
                    -9.19
                ],
                [
                    "2025-07-25",
                    7.88
                ],
                [
                    "2025-07-28",
                    7.72
                ],
                [
                    "2025-07-29",
                    -5.14
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-04-09",
                    -3.98
                ],
                [
                    "2025-04-10",
                    -4.41
                ],
                [
                    "2025-04-21",
                    2.42
                ],
                [
                    "2025-05-27",
                    -1.18
                ],
                [
                    "2025-05-28",
                    -1.61
                ],
                [
                    "2025-06-09",
                    10.20
                ],
                [
                    "2025-06-10",
                    -3.62
                ],
                [
                    "2025-07-09",
                    -3.02
                ],
                [
                    "2025-07-10",
                    16.56
                ],
                [
                    "2025-07-14",
                    2.06
                ],
                [
                    "2025-07-24",
                    -3.37
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-28",
                    2.93
                ],
                [
                    "2025-04-08",
                    7.33
                ],
                [
                    "2025-04-09",
                    -6.71
                ],
                [
                    "2025-04-10",
                    -1.43
                ],
                [
                    "2025-05-13",
                    1.09
                ],
                [
                    "2025-05-14",
                    11.27
                ],
                [
                    "2025-05-23",
                    0.37
                ],
                [
                    "2025-05-26",
                    -3.91
                ],
                [
                    "2025-05-27",
                    -3.19
                ],
                [
                    "2025-05-29",
                    4.90
                ],
                [
                    "2025-06-03",
                    4.08
                ],
                [
                    "2025-06-04",
                    4.11
                ],
                [
                    "2025-06-05",
                    -0.07
                ],
                [
                    "2025-06-06",
                    1.66
                ],
                [
                    "2025-06-09",
                    0.74
                ],
                [
                    "2025-06-10",
                    -4.79
                ],
                [
                    "2025-07-10",
                    -1.57
                ],
                [
                    "2025-07-23",
                    4.05
                ],
                [
                    "2025-07-24",
                    12.56
                ],
                [
                    "2025-07-25",
                    -1.94
                ],
                [
                    "2025-07-28",
                    5.22
                ],
                [
                    "2025-07-29",
                    7.28
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    9.57
                ],
                [
                    "2025-02-11",
                    10.40
                ],
                [
                    "2025-02-12",
                    21.52
                ],
                [
                    "2025-02-13",
                    11.24
                ],
                [
                    "2025-02-14",
                    1.43
                ],
                [
                    "2025-02-18",
                    13.62
                ],
                [
                    "2025-02-19",
                    8.93
                ],
                [
                    "2025-02-21",
                    5.68
                ],
                [
                    "2025-02-24",
                    11.37
                ],
                [
                    "2025-02-25",
                    -2.58
                ],
                [
                    "2025-02-26",
                    -4.09
                ],
                [
                    "2025-02-27",
                    4.41
                ],
                [
                    "2025-02-28",
                    5.87
                ],
                [
                    "2025-03-03",
                    0.08
                ],
                [
                    "2025-03-04",
                    4.30
                ],
                [
                    "2025-03-05",
                    -2.48
                ],
                [
                    "2025-03-06",
                    8.90
                ],
                [
                    "2025-03-07",
                    5.75
                ],
                [
                    "2025-03-10",
                    11.83
                ],
                [
                    "2025-03-11",
                    3.59
                ],
                [
                    "2025-03-12",
                    -1.01
                ],
                [
                    "2025-03-13",
                    7.83
                ],
                [
                    "2025-03-14",
                    -0.19
                ],
                [
                    "2025-03-17",
                    -0.97
                ],
                [
                    "2025-03-18",
                    6.24
                ],
                [
                    "2025-03-21",
                    6.79
                ],
                [
                    "2025-03-24",
                    -7.42
                ],
                [
                    "2025-03-25",
                    7.97
                ],
                [
                    "2025-03-26",
                    3.08
                ],
                [
                    "2025-03-27",
                    0.15
                ],
                [
                    "2025-03-31",
                    13.04
                ],
                [
                    "2025-04-01",
                    14.75
                ],
                [
                    "2025-04-02",
                    -7.43
                ],
                [
                    "2025-04-03",
                    -5.36
                ],
                [
                    "2025-04-07",
                    -13.14
                ],
                [
                    "2025-04-11",
                    6.64
                ],
                [
                    "2025-04-14",
                    5.61
                ],
                [
                    "2025-04-15",
                    -3.52
                ],
                [
                    "2025-04-16",
                    -6.03
                ],
                [
                    "2025-04-25",
                    3.82
                ],
                [
                    "2025-04-28",
                    7.11
                ],
                [
                    "2025-04-29",
                    9.99
                ],
                [
                    "2025-04-30",
                    8.89
                ],
                [
                    "2025-05-06",
                    -1.21
                ],
                [
                    "2025-05-07",
                    4.65
                ],
                [
                    "2025-05-08",
                    5.22
                ],
                [
                    "2025-05-09",
                    -2.65
                ],
                [
                    "2025-05-12",
                    1.51
                ],
                [
                    "2025-05-15",
                    13.44
                ],
                [
                    "2025-05-16",
                    22.77
                ],
                [
                    "2025-05-19",
                    -0.73
                ],
                [
                    "2025-05-20",
                    3.89
                ],
                [
                    "2025-05-21",
                    -9.12
                ],
                [
                    "2025-05-22",
                    -4.33
                ],
                [
                    "2025-05-30",
                    4.52
                ],
                [
                    "2025-06-11",
                    14.53
                ],
                [
                    "2025-06-12",
                    2.51
                ],
                [
                    "2025-06-13",
                    8.45
                ],
                [
                    "2025-06-16",
                    8.60
                ],
                [
                    "2025-06-17",
                    6.24
                ],
                [
                    "2025-06-18",
                    -6.57
                ],
                [
                    "2025-06-19",
                    7.66
                ],
                [
                    "2025-06-20",
                    9.33
                ],
                [
                    "2025-06-23",
                    8.43
                ],
                [
                    "2025-06-24",
                    0.79
                ],
                [
                    "2025-06-25",
                    7.34
                ],
                [
                    "2025-06-26",
                    14.21
                ],
                [
                    "2025-06-27",
                    7.05
                ],
                [
                    "2025-06-30",
                    8.38
                ],
                [
                    "2025-07-01",
                    -3.62
                ],
                [
                    "2025-07-02",
                    11.64
                ],
                [
                    "2025-07-03",
                    0.83
                ],
                [
                    "2025-07-07",
                    5.30
                ],
                [
                    "2025-07-11",
                    10.11
                ],
                [
                    "2025-07-15",
                    12.28
                ],
                [
                    "2025-07-16",
                    13.18
                ],
                [
                    "2025-07-17",
                    19.44
                ],
                [
                    "2025-07-18",
                    -9.03
                ],
                [
                    "2025-07-21",
                    7.47
                ],
                [
                    "2025-07-22",
                    -4.35
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600661 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_ab37b2f1b6604e7e9d8b1b4dbfa2e584.setOption(option_ab37b2f1b6604e7e9d8b1b4dbfa2e584);
            window.addEventListener('resize', function(){
                chart_ab37b2f1b6604e7e9d8b1b4dbfa2e584.resize();
            })
    </script>
</body>
</html>
