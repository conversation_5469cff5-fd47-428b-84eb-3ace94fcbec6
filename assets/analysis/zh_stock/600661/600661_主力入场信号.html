<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="3fec746058884188b22afa27302baf62" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_3fec746058884188b22afa27302baf62 = echarts.init(
            document.getElementById('3fec746058884188b22afa27302baf62'), 'white', {renderer: 'canvas'});
        var option_3fec746058884188b22afa27302baf62 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    10.72,
                    10.57,
                    10.55,
                    10.8
                ],
                [
                    10.61,
                    10.93,
                    10.59,
                    11.0
                ],
                [
                    10.75,
                    11.14,
                    10.68,
                    11.14
                ],
                [
                    11.08,
                    11.37,
                    11.07,
                    11.43
                ],
                [
                    11.39,
                    11.72,
                    11.38,
                    11.82
                ],
                [
                    11.74,
                    11.64,
                    11.5,
                    11.9
                ],
                [
                    11.7,
                    11.6,
                    11.54,
                    11.88
                ],
                [
                    11.66,
                    11.5,
                    11.44,
                    11.77
                ],
                [
                    11.49,
                    11.73,
                    11.31,
                    11.98
                ],
                [
                    12.12,
                    12.3,
                    12.02,
                    12.9
                ],
                [
                    12.0,
                    11.23,
                    11.2,
                    12.11
                ],
                [
                    11.22,
                    11.37,
                    11.22,
                    11.55
                ],
                [
                    11.33,
                    11.68,
                    11.28,
                    11.88
                ],
                [
                    11.61,
                    11.47,
                    11.18,
                    11.62
                ],
                [
                    11.37,
                    11.29,
                    11.15,
                    11.4
                ],
                [
                    11.16,
                    11.13,
                    11.05,
                    11.33
                ],
                [
                    11.14,
                    11.23,
                    11.06,
                    11.28
                ],
                [
                    11.23,
                    11.06,
                    10.86,
                    11.43
                ],
                [
                    11.0,
                    10.68,
                    10.63,
                    11.14
                ],
                [
                    10.84,
                    10.69,
                    10.63,
                    10.99
                ],
                [
                    10.63,
                    10.94,
                    10.55,
                    10.95
                ],
                [
                    10.91,
                    10.92,
                    10.78,
                    11.04
                ],
                [
                    10.98,
                    11.18,
                    10.91,
                    11.19
                ],
                [
                    11.19,
                    11.03,
                    10.97,
                    11.28
                ],
                [
                    11.04,
                    10.96,
                    10.9,
                    11.14
                ],
                [
                    10.78,
                    10.96,
                    10.72,
                    10.97
                ],
                [
                    10.99,
                    10.91,
                    10.91,
                    11.06
                ],
                [
                    10.86,
                    10.76,
                    10.6,
                    10.99
                ],
                [
                    10.82,
                    11.2,
                    10.68,
                    11.2
                ],
                [
                    11.28,
                    11.28,
                    11.24,
                    11.55
                ],
                [
                    11.38,
                    11.27,
                    11.23,
                    11.57
                ],
                [
                    11.27,
                    11.07,
                    11.04,
                    11.32
                ],
                [
                    11.05,
                    10.95,
                    10.91,
                    11.12
                ],
                [
                    10.9,
                    10.75,
                    10.68,
                    10.94
                ],
                [
                    10.72,
                    10.67,
                    10.46,
                    10.82
                ],
                [
                    10.66,
                    10.58,
                    10.4,
                    10.66
                ],
                [
                    10.54,
                    10.67,
                    10.45,
                    10.71
                ],
                [
                    10.68,
                    10.54,
                    10.36,
                    10.69
                ],
                [
                    10.52,
                    10.49,
                    10.44,
                    10.58
                ],
                [
                    10.52,
                    10.18,
                    10.08,
                    10.53
                ],
                [
                    10.18,
                    10.26,
                    10.18,
                    10.37
                ],
                [
                    10.27,
                    10.23,
                    10.21,
                    10.33
                ],
                [
                    10.17,
                    10.36,
                    10.13,
                    10.39
                ],
                [
                    9.61,
                    9.32,
                    9.32,
                    9.75
                ],
                [
                    9.28,
                    9.1,
                    8.92,
                    9.43
                ],
                [
                    9.0,
                    9.44,
                    8.43,
                    9.49
                ],
                [
                    9.48,
                    9.55,
                    9.46,
                    9.69
                ],
                [
                    9.44,
                    9.57,
                    9.4,
                    9.68
                ],
                [
                    9.59,
                    9.74,
                    9.59,
                    9.85
                ],
                [
                    9.8,
                    9.71,
                    9.64,
                    9.94
                ],
                [
                    9.64,
                    9.63,
                    9.47,
                    9.88
                ],
                [
                    9.44,
                    9.82,
                    9.44,
                    9.89
                ],
                [
                    9.72,
                    10.01,
                    9.65,
                    10.12
                ],
                [
                    9.99,
                    10.26,
                    9.95,
                    10.43
                ],
                [
                    10.25,
                    10.67,
                    10.25,
                    11.04
                ],
                [
                    10.6,
                    10.42,
                    10.4,
                    10.78
                ],
                [
                    10.49,
                    10.45,
                    10.33,
                    10.68
                ],
                [
                    10.35,
                    10.25,
                    10.24,
                    10.53
                ],
                [
                    10.23,
                    10.28,
                    10.04,
                    10.34
                ],
                [
                    10.26,
                    10.01,
                    9.94,
                    10.26
                ],
                [
                    10.11,
                    10.16,
                    10.0,
                    10.31
                ],
                [
                    10.32,
                    10.46,
                    10.22,
                    10.47
                ],
                [
                    10.51,
                    10.36,
                    10.27,
                    10.59
                ],
                [
                    10.34,
                    10.46,
                    10.28,
                    10.51
                ],
                [
                    10.5,
                    10.29,
                    10.27,
                    10.5
                ],
                [
                    10.34,
                    10.28,
                    10.11,
                    10.4
                ],
                [
                    10.39,
                    10.3,
                    10.18,
                    10.49
                ],
                [
                    10.3,
                    10.16,
                    10.11,
                    10.3
                ],
                [
                    10.13,
                    9.96,
                    9.88,
                    10.15
                ],
                [
                    9.94,
                    9.93,
                    9.87,
                    10.01
                ],
                [
                    9.93,
                    10.11,
                    9.85,
                    10.15
                ],
                [
                    10.08,
                    10.4,
                    9.98,
                    10.44
                ],
                [
                    10.4,
                    10.3,
                    10.16,
                    10.4
                ],
                [
                    10.22,
                    10.18,
                    10.12,
                    10.65
                ],
                [
                    10.18,
                    9.95,
                    9.94,
                    10.37
                ],
                [
                    10.06,
                    10.33,
                    9.99,
                    10.43
                ],
                [
                    10.3,
                    10.26,
                    10.08,
                    10.33
                ],
                [
                    10.21,
                    9.84,
                    9.79,
                    10.39
                ],
                [
                    9.8,
                    10.03,
                    9.66,
                    10.06
                ],
                [
                    10.04,
                    9.9,
                    9.87,
                    10.15
                ],
                [
                    9.99,
                    10.31,
                    9.91,
                    10.55
                ],
                [
                    10.29,
                    10.42,
                    10.15,
                    10.53
                ],
                [
                    10.48,
                    10.28,
                    10.21,
                    10.5
                ],
                [
                    10.33,
                    10.39,
                    10.23,
                    10.41
                ],
                [
                    10.51,
                    10.66,
                    10.37,
                    10.8
                ],
                [
                    10.64,
                    10.56,
                    10.37,
                    10.7
                ],
                [
                    10.52,
                    10.55,
                    10.49,
                    10.66
                ],
                [
                    10.5,
                    10.61,
                    10.41,
                    10.74
                ],
                [
                    10.58,
                    10.31,
                    10.28,
                    10.62
                ],
                [
                    10.27,
                    10.34,
                    10.26,
                    10.44
                ],
                [
                    10.42,
                    10.27,
                    10.19,
                    10.42
                ],
                [
                    10.18,
                    10.19,
                    10.13,
                    10.42
                ],
                [
                    10.17,
                    10.05,
                    10.0,
                    10.27
                ],
                [
                    10.05,
                    9.98,
                    9.94,
                    10.15
                ],
                [
                    9.9,
                    10.16,
                    9.86,
                    10.18
                ],
                [
                    10.18,
                    10.53,
                    10.16,
                    10.74
                ],
                [
                    10.53,
                    10.64,
                    10.5,
                    10.73
                ],
                [
                    10.62,
                    10.57,
                    10.5,
                    10.72
                ],
                [
                    10.54,
                    10.57,
                    10.49,
                    10.61
                ],
                [
                    10.56,
                    10.65,
                    10.51,
                    10.67
                ],
                [
                    10.67,
                    10.86,
                    10.67,
                    11.06
                ],
                [
                    10.86,
                    10.66,
                    10.58,
                    10.86
                ],
                [
                    10.65,
                    10.76,
                    10.62,
                    10.83
                ],
                [
                    10.77,
                    10.81,
                    10.71,
                    11.1
                ],
                [
                    10.77,
                    10.77,
                    10.64,
                    10.81
                ],
                [
                    10.76,
                    10.88,
                    10.7,
                    10.92
                ],
                [
                    11.09,
                    10.97,
                    10.88,
                    11.3
                ],
                [
                    10.92,
                    11.29,
                    10.92,
                    11.69
                ],
                [
                    11.29,
                    11.1,
                    11.01,
                    11.45
                ],
                [
                    11.22,
                    11.15,
                    10.95,
                    11.25
                ],
                [
                    11.18,
                    10.94,
                    10.82,
                    11.25
                ],
                [
                    10.94,
                    11.03,
                    10.75,
                    11.03
                ],
                [
                    10.98,
                    10.88,
                    10.81,
                    11.04
                ],
                [
                    10.88,
                    11.44,
                    10.84,
                    11.78
                ],
                [
                    11.37,
                    11.26,
                    11.18,
                    11.53
                ],
                [
                    11.25,
                    11.03,
                    10.98,
                    11.28
                ],
                [
                    11.0,
                    11.01,
                    10.93,
                    11.25
                ],
                [
                    11.0,
                    11.13,
                    10.95,
                    11.2
                ],
                [
                    11.2,
                    11.52,
                    11.13,
                    11.8
                ],
                [
                    11.85,
                    11.39,
                    11.31,
                    11.85
                ],
                [
                    11.68,
                    11.27,
                    11.11,
                    11.83
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#880000",
                "borderColor0": "#008F28"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5438\u7b79",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 18,
            "data": [
                [
                    "2025-02-14",
                    11.31
                ],
                [
                    "2025-02-17",
                    12.02
                ],
                [
                    "2025-03-17",
                    11.24
                ],
                [
                    "2025-04-16",
                    9.47
                ],
                [
                    "2025-04-24",
                    10.33
                ],
                [
                    "2025-05-13",
                    10.18
                ],
                [
                    "2025-05-22",
                    10.12
                ],
                [
                    "2025-06-06",
                    10.23
                ],
                [
                    "2025-06-09",
                    10.37
                ],
                [
                    "2025-06-18",
                    10.13
                ],
                [
                    "2025-07-03",
                    10.62
                ],
                [
                    "2025-07-04",
                    10.71
                ],
                [
                    "2025-07-08",
                    10.7
                ],
                [
                    "2025-07-23",
                    10.93
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#F4B400"
            }
        },
        {
            "type": "scatter",
            "name": "\u52a0\u4ed3",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 18,
            "data": [
                [
                    "2025-02-17",
                    12.02
                ],
                [
                    "2025-03-14",
                    10.68
                ],
                [
                    "2025-04-22",
                    10.25
                ],
                [
                    "2025-07-18",
                    10.84
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4285F4"
            }
        },
        {
            "type": "scatter",
            "name": "\u6d17\u76d8",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "rect",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#16A085"
            }
        },
        {
            "type": "scatter",
            "name": "\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "circle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#DB4437"
            }
        },
        {
            "type": "scatter",
            "name": "\u9690\u5f62\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "pin",
            "symbolSize": 18,
            "data": [
                [
                    "2025-06-17",
                    10.19
                ],
                [
                    "2025-06-20",
                    9.94
                ],
                [
                    "2025-06-23",
                    9.86
                ],
                [
                    "2025-06-27",
                    10.49
                ],
                [
                    "2025-06-30",
                    10.51
                ],
                [
                    "2025-07-16",
                    10.75
                ],
                [
                    "2025-07-17",
                    10.81
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#8E44AD"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5438\u7b79",
                "\u52a0\u4ed3",
                "\u6d17\u76d8",
                "\u51fa\u8d27",
                "\u9690\u5f62\u51fa\u8d27"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10,
            "top": "2%"
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "interval": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 0
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600661 \u4e3b\u529b\u4fe1\u53f7 (\u542b\u6d17\u76d8)",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "90%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_3fec746058884188b22afa27302baf62.setOption(option_3fec746058884188b22afa27302baf62);
            window.addEventListener('resize', function(){
                chart_3fec746058884188b22afa27302baf62.resize();
            })
    </script>
</body>
</html>
