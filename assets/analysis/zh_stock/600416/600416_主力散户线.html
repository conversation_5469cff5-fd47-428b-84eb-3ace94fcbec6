<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="e5d56e147c8449d28715741cc941e593" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_e5d56e147c8449d28715741cc941e593 = echarts.init(
            document.getElementById('e5d56e147c8449d28715741cc941e593'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_e5d56e147c8449d28715741cc941e593 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    10.23,
                    10.18,
                    10.05,
                    10.31
                ],
                [
                    10.2,
                    10.34,
                    10.15,
                    10.53
                ],
                [
                    10.31,
                    10.64,
                    10.25,
                    10.68
                ],
                [
                    10.65,
                    10.59,
                    10.48,
                    10.8
                ],
                [
                    10.56,
                    10.77,
                    10.56,
                    10.83
                ],
                [
                    10.75,
                    10.65,
                    10.56,
                    10.76
                ],
                [
                    10.62,
                    10.66,
                    10.53,
                    10.7
                ],
                [
                    10.68,
                    10.48,
                    10.48,
                    10.7
                ],
                [
                    10.48,
                    10.51,
                    10.42,
                    10.54
                ],
                [
                    10.5,
                    10.5,
                    10.43,
                    10.56
                ],
                [
                    10.49,
                    10.22,
                    10.17,
                    10.53
                ],
                [
                    10.18,
                    10.51,
                    10.18,
                    10.52
                ],
                [
                    10.51,
                    10.45,
                    10.32,
                    10.55
                ],
                [
                    10.43,
                    10.42,
                    10.34,
                    10.51
                ],
                [
                    10.41,
                    10.37,
                    10.28,
                    10.5
                ],
                [
                    10.24,
                    10.2,
                    10.15,
                    10.37
                ],
                [
                    10.16,
                    11.03,
                    10.16,
                    11.2
                ],
                [
                    11.0,
                    10.73,
                    10.56,
                    11.0
                ],
                [
                    10.67,
                    10.57,
                    10.55,
                    10.95
                ],
                [
                    10.55,
                    10.64,
                    10.54,
                    10.87
                ],
                [
                    10.59,
                    11.33,
                    10.55,
                    11.48
                ],
                [
                    11.16,
                    11.11,
                    11.06,
                    11.43
                ],
                [
                    11.19,
                    11.19,
                    11.15,
                    11.32
                ],
                [
                    11.2,
                    11.24,
                    11.11,
                    11.65
                ],
                [
                    11.2,
                    11.74,
                    11.17,
                    11.85
                ],
                [
                    11.5,
                    12.24,
                    11.46,
                    12.36
                ],
                [
                    12.25,
                    12.09,
                    12.0,
                    12.38
                ],
                [
                    12.09,
                    12.43,
                    11.92,
                    12.5
                ],
                [
                    12.5,
                    12.3,
                    12.05,
                    12.54
                ],
                [
                    12.4,
                    11.92,
                    11.9,
                    12.49
                ],
                [
                    11.9,
                    11.97,
                    11.86,
                    12.12
                ],
                [
                    11.97,
                    11.91,
                    11.85,
                    12.1
                ],
                [
                    11.88,
                    11.82,
                    11.71,
                    12.02
                ],
                [
                    11.77,
                    11.77,
                    11.69,
                    12.04
                ],
                [
                    11.84,
                    11.33,
                    11.11,
                    11.86
                ],
                [
                    11.26,
                    11.38,
                    11.23,
                    11.6
                ],
                [
                    11.32,
                    11.5,
                    11.27,
                    11.66
                ],
                [
                    11.43,
                    11.22,
                    11.07,
                    11.43
                ],
                [
                    11.22,
                    11.07,
                    11.06,
                    11.28
                ],
                [
                    11.02,
                    10.78,
                    10.64,
                    11.02
                ],
                [
                    10.78,
                    11.0,
                    10.77,
                    11.07
                ],
                [
                    10.96,
                    10.94,
                    10.85,
                    11.09
                ],
                [
                    10.86,
                    10.79,
                    10.73,
                    11.11
                ],
                [
                    10.31,
                    9.71,
                    9.71,
                    10.34
                ],
                [
                    9.72,
                    9.65,
                    9.48,
                    9.96
                ],
                [
                    9.51,
                    9.94,
                    9.13,
                    10.03
                ],
                [
                    9.98,
                    10.24,
                    9.98,
                    10.36
                ],
                [
                    10.2,
                    10.25,
                    10.1,
                    10.33
                ],
                [
                    10.27,
                    10.33,
                    10.24,
                    10.42
                ],
                [
                    10.33,
                    10.11,
                    10.05,
                    10.34
                ],
                [
                    10.04,
                    10.0,
                    9.78,
                    10.1
                ],
                [
                    9.94,
                    9.91,
                    9.9,
                    10.06
                ],
                [
                    9.94,
                    10.0,
                    9.82,
                    10.03
                ],
                [
                    10.0,
                    10.03,
                    9.93,
                    10.1
                ],
                [
                    10.02,
                    10.01,
                    9.95,
                    10.14
                ],
                [
                    10.04,
                    10.15,
                    10.02,
                    10.24
                ],
                [
                    10.15,
                    9.99,
                    9.94,
                    10.18
                ],
                [
                    9.96,
                    10.06,
                    9.96,
                    10.09
                ],
                [
                    10.1,
                    9.95,
                    9.9,
                    10.13
                ],
                [
                    9.9,
                    9.9,
                    9.87,
                    10.03
                ],
                [
                    9.87,
                    9.9,
                    9.78,
                    9.97
                ],
                [
                    9.94,
                    10.18,
                    9.91,
                    10.19
                ],
                [
                    10.25,
                    10.36,
                    10.12,
                    10.54
                ],
                [
                    10.3,
                    10.69,
                    10.23,
                    10.76
                ],
                [
                    10.7,
                    10.41,
                    10.38,
                    10.72
                ],
                [
                    10.4,
                    10.84,
                    10.4,
                    11.24
                ],
                [
                    10.87,
                    10.64,
                    10.59,
                    10.93
                ],
                [
                    10.63,
                    10.53,
                    10.46,
                    10.65
                ],
                [
                    10.5,
                    10.41,
                    10.38,
                    10.56
                ],
                [
                    10.39,
                    10.47,
                    10.38,
                    10.56
                ],
                [
                    10.5,
                    10.3,
                    10.2,
                    10.51
                ],
                [
                    10.3,
                    10.74,
                    10.21,
                    10.93
                ],
                [
                    10.74,
                    10.62,
                    10.55,
                    10.82
                ],
                [
                    10.6,
                    10.46,
                    10.44,
                    10.69
                ],
                [
                    10.46,
                    10.6,
                    10.42,
                    10.82
                ],
                [
                    10.85,
                    10.99,
                    10.73,
                    11.2
                ],
                [
                    10.96,
                    10.98,
                    10.79,
                    11.07
                ],
                [
                    10.98,
                    11.11,
                    10.94,
                    11.3
                ],
                [
                    11.15,
                    11.17,
                    11.07,
                    11.29
                ],
                [
                    11.12,
                    10.97,
                    10.89,
                    11.12
                ],
                [
                    10.95,
                    11.09,
                    10.86,
                    11.15
                ],
                [
                    11.06,
                    11.46,
                    11.06,
                    11.66
                ],
                [
                    11.44,
                    11.61,
                    11.42,
                    11.69
                ],
                [
                    11.59,
                    11.47,
                    11.41,
                    11.64
                ],
                [
                    11.5,
                    11.77,
                    11.48,
                    11.86
                ],
                [
                    11.76,
                    11.46,
                    11.4,
                    11.77
                ],
                [
                    11.54,
                    11.67,
                    11.54,
                    11.85
                ],
                [
                    11.71,
                    11.8,
                    11.65,
                    11.87
                ],
                [
                    11.85,
                    11.6,
                    11.6,
                    11.85
                ],
                [
                    11.5,
                    11.56,
                    11.21,
                    11.6
                ],
                [
                    11.61,
                    11.94,
                    11.52,
                    12.12
                ],
                [
                    11.88,
                    11.77,
                    11.67,
                    12.14
                ],
                [
                    11.71,
                    11.42,
                    11.38,
                    11.76
                ],
                [
                    11.39,
                    11.3,
                    11.23,
                    11.52
                ],
                [
                    11.22,
                    11.63,
                    11.16,
                    11.72
                ],
                [
                    11.63,
                    12.07,
                    11.56,
                    12.26
                ],
                [
                    12.1,
                    12.25,
                    11.97,
                    12.26
                ],
                [
                    12.28,
                    12.2,
                    12.15,
                    12.72
                ],
                [
                    12.2,
                    12.22,
                    12.13,
                    12.42
                ],
                [
                    12.21,
                    13.44,
                    12.15,
                    13.44
                ],
                [
                    13.17,
                    14.31,
                    13.0,
                    14.78
                ],
                [
                    14.03,
                    14.07,
                    13.6,
                    14.65
                ],
                [
                    13.76,
                    13.39,
                    13.36,
                    14.38
                ],
                [
                    13.47,
                    13.32,
                    12.98,
                    13.53
                ],
                [
                    13.32,
                    13.55,
                    13.1,
                    13.71
                ],
                [
                    13.46,
                    13.56,
                    13.41,
                    13.74
                ],
                [
                    13.59,
                    13.25,
                    13.19,
                    13.7
                ],
                [
                    13.11,
                    13.15,
                    13.01,
                    13.37
                ],
                [
                    13.16,
                    13.23,
                    13.07,
                    13.38
                ],
                [
                    13.15,
                    12.99,
                    12.88,
                    13.21
                ],
                [
                    13.17,
                    13.05,
                    12.88,
                    13.2
                ],
                [
                    13.01,
                    12.93,
                    12.87,
                    13.13
                ],
                [
                    12.93,
                    13.16,
                    12.82,
                    13.29
                ],
                [
                    13.16,
                    13.29,
                    13.06,
                    13.32
                ],
                [
                    13.33,
                    13.38,
                    13.19,
                    13.64
                ],
                [
                    13.39,
                    13.39,
                    13.35,
                    13.68
                ],
                [
                    13.35,
                    13.82,
                    13.23,
                    13.95
                ],
                [
                    13.9,
                    14.06,
                    13.78,
                    14.11
                ],
                [
                    13.95,
                    14.07,
                    13.87,
                    14.2
                ],
                [
                    14.02,
                    14.41,
                    14.0,
                    14.95
                ],
                [
                    14.52,
                    14.06,
                    13.83,
                    14.58
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    13.19
                ],
                [
                    "2025-02-05",
                    2.60
                ],
                [
                    "2025-02-06",
                    8.33
                ],
                [
                    "2025-02-07",
                    -5.32
                ],
                [
                    "2025-02-10",
                    5.16
                ],
                [
                    "2025-02-11",
                    -12.63
                ],
                [
                    "2025-02-12",
                    -15.78
                ],
                [
                    "2025-02-13",
                    -28.46
                ],
                [
                    "2025-02-14",
                    -17.32
                ],
                [
                    "2025-02-17",
                    -5.76
                ],
                [
                    "2025-02-18",
                    -21.53
                ],
                [
                    "2025-02-19",
                    -5.11
                ],
                [
                    "2025-02-20",
                    -3.12
                ],
                [
                    "2025-02-21",
                    -15.81
                ],
                [
                    "2025-02-24",
                    -13.09
                ],
                [
                    "2025-02-25",
                    -25.07
                ],
                [
                    "2025-02-26",
                    10.05
                ],
                [
                    "2025-02-27",
                    -9.91
                ],
                [
                    "2025-02-28",
                    2.52
                ],
                [
                    "2025-03-03",
                    -1.67
                ],
                [
                    "2025-03-04",
                    15.36
                ],
                [
                    "2025-03-05",
                    -15.30
                ],
                [
                    "2025-03-06",
                    -6.05
                ],
                [
                    "2025-03-07",
                    4.90
                ],
                [
                    "2025-03-10",
                    20.09
                ],
                [
                    "2025-03-11",
                    10.10
                ],
                [
                    "2025-03-12",
                    -0.71
                ],
                [
                    "2025-03-13",
                    10.11
                ],
                [
                    "2025-03-14",
                    4.77
                ],
                [
                    "2025-03-17",
                    -9.03
                ],
                [
                    "2025-03-18",
                    -7.98
                ],
                [
                    "2025-03-19",
                    -3.47
                ],
                [
                    "2025-03-20",
                    -7.33
                ],
                [
                    "2025-03-21",
                    -10.66
                ],
                [
                    "2025-03-24",
                    -11.27
                ],
                [
                    "2025-03-25",
                    0.46
                ],
                [
                    "2025-03-26",
                    -7.99
                ],
                [
                    "2025-03-27",
                    -11.51
                ],
                [
                    "2025-03-28",
                    -0.69
                ],
                [
                    "2025-03-31",
                    -14.00
                ],
                [
                    "2025-04-01",
                    7.35
                ],
                [
                    "2025-04-02",
                    -0.87
                ],
                [
                    "2025-04-03",
                    -9.80
                ],
                [
                    "2025-04-07",
                    -12.32
                ],
                [
                    "2025-04-08",
                    -24.06
                ],
                [
                    "2025-04-09",
                    -4.56
                ],
                [
                    "2025-04-10",
                    -0.49
                ],
                [
                    "2025-04-11",
                    -8.64
                ],
                [
                    "2025-04-14",
                    -12.17
                ],
                [
                    "2025-04-15",
                    -19.57
                ],
                [
                    "2025-04-16",
                    -7.47
                ],
                [
                    "2025-04-17",
                    -1.15
                ],
                [
                    "2025-04-18",
                    -12.93
                ],
                [
                    "2025-04-21",
                    3.67
                ],
                [
                    "2025-04-22",
                    8.52
                ],
                [
                    "2025-04-23",
                    -6.70
                ],
                [
                    "2025-04-24",
                    5.67
                ],
                [
                    "2025-04-25",
                    -13.86
                ],
                [
                    "2025-04-28",
                    -3.61
                ],
                [
                    "2025-04-29",
                    -11.83
                ],
                [
                    "2025-04-30",
                    -6.06
                ],
                [
                    "2025-05-06",
                    -2.46
                ],
                [
                    "2025-05-07",
                    6.06
                ],
                [
                    "2025-05-08",
                    3.49
                ],
                [
                    "2025-05-09",
                    -7.96
                ],
                [
                    "2025-05-12",
                    8.74
                ],
                [
                    "2025-05-13",
                    -6.33
                ],
                [
                    "2025-05-14",
                    -4.53
                ],
                [
                    "2025-05-15",
                    2.41
                ],
                [
                    "2025-05-16",
                    -11.42
                ],
                [
                    "2025-05-19",
                    -22.46
                ],
                [
                    "2025-05-20",
                    15.13
                ],
                [
                    "2025-05-21",
                    -1.69
                ],
                [
                    "2025-05-22",
                    2.36
                ],
                [
                    "2025-05-23",
                    5.00
                ],
                [
                    "2025-05-26",
                    7.99
                ],
                [
                    "2025-05-27",
                    4.05
                ],
                [
                    "2025-05-28",
                    3.02
                ],
                [
                    "2025-05-29",
                    1.77
                ],
                [
                    "2025-05-30",
                    -13.33
                ],
                [
                    "2025-06-03",
                    6.16
                ],
                [
                    "2025-06-04",
                    11.48
                ],
                [
                    "2025-06-05",
                    5.10
                ],
                [
                    "2025-06-06",
                    -1.15
                ],
                [
                    "2025-06-09",
                    5.90
                ],
                [
                    "2025-06-10",
                    -4.03
                ],
                [
                    "2025-06-11",
                    -2.75
                ],
                [
                    "2025-06-12",
                    -13.60
                ],
                [
                    "2025-06-13",
                    -11.71
                ],
                [
                    "2025-06-16",
                    -0.79
                ],
                [
                    "2025-06-17",
                    6.57
                ],
                [
                    "2025-06-18",
                    -6.02
                ],
                [
                    "2025-06-19",
                    -13.86
                ],
                [
                    "2025-06-20",
                    -14.32
                ],
                [
                    "2025-06-23",
                    8.04
                ],
                [
                    "2025-06-24",
                    14.08
                ],
                [
                    "2025-06-25",
                    5.88
                ],
                [
                    "2025-06-26",
                    -6.44
                ],
                [
                    "2025-06-27",
                    -9.00
                ],
                [
                    "2025-06-30",
                    18.28
                ],
                [
                    "2025-07-01",
                    6.23
                ],
                [
                    "2025-07-02",
                    4.61
                ],
                [
                    "2025-07-03",
                    -6.73
                ],
                [
                    "2025-07-04",
                    -6.15
                ],
                [
                    "2025-07-07",
                    19.96
                ],
                [
                    "2025-07-08",
                    -0.80
                ],
                [
                    "2025-07-09",
                    -11.24
                ],
                [
                    "2025-07-10",
                    -16.21
                ],
                [
                    "2025-07-11",
                    4.85
                ],
                [
                    "2025-07-14",
                    -18.20
                ],
                [
                    "2025-07-15",
                    1.10
                ],
                [
                    "2025-07-16",
                    -5.34
                ],
                [
                    "2025-07-17",
                    -0.18
                ],
                [
                    "2025-07-18",
                    0.38
                ],
                [
                    "2025-07-21",
                    2.40
                ],
                [
                    "2025-07-22",
                    -5.28
                ],
                [
                    "2025-07-23",
                    15.39
                ],
                [
                    "2025-07-24",
                    8.73
                ],
                [
                    "2025-07-25",
                    3.79
                ],
                [
                    "2025-07-28",
                    -1.10
                ],
                [
                    "2025-07-29",
                    -12.84
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -4.28
                ],
                [
                    "2025-02-05",
                    -3.01
                ],
                [
                    "2025-02-06",
                    -9.39
                ],
                [
                    "2025-02-07",
                    0.49
                ],
                [
                    "2025-02-10",
                    0.56
                ],
                [
                    "2025-02-11",
                    3.47
                ],
                [
                    "2025-02-12",
                    3.17
                ],
                [
                    "2025-02-13",
                    11.14
                ],
                [
                    "2025-02-14",
                    5.05
                ],
                [
                    "2025-02-17",
                    4.27
                ],
                [
                    "2025-02-18",
                    5.83
                ],
                [
                    "2025-02-19",
                    0.72
                ],
                [
                    "2025-02-20",
                    4.73
                ],
                [
                    "2025-02-21",
                    3.32
                ],
                [
                    "2025-02-24",
                    4.13
                ],
                [
                    "2025-02-25",
                    7.42
                ],
                [
                    "2025-02-26",
                    -1.61
                ],
                [
                    "2025-02-27",
                    3.19
                ],
                [
                    "2025-02-28",
                    2.20
                ],
                [
                    "2025-03-03",
                    2.85
                ],
                [
                    "2025-03-04",
                    -1.40
                ],
                [
                    "2025-03-05",
                    9.09
                ],
                [
                    "2025-03-06",
                    3.52
                ],
                [
                    "2025-03-07",
                    1.23
                ],
                [
                    "2025-03-10",
                    -6.57
                ],
                [
                    "2025-03-11",
                    -1.95
                ],
                [
                    "2025-03-12",
                    -0.39
                ],
                [
                    "2025-03-13",
                    -1.62
                ],
                [
                    "2025-03-14",
                    -1.63
                ],
                [
                    "2025-03-17",
                    2.46
                ],
                [
                    "2025-03-18",
                    3.31
                ],
                [
                    "2025-03-19",
                    1.40
                ],
                [
                    "2025-03-20",
                    -4.75
                ],
                [
                    "2025-03-21",
                    4.43
                ],
                [
                    "2025-03-24",
                    -2.72
                ],
                [
                    "2025-03-25",
                    -3.75
                ],
                [
                    "2025-03-26",
                    6.25
                ],
                [
                    "2025-03-27",
                    4.35
                ],
                [
                    "2025-03-28",
                    -7.01
                ],
                [
                    "2025-03-31",
                    2.02
                ],
                [
                    "2025-04-01",
                    -4.19
                ],
                [
                    "2025-04-02",
                    -5.19
                ],
                [
                    "2025-04-03",
                    4.56
                ],
                [
                    "2025-04-07",
                    8.11
                ],
                [
                    "2025-04-08",
                    3.67
                ],
                [
                    "2025-04-09",
                    -4.27
                ],
                [
                    "2025-04-10",
                    -8.07
                ],
                [
                    "2025-04-11",
                    5.39
                ],
                [
                    "2025-04-14",
                    6.95
                ],
                [
                    "2025-04-15",
                    6.29
                ],
                [
                    "2025-04-16",
                    5.13
                ],
                [
                    "2025-04-17",
                    -13.28
                ],
                [
                    "2025-04-18",
                    13.22
                ],
                [
                    "2025-04-21",
                    8.94
                ],
                [
                    "2025-04-22",
                    -3.67
                ],
                [
                    "2025-04-23",
                    1.01
                ],
                [
                    "2025-04-24",
                    -8.46
                ],
                [
                    "2025-04-25",
                    3.38
                ],
                [
                    "2025-04-28",
                    1.64
                ],
                [
                    "2025-04-29",
                    5.40
                ],
                [
                    "2025-04-30",
                    4.43
                ],
                [
                    "2025-05-06",
                    0.02
                ],
                [
                    "2025-05-07",
                    -4.38
                ],
                [
                    "2025-05-08",
                    -0.29
                ],
                [
                    "2025-05-09",
                    9.74
                ],
                [
                    "2025-05-12",
                    -2.59
                ],
                [
                    "2025-05-13",
                    2.42
                ],
                [
                    "2025-05-14",
                    -3.18
                ],
                [
                    "2025-05-15",
                    -2.07
                ],
                [
                    "2025-05-16",
                    11.49
                ],
                [
                    "2025-05-19",
                    11.14
                ],
                [
                    "2025-05-20",
                    -7.49
                ],
                [
                    "2025-05-21",
                    0.89
                ],
                [
                    "2025-05-22",
                    0.43
                ],
                [
                    "2025-05-23",
                    2.82
                ],
                [
                    "2025-05-26",
                    -0.57
                ],
                [
                    "2025-05-27",
                    0.49
                ],
                [
                    "2025-05-28",
                    6.07
                ],
                [
                    "2025-05-29",
                    -0.57
                ],
                [
                    "2025-05-30",
                    1.58
                ],
                [
                    "2025-06-03",
                    -4.48
                ],
                [
                    "2025-06-04",
                    -4.05
                ],
                [
                    "2025-06-05",
                    -4.21
                ],
                [
                    "2025-06-06",
                    -0.60
                ],
                [
                    "2025-06-09",
                    -2.79
                ],
                [
                    "2025-06-10",
                    3.50
                ],
                [
                    "2025-06-11",
                    -6.85
                ],
                [
                    "2025-06-12",
                    10.06
                ],
                [
                    "2025-06-13",
                    -3.16
                ],
                [
                    "2025-06-16",
                    -6.98
                ],
                [
                    "2025-06-17",
                    -2.61
                ],
                [
                    "2025-06-18",
                    -0.35
                ],
                [
                    "2025-06-19",
                    6.77
                ],
                [
                    "2025-06-20",
                    2.44
                ],
                [
                    "2025-06-23",
                    -1.68
                ],
                [
                    "2025-06-24",
                    -6.00
                ],
                [
                    "2025-06-25",
                    -5.01
                ],
                [
                    "2025-06-26",
                    4.15
                ],
                [
                    "2025-06-27",
                    1.31
                ],
                [
                    "2025-06-30",
                    -9.03
                ],
                [
                    "2025-07-01",
                    -2.74
                ],
                [
                    "2025-07-02",
                    -4.39
                ],
                [
                    "2025-07-03",
                    2.70
                ],
                [
                    "2025-07-04",
                    -10.70
                ],
                [
                    "2025-07-07",
                    -1.31
                ],
                [
                    "2025-07-08",
                    4.22
                ],
                [
                    "2025-07-09",
                    -1.41
                ],
                [
                    "2025-07-10",
                    1.85
                ],
                [
                    "2025-07-11",
                    -2.63
                ],
                [
                    "2025-07-14",
                    1.64
                ],
                [
                    "2025-07-15",
                    0.10
                ],
                [
                    "2025-07-16",
                    4.37
                ],
                [
                    "2025-07-17",
                    1.16
                ],
                [
                    "2025-07-18",
                    4.10
                ],
                [
                    "2025-07-21",
                    -2.06
                ],
                [
                    "2025-07-22",
                    5.36
                ],
                [
                    "2025-07-23",
                    -7.22
                ],
                [
                    "2025-07-24",
                    -5.41
                ],
                [
                    "2025-07-25",
                    -3.58
                ],
                [
                    "2025-07-28",
                    -0.20
                ],
                [
                    "2025-07-29",
                    0.32
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -8.90
                ],
                [
                    "2025-02-05",
                    0.41
                ],
                [
                    "2025-02-06",
                    1.06
                ],
                [
                    "2025-02-07",
                    4.82
                ],
                [
                    "2025-02-10",
                    -5.72
                ],
                [
                    "2025-02-11",
                    9.16
                ],
                [
                    "2025-02-12",
                    12.61
                ],
                [
                    "2025-02-13",
                    17.32
                ],
                [
                    "2025-02-14",
                    12.26
                ],
                [
                    "2025-02-17",
                    1.50
                ],
                [
                    "2025-02-18",
                    15.70
                ],
                [
                    "2025-02-19",
                    4.40
                ],
                [
                    "2025-02-20",
                    -1.60
                ],
                [
                    "2025-02-21",
                    12.49
                ],
                [
                    "2025-02-24",
                    8.96
                ],
                [
                    "2025-02-25",
                    17.65
                ],
                [
                    "2025-02-26",
                    -8.44
                ],
                [
                    "2025-02-27",
                    6.72
                ],
                [
                    "2025-02-28",
                    -4.71
                ],
                [
                    "2025-03-03",
                    -1.18
                ],
                [
                    "2025-03-04",
                    -13.96
                ],
                [
                    "2025-03-05",
                    6.20
                ],
                [
                    "2025-03-06",
                    2.53
                ],
                [
                    "2025-03-07",
                    -6.13
                ],
                [
                    "2025-03-10",
                    -13.53
                ],
                [
                    "2025-03-11",
                    -8.15
                ],
                [
                    "2025-03-12",
                    1.10
                ],
                [
                    "2025-03-13",
                    -8.49
                ],
                [
                    "2025-03-14",
                    -3.14
                ],
                [
                    "2025-03-17",
                    6.57
                ],
                [
                    "2025-03-18",
                    4.67
                ],
                [
                    "2025-03-19",
                    2.06
                ],
                [
                    "2025-03-20",
                    12.08
                ],
                [
                    "2025-03-21",
                    6.23
                ],
                [
                    "2025-03-24",
                    13.99
                ],
                [
                    "2025-03-25",
                    3.29
                ],
                [
                    "2025-03-26",
                    1.74
                ],
                [
                    "2025-03-27",
                    7.16
                ],
                [
                    "2025-03-28",
                    7.70
                ],
                [
                    "2025-03-31",
                    11.99
                ],
                [
                    "2025-04-01",
                    -3.17
                ],
                [
                    "2025-04-02",
                    6.06
                ],
                [
                    "2025-04-03",
                    5.24
                ],
                [
                    "2025-04-07",
                    4.20
                ],
                [
                    "2025-04-08",
                    20.39
                ],
                [
                    "2025-04-09",
                    8.83
                ],
                [
                    "2025-04-10",
                    8.55
                ],
                [
                    "2025-04-11",
                    3.24
                ],
                [
                    "2025-04-14",
                    5.22
                ],
                [
                    "2025-04-15",
                    13.27
                ],
                [
                    "2025-04-16",
                    2.34
                ],
                [
                    "2025-04-17",
                    14.43
                ],
                [
                    "2025-04-18",
                    -0.29
                ],
                [
                    "2025-04-21",
                    -12.61
                ],
                [
                    "2025-04-22",
                    -4.84
                ],
                [
                    "2025-04-23",
                    5.70
                ],
                [
                    "2025-04-24",
                    2.79
                ],
                [
                    "2025-04-25",
                    10.48
                ],
                [
                    "2025-04-28",
                    1.97
                ],
                [
                    "2025-04-29",
                    6.43
                ],
                [
                    "2025-04-30",
                    1.63
                ],
                [
                    "2025-05-06",
                    2.45
                ],
                [
                    "2025-05-07",
                    -1.68
                ],
                [
                    "2025-05-08",
                    -3.20
                ],
                [
                    "2025-05-09",
                    -1.77
                ],
                [
                    "2025-05-12",
                    -6.15
                ],
                [
                    "2025-05-13",
                    3.91
                ],
                [
                    "2025-05-14",
                    7.70
                ],
                [
                    "2025-05-15",
                    -0.34
                ],
                [
                    "2025-05-16",
                    -0.07
                ],
                [
                    "2025-05-19",
                    11.32
                ],
                [
                    "2025-05-20",
                    -7.64
                ],
                [
                    "2025-05-21",
                    0.80
                ],
                [
                    "2025-05-22",
                    -2.79
                ],
                [
                    "2025-05-23",
                    -7.82
                ],
                [
                    "2025-05-26",
                    -7.42
                ],
                [
                    "2025-05-27",
                    -4.54
                ],
                [
                    "2025-05-28",
                    -9.09
                ],
                [
                    "2025-05-29",
                    -1.20
                ],
                [
                    "2025-05-30",
                    11.75
                ],
                [
                    "2025-06-03",
                    -1.69
                ],
                [
                    "2025-06-04",
                    -7.43
                ],
                [
                    "2025-06-05",
                    -0.90
                ],
                [
                    "2025-06-06",
                    1.75
                ],
                [
                    "2025-06-09",
                    -3.11
                ],
                [
                    "2025-06-10",
                    0.54
                ],
                [
                    "2025-06-11",
                    9.60
                ],
                [
                    "2025-06-12",
                    3.54
                ],
                [
                    "2025-06-13",
                    14.86
                ],
                [
                    "2025-06-16",
                    7.77
                ],
                [
                    "2025-06-17",
                    -3.97
                ],
                [
                    "2025-06-18",
                    6.38
                ],
                [
                    "2025-06-19",
                    7.10
                ],
                [
                    "2025-06-20",
                    11.89
                ],
                [
                    "2025-06-23",
                    -6.36
                ],
                [
                    "2025-06-24",
                    -8.08
                ],
                [
                    "2025-06-25",
                    -0.87
                ],
                [
                    "2025-06-26",
                    2.29
                ],
                [
                    "2025-06-27",
                    7.69
                ],
                [
                    "2025-06-30",
                    -9.26
                ],
                [
                    "2025-07-01",
                    -3.49
                ],
                [
                    "2025-07-02",
                    -0.22
                ],
                [
                    "2025-07-03",
                    4.03
                ],
                [
                    "2025-07-04",
                    16.86
                ],
                [
                    "2025-07-07",
                    -18.64
                ],
                [
                    "2025-07-08",
                    -3.43
                ],
                [
                    "2025-07-09",
                    12.65
                ],
                [
                    "2025-07-10",
                    14.37
                ],
                [
                    "2025-07-11",
                    -2.23
                ],
                [
                    "2025-07-14",
                    16.56
                ],
                [
                    "2025-07-15",
                    -1.20
                ],
                [
                    "2025-07-16",
                    0.97
                ],
                [
                    "2025-07-17",
                    -0.98
                ],
                [
                    "2025-07-18",
                    -4.48
                ],
                [
                    "2025-07-21",
                    -0.34
                ],
                [
                    "2025-07-22",
                    -0.08
                ],
                [
                    "2025-07-23",
                    -8.17
                ],
                [
                    "2025-07-24",
                    -3.32
                ],
                [
                    "2025-07-25",
                    -0.21
                ],
                [
                    "2025-07-28",
                    1.30
                ],
                [
                    "2025-07-29",
                    12.52
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    5.16
                ],
                [
                    "2025-03-04",
                    15.36
                ],
                [
                    "2025-03-10",
                    20.09
                ],
                [
                    "2025-03-11",
                    10.10
                ],
                [
                    "2025-03-12",
                    -0.71
                ],
                [
                    "2025-03-13",
                    10.11
                ],
                [
                    "2025-03-14",
                    4.77
                ],
                [
                    "2025-03-17",
                    -9.03
                ],
                [
                    "2025-05-12",
                    8.74
                ],
                [
                    "2025-05-13",
                    -6.33
                ],
                [
                    "2025-05-26",
                    7.99
                ],
                [
                    "2025-05-27",
                    4.05
                ],
                [
                    "2025-05-28",
                    3.02
                ],
                [
                    "2025-05-29",
                    1.77
                ],
                [
                    "2025-05-30",
                    -13.33
                ],
                [
                    "2025-06-03",
                    6.16
                ],
                [
                    "2025-06-04",
                    11.48
                ],
                [
                    "2025-06-09",
                    5.90
                ],
                [
                    "2025-06-10",
                    -4.03
                ],
                [
                    "2025-06-26",
                    -6.44
                ],
                [
                    "2025-06-27",
                    -9.00
                ],
                [
                    "2025-06-30",
                    18.28
                ],
                [
                    "2025-07-01",
                    6.23
                ],
                [
                    "2025-07-02",
                    4.61
                ],
                [
                    "2025-07-03",
                    -6.73
                ],
                [
                    "2025-07-07",
                    19.96
                ],
                [
                    "2025-07-08",
                    -0.80
                ],
                [
                    "2025-07-23",
                    15.39
                ],
                [
                    "2025-07-24",
                    8.73
                ],
                [
                    "2025-07-25",
                    3.79
                ],
                [
                    "2025-07-28",
                    -1.10
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-04",
                    -1.40
                ],
                [
                    "2025-03-05",
                    9.09
                ],
                [
                    "2025-03-06",
                    3.52
                ],
                [
                    "2025-03-07",
                    1.23
                ],
                [
                    "2025-03-10",
                    -6.57
                ],
                [
                    "2025-03-11",
                    -1.95
                ],
                [
                    "2025-04-22",
                    -3.67
                ],
                [
                    "2025-04-24",
                    -8.46
                ],
                [
                    "2025-05-09",
                    9.74
                ],
                [
                    "2025-05-12",
                    -2.59
                ],
                [
                    "2025-05-13",
                    2.42
                ],
                [
                    "2025-05-23",
                    2.82
                ],
                [
                    "2025-05-27",
                    0.49
                ],
                [
                    "2025-05-28",
                    6.07
                ],
                [
                    "2025-05-29",
                    -0.57
                ],
                [
                    "2025-05-30",
                    1.58
                ],
                [
                    "2025-06-03",
                    -4.48
                ],
                [
                    "2025-07-21",
                    -2.06
                ],
                [
                    "2025-07-22",
                    5.36
                ],
                [
                    "2025-07-23",
                    -7.22
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-11",
                    9.16
                ],
                [
                    "2025-02-12",
                    12.61
                ],
                [
                    "2025-02-13",
                    17.32
                ],
                [
                    "2025-02-14",
                    12.26
                ],
                [
                    "2025-02-17",
                    1.50
                ],
                [
                    "2025-02-18",
                    15.70
                ],
                [
                    "2025-02-19",
                    4.40
                ],
                [
                    "2025-02-20",
                    -1.60
                ],
                [
                    "2025-02-21",
                    12.49
                ],
                [
                    "2025-02-24",
                    8.96
                ],
                [
                    "2025-02-25",
                    17.65
                ],
                [
                    "2025-02-26",
                    -8.44
                ],
                [
                    "2025-02-27",
                    6.72
                ],
                [
                    "2025-02-28",
                    -4.71
                ],
                [
                    "2025-03-03",
                    -1.18
                ],
                [
                    "2025-03-18",
                    4.67
                ],
                [
                    "2025-03-19",
                    2.06
                ],
                [
                    "2025-03-20",
                    12.08
                ],
                [
                    "2025-03-21",
                    6.23
                ],
                [
                    "2025-03-24",
                    13.99
                ],
                [
                    "2025-03-25",
                    3.29
                ],
                [
                    "2025-03-26",
                    1.74
                ],
                [
                    "2025-03-27",
                    7.16
                ],
                [
                    "2025-03-28",
                    7.70
                ],
                [
                    "2025-03-31",
                    11.99
                ],
                [
                    "2025-04-01",
                    -3.17
                ],
                [
                    "2025-04-02",
                    6.06
                ],
                [
                    "2025-04-03",
                    5.24
                ],
                [
                    "2025-04-07",
                    4.20
                ],
                [
                    "2025-04-08",
                    20.39
                ],
                [
                    "2025-04-09",
                    8.83
                ],
                [
                    "2025-04-10",
                    8.55
                ],
                [
                    "2025-04-11",
                    3.24
                ],
                [
                    "2025-04-14",
                    5.22
                ],
                [
                    "2025-04-15",
                    13.27
                ],
                [
                    "2025-04-16",
                    2.34
                ],
                [
                    "2025-04-17",
                    14.43
                ],
                [
                    "2025-04-18",
                    -0.29
                ],
                [
                    "2025-04-21",
                    -12.61
                ],
                [
                    "2025-04-23",
                    5.70
                ],
                [
                    "2025-04-25",
                    10.48
                ],
                [
                    "2025-04-28",
                    1.97
                ],
                [
                    "2025-04-29",
                    6.43
                ],
                [
                    "2025-04-30",
                    1.63
                ],
                [
                    "2025-05-06",
                    2.45
                ],
                [
                    "2025-05-07",
                    -1.68
                ],
                [
                    "2025-05-08",
                    -3.20
                ],
                [
                    "2025-05-14",
                    7.70
                ],
                [
                    "2025-05-15",
                    -0.34
                ],
                [
                    "2025-05-16",
                    -0.07
                ],
                [
                    "2025-05-19",
                    11.32
                ],
                [
                    "2025-05-20",
                    -7.64
                ],
                [
                    "2025-05-21",
                    0.80
                ],
                [
                    "2025-05-22",
                    -2.79
                ],
                [
                    "2025-06-12",
                    3.54
                ],
                [
                    "2025-06-13",
                    14.86
                ],
                [
                    "2025-06-16",
                    7.77
                ],
                [
                    "2025-06-17",
                    -3.97
                ],
                [
                    "2025-06-18",
                    6.38
                ],
                [
                    "2025-06-19",
                    7.10
                ],
                [
                    "2025-06-20",
                    11.89
                ],
                [
                    "2025-06-23",
                    -6.36
                ],
                [
                    "2025-06-24",
                    -8.08
                ],
                [
                    "2025-06-25",
                    -0.87
                ],
                [
                    "2025-07-09",
                    12.65
                ],
                [
                    "2025-07-10",
                    14.37
                ],
                [
                    "2025-07-11",
                    -2.23
                ],
                [
                    "2025-07-14",
                    16.56
                ],
                [
                    "2025-07-15",
                    -1.20
                ],
                [
                    "2025-07-16",
                    0.97
                ],
                [
                    "2025-07-17",
                    -0.98
                ],
                [
                    "2025-07-18",
                    -4.48
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600416 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_e5d56e147c8449d28715741cc941e593.setOption(option_e5d56e147c8449d28715741cc941e593);
            window.addEventListener('resize', function(){
                chart_e5d56e147c8449d28715741cc941e593.resize();
            })
    </script>
</body>
</html>
