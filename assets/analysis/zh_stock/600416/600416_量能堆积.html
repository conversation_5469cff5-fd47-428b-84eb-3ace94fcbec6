<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="0cf345df617540d69d4de10c5e8ba143" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_0cf345df617540d69d4de10c5e8ba143 = echarts.init(
            document.getElementById('0cf345df617540d69d4de10c5e8ba143'), 'white', {renderer: 'canvas'});
        var option_0cf345df617540d69d4de10c5e8ba143 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    10.23,
                    10.18,
                    10.05,
                    10.31
                ],
                [
                    10.20,
                    10.34,
                    10.15,
                    10.53
                ],
                [
                    10.31,
                    10.64,
                    10.25,
                    10.68
                ],
                [
                    10.65,
                    10.59,
                    10.48,
                    10.80
                ],
                [
                    10.56,
                    10.77,
                    10.56,
                    10.83
                ],
                [
                    10.75,
                    10.65,
                    10.56,
                    10.76
                ],
                [
                    10.62,
                    10.66,
                    10.53,
                    10.70
                ],
                [
                    10.68,
                    10.48,
                    10.48,
                    10.70
                ],
                [
                    10.48,
                    10.51,
                    10.42,
                    10.54
                ],
                [
                    10.50,
                    10.50,
                    10.43,
                    10.56
                ],
                [
                    10.49,
                    10.22,
                    10.17,
                    10.53
                ],
                [
                    10.18,
                    10.51,
                    10.18,
                    10.52
                ],
                [
                    10.51,
                    10.45,
                    10.32,
                    10.55
                ],
                [
                    10.43,
                    10.42,
                    10.34,
                    10.51
                ],
                [
                    10.41,
                    10.37,
                    10.28,
                    10.50
                ],
                [
                    10.24,
                    10.20,
                    10.15,
                    10.37
                ],
                [
                    10.16,
                    11.03,
                    10.16,
                    11.20
                ],
                [
                    11.00,
                    10.73,
                    10.56,
                    11.00
                ],
                [
                    10.67,
                    10.57,
                    10.55,
                    10.95
                ],
                [
                    10.55,
                    10.64,
                    10.54,
                    10.87
                ],
                [
                    10.59,
                    11.33,
                    10.55,
                    11.48
                ],
                [
                    11.16,
                    11.11,
                    11.06,
                    11.43
                ],
                [
                    11.19,
                    11.19,
                    11.15,
                    11.32
                ],
                [
                    11.20,
                    11.24,
                    11.11,
                    11.65
                ],
                [
                    11.20,
                    11.74,
                    11.17,
                    11.85
                ],
                [
                    11.50,
                    12.24,
                    11.46,
                    12.36
                ],
                [
                    12.25,
                    12.09,
                    12.00,
                    12.38
                ],
                [
                    12.09,
                    12.43,
                    11.92,
                    12.50
                ],
                [
                    12.50,
                    12.30,
                    12.05,
                    12.54
                ],
                [
                    12.40,
                    11.92,
                    11.90,
                    12.49
                ],
                [
                    11.90,
                    11.97,
                    11.86,
                    12.12
                ],
                [
                    11.97,
                    11.91,
                    11.85,
                    12.10
                ],
                [
                    11.88,
                    11.82,
                    11.71,
                    12.02
                ],
                [
                    11.77,
                    11.77,
                    11.69,
                    12.04
                ],
                [
                    11.84,
                    11.33,
                    11.11,
                    11.86
                ],
                [
                    11.26,
                    11.38,
                    11.23,
                    11.60
                ],
                [
                    11.32,
                    11.50,
                    11.27,
                    11.66
                ],
                [
                    11.43,
                    11.22,
                    11.07,
                    11.43
                ],
                [
                    11.22,
                    11.07,
                    11.06,
                    11.28
                ],
                [
                    11.02,
                    10.78,
                    10.64,
                    11.02
                ],
                [
                    10.78,
                    11.00,
                    10.77,
                    11.07
                ],
                [
                    10.96,
                    10.94,
                    10.85,
                    11.09
                ],
                [
                    10.86,
                    10.79,
                    10.73,
                    11.11
                ],
                [
                    10.31,
                    9.71,
                    9.71,
                    10.34
                ],
                [
                    9.72,
                    9.65,
                    9.48,
                    9.96
                ],
                [
                    9.51,
                    9.94,
                    9.13,
                    10.03
                ],
                [
                    9.98,
                    10.24,
                    9.98,
                    10.36
                ],
                [
                    10.20,
                    10.25,
                    10.10,
                    10.33
                ],
                [
                    10.27,
                    10.33,
                    10.24,
                    10.42
                ],
                [
                    10.33,
                    10.11,
                    10.05,
                    10.34
                ],
                [
                    10.04,
                    10.00,
                    9.78,
                    10.10
                ],
                [
                    9.94,
                    9.91,
                    9.90,
                    10.06
                ],
                [
                    9.94,
                    10.00,
                    9.82,
                    10.03
                ],
                [
                    10.00,
                    10.03,
                    9.93,
                    10.10
                ],
                [
                    10.02,
                    10.01,
                    9.95,
                    10.14
                ],
                [
                    10.04,
                    10.15,
                    10.02,
                    10.24
                ],
                [
                    10.15,
                    9.99,
                    9.94,
                    10.18
                ],
                [
                    9.96,
                    10.06,
                    9.96,
                    10.09
                ],
                [
                    10.10,
                    9.95,
                    9.90,
                    10.13
                ],
                [
                    9.90,
                    9.90,
                    9.87,
                    10.03
                ],
                [
                    9.87,
                    9.90,
                    9.78,
                    9.97
                ],
                [
                    9.94,
                    10.18,
                    9.91,
                    10.19
                ],
                [
                    10.25,
                    10.36,
                    10.12,
                    10.54
                ],
                [
                    10.30,
                    10.69,
                    10.23,
                    10.76
                ],
                [
                    10.70,
                    10.41,
                    10.38,
                    10.72
                ],
                [
                    10.40,
                    10.84,
                    10.40,
                    11.24
                ],
                [
                    10.87,
                    10.64,
                    10.59,
                    10.93
                ],
                [
                    10.63,
                    10.53,
                    10.46,
                    10.65
                ],
                [
                    10.50,
                    10.41,
                    10.38,
                    10.56
                ],
                [
                    10.39,
                    10.47,
                    10.38,
                    10.56
                ],
                [
                    10.50,
                    10.30,
                    10.20,
                    10.51
                ],
                [
                    10.30,
                    10.74,
                    10.21,
                    10.93
                ],
                [
                    10.74,
                    10.62,
                    10.55,
                    10.82
                ],
                [
                    10.60,
                    10.46,
                    10.44,
                    10.69
                ],
                [
                    10.46,
                    10.60,
                    10.42,
                    10.82
                ],
                [
                    10.85,
                    10.99,
                    10.73,
                    11.20
                ],
                [
                    10.96,
                    10.98,
                    10.79,
                    11.07
                ],
                [
                    10.98,
                    11.11,
                    10.94,
                    11.30
                ],
                [
                    11.15,
                    11.17,
                    11.07,
                    11.29
                ],
                [
                    11.12,
                    10.97,
                    10.89,
                    11.12
                ],
                [
                    10.95,
                    11.09,
                    10.86,
                    11.15
                ],
                [
                    11.06,
                    11.46,
                    11.06,
                    11.66
                ],
                [
                    11.44,
                    11.61,
                    11.42,
                    11.69
                ],
                [
                    11.59,
                    11.47,
                    11.41,
                    11.64
                ],
                [
                    11.50,
                    11.77,
                    11.48,
                    11.86
                ],
                [
                    11.76,
                    11.46,
                    11.40,
                    11.77
                ],
                [
                    11.54,
                    11.67,
                    11.54,
                    11.85
                ],
                [
                    11.71,
                    11.80,
                    11.65,
                    11.87
                ],
                [
                    11.85,
                    11.60,
                    11.60,
                    11.85
                ],
                [
                    11.50,
                    11.56,
                    11.21,
                    11.60
                ],
                [
                    11.61,
                    11.94,
                    11.52,
                    12.12
                ],
                [
                    11.88,
                    11.77,
                    11.67,
                    12.14
                ],
                [
                    11.71,
                    11.42,
                    11.38,
                    11.76
                ],
                [
                    11.39,
                    11.30,
                    11.23,
                    11.52
                ],
                [
                    11.22,
                    11.63,
                    11.16,
                    11.72
                ],
                [
                    11.63,
                    12.07,
                    11.56,
                    12.26
                ],
                [
                    12.10,
                    12.25,
                    11.97,
                    12.26
                ],
                [
                    12.28,
                    12.20,
                    12.15,
                    12.72
                ],
                [
                    12.20,
                    12.22,
                    12.13,
                    12.42
                ],
                [
                    12.21,
                    13.44,
                    12.15,
                    13.44
                ],
                [
                    13.17,
                    14.31,
                    13.00,
                    14.78
                ],
                [
                    14.03,
                    14.07,
                    13.60,
                    14.65
                ],
                [
                    13.76,
                    13.39,
                    13.36,
                    14.38
                ],
                [
                    13.47,
                    13.32,
                    12.98,
                    13.53
                ],
                [
                    13.32,
                    13.55,
                    13.10,
                    13.71
                ],
                [
                    13.46,
                    13.56,
                    13.41,
                    13.74
                ],
                [
                    13.59,
                    13.25,
                    13.19,
                    13.70
                ],
                [
                    13.11,
                    13.15,
                    13.01,
                    13.37
                ],
                [
                    13.16,
                    13.23,
                    13.07,
                    13.38
                ],
                [
                    13.15,
                    12.99,
                    12.88,
                    13.21
                ],
                [
                    13.17,
                    13.05,
                    12.88,
                    13.20
                ],
                [
                    13.01,
                    12.93,
                    12.87,
                    13.13
                ],
                [
                    12.93,
                    13.16,
                    12.82,
                    13.29
                ],
                [
                    13.16,
                    13.29,
                    13.06,
                    13.32
                ],
                [
                    13.33,
                    13.38,
                    13.19,
                    13.64
                ],
                [
                    13.39,
                    13.39,
                    13.35,
                    13.68
                ],
                [
                    13.35,
                    13.82,
                    13.23,
                    13.95
                ],
                [
                    13.90,
                    14.06,
                    13.78,
                    14.11
                ],
                [
                    13.95,
                    14.07,
                    13.87,
                    14.20
                ],
                [
                    14.02,
                    14.41,
                    14.00,
                    14.95
                ],
                [
                    14.52,
                    14.06,
                    13.83,
                    14.58
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u7a81\u7834",
            "symbol": "pin",
            "symbolSize": 16,
            "data": [
                [
                    "2025-07-01",
                    14.78
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#1f77b4"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u7a81\u7834"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600416 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_0cf345df617540d69d4de10c5e8ba143.setOption(option_0cf345df617540d69d4de10c5e8ba143);
            window.addEventListener('resize', function(){
                chart_0cf345df617540d69d4de10c5e8ba143.resize();
            })
    </script>
</body>
</html>
