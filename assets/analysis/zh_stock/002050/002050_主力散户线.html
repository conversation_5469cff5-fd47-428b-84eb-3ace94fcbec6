<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="a469a86bcb6d4f4ca4b5cbf6a140ec30" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_a469a86bcb6d4f4ca4b5cbf6a140ec30 = echarts.init(
            document.getElementById('a469a86bcb6d4f4ca4b5cbf6a140ec30'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_a469a86bcb6d4f4ca4b5cbf6a140ec30 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    31.13,
                    30.57,
                    30.3,
                    31.41
                ],
                [
                    31.44,
                    32.56,
                    30.87,
                    33.59
                ],
                [
                    32.5,
                    35.22,
                    32.29,
                    35.82
                ],
                [
                    34.48,
                    35.32,
                    34.12,
                    36.96
                ],
                [
                    34.87,
                    34.14,
                    33.47,
                    35.1
                ],
                [
                    33.78,
                    33.9,
                    33.54,
                    35.1
                ],
                [
                    32.21,
                    33.23,
                    31.34,
                    33.56
                ],
                [
                    33.0,
                    32.4,
                    32.24,
                    33.62
                ],
                [
                    32.31,
                    31.97,
                    31.67,
                    32.5
                ],
                [
                    32.05,
                    33.37,
                    32.05,
                    33.4
                ],
                [
                    33.06,
                    32.67,
                    32.41,
                    33.88
                ],
                [
                    32.45,
                    34.59,
                    32.31,
                    34.98
                ],
                [
                    34.45,
                    34.03,
                    33.8,
                    34.87
                ],
                [
                    33.74,
                    34.35,
                    33.65,
                    35.18
                ],
                [
                    34.3,
                    33.57,
                    32.89,
                    35.29
                ],
                [
                    32.8,
                    34.0,
                    32.6,
                    34.5
                ],
                [
                    33.7,
                    34.72,
                    33.63,
                    35.97
                ],
                [
                    34.24,
                    32.42,
                    31.51,
                    34.46
                ],
                [
                    31.83,
                    30.32,
                    30.16,
                    31.83
                ],
                [
                    30.5,
                    30.52,
                    29.62,
                    31.1
                ],
                [
                    30.1,
                    31.02,
                    30.1,
                    31.88
                ],
                [
                    31.0,
                    31.25,
                    30.8,
                    31.75
                ],
                [
                    31.53,
                    31.7,
                    31.4,
                    32.57
                ],
                [
                    31.55,
                    31.7,
                    31.07,
                    32.12
                ],
                [
                    32.09,
                    31.6,
                    31.34,
                    32.15
                ],
                [
                    30.49,
                    30.49,
                    30.09,
                    31.12
                ],
                [
                    30.8,
                    30.35,
                    30.33,
                    30.9
                ],
                [
                    30.45,
                    29.47,
                    29.19,
                    30.55
                ],
                [
                    29.4,
                    31.57,
                    29.27,
                    32.2
                ],
                [
                    31.2,
                    31.31,
                    30.58,
                    31.48
                ],
                [
                    31.4,
                    30.92,
                    30.66,
                    31.48
                ],
                [
                    30.82,
                    30.32,
                    30.09,
                    30.87
                ],
                [
                    30.5,
                    30.73,
                    30.06,
                    31.33
                ],
                [
                    30.45,
                    29.48,
                    29.4,
                    30.73
                ],
                [
                    29.7,
                    29.67,
                    29.0,
                    29.77
                ],
                [
                    30.03,
                    29.22,
                    29.1,
                    30.25
                ],
                [
                    29.0,
                    29.48,
                    29.0,
                    30.23
                ],
                [
                    29.31,
                    30.29,
                    29.03,
                    31.1
                ],
                [
                    30.0,
                    29.67,
                    29.55,
                    30.4
                ],
                [
                    29.45,
                    28.83,
                    28.37,
                    29.55
                ],
                [
                    28.93,
                    28.57,
                    28.49,
                    29.12
                ],
                [
                    28.67,
                    28.84,
                    28.47,
                    29.16
                ],
                [
                    28.17,
                    28.01,
                    27.94,
                    28.86
                ],
                [
                    25.21,
                    25.21,
                    25.21,
                    25.74
                ],
                [
                    24.55,
                    22.86,
                    22.69,
                    24.8
                ],
                [
                    22.06,
                    23.15,
                    21.26,
                    23.68
                ],
                [
                    24.98,
                    24.4,
                    24.33,
                    25.45
                ],
                [
                    23.98,
                    25.05,
                    23.8,
                    25.35
                ],
                [
                    25.44,
                    24.81,
                    24.75,
                    25.58
                ],
                [
                    25.05,
                    24.75,
                    24.6,
                    25.8
                ],
                [
                    24.5,
                    24.0,
                    23.64,
                    24.57
                ],
                [
                    23.95,
                    23.98,
                    23.8,
                    24.38
                ],
                [
                    23.96,
                    24.08,
                    23.7,
                    24.16
                ],
                [
                    24.01,
                    25.72,
                    23.77,
                    26.09
                ],
                [
                    25.6,
                    24.91,
                    24.86,
                    25.7
                ],
                [
                    25.65,
                    25.83,
                    25.51,
                    26.16
                ],
                [
                    25.83,
                    25.21,
                    25.13,
                    25.99
                ],
                [
                    25.31,
                    25.45,
                    25.3,
                    25.95
                ],
                [
                    25.58,
                    25.56,
                    25.34,
                    26.0
                ],
                [
                    25.56,
                    26.04,
                    25.4,
                    26.29
                ],
                [
                    26.58,
                    26.8,
                    26.01,
                    27.08
                ],
                [
                    27.0,
                    27.32,
                    26.87,
                    27.54
                ],
                [
                    27.98,
                    26.94,
                    26.66,
                    28.05
                ],
                [
                    26.89,
                    27.05,
                    26.79,
                    27.33
                ],
                [
                    27.12,
                    26.59,
                    26.42,
                    27.5
                ],
                [
                    27.39,
                    27.9,
                    27.35,
                    28.1
                ],
                [
                    28.19,
                    27.57,
                    27.5,
                    28.2
                ],
                [
                    27.5,
                    27.56,
                    27.16,
                    27.95
                ],
                [
                    27.49,
                    26.71,
                    26.7,
                    27.57
                ],
                [
                    26.58,
                    27.0,
                    26.52,
                    27.4
                ],
                [
                    26.9,
                    26.66,
                    26.13,
                    26.96
                ],
                [
                    26.54,
                    26.69,
                    26.27,
                    26.97
                ],
                [
                    26.66,
                    26.89,
                    26.18,
                    27.03
                ],
                [
                    26.5,
                    26.32,
                    26.29,
                    26.64
                ],
                [
                    26.27,
                    27.09,
                    26.16,
                    27.94
                ],
                [
                    26.9,
                    26.54,
                    26.3,
                    27.08
                ],
                [
                    26.4,
                    25.63,
                    25.48,
                    26.48
                ],
                [
                    25.68,
                    25.72,
                    25.5,
                    26.19
                ],
                [
                    25.65,
                    26.57,
                    25.6,
                    26.72
                ],
                [
                    26.34,
                    25.85,
                    25.64,
                    26.39
                ],
                [
                    25.58,
                    25.76,
                    25.46,
                    25.91
                ],
                [
                    25.83,
                    26.18,
                    25.74,
                    26.48
                ],
                [
                    26.48,
                    27.09,
                    25.92,
                    27.25
                ],
                [
                    26.54,
                    26.15,
                    25.88,
                    26.6
                ],
                [
                    26.14,
                    25.92,
                    25.8,
                    26.14
                ],
                [
                    26.02,
                    25.93,
                    25.55,
                    26.1
                ],
                [
                    26.08,
                    26.21,
                    26.0,
                    26.56
                ],
                [
                    26.12,
                    25.88,
                    25.83,
                    26.19
                ],
                [
                    25.6,
                    25.26,
                    25.2,
                    25.77
                ],
                [
                    25.15,
                    25.38,
                    25.08,
                    25.41
                ],
                [
                    25.4,
                    25.22,
                    25.18,
                    25.66
                ],
                [
                    25.14,
                    25.48,
                    25.02,
                    25.58
                ],
                [
                    25.49,
                    26.12,
                    25.36,
                    26.84
                ],
                [
                    25.8,
                    24.95,
                    24.74,
                    25.85
                ],
                [
                    24.2,
                    24.5,
                    23.8,
                    24.62
                ],
                [
                    24.8,
                    25.7,
                    24.79,
                    25.72
                ],
                [
                    26.15,
                    26.49,
                    25.82,
                    26.5
                ],
                [
                    26.48,
                    26.12,
                    26.1,
                    26.67
                ],
                [
                    26.25,
                    26.04,
                    25.98,
                    26.34
                ],
                [
                    26.16,
                    26.38,
                    26.05,
                    26.45
                ],
                [
                    26.39,
                    26.26,
                    26.08,
                    26.43
                ],
                [
                    26.03,
                    25.76,
                    25.7,
                    26.11
                ],
                [
                    25.71,
                    25.96,
                    25.71,
                    26.11
                ],
                [
                    25.87,
                    25.67,
                    25.52,
                    25.99
                ],
                [
                    25.52,
                    25.17,
                    25.11,
                    25.52
                ],
                [
                    25.17,
                    25.47,
                    25.06,
                    25.51
                ],
                [
                    25.57,
                    25.47,
                    25.45,
                    25.9
                ],
                [
                    25.36,
                    25.42,
                    25.25,
                    25.62
                ],
                [
                    25.45,
                    25.75,
                    25.3,
                    25.98
                ],
                [
                    25.7,
                    25.87,
                    25.56,
                    26.08
                ],
                [
                    25.98,
                    26.7,
                    25.78,
                    26.7
                ],
                [
                    26.6,
                    27.53,
                    26.57,
                    28.17
                ],
                [
                    27.3,
                    27.9,
                    27.23,
                    27.93
                ],
                [
                    27.9,
                    27.68,
                    27.47,
                    27.91
                ],
                [
                    27.75,
                    27.8,
                    27.43,
                    28.3
                ],
                [
                    27.57,
                    27.62,
                    27.31,
                    27.95
                ],
                [
                    27.5,
                    27.46,
                    27.35,
                    27.73
                ],
                [
                    27.28,
                    27.79,
                    27.28,
                    27.91
                ],
                [
                    27.72,
                    27.07,
                    26.96,
                    27.74
                ],
                [
                    27.48,
                    27.34,
                    27.21,
                    27.87
                ],
                [
                    27.19,
                    27.16,
                    26.96,
                    27.32
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -4.67
                ],
                [
                    "2025-02-05",
                    4.54
                ],
                [
                    "2025-02-06",
                    -0.59
                ],
                [
                    "2025-02-07",
                    -11.73
                ],
                [
                    "2025-02-10",
                    -10.04
                ],
                [
                    "2025-02-11",
                    -4.51
                ],
                [
                    "2025-02-12",
                    -7.15
                ],
                [
                    "2025-02-13",
                    -10.57
                ],
                [
                    "2025-02-14",
                    -3.13
                ],
                [
                    "2025-02-17",
                    4.44
                ],
                [
                    "2025-02-18",
                    -6.68
                ],
                [
                    "2025-02-19",
                    8.35
                ],
                [
                    "2025-02-20",
                    -10.71
                ],
                [
                    "2025-02-21",
                    -4.84
                ],
                [
                    "2025-02-24",
                    -7.67
                ],
                [
                    "2025-02-25",
                    -0.50
                ],
                [
                    "2025-02-26",
                    4.67
                ],
                [
                    "2025-02-27",
                    -20.05
                ],
                [
                    "2025-02-28",
                    -13.30
                ],
                [
                    "2025-03-03",
                    1.80
                ],
                [
                    "2025-03-04",
                    -1.78
                ],
                [
                    "2025-03-05",
                    -6.17
                ],
                [
                    "2025-03-06",
                    1.84
                ],
                [
                    "2025-03-07",
                    3.27
                ],
                [
                    "2025-03-10",
                    -3.29
                ],
                [
                    "2025-03-11",
                    -9.87
                ],
                [
                    "2025-03-12",
                    -7.55
                ],
                [
                    "2025-03-13",
                    -16.11
                ],
                [
                    "2025-03-14",
                    13.33
                ],
                [
                    "2025-03-17",
                    -6.39
                ],
                [
                    "2025-03-18",
                    -9.19
                ],
                [
                    "2025-03-19",
                    -10.85
                ],
                [
                    "2025-03-20",
                    9.19
                ],
                [
                    "2025-03-21",
                    -14.49
                ],
                [
                    "2025-03-24",
                    -3.27
                ],
                [
                    "2025-03-25",
                    -3.26
                ],
                [
                    "2025-03-26",
                    6.37
                ],
                [
                    "2025-03-27",
                    12.41
                ],
                [
                    "2025-03-28",
                    -13.16
                ],
                [
                    "2025-03-31",
                    -12.19
                ],
                [
                    "2025-04-01",
                    -7.56
                ],
                [
                    "2025-04-02",
                    -0.18
                ],
                [
                    "2025-04-03",
                    -10.16
                ],
                [
                    "2025-04-07",
                    -26.34
                ],
                [
                    "2025-04-08",
                    -11.33
                ],
                [
                    "2025-04-09",
                    -3.63
                ],
                [
                    "2025-04-10",
                    3.42
                ],
                [
                    "2025-04-11",
                    3.80
                ],
                [
                    "2025-04-14",
                    -6.42
                ],
                [
                    "2025-04-15",
                    -2.44
                ],
                [
                    "2025-04-16",
                    -10.69
                ],
                [
                    "2025-04-17",
                    -0.33
                ],
                [
                    "2025-04-18",
                    -4.75
                ],
                [
                    "2025-04-21",
                    9.48
                ],
                [
                    "2025-04-22",
                    -8.18
                ],
                [
                    "2025-04-23",
                    5.84
                ],
                [
                    "2025-04-24",
                    -10.61
                ],
                [
                    "2025-04-25",
                    7.99
                ],
                [
                    "2025-04-28",
                    -8.37
                ],
                [
                    "2025-04-29",
                    8.84
                ],
                [
                    "2025-04-30",
                    4.53
                ],
                [
                    "2025-05-06",
                    -6.24
                ],
                [
                    "2025-05-07",
                    -7.94
                ],
                [
                    "2025-05-08",
                    2.28
                ],
                [
                    "2025-05-09",
                    -17.49
                ],
                [
                    "2025-05-12",
                    6.37
                ],
                [
                    "2025-05-13",
                    -6.86
                ],
                [
                    "2025-05-14",
                    -1.46
                ],
                [
                    "2025-05-15",
                    -14.22
                ],
                [
                    "2025-05-16",
                    6.52
                ],
                [
                    "2025-05-19",
                    -4.91
                ],
                [
                    "2025-05-20",
                    3.46
                ],
                [
                    "2025-05-21",
                    1.82
                ],
                [
                    "2025-05-22",
                    -3.00
                ],
                [
                    "2025-05-23",
                    11.95
                ],
                [
                    "2025-05-26",
                    -6.07
                ],
                [
                    "2025-05-27",
                    -10.11
                ],
                [
                    "2025-05-28",
                    5.77
                ],
                [
                    "2025-05-29",
                    4.30
                ],
                [
                    "2025-05-30",
                    -15.82
                ],
                [
                    "2025-06-03",
                    0.54
                ],
                [
                    "2025-06-04",
                    11.31
                ],
                [
                    "2025-06-05",
                    9.55
                ],
                [
                    "2025-06-06",
                    -5.08
                ],
                [
                    "2025-06-09",
                    -14.72
                ],
                [
                    "2025-06-10",
                    -2.06
                ],
                [
                    "2025-06-11",
                    12.10
                ],
                [
                    "2025-06-12",
                    -12.13
                ],
                [
                    "2025-06-13",
                    -15.32
                ],
                [
                    "2025-06-16",
                    4.04
                ],
                [
                    "2025-06-17",
                    -5.63
                ],
                [
                    "2025-06-18",
                    6.24
                ],
                [
                    "2025-06-19",
                    7.67
                ],
                [
                    "2025-06-20",
                    -13.52
                ],
                [
                    "2025-06-23",
                    -9.10
                ],
                [
                    "2025-06-24",
                    12.49
                ],
                [
                    "2025-06-25",
                    1.45
                ],
                [
                    "2025-06-26",
                    -5.15
                ],
                [
                    "2025-06-27",
                    -3.88
                ],
                [
                    "2025-06-30",
                    4.37
                ],
                [
                    "2025-07-01",
                    -1.85
                ],
                [
                    "2025-07-02",
                    -12.46
                ],
                [
                    "2025-07-03",
                    2.82
                ],
                [
                    "2025-07-04",
                    -14.74
                ],
                [
                    "2025-07-07",
                    -9.36
                ],
                [
                    "2025-07-08",
                    -2.70
                ],
                [
                    "2025-07-09",
                    2.01
                ],
                [
                    "2025-07-10",
                    -8.28
                ],
                [
                    "2025-07-11",
                    8.34
                ],
                [
                    "2025-07-14",
                    4.74
                ],
                [
                    "2025-07-15",
                    11.88
                ],
                [
                    "2025-07-16",
                    9.16
                ],
                [
                    "2025-07-17",
                    -0.88
                ],
                [
                    "2025-07-18",
                    -7.16
                ],
                [
                    "2025-07-21",
                    -11.09
                ],
                [
                    "2025-07-22",
                    -4.88
                ],
                [
                    "2025-07-23",
                    -11.56
                ],
                [
                    "2025-07-24",
                    2.82
                ],
                [
                    "2025-07-25",
                    -16.08
                ],
                [
                    "2025-07-28",
                    4.02
                ],
                [
                    "2025-07-29",
                    -10.89
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    1.08
                ],
                [
                    "2025-02-05",
                    -1.88
                ],
                [
                    "2025-02-06",
                    -0.53
                ],
                [
                    "2025-02-07",
                    2.48
                ],
                [
                    "2025-02-10",
                    1.38
                ],
                [
                    "2025-02-11",
                    -0.06
                ],
                [
                    "2025-02-12",
                    -0.38
                ],
                [
                    "2025-02-13",
                    0.68
                ],
                [
                    "2025-02-14",
                    -5.57
                ],
                [
                    "2025-02-17",
                    -0.21
                ],
                [
                    "2025-02-18",
                    3.22
                ],
                [
                    "2025-02-19",
                    -3.30
                ],
                [
                    "2025-02-20",
                    2.77
                ],
                [
                    "2025-02-21",
                    0.86
                ],
                [
                    "2025-02-24",
                    1.19
                ],
                [
                    "2025-02-25",
                    1.17
                ],
                [
                    "2025-02-26",
                    -0.61
                ],
                [
                    "2025-02-27",
                    6.62
                ],
                [
                    "2025-02-28",
                    -0.29
                ],
                [
                    "2025-03-03",
                    -3.29
                ],
                [
                    "2025-03-04",
                    2.97
                ],
                [
                    "2025-03-05",
                    6.18
                ],
                [
                    "2025-03-06",
                    -1.64
                ],
                [
                    "2025-03-07",
                    -4.34
                ],
                [
                    "2025-03-10",
                    1.96
                ],
                [
                    "2025-03-11",
                    -0.87
                ],
                [
                    "2025-03-12",
                    -0.94
                ],
                [
                    "2025-03-13",
                    1.86
                ],
                [
                    "2025-03-14",
                    -7.69
                ],
                [
                    "2025-03-17",
                    6.04
                ],
                [
                    "2025-03-18",
                    1.38
                ],
                [
                    "2025-03-19",
                    -1.64
                ],
                [
                    "2025-03-20",
                    -2.28
                ],
                [
                    "2025-03-21",
                    2.04
                ],
                [
                    "2025-03-24",
                    -2.34
                ],
                [
                    "2025-03-25",
                    0.26
                ],
                [
                    "2025-03-26",
                    -2.14
                ],
                [
                    "2025-03-27",
                    -6.44
                ],
                [
                    "2025-03-28",
                    4.69
                ],
                [
                    "2025-03-31",
                    1.13
                ],
                [
                    "2025-04-01",
                    0.84
                ],
                [
                    "2025-04-02",
                    1.32
                ],
                [
                    "2025-04-03",
                    1.16
                ],
                [
                    "2025-04-07",
                    10.89
                ],
                [
                    "2025-04-08",
                    2.71
                ],
                [
                    "2025-04-09",
                    -1.45
                ],
                [
                    "2025-04-10",
                    -8.02
                ],
                [
                    "2025-04-11",
                    -5.39
                ],
                [
                    "2025-04-14",
                    0.06
                ],
                [
                    "2025-04-15",
                    3.35
                ],
                [
                    "2025-04-16",
                    -0.93
                ],
                [
                    "2025-04-17",
                    -1.27
                ],
                [
                    "2025-04-18",
                    -0.36
                ],
                [
                    "2025-04-21",
                    -1.38
                ],
                [
                    "2025-04-22",
                    5.94
                ],
                [
                    "2025-04-23",
                    -2.73
                ],
                [
                    "2025-04-24",
                    4.24
                ],
                [
                    "2025-04-25",
                    0.31
                ],
                [
                    "2025-04-28",
                    4.96
                ],
                [
                    "2025-04-29",
                    -5.03
                ],
                [
                    "2025-04-30",
                    1.26
                ],
                [
                    "2025-05-06",
                    6.91
                ],
                [
                    "2025-05-07",
                    1.76
                ],
                [
                    "2025-05-08",
                    -3.44
                ],
                [
                    "2025-05-09",
                    4.11
                ],
                [
                    "2025-05-12",
                    -5.06
                ],
                [
                    "2025-05-13",
                    2.50
                ],
                [
                    "2025-05-14",
                    0.06
                ],
                [
                    "2025-05-15",
                    5.92
                ],
                [
                    "2025-05-16",
                    -3.74
                ],
                [
                    "2025-05-19",
                    1.19
                ],
                [
                    "2025-05-20",
                    -5.71
                ],
                [
                    "2025-05-21",
                    0.80
                ],
                [
                    "2025-05-22",
                    0.30
                ],
                [
                    "2025-05-23",
                    -5.51
                ],
                [
                    "2025-05-26",
                    7.20
                ],
                [
                    "2025-05-27",
                    2.48
                ],
                [
                    "2025-05-28",
                    -0.30
                ],
                [
                    "2025-05-29",
                    -6.60
                ],
                [
                    "2025-05-30",
                    8.54
                ],
                [
                    "2025-06-03",
                    -7.19
                ],
                [
                    "2025-06-04",
                    -3.28
                ],
                [
                    "2025-06-05",
                    -1.32
                ],
                [
                    "2025-06-06",
                    1.82
                ],
                [
                    "2025-06-09",
                    1.98
                ],
                [
                    "2025-06-10",
                    -2.17
                ],
                [
                    "2025-06-11",
                    -5.40
                ],
                [
                    "2025-06-12",
                    3.70
                ],
                [
                    "2025-06-13",
                    1.29
                ],
                [
                    "2025-06-16",
                    -5.58
                ],
                [
                    "2025-06-17",
                    -4.16
                ],
                [
                    "2025-06-18",
                    -5.03
                ],
                [
                    "2025-06-19",
                    -4.97
                ],
                [
                    "2025-06-20",
                    4.15
                ],
                [
                    "2025-06-23",
                    0.91
                ],
                [
                    "2025-06-24",
                    -10.87
                ],
                [
                    "2025-06-25",
                    -1.95
                ],
                [
                    "2025-06-26",
                    -2.34
                ],
                [
                    "2025-06-27",
                    2.48
                ],
                [
                    "2025-06-30",
                    0.58
                ],
                [
                    "2025-07-01",
                    0.04
                ],
                [
                    "2025-07-02",
                    3.88
                ],
                [
                    "2025-07-03",
                    -2.36
                ],
                [
                    "2025-07-04",
                    3.85
                ],
                [
                    "2025-07-07",
                    -0.49
                ],
                [
                    "2025-07-08",
                    -0.51
                ],
                [
                    "2025-07-09",
                    1.28
                ],
                [
                    "2025-07-10",
                    -1.23
                ],
                [
                    "2025-07-11",
                    -2.02
                ],
                [
                    "2025-07-14",
                    -3.54
                ],
                [
                    "2025-07-15",
                    -1.03
                ],
                [
                    "2025-07-16",
                    -2.34
                ],
                [
                    "2025-07-17",
                    -2.94
                ],
                [
                    "2025-07-18",
                    1.81
                ],
                [
                    "2025-07-21",
                    4.20
                ],
                [
                    "2025-07-22",
                    -0.92
                ],
                [
                    "2025-07-23",
                    2.75
                ],
                [
                    "2025-07-24",
                    -2.68
                ],
                [
                    "2025-07-25",
                    -0.18
                ],
                [
                    "2025-07-28",
                    -5.42
                ],
                [
                    "2025-07-29",
                    1.59
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    3.59
                ],
                [
                    "2025-02-05",
                    -2.66
                ],
                [
                    "2025-02-06",
                    1.12
                ],
                [
                    "2025-02-07",
                    9.24
                ],
                [
                    "2025-02-10",
                    8.66
                ],
                [
                    "2025-02-11",
                    4.57
                ],
                [
                    "2025-02-12",
                    7.54
                ],
                [
                    "2025-02-13",
                    9.89
                ],
                [
                    "2025-02-14",
                    8.71
                ],
                [
                    "2025-02-17",
                    -4.23
                ],
                [
                    "2025-02-18",
                    3.46
                ],
                [
                    "2025-02-19",
                    -5.04
                ],
                [
                    "2025-02-20",
                    7.94
                ],
                [
                    "2025-02-21",
                    3.98
                ],
                [
                    "2025-02-24",
                    6.48
                ],
                [
                    "2025-02-25",
                    -0.67
                ],
                [
                    "2025-02-26",
                    -4.06
                ],
                [
                    "2025-02-27",
                    13.43
                ],
                [
                    "2025-02-28",
                    13.60
                ],
                [
                    "2025-03-03",
                    1.48
                ],
                [
                    "2025-03-04",
                    -1.19
                ],
                [
                    "2025-03-05",
                    0.00
                ],
                [
                    "2025-03-06",
                    -0.20
                ],
                [
                    "2025-03-07",
                    1.06
                ],
                [
                    "2025-03-10",
                    1.33
                ],
                [
                    "2025-03-11",
                    10.75
                ],
                [
                    "2025-03-12",
                    8.49
                ],
                [
                    "2025-03-13",
                    14.25
                ],
                [
                    "2025-03-14",
                    -5.64
                ],
                [
                    "2025-03-17",
                    0.35
                ],
                [
                    "2025-03-18",
                    7.82
                ],
                [
                    "2025-03-19",
                    12.49
                ],
                [
                    "2025-03-20",
                    -6.90
                ],
                [
                    "2025-03-21",
                    12.45
                ],
                [
                    "2025-03-24",
                    5.62
                ],
                [
                    "2025-03-25",
                    3.00
                ],
                [
                    "2025-03-26",
                    -4.23
                ],
                [
                    "2025-03-27",
                    -5.97
                ],
                [
                    "2025-03-28",
                    8.47
                ],
                [
                    "2025-03-31",
                    11.06
                ],
                [
                    "2025-04-01",
                    6.72
                ],
                [
                    "2025-04-02",
                    -1.14
                ],
                [
                    "2025-04-03",
                    9.00
                ],
                [
                    "2025-04-07",
                    15.44
                ],
                [
                    "2025-04-08",
                    8.62
                ],
                [
                    "2025-04-09",
                    5.08
                ],
                [
                    "2025-04-10",
                    4.59
                ],
                [
                    "2025-04-11",
                    1.59
                ],
                [
                    "2025-04-14",
                    6.36
                ],
                [
                    "2025-04-15",
                    -0.91
                ],
                [
                    "2025-04-16",
                    11.63
                ],
                [
                    "2025-04-17",
                    1.60
                ],
                [
                    "2025-04-18",
                    5.12
                ],
                [
                    "2025-04-21",
                    -8.10
                ],
                [
                    "2025-04-22",
                    2.24
                ],
                [
                    "2025-04-23",
                    -3.12
                ],
                [
                    "2025-04-24",
                    6.38
                ],
                [
                    "2025-04-25",
                    -8.29
                ],
                [
                    "2025-04-28",
                    3.41
                ],
                [
                    "2025-04-29",
                    -3.81
                ],
                [
                    "2025-04-30",
                    -5.79
                ],
                [
                    "2025-05-06",
                    -0.68
                ],
                [
                    "2025-05-07",
                    6.19
                ],
                [
                    "2025-05-08",
                    1.16
                ],
                [
                    "2025-05-09",
                    13.38
                ],
                [
                    "2025-05-12",
                    -1.32
                ],
                [
                    "2025-05-13",
                    4.36
                ],
                [
                    "2025-05-14",
                    1.41
                ],
                [
                    "2025-05-15",
                    8.30
                ],
                [
                    "2025-05-16",
                    -2.78
                ],
                [
                    "2025-05-19",
                    3.72
                ],
                [
                    "2025-05-20",
                    2.24
                ],
                [
                    "2025-05-21",
                    -2.63
                ],
                [
                    "2025-05-22",
                    2.70
                ],
                [
                    "2025-05-23",
                    -6.43
                ],
                [
                    "2025-05-26",
                    -1.13
                ],
                [
                    "2025-05-27",
                    7.64
                ],
                [
                    "2025-05-28",
                    -5.47
                ],
                [
                    "2025-05-29",
                    2.30
                ],
                [
                    "2025-05-30",
                    7.28
                ],
                [
                    "2025-06-03",
                    6.65
                ],
                [
                    "2025-06-04",
                    -8.03
                ],
                [
                    "2025-06-05",
                    -8.23
                ],
                [
                    "2025-06-06",
                    3.27
                ],
                [
                    "2025-06-09",
                    12.74
                ],
                [
                    "2025-06-10",
                    4.23
                ],
                [
                    "2025-06-11",
                    -6.70
                ],
                [
                    "2025-06-12",
                    8.42
                ],
                [
                    "2025-06-13",
                    14.03
                ],
                [
                    "2025-06-16",
                    1.53
                ],
                [
                    "2025-06-17",
                    9.79
                ],
                [
                    "2025-06-18",
                    -1.21
                ],
                [
                    "2025-06-19",
                    -2.69
                ],
                [
                    "2025-06-20",
                    9.37
                ],
                [
                    "2025-06-23",
                    8.19
                ],
                [
                    "2025-06-24",
                    -1.62
                ],
                [
                    "2025-06-25",
                    0.50
                ],
                [
                    "2025-06-26",
                    7.49
                ],
                [
                    "2025-06-27",
                    1.40
                ],
                [
                    "2025-06-30",
                    -4.94
                ],
                [
                    "2025-07-01",
                    1.80
                ],
                [
                    "2025-07-02",
                    8.57
                ],
                [
                    "2025-07-03",
                    -0.46
                ],
                [
                    "2025-07-04",
                    10.88
                ],
                [
                    "2025-07-07",
                    9.86
                ],
                [
                    "2025-07-08",
                    3.21
                ],
                [
                    "2025-07-09",
                    -3.29
                ],
                [
                    "2025-07-10",
                    9.52
                ],
                [
                    "2025-07-11",
                    -6.32
                ],
                [
                    "2025-07-14",
                    -1.21
                ],
                [
                    "2025-07-15",
                    -10.85
                ],
                [
                    "2025-07-16",
                    -6.82
                ],
                [
                    "2025-07-17",
                    3.83
                ],
                [
                    "2025-07-18",
                    5.35
                ],
                [
                    "2025-07-21",
                    6.89
                ],
                [
                    "2025-07-22",
                    5.79
                ],
                [
                    "2025-07-23",
                    8.81
                ],
                [
                    "2025-07-24",
                    -0.14
                ],
                [
                    "2025-07-25",
                    16.26
                ],
                [
                    "2025-07-28",
                    1.40
                ],
                [
                    "2025-07-29",
                    9.30
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-04-23",
                    5.84
                ],
                [
                    "2025-04-25",
                    7.99
                ],
                [
                    "2025-04-29",
                    8.84
                ],
                [
                    "2025-04-30",
                    4.53
                ],
                [
                    "2025-05-06",
                    -6.24
                ],
                [
                    "2025-05-08",
                    2.28
                ],
                [
                    "2025-05-23",
                    11.95
                ],
                [
                    "2025-05-26",
                    -6.07
                ],
                [
                    "2025-05-29",
                    4.30
                ],
                [
                    "2025-06-05",
                    9.55
                ],
                [
                    "2025-07-15",
                    11.88
                ],
                [
                    "2025-07-16",
                    9.16
                ],
                [
                    "2025-07-17",
                    -0.88
                ],
                [
                    "2025-07-18",
                    -7.16
                ],
                [
                    "2025-07-21",
                    -11.09
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-04-23",
                    -2.73
                ],
                [
                    "2025-04-25",
                    0.31
                ],
                [
                    "2025-04-29",
                    -5.03
                ],
                [
                    "2025-04-30",
                    1.26
                ],
                [
                    "2025-05-06",
                    6.91
                ],
                [
                    "2025-05-07",
                    1.76
                ],
                [
                    "2025-05-08",
                    -3.44
                ],
                [
                    "2025-05-28",
                    -0.30
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    8.66
                ],
                [
                    "2025-02-11",
                    4.57
                ],
                [
                    "2025-02-12",
                    7.54
                ],
                [
                    "2025-02-13",
                    9.89
                ],
                [
                    "2025-02-14",
                    8.71
                ],
                [
                    "2025-02-17",
                    -4.23
                ],
                [
                    "2025-02-18",
                    3.46
                ],
                [
                    "2025-02-19",
                    -5.04
                ],
                [
                    "2025-02-20",
                    7.94
                ],
                [
                    "2025-02-21",
                    3.98
                ],
                [
                    "2025-02-24",
                    6.48
                ],
                [
                    "2025-02-25",
                    -0.67
                ],
                [
                    "2025-02-26",
                    -4.06
                ],
                [
                    "2025-02-27",
                    13.43
                ],
                [
                    "2025-02-28",
                    13.60
                ],
                [
                    "2025-03-03",
                    1.48
                ],
                [
                    "2025-03-04",
                    -1.19
                ],
                [
                    "2025-03-05",
                    0.00
                ],
                [
                    "2025-03-06",
                    -0.20
                ],
                [
                    "2025-03-07",
                    1.06
                ],
                [
                    "2025-03-10",
                    1.33
                ],
                [
                    "2025-03-11",
                    10.75
                ],
                [
                    "2025-03-12",
                    8.49
                ],
                [
                    "2025-03-13",
                    14.25
                ],
                [
                    "2025-03-14",
                    -5.64
                ],
                [
                    "2025-03-17",
                    0.35
                ],
                [
                    "2025-03-18",
                    7.82
                ],
                [
                    "2025-03-19",
                    12.49
                ],
                [
                    "2025-03-20",
                    -6.90
                ],
                [
                    "2025-03-21",
                    12.45
                ],
                [
                    "2025-03-24",
                    5.62
                ],
                [
                    "2025-03-25",
                    3.00
                ],
                [
                    "2025-03-26",
                    -4.23
                ],
                [
                    "2025-03-27",
                    -5.97
                ],
                [
                    "2025-03-28",
                    8.47
                ],
                [
                    "2025-03-31",
                    11.06
                ],
                [
                    "2025-04-01",
                    6.72
                ],
                [
                    "2025-04-02",
                    -1.14
                ],
                [
                    "2025-04-03",
                    9.00
                ],
                [
                    "2025-04-07",
                    15.44
                ],
                [
                    "2025-04-08",
                    8.62
                ],
                [
                    "2025-04-09",
                    5.08
                ],
                [
                    "2025-04-10",
                    4.59
                ],
                [
                    "2025-04-11",
                    1.59
                ],
                [
                    "2025-04-14",
                    6.36
                ],
                [
                    "2025-04-15",
                    -0.91
                ],
                [
                    "2025-04-16",
                    11.63
                ],
                [
                    "2025-04-17",
                    1.60
                ],
                [
                    "2025-04-18",
                    5.12
                ],
                [
                    "2025-04-21",
                    -8.10
                ],
                [
                    "2025-04-22",
                    2.24
                ],
                [
                    "2025-04-24",
                    6.38
                ],
                [
                    "2025-04-28",
                    3.41
                ],
                [
                    "2025-05-09",
                    13.38
                ],
                [
                    "2025-05-12",
                    -1.32
                ],
                [
                    "2025-05-13",
                    4.36
                ],
                [
                    "2025-05-14",
                    1.41
                ],
                [
                    "2025-05-15",
                    8.30
                ],
                [
                    "2025-05-16",
                    -2.78
                ],
                [
                    "2025-05-19",
                    3.72
                ],
                [
                    "2025-05-20",
                    2.24
                ],
                [
                    "2025-05-21",
                    -2.63
                ],
                [
                    "2025-05-27",
                    7.64
                ],
                [
                    "2025-05-30",
                    7.28
                ],
                [
                    "2025-06-03",
                    6.65
                ],
                [
                    "2025-06-10",
                    4.23
                ],
                [
                    "2025-06-11",
                    -6.70
                ],
                [
                    "2025-06-12",
                    8.42
                ],
                [
                    "2025-06-13",
                    14.03
                ],
                [
                    "2025-06-16",
                    1.53
                ],
                [
                    "2025-06-17",
                    9.79
                ],
                [
                    "2025-06-18",
                    -1.21
                ],
                [
                    "2025-06-19",
                    -2.69
                ],
                [
                    "2025-06-20",
                    9.37
                ],
                [
                    "2025-06-23",
                    8.19
                ],
                [
                    "2025-06-25",
                    0.50
                ],
                [
                    "2025-06-26",
                    7.49
                ],
                [
                    "2025-06-27",
                    1.40
                ],
                [
                    "2025-07-01",
                    1.80
                ],
                [
                    "2025-07-02",
                    8.57
                ],
                [
                    "2025-07-03",
                    -0.46
                ],
                [
                    "2025-07-04",
                    10.88
                ],
                [
                    "2025-07-07",
                    9.86
                ],
                [
                    "2025-07-08",
                    3.21
                ],
                [
                    "2025-07-09",
                    -3.29
                ],
                [
                    "2025-07-10",
                    9.52
                ],
                [
                    "2025-07-11",
                    -6.32
                ],
                [
                    "2025-07-22",
                    5.79
                ],
                [
                    "2025-07-23",
                    8.81
                ],
                [
                    "2025-07-24",
                    -0.14
                ],
                [
                    "2025-07-25",
                    16.26
                ],
                [
                    "2025-07-28",
                    1.40
                ],
                [
                    "2025-07-29",
                    9.30
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002050 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_a469a86bcb6d4f4ca4b5cbf6a140ec30.setOption(option_a469a86bcb6d4f4ca4b5cbf6a140ec30);
            window.addEventListener('resize', function(){
                chart_a469a86bcb6d4f4ca4b5cbf6a140ec30.resize();
            })
    </script>
</body>
</html>
