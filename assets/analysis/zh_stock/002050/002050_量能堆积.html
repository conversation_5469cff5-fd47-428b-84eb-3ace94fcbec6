<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="61b08392372442faab799e07f321b582" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_61b08392372442faab799e07f321b582 = echarts.init(
            document.getElementById('61b08392372442faab799e07f321b582'), 'white', {renderer: 'canvas'});
        var option_61b08392372442faab799e07f321b582 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    31.13,
                    30.57,
                    30.30,
                    31.41
                ],
                [
                    31.44,
                    32.56,
                    30.87,
                    33.59
                ],
                [
                    32.50,
                    35.22,
                    32.29,
                    35.82
                ],
                [
                    34.48,
                    35.32,
                    34.12,
                    36.96
                ],
                [
                    34.87,
                    34.14,
                    33.47,
                    35.10
                ],
                [
                    33.78,
                    33.90,
                    33.54,
                    35.10
                ],
                [
                    32.21,
                    33.23,
                    31.34,
                    33.56
                ],
                [
                    33.00,
                    32.40,
                    32.24,
                    33.62
                ],
                [
                    32.31,
                    31.97,
                    31.67,
                    32.50
                ],
                [
                    32.05,
                    33.37,
                    32.05,
                    33.40
                ],
                [
                    33.06,
                    32.67,
                    32.41,
                    33.88
                ],
                [
                    32.45,
                    34.59,
                    32.31,
                    34.98
                ],
                [
                    34.45,
                    34.03,
                    33.80,
                    34.87
                ],
                [
                    33.74,
                    34.35,
                    33.65,
                    35.18
                ],
                [
                    34.30,
                    33.57,
                    32.89,
                    35.29
                ],
                [
                    32.80,
                    34.00,
                    32.60,
                    34.50
                ],
                [
                    33.70,
                    34.72,
                    33.63,
                    35.97
                ],
                [
                    34.24,
                    32.42,
                    31.51,
                    34.46
                ],
                [
                    31.83,
                    30.32,
                    30.16,
                    31.83
                ],
                [
                    30.50,
                    30.52,
                    29.62,
                    31.10
                ],
                [
                    30.10,
                    31.02,
                    30.10,
                    31.88
                ],
                [
                    31.00,
                    31.25,
                    30.80,
                    31.75
                ],
                [
                    31.53,
                    31.70,
                    31.40,
                    32.57
                ],
                [
                    31.55,
                    31.70,
                    31.07,
                    32.12
                ],
                [
                    32.09,
                    31.60,
                    31.34,
                    32.15
                ],
                [
                    30.49,
                    30.49,
                    30.09,
                    31.12
                ],
                [
                    30.80,
                    30.35,
                    30.33,
                    30.90
                ],
                [
                    30.45,
                    29.47,
                    29.19,
                    30.55
                ],
                [
                    29.40,
                    31.57,
                    29.27,
                    32.20
                ],
                [
                    31.20,
                    31.31,
                    30.58,
                    31.48
                ],
                [
                    31.40,
                    30.92,
                    30.66,
                    31.48
                ],
                [
                    30.82,
                    30.32,
                    30.09,
                    30.87
                ],
                [
                    30.50,
                    30.73,
                    30.06,
                    31.33
                ],
                [
                    30.45,
                    29.48,
                    29.40,
                    30.73
                ],
                [
                    29.70,
                    29.67,
                    29.00,
                    29.77
                ],
                [
                    30.03,
                    29.22,
                    29.10,
                    30.25
                ],
                [
                    29.00,
                    29.48,
                    29.00,
                    30.23
                ],
                [
                    29.31,
                    30.29,
                    29.03,
                    31.10
                ],
                [
                    30.00,
                    29.67,
                    29.55,
                    30.40
                ],
                [
                    29.45,
                    28.83,
                    28.37,
                    29.55
                ],
                [
                    28.93,
                    28.57,
                    28.49,
                    29.12
                ],
                [
                    28.67,
                    28.84,
                    28.47,
                    29.16
                ],
                [
                    28.17,
                    28.01,
                    27.94,
                    28.86
                ],
                [
                    25.21,
                    25.21,
                    25.21,
                    25.74
                ],
                [
                    24.55,
                    22.86,
                    22.69,
                    24.80
                ],
                [
                    22.06,
                    23.15,
                    21.26,
                    23.68
                ],
                [
                    24.98,
                    24.40,
                    24.33,
                    25.45
                ],
                [
                    23.98,
                    25.05,
                    23.80,
                    25.35
                ],
                [
                    25.44,
                    24.81,
                    24.75,
                    25.58
                ],
                [
                    25.05,
                    24.75,
                    24.60,
                    25.80
                ],
                [
                    24.50,
                    24.00,
                    23.64,
                    24.57
                ],
                [
                    23.95,
                    23.98,
                    23.80,
                    24.38
                ],
                [
                    23.96,
                    24.08,
                    23.70,
                    24.16
                ],
                [
                    24.01,
                    25.72,
                    23.77,
                    26.09
                ],
                [
                    25.60,
                    24.91,
                    24.86,
                    25.70
                ],
                [
                    25.65,
                    25.83,
                    25.51,
                    26.16
                ],
                [
                    25.83,
                    25.21,
                    25.13,
                    25.99
                ],
                [
                    25.31,
                    25.45,
                    25.30,
                    25.95
                ],
                [
                    25.58,
                    25.56,
                    25.34,
                    26.00
                ],
                [
                    25.56,
                    26.04,
                    25.40,
                    26.29
                ],
                [
                    26.58,
                    26.80,
                    26.01,
                    27.08
                ],
                [
                    27.00,
                    27.32,
                    26.87,
                    27.54
                ],
                [
                    27.98,
                    26.94,
                    26.66,
                    28.05
                ],
                [
                    26.89,
                    27.05,
                    26.79,
                    27.33
                ],
                [
                    27.12,
                    26.59,
                    26.42,
                    27.50
                ],
                [
                    27.39,
                    27.90,
                    27.35,
                    28.10
                ],
                [
                    28.19,
                    27.57,
                    27.50,
                    28.20
                ],
                [
                    27.50,
                    27.56,
                    27.16,
                    27.95
                ],
                [
                    27.49,
                    26.71,
                    26.70,
                    27.57
                ],
                [
                    26.58,
                    27.00,
                    26.52,
                    27.40
                ],
                [
                    26.90,
                    26.66,
                    26.13,
                    26.96
                ],
                [
                    26.54,
                    26.69,
                    26.27,
                    26.97
                ],
                [
                    26.66,
                    26.89,
                    26.18,
                    27.03
                ],
                [
                    26.50,
                    26.32,
                    26.29,
                    26.64
                ],
                [
                    26.27,
                    27.09,
                    26.16,
                    27.94
                ],
                [
                    26.90,
                    26.54,
                    26.30,
                    27.08
                ],
                [
                    26.40,
                    25.63,
                    25.48,
                    26.48
                ],
                [
                    25.68,
                    25.72,
                    25.50,
                    26.19
                ],
                [
                    25.65,
                    26.57,
                    25.60,
                    26.72
                ],
                [
                    26.34,
                    25.85,
                    25.64,
                    26.39
                ],
                [
                    25.58,
                    25.76,
                    25.46,
                    25.91
                ],
                [
                    25.83,
                    26.18,
                    25.74,
                    26.48
                ],
                [
                    26.48,
                    27.09,
                    25.92,
                    27.25
                ],
                [
                    26.54,
                    26.15,
                    25.88,
                    26.60
                ],
                [
                    26.14,
                    25.92,
                    25.80,
                    26.14
                ],
                [
                    26.02,
                    25.93,
                    25.55,
                    26.10
                ],
                [
                    26.08,
                    26.21,
                    26.00,
                    26.56
                ],
                [
                    26.12,
                    25.88,
                    25.83,
                    26.19
                ],
                [
                    25.60,
                    25.26,
                    25.20,
                    25.77
                ],
                [
                    25.15,
                    25.38,
                    25.08,
                    25.41
                ],
                [
                    25.40,
                    25.22,
                    25.18,
                    25.66
                ],
                [
                    25.14,
                    25.48,
                    25.02,
                    25.58
                ],
                [
                    25.49,
                    26.12,
                    25.36,
                    26.84
                ],
                [
                    25.80,
                    24.95,
                    24.74,
                    25.85
                ],
                [
                    24.20,
                    24.50,
                    23.80,
                    24.62
                ],
                [
                    24.80,
                    25.70,
                    24.79,
                    25.72
                ],
                [
                    26.15,
                    26.49,
                    25.82,
                    26.50
                ],
                [
                    26.48,
                    26.12,
                    26.10,
                    26.67
                ],
                [
                    26.25,
                    26.04,
                    25.98,
                    26.34
                ],
                [
                    26.16,
                    26.38,
                    26.05,
                    26.45
                ],
                [
                    26.39,
                    26.26,
                    26.08,
                    26.43
                ],
                [
                    26.03,
                    25.76,
                    25.70,
                    26.11
                ],
                [
                    25.71,
                    25.96,
                    25.71,
                    26.11
                ],
                [
                    25.87,
                    25.67,
                    25.52,
                    25.99
                ],
                [
                    25.52,
                    25.17,
                    25.11,
                    25.52
                ],
                [
                    25.17,
                    25.47,
                    25.06,
                    25.51
                ],
                [
                    25.57,
                    25.47,
                    25.45,
                    25.90
                ],
                [
                    25.36,
                    25.42,
                    25.25,
                    25.62
                ],
                [
                    25.45,
                    25.75,
                    25.30,
                    25.98
                ],
                [
                    25.70,
                    25.87,
                    25.56,
                    26.08
                ],
                [
                    25.98,
                    26.70,
                    25.78,
                    26.70
                ],
                [
                    26.60,
                    27.53,
                    26.57,
                    28.17
                ],
                [
                    27.30,
                    27.90,
                    27.23,
                    27.93
                ],
                [
                    27.90,
                    27.68,
                    27.47,
                    27.91
                ],
                [
                    27.75,
                    27.80,
                    27.43,
                    28.30
                ],
                [
                    27.57,
                    27.62,
                    27.31,
                    27.95
                ],
                [
                    27.50,
                    27.46,
                    27.35,
                    27.73
                ],
                [
                    27.28,
                    27.79,
                    27.28,
                    27.91
                ],
                [
                    27.72,
                    27.07,
                    26.96,
                    27.74
                ],
                [
                    27.48,
                    27.34,
                    27.21,
                    27.87
                ],
                [
                    27.19,
                    27.16,
                    26.96,
                    27.32
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u7a81\u7834",
            "symbol": "pin",
            "symbolSize": 16,
            "data": [
                [
                    "2025-07-16",
                    28.17
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#1f77b4"
            }
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "symbol": "diamond",
            "symbolSize": 16,
            "data": [
                [
                    "2025-06-06",
                    26.60
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u7a81\u7834",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002050 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_61b08392372442faab799e07f321b582.setOption(option_61b08392372442faab799e07f321b582);
            window.addEventListener('resize', function(){
                chart_61b08392372442faab799e07f321b582.resize();
            })
    </script>
</body>
</html>
