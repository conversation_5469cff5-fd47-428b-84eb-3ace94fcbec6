<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="095e7db0625847929e982a0e138a6d8a" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_095e7db0625847929e982a0e138a6d8a = echarts.init(
            document.getElementById('095e7db0625847929e982a0e138a6d8a'), 'white', {renderer: 'canvas'});
        var option_095e7db0625847929e982a0e138a6d8a = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    11.38,
                    11.47,
                    11.38,
                    11.55
                ],
                [
                    11.50,
                    11.37,
                    11.33,
                    11.52
                ],
                [
                    11.35,
                    11.36,
                    11.30,
                    11.45
                ],
                [
                    11.36,
                    11.38,
                    11.31,
                    11.45
                ],
                [
                    11.38,
                    11.43,
                    11.36,
                    11.50
                ],
                [
                    11.40,
                    11.42,
                    11.37,
                    11.47
                ],
                [
                    11.41,
                    11.42,
                    11.34,
                    11.43
                ],
                [
                    11.42,
                    11.50,
                    11.40,
                    11.55
                ],
                [
                    11.49,
                    11.55,
                    11.43,
                    11.55
                ],
                [
                    11.60,
                    11.78,
                    11.55,
                    11.80
                ],
                [
                    11.76,
                    11.81,
                    11.76,
                    11.96
                ],
                [
                    11.80,
                    11.71,
                    11.68,
                    11.81
                ],
                [
                    11.71,
                    11.66,
                    11.65,
                    11.76
                ],
                [
                    11.69,
                    11.64,
                    11.55,
                    11.71
                ],
                [
                    11.63,
                    11.59,
                    11.56,
                    11.69
                ],
                [
                    11.56,
                    11.47,
                    11.46,
                    11.58
                ],
                [
                    11.47,
                    11.52,
                    11.47,
                    11.60
                ],
                [
                    11.53,
                    11.62,
                    11.46,
                    11.63
                ],
                [
                    11.58,
                    11.53,
                    11.50,
                    11.68
                ],
                [
                    11.52,
                    11.51,
                    11.45,
                    11.56
                ],
                [
                    11.47,
                    11.51,
                    11.44,
                    11.55
                ],
                [
                    11.52,
                    11.66,
                    11.48,
                    11.67
                ],
                [
                    11.69,
                    11.63,
                    11.60,
                    11.70
                ],
                [
                    11.63,
                    11.67,
                    11.60,
                    11.69
                ],
                [
                    11.66,
                    11.59,
                    11.55,
                    11.67
                ],
                [
                    11.54,
                    11.61,
                    11.52,
                    11.61
                ],
                [
                    11.60,
                    11.85,
                    11.56,
                    11.87
                ],
                [
                    11.81,
                    11.84,
                    11.78,
                    11.91
                ],
                [
                    11.82,
                    11.97,
                    11.82,
                    12.00
                ],
                [
                    11.63,
                    11.50,
                    11.46,
                    11.67
                ],
                [
                    11.52,
                    11.49,
                    11.48,
                    11.54
                ],
                [
                    11.48,
                    11.52,
                    11.46,
                    11.53
                ],
                [
                    11.51,
                    11.49,
                    11.49,
                    11.61
                ],
                [
                    11.49,
                    11.42,
                    11.39,
                    11.52
                ],
                [
                    11.41,
                    11.38,
                    11.34,
                    11.44
                ],
                [
                    11.38,
                    11.43,
                    11.36,
                    11.43
                ],
                [
                    11.42,
                    11.38,
                    11.37,
                    11.43
                ],
                [
                    11.37,
                    11.39,
                    11.35,
                    11.41
                ],
                [
                    11.39,
                    11.35,
                    11.34,
                    11.40
                ],
                [
                    11.36,
                    11.26,
                    11.26,
                    11.38
                ],
                [
                    11.27,
                    11.27,
                    11.22,
                    11.30
                ],
                [
                    11.25,
                    11.37,
                    11.25,
                    11.41
                ],
                [
                    11.31,
                    11.34,
                    11.30,
                    11.39
                ],
                [
                    11.00,
                    10.70,
                    10.48,
                    11.05
                ],
                [
                    10.68,
                    10.82,
                    10.66,
                    10.85
                ],
                [
                    10.73,
                    10.80,
                    10.66,
                    10.83
                ],
                [
                    10.86,
                    10.90,
                    10.82,
                    10.93
                ],
                [
                    10.87,
                    10.89,
                    10.83,
                    10.90
                ],
                [
                    10.95,
                    10.93,
                    10.92,
                    11.04
                ],
                [
                    10.92,
                    10.95,
                    10.90,
                    10.97
                ],
                [
                    10.93,
                    11.00,
                    10.91,
                    11.01
                ],
                [
                    10.95,
                    11.06,
                    10.93,
                    11.08
                ],
                [
                    11.04,
                    11.18,
                    11.03,
                    11.19
                ],
                [
                    11.00,
                    11.02,
                    10.97,
                    11.13
                ],
                [
                    11.02,
                    11.04,
                    10.98,
                    11.06
                ],
                [
                    11.04,
                    11.01,
                    10.97,
                    11.05
                ],
                [
                    11.00,
                    11.03,
                    10.99,
                    11.06
                ],
                [
                    11.04,
                    11.01,
                    10.99,
                    11.05
                ],
                [
                    11.00,
                    11.00,
                    10.96,
                    11.04
                ],
                [
                    11.00,
                    10.98,
                    10.95,
                    11.02
                ],
                [
                    10.96,
                    10.91,
                    10.89,
                    10.97
                ],
                [
                    10.92,
                    10.96,
                    10.89,
                    11.00
                ],
                [
                    11.01,
                    11.03,
                    10.97,
                    11.04
                ],
                [
                    11.00,
                    11.08,
                    10.99,
                    11.08
                ],
                [
                    11.07,
                    11.15,
                    11.06,
                    11.17
                ],
                [
                    11.16,
                    11.16,
                    11.13,
                    11.22
                ],
                [
                    11.19,
                    11.29,
                    11.15,
                    11.33
                ],
                [
                    11.29,
                    11.43,
                    11.25,
                    11.47
                ],
                [
                    11.43,
                    11.39,
                    11.38,
                    11.50
                ],
                [
                    11.37,
                    11.38,
                    11.31,
                    11.43
                ],
                [
                    11.40,
                    11.37,
                    11.36,
                    11.47
                ],
                [
                    11.40,
                    11.39,
                    11.37,
                    11.47
                ],
                [
                    11.40,
                    11.48,
                    11.39,
                    11.59
                ],
                [
                    11.45,
                    11.55,
                    11.44,
                    11.56
                ],
                [
                    11.55,
                    11.46,
                    11.43,
                    11.60
                ],
                [
                    11.44,
                    11.42,
                    11.40,
                    11.50
                ],
                [
                    11.45,
                    11.49,
                    11.42,
                    11.54
                ],
                [
                    11.50,
                    11.53,
                    11.44,
                    11.55
                ],
                [
                    11.52,
                    11.46,
                    11.45,
                    11.55
                ],
                [
                    11.47,
                    11.56,
                    11.44,
                    11.58
                ],
                [
                    11.54,
                    11.81,
                    11.53,
                    11.91
                ],
                [
                    11.82,
                    11.84,
                    11.78,
                    11.88
                ],
                [
                    11.88,
                    11.67,
                    11.66,
                    11.91
                ],
                [
                    11.70,
                    11.70,
                    11.68,
                    11.79
                ],
                [
                    11.73,
                    11.71,
                    11.65,
                    11.75
                ],
                [
                    11.72,
                    11.81,
                    11.71,
                    11.89
                ],
                [
                    11.82,
                    11.85,
                    11.79,
                    11.93
                ],
                [
                    11.56,
                    11.68,
                    11.52,
                    11.70
                ],
                [
                    11.68,
                    11.58,
                    11.56,
                    11.74
                ],
                [
                    11.57,
                    11.79,
                    11.53,
                    11.79
                ],
                [
                    11.79,
                    11.76,
                    11.71,
                    11.88
                ],
                [
                    11.78,
                    11.77,
                    11.66,
                    11.81
                ],
                [
                    11.76,
                    11.70,
                    11.65,
                    11.78
                ],
                [
                    11.70,
                    11.84,
                    11.67,
                    11.86
                ],
                [
                    11.81,
                    11.93,
                    11.67,
                    11.97
                ],
                [
                    11.90,
                    11.93,
                    11.84,
                    12.01
                ],
                [
                    11.93,
                    12.06,
                    11.83,
                    12.08
                ],
                [
                    12.05,
                    12.41,
                    12.01,
                    12.48
                ],
                [
                    12.37,
                    12.20,
                    12.13,
                    12.58
                ],
                [
                    12.14,
                    12.07,
                    11.96,
                    12.17
                ],
                [
                    12.06,
                    12.30,
                    12.06,
                    12.33
                ],
                [
                    12.31,
                    12.32,
                    12.23,
                    12.41
                ],
                [
                    12.33,
                    12.35,
                    12.28,
                    12.42
                ],
                [
                    12.35,
                    12.60,
                    12.30,
                    12.72
                ],
                [
                    12.60,
                    12.78,
                    12.60,
                    12.82
                ],
                [
                    12.75,
                    12.69,
                    12.65,
                    12.84
                ],
                [
                    12.66,
                    12.84,
                    12.66,
                    12.90
                ],
                [
                    12.82,
                    13.18,
                    12.79,
                    13.33
                ],
                [
                    13.19,
                    12.91,
                    12.89,
                    13.30
                ],
                [
                    12.87,
                    12.98,
                    12.86,
                    13.17
                ],
                [
                    12.99,
                    12.73,
                    12.71,
                    13.04
                ],
                [
                    12.73,
                    12.64,
                    12.50,
                    12.76
                ],
                [
                    12.61,
                    12.59,
                    12.54,
                    12.70
                ],
                [
                    12.62,
                    12.70,
                    12.60,
                    12.75
                ],
                [
                    12.64,
                    12.61,
                    12.55,
                    12.71
                ],
                [
                    12.60,
                    12.49,
                    12.32,
                    12.60
                ],
                [
                    12.46,
                    12.53,
                    12.45,
                    12.63
                ],
                [
                    12.53,
                    12.35,
                    12.33,
                    12.53
                ],
                [
                    12.33,
                    12.35,
                    12.32,
                    12.46
                ],
                [
                    12.35,
                    12.46,
                    12.32,
                    12.53
                ],
                [
                    12.46,
                    12.34,
                    12.34,
                    12.51
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "000001 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_095e7db0625847929e982a0e138a6d8a.setOption(option_095e7db0625847929e982a0e138a6d8a);
            window.addEventListener('resize', function(){
                chart_095e7db0625847929e982a0e138a6d8a.resize();
            })
    </script>
</body>
</html>
