<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="40e1656b325a451aa2816ada0fb57491" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_40e1656b325a451aa2816ada0fb57491 = echarts.init(
            document.getElementById('40e1656b325a451aa2816ada0fb57491'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_40e1656b325a451aa2816ada0fb57491 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    11.38,
                    11.47,
                    11.38,
                    11.55
                ],
                [
                    11.5,
                    11.37,
                    11.33,
                    11.52
                ],
                [
                    11.35,
                    11.36,
                    11.3,
                    11.45
                ],
                [
                    11.36,
                    11.38,
                    11.31,
                    11.45
                ],
                [
                    11.38,
                    11.43,
                    11.36,
                    11.5
                ],
                [
                    11.4,
                    11.42,
                    11.37,
                    11.47
                ],
                [
                    11.41,
                    11.42,
                    11.34,
                    11.43
                ],
                [
                    11.42,
                    11.5,
                    11.4,
                    11.55
                ],
                [
                    11.49,
                    11.55,
                    11.43,
                    11.55
                ],
                [
                    11.6,
                    11.78,
                    11.55,
                    11.8
                ],
                [
                    11.76,
                    11.81,
                    11.76,
                    11.96
                ],
                [
                    11.8,
                    11.71,
                    11.68,
                    11.81
                ],
                [
                    11.71,
                    11.66,
                    11.65,
                    11.76
                ],
                [
                    11.69,
                    11.64,
                    11.55,
                    11.71
                ],
                [
                    11.63,
                    11.59,
                    11.56,
                    11.69
                ],
                [
                    11.56,
                    11.47,
                    11.46,
                    11.58
                ],
                [
                    11.47,
                    11.52,
                    11.47,
                    11.6
                ],
                [
                    11.53,
                    11.62,
                    11.46,
                    11.63
                ],
                [
                    11.58,
                    11.53,
                    11.5,
                    11.68
                ],
                [
                    11.52,
                    11.51,
                    11.45,
                    11.56
                ],
                [
                    11.47,
                    11.51,
                    11.44,
                    11.55
                ],
                [
                    11.52,
                    11.66,
                    11.48,
                    11.67
                ],
                [
                    11.69,
                    11.63,
                    11.6,
                    11.7
                ],
                [
                    11.63,
                    11.67,
                    11.6,
                    11.69
                ],
                [
                    11.66,
                    11.59,
                    11.55,
                    11.67
                ],
                [
                    11.54,
                    11.61,
                    11.52,
                    11.61
                ],
                [
                    11.6,
                    11.85,
                    11.56,
                    11.87
                ],
                [
                    11.81,
                    11.84,
                    11.78,
                    11.91
                ],
                [
                    11.82,
                    11.97,
                    11.82,
                    12.0
                ],
                [
                    11.63,
                    11.5,
                    11.46,
                    11.67
                ],
                [
                    11.52,
                    11.49,
                    11.48,
                    11.54
                ],
                [
                    11.48,
                    11.52,
                    11.46,
                    11.53
                ],
                [
                    11.51,
                    11.49,
                    11.49,
                    11.61
                ],
                [
                    11.49,
                    11.42,
                    11.39,
                    11.52
                ],
                [
                    11.41,
                    11.38,
                    11.34,
                    11.44
                ],
                [
                    11.38,
                    11.43,
                    11.36,
                    11.43
                ],
                [
                    11.42,
                    11.38,
                    11.37,
                    11.43
                ],
                [
                    11.37,
                    11.39,
                    11.35,
                    11.41
                ],
                [
                    11.39,
                    11.35,
                    11.34,
                    11.4
                ],
                [
                    11.36,
                    11.26,
                    11.26,
                    11.38
                ],
                [
                    11.27,
                    11.27,
                    11.22,
                    11.3
                ],
                [
                    11.25,
                    11.37,
                    11.25,
                    11.41
                ],
                [
                    11.31,
                    11.34,
                    11.3,
                    11.39
                ],
                [
                    11.0,
                    10.7,
                    10.48,
                    11.05
                ],
                [
                    10.68,
                    10.82,
                    10.66,
                    10.85
                ],
                [
                    10.73,
                    10.8,
                    10.66,
                    10.83
                ],
                [
                    10.86,
                    10.9,
                    10.82,
                    10.93
                ],
                [
                    10.87,
                    10.89,
                    10.83,
                    10.9
                ],
                [
                    10.95,
                    10.93,
                    10.92,
                    11.04
                ],
                [
                    10.92,
                    10.95,
                    10.9,
                    10.97
                ],
                [
                    10.93,
                    11.0,
                    10.91,
                    11.01
                ],
                [
                    10.95,
                    11.06,
                    10.93,
                    11.08
                ],
                [
                    11.04,
                    11.18,
                    11.03,
                    11.19
                ],
                [
                    11.0,
                    11.02,
                    10.97,
                    11.13
                ],
                [
                    11.02,
                    11.04,
                    10.98,
                    11.06
                ],
                [
                    11.04,
                    11.01,
                    10.97,
                    11.05
                ],
                [
                    11.0,
                    11.03,
                    10.99,
                    11.06
                ],
                [
                    11.04,
                    11.01,
                    10.99,
                    11.05
                ],
                [
                    11.0,
                    11.0,
                    10.96,
                    11.04
                ],
                [
                    11.0,
                    10.98,
                    10.95,
                    11.02
                ],
                [
                    10.96,
                    10.91,
                    10.89,
                    10.97
                ],
                [
                    10.92,
                    10.96,
                    10.89,
                    11.0
                ],
                [
                    11.01,
                    11.03,
                    10.97,
                    11.04
                ],
                [
                    11.0,
                    11.08,
                    10.99,
                    11.08
                ],
                [
                    11.07,
                    11.15,
                    11.06,
                    11.17
                ],
                [
                    11.16,
                    11.16,
                    11.13,
                    11.22
                ],
                [
                    11.19,
                    11.29,
                    11.15,
                    11.33
                ],
                [
                    11.29,
                    11.43,
                    11.25,
                    11.47
                ],
                [
                    11.43,
                    11.39,
                    11.38,
                    11.5
                ],
                [
                    11.37,
                    11.38,
                    11.31,
                    11.43
                ],
                [
                    11.4,
                    11.37,
                    11.36,
                    11.47
                ],
                [
                    11.4,
                    11.39,
                    11.37,
                    11.47
                ],
                [
                    11.4,
                    11.48,
                    11.39,
                    11.59
                ],
                [
                    11.45,
                    11.55,
                    11.44,
                    11.56
                ],
                [
                    11.55,
                    11.46,
                    11.43,
                    11.6
                ],
                [
                    11.44,
                    11.42,
                    11.4,
                    11.5
                ],
                [
                    11.45,
                    11.49,
                    11.42,
                    11.54
                ],
                [
                    11.5,
                    11.53,
                    11.44,
                    11.55
                ],
                [
                    11.52,
                    11.46,
                    11.45,
                    11.55
                ],
                [
                    11.47,
                    11.56,
                    11.44,
                    11.58
                ],
                [
                    11.54,
                    11.81,
                    11.53,
                    11.91
                ],
                [
                    11.82,
                    11.84,
                    11.78,
                    11.88
                ],
                [
                    11.88,
                    11.67,
                    11.66,
                    11.91
                ],
                [
                    11.7,
                    11.7,
                    11.68,
                    11.79
                ],
                [
                    11.73,
                    11.71,
                    11.65,
                    11.75
                ],
                [
                    11.72,
                    11.81,
                    11.71,
                    11.89
                ],
                [
                    11.82,
                    11.85,
                    11.79,
                    11.93
                ],
                [
                    11.56,
                    11.68,
                    11.52,
                    11.7
                ],
                [
                    11.68,
                    11.58,
                    11.56,
                    11.74
                ],
                [
                    11.57,
                    11.79,
                    11.53,
                    11.79
                ],
                [
                    11.79,
                    11.76,
                    11.71,
                    11.88
                ],
                [
                    11.78,
                    11.77,
                    11.66,
                    11.81
                ],
                [
                    11.76,
                    11.7,
                    11.65,
                    11.78
                ],
                [
                    11.7,
                    11.84,
                    11.67,
                    11.86
                ],
                [
                    11.81,
                    11.93,
                    11.67,
                    11.97
                ],
                [
                    11.9,
                    11.93,
                    11.84,
                    12.01
                ],
                [
                    11.93,
                    12.06,
                    11.83,
                    12.08
                ],
                [
                    12.05,
                    12.41,
                    12.01,
                    12.48
                ],
                [
                    12.37,
                    12.2,
                    12.13,
                    12.58
                ],
                [
                    12.14,
                    12.07,
                    11.96,
                    12.17
                ],
                [
                    12.06,
                    12.3,
                    12.06,
                    12.33
                ],
                [
                    12.31,
                    12.32,
                    12.23,
                    12.41
                ],
                [
                    12.33,
                    12.35,
                    12.28,
                    12.42
                ],
                [
                    12.35,
                    12.6,
                    12.3,
                    12.72
                ],
                [
                    12.6,
                    12.78,
                    12.6,
                    12.82
                ],
                [
                    12.75,
                    12.69,
                    12.65,
                    12.84
                ],
                [
                    12.66,
                    12.84,
                    12.66,
                    12.9
                ],
                [
                    12.82,
                    13.18,
                    12.79,
                    13.33
                ],
                [
                    13.19,
                    12.91,
                    12.89,
                    13.3
                ],
                [
                    12.87,
                    12.98,
                    12.86,
                    13.17
                ],
                [
                    12.99,
                    12.73,
                    12.71,
                    13.04
                ],
                [
                    12.73,
                    12.64,
                    12.5,
                    12.76
                ],
                [
                    12.61,
                    12.59,
                    12.54,
                    12.7
                ],
                [
                    12.62,
                    12.7,
                    12.6,
                    12.75
                ],
                [
                    12.64,
                    12.61,
                    12.55,
                    12.71
                ],
                [
                    12.6,
                    12.49,
                    12.32,
                    12.6
                ],
                [
                    12.46,
                    12.53,
                    12.45,
                    12.63
                ],
                [
                    12.53,
                    12.35,
                    12.33,
                    12.53
                ],
                [
                    12.33,
                    12.35,
                    12.32,
                    12.46
                ],
                [
                    12.35,
                    12.46,
                    12.32,
                    12.53
                ],
                [
                    12.46,
                    12.34,
                    12.34,
                    12.51
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    1.55
                ],
                [
                    "2025-02-05",
                    -7.22
                ],
                [
                    "2025-02-06",
                    -1.47
                ],
                [
                    "2025-02-07",
                    -3.69
                ],
                [
                    "2025-02-10",
                    -1.09
                ],
                [
                    "2025-02-11",
                    -11.78
                ],
                [
                    "2025-02-12",
                    -7.53
                ],
                [
                    "2025-02-13",
                    4.29
                ],
                [
                    "2025-02-14",
                    -4.01
                ],
                [
                    "2025-02-17",
                    0.10
                ],
                [
                    "2025-02-18",
                    -4.01
                ],
                [
                    "2025-02-19",
                    1.27
                ],
                [
                    "2025-02-20",
                    -5.05
                ],
                [
                    "2025-02-21",
                    -9.78
                ],
                [
                    "2025-02-24",
                    -2.51
                ],
                [
                    "2025-02-25",
                    -14.27
                ],
                [
                    "2025-02-26",
                    -0.97
                ],
                [
                    "2025-02-27",
                    10.50
                ],
                [
                    "2025-02-28",
                    -4.02
                ],
                [
                    "2025-03-03",
                    -0.01
                ],
                [
                    "2025-03-04",
                    4.37
                ],
                [
                    "2025-03-05",
                    15.65
                ],
                [
                    "2025-03-06",
                    -8.50
                ],
                [
                    "2025-03-07",
                    5.56
                ],
                [
                    "2025-03-10",
                    -6.98
                ],
                [
                    "2025-03-11",
                    -1.96
                ],
                [
                    "2025-03-12",
                    9.32
                ],
                [
                    "2025-03-13",
                    -5.29
                ],
                [
                    "2025-03-14",
                    10.01
                ],
                [
                    "2025-03-17",
                    -10.10
                ],
                [
                    "2025-03-18",
                    -9.40
                ],
                [
                    "2025-03-19",
                    -2.05
                ],
                [
                    "2025-03-20",
                    -7.77
                ],
                [
                    "2025-03-21",
                    -14.43
                ],
                [
                    "2025-03-24",
                    -8.91
                ],
                [
                    "2025-03-25",
                    -3.01
                ],
                [
                    "2025-03-26",
                    -2.38
                ],
                [
                    "2025-03-27",
                    1.03
                ],
                [
                    "2025-03-28",
                    -5.96
                ],
                [
                    "2025-03-31",
                    -10.90
                ],
                [
                    "2025-04-01",
                    -9.72
                ],
                [
                    "2025-04-02",
                    11.67
                ],
                [
                    "2025-04-03",
                    -11.01
                ],
                [
                    "2025-04-07",
                    -18.58
                ],
                [
                    "2025-04-08",
                    -18.01
                ],
                [
                    "2025-04-09",
                    -8.14
                ],
                [
                    "2025-04-10",
                    9.70
                ],
                [
                    "2025-04-11",
                    -0.58
                ],
                [
                    "2025-04-14",
                    7.37
                ],
                [
                    "2025-04-15",
                    2.08
                ],
                [
                    "2025-04-16",
                    1.31
                ],
                [
                    "2025-04-17",
                    -2.42
                ],
                [
                    "2025-04-18",
                    1.44
                ],
                [
                    "2025-04-21",
                    -6.01
                ],
                [
                    "2025-04-22",
                    -6.66
                ],
                [
                    "2025-04-23",
                    -7.78
                ],
                [
                    "2025-04-24",
                    -2.97
                ],
                [
                    "2025-04-25",
                    -11.90
                ],
                [
                    "2025-04-28",
                    -4.25
                ],
                [
                    "2025-04-29",
                    -1.83
                ],
                [
                    "2025-04-30",
                    -7.73
                ],
                [
                    "2025-05-06",
                    10.61
                ],
                [
                    "2025-05-07",
                    11.15
                ],
                [
                    "2025-05-08",
                    7.63
                ],
                [
                    "2025-05-09",
                    1.08
                ],
                [
                    "2025-05-12",
                    -0.25
                ],
                [
                    "2025-05-13",
                    8.71
                ],
                [
                    "2025-05-14",
                    -2.28
                ],
                [
                    "2025-05-15",
                    -5.48
                ],
                [
                    "2025-05-16",
                    3.59
                ],
                [
                    "2025-05-19",
                    -4.10
                ],
                [
                    "2025-05-20",
                    -3.50
                ],
                [
                    "2025-05-21",
                    3.64
                ],
                [
                    "2025-05-22",
                    -0.26
                ],
                [
                    "2025-05-23",
                    -2.89
                ],
                [
                    "2025-05-26",
                    -2.46
                ],
                [
                    "2025-05-27",
                    -10.10
                ],
                [
                    "2025-05-28",
                    -6.12
                ],
                [
                    "2025-05-29",
                    8.55
                ],
                [
                    "2025-05-30",
                    3.90
                ],
                [
                    "2025-06-03",
                    5.91
                ],
                [
                    "2025-06-04",
                    -6.53
                ],
                [
                    "2025-06-05",
                    -8.60
                ],
                [
                    "2025-06-06",
                    -7.89
                ],
                [
                    "2025-06-09",
                    -0.25
                ],
                [
                    "2025-06-10",
                    -2.66
                ],
                [
                    "2025-06-11",
                    -4.37
                ],
                [
                    "2025-06-12",
                    8.41
                ],
                [
                    "2025-06-13",
                    -11.67
                ],
                [
                    "2025-06-16",
                    6.51
                ],
                [
                    "2025-06-17",
                    -4.48
                ],
                [
                    "2025-06-18",
                    -0.57
                ],
                [
                    "2025-06-19",
                    1.91
                ],
                [
                    "2025-06-20",
                    12.62
                ],
                [
                    "2025-06-23",
                    0.88
                ],
                [
                    "2025-06-24",
                    -4.97
                ],
                [
                    "2025-06-25",
                    -3.35
                ],
                [
                    "2025-06-26",
                    12.19
                ],
                [
                    "2025-06-27",
                    -7.09
                ],
                [
                    "2025-06-30",
                    -5.01
                ],
                [
                    "2025-07-01",
                    6.76
                ],
                [
                    "2025-07-02",
                    -5.49
                ],
                [
                    "2025-07-03",
                    -1.35
                ],
                [
                    "2025-07-04",
                    6.98
                ],
                [
                    "2025-07-07",
                    13.85
                ],
                [
                    "2025-07-08",
                    -1.77
                ],
                [
                    "2025-07-09",
                    4.49
                ],
                [
                    "2025-07-10",
                    9.67
                ],
                [
                    "2025-07-11",
                    -6.26
                ],
                [
                    "2025-07-14",
                    -9.93
                ],
                [
                    "2025-07-15",
                    -14.08
                ],
                [
                    "2025-07-16",
                    -4.15
                ],
                [
                    "2025-07-17",
                    -4.51
                ],
                [
                    "2025-07-18",
                    2.85
                ],
                [
                    "2025-07-21",
                    1.17
                ],
                [
                    "2025-07-22",
                    -11.04
                ],
                [
                    "2025-07-23",
                    -2.13
                ],
                [
                    "2025-07-24",
                    2.31
                ],
                [
                    "2025-07-25",
                    4.38
                ],
                [
                    "2025-07-28",
                    -0.57
                ],
                [
                    "2025-07-29",
                    -1.93
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -5.70
                ],
                [
                    "2025-02-05",
                    4.83
                ],
                [
                    "2025-02-06",
                    3.58
                ],
                [
                    "2025-02-07",
                    3.17
                ],
                [
                    "2025-02-10",
                    1.68
                ],
                [
                    "2025-02-11",
                    4.78
                ],
                [
                    "2025-02-12",
                    4.65
                ],
                [
                    "2025-02-13",
                    -6.42
                ],
                [
                    "2025-02-14",
                    0.59
                ],
                [
                    "2025-02-17",
                    -0.97
                ],
                [
                    "2025-02-18",
                    -1.32
                ],
                [
                    "2025-02-19",
                    1.94
                ],
                [
                    "2025-02-20",
                    1.90
                ],
                [
                    "2025-02-21",
                    3.89
                ],
                [
                    "2025-02-24",
                    5.16
                ],
                [
                    "2025-02-25",
                    6.92
                ],
                [
                    "2025-02-26",
                    2.75
                ],
                [
                    "2025-02-27",
                    -4.31
                ],
                [
                    "2025-02-28",
                    3.30
                ],
                [
                    "2025-03-03",
                    1.38
                ],
                [
                    "2025-03-04",
                    -3.96
                ],
                [
                    "2025-03-05",
                    -8.94
                ],
                [
                    "2025-03-06",
                    3.29
                ],
                [
                    "2025-03-07",
                    -7.10
                ],
                [
                    "2025-03-10",
                    5.55
                ],
                [
                    "2025-03-11",
                    3.66
                ],
                [
                    "2025-03-12",
                    -6.97
                ],
                [
                    "2025-03-13",
                    -0.70
                ],
                [
                    "2025-03-14",
                    -5.93
                ],
                [
                    "2025-03-17",
                    7.64
                ],
                [
                    "2025-03-18",
                    6.71
                ],
                [
                    "2025-03-19",
                    2.39
                ],
                [
                    "2025-03-20",
                    5.76
                ],
                [
                    "2025-03-21",
                    6.78
                ],
                [
                    "2025-03-24",
                    1.78
                ],
                [
                    "2025-03-25",
                    1.76
                ],
                [
                    "2025-03-26",
                    -1.54
                ],
                [
                    "2025-03-27",
                    -0.64
                ],
                [
                    "2025-03-28",
                    1.67
                ],
                [
                    "2025-03-31",
                    4.07
                ],
                [
                    "2025-04-01",
                    3.51
                ],
                [
                    "2025-04-02",
                    -6.61
                ],
                [
                    "2025-04-03",
                    5.18
                ],
                [
                    "2025-04-07",
                    5.72
                ],
                [
                    "2025-04-08",
                    7.20
                ],
                [
                    "2025-04-09",
                    4.18
                ],
                [
                    "2025-04-10",
                    -9.11
                ],
                [
                    "2025-04-11",
                    3.46
                ],
                [
                    "2025-04-14",
                    -3.14
                ],
                [
                    "2025-04-15",
                    -3.50
                ],
                [
                    "2025-04-16",
                    -0.27
                ],
                [
                    "2025-04-17",
                    -2.31
                ],
                [
                    "2025-04-18",
                    -4.57
                ],
                [
                    "2025-04-21",
                    4.32
                ],
                [
                    "2025-04-22",
                    0.44
                ],
                [
                    "2025-04-23",
                    4.76
                ],
                [
                    "2025-04-24",
                    1.10
                ],
                [
                    "2025-04-25",
                    13.57
                ],
                [
                    "2025-04-28",
                    1.35
                ],
                [
                    "2025-04-29",
                    9.27
                ],
                [
                    "2025-04-30",
                    3.39
                ],
                [
                    "2025-05-06",
                    7.90
                ],
                [
                    "2025-05-07",
                    -3.40
                ],
                [
                    "2025-05-08",
                    4.18
                ],
                [
                    "2025-05-09",
                    -4.88
                ],
                [
                    "2025-05-12",
                    -0.97
                ],
                [
                    "2025-05-13",
                    -6.71
                ],
                [
                    "2025-05-14",
                    1.41
                ],
                [
                    "2025-05-15",
                    5.38
                ],
                [
                    "2025-05-16",
                    -3.94
                ],
                [
                    "2025-05-19",
                    -0.35
                ],
                [
                    "2025-05-20",
                    4.86
                ],
                [
                    "2025-05-21",
                    -2.30
                ],
                [
                    "2025-05-22",
                    0.31
                ],
                [
                    "2025-05-23",
                    1.47
                ],
                [
                    "2025-05-26",
                    -2.73
                ],
                [
                    "2025-05-27",
                    4.70
                ],
                [
                    "2025-05-28",
                    2.27
                ],
                [
                    "2025-05-29",
                    -3.47
                ],
                [
                    "2025-05-30",
                    -5.27
                ],
                [
                    "2025-06-03",
                    0.10
                ],
                [
                    "2025-06-04",
                    -1.09
                ],
                [
                    "2025-06-05",
                    1.38
                ],
                [
                    "2025-06-06",
                    2.02
                ],
                [
                    "2025-06-09",
                    -1.43
                ],
                [
                    "2025-06-10",
                    1.44
                ],
                [
                    "2025-06-11",
                    5.26
                ],
                [
                    "2025-06-12",
                    -6.35
                ],
                [
                    "2025-06-13",
                    2.09
                ],
                [
                    "2025-06-16",
                    -1.72
                ],
                [
                    "2025-06-17",
                    1.45
                ],
                [
                    "2025-06-18",
                    -0.55
                ],
                [
                    "2025-06-19",
                    -3.20
                ],
                [
                    "2025-06-20",
                    -5.06
                ],
                [
                    "2025-06-23",
                    2.09
                ],
                [
                    "2025-06-24",
                    7.40
                ],
                [
                    "2025-06-25",
                    5.63
                ],
                [
                    "2025-06-26",
                    -3.14
                ],
                [
                    "2025-06-27",
                    1.43
                ],
                [
                    "2025-06-30",
                    3.90
                ],
                [
                    "2025-07-01",
                    -3.16
                ],
                [
                    "2025-07-02",
                    1.40
                ],
                [
                    "2025-07-03",
                    -0.40
                ],
                [
                    "2025-07-04",
                    -1.00
                ],
                [
                    "2025-07-07",
                    -4.94
                ],
                [
                    "2025-07-08",
                    1.15
                ],
                [
                    "2025-07-09",
                    -1.83
                ],
                [
                    "2025-07-10",
                    -2.82
                ],
                [
                    "2025-07-11",
                    1.67
                ],
                [
                    "2025-07-14",
                    4.53
                ],
                [
                    "2025-07-15",
                    3.40
                ],
                [
                    "2025-07-16",
                    1.09
                ],
                [
                    "2025-07-17",
                    -0.10
                ],
                [
                    "2025-07-18",
                    -1.82
                ],
                [
                    "2025-07-21",
                    2.08
                ],
                [
                    "2025-07-22",
                    5.07
                ],
                [
                    "2025-07-23",
                    1.70
                ],
                [
                    "2025-07-24",
                    1.11
                ],
                [
                    "2025-07-25",
                    -4.77
                ],
                [
                    "2025-07-28",
                    -0.12
                ],
                [
                    "2025-07-29",
                    0.50
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    4.15
                ],
                [
                    "2025-02-05",
                    2.39
                ],
                [
                    "2025-02-06",
                    -2.12
                ],
                [
                    "2025-02-07",
                    0.52
                ],
                [
                    "2025-02-10",
                    -0.59
                ],
                [
                    "2025-02-11",
                    7.01
                ],
                [
                    "2025-02-12",
                    2.88
                ],
                [
                    "2025-02-13",
                    2.12
                ],
                [
                    "2025-02-14",
                    3.42
                ],
                [
                    "2025-02-17",
                    0.87
                ],
                [
                    "2025-02-18",
                    5.33
                ],
                [
                    "2025-02-19",
                    -3.20
                ],
                [
                    "2025-02-20",
                    3.15
                ],
                [
                    "2025-02-21",
                    5.89
                ],
                [
                    "2025-02-24",
                    -2.65
                ],
                [
                    "2025-02-25",
                    7.36
                ],
                [
                    "2025-02-26",
                    -1.78
                ],
                [
                    "2025-02-27",
                    -6.19
                ],
                [
                    "2025-02-28",
                    0.72
                ],
                [
                    "2025-03-03",
                    -1.37
                ],
                [
                    "2025-03-04",
                    -0.42
                ],
                [
                    "2025-03-05",
                    -6.71
                ],
                [
                    "2025-03-06",
                    5.21
                ],
                [
                    "2025-03-07",
                    1.54
                ],
                [
                    "2025-03-10",
                    1.43
                ],
                [
                    "2025-03-11",
                    -1.71
                ],
                [
                    "2025-03-12",
                    -2.35
                ],
                [
                    "2025-03-13",
                    5.99
                ],
                [
                    "2025-03-14",
                    -4.08
                ],
                [
                    "2025-03-17",
                    2.45
                ],
                [
                    "2025-03-18",
                    2.69
                ],
                [
                    "2025-03-19",
                    -0.34
                ],
                [
                    "2025-03-20",
                    2.01
                ],
                [
                    "2025-03-21",
                    7.65
                ],
                [
                    "2025-03-24",
                    7.13
                ],
                [
                    "2025-03-25",
                    1.25
                ],
                [
                    "2025-03-26",
                    3.92
                ],
                [
                    "2025-03-27",
                    -0.39
                ],
                [
                    "2025-03-28",
                    4.28
                ],
                [
                    "2025-03-31",
                    6.83
                ],
                [
                    "2025-04-01",
                    6.21
                ],
                [
                    "2025-04-02",
                    -5.06
                ],
                [
                    "2025-04-03",
                    5.83
                ],
                [
                    "2025-04-07",
                    12.86
                ],
                [
                    "2025-04-08",
                    10.82
                ],
                [
                    "2025-04-09",
                    3.96
                ],
                [
                    "2025-04-10",
                    -0.59
                ],
                [
                    "2025-04-11",
                    -2.88
                ],
                [
                    "2025-04-14",
                    -4.23
                ],
                [
                    "2025-04-15",
                    1.42
                ],
                [
                    "2025-04-16",
                    -1.03
                ],
                [
                    "2025-04-17",
                    4.73
                ],
                [
                    "2025-04-18",
                    3.13
                ],
                [
                    "2025-04-21",
                    1.70
                ],
                [
                    "2025-04-22",
                    6.22
                ],
                [
                    "2025-04-23",
                    3.02
                ],
                [
                    "2025-04-24",
                    1.88
                ],
                [
                    "2025-04-25",
                    -1.68
                ],
                [
                    "2025-04-28",
                    2.91
                ],
                [
                    "2025-04-29",
                    -7.45
                ],
                [
                    "2025-04-30",
                    4.35
                ],
                [
                    "2025-05-06",
                    -18.51
                ],
                [
                    "2025-05-07",
                    -7.76
                ],
                [
                    "2025-05-08",
                    -11.80
                ],
                [
                    "2025-05-09",
                    3.80
                ],
                [
                    "2025-05-12",
                    1.22
                ],
                [
                    "2025-05-13",
                    -1.99
                ],
                [
                    "2025-05-14",
                    0.87
                ],
                [
                    "2025-05-15",
                    0.10
                ],
                [
                    "2025-05-16",
                    0.35
                ],
                [
                    "2025-05-19",
                    4.45
                ],
                [
                    "2025-05-20",
                    -1.36
                ],
                [
                    "2025-05-21",
                    -1.34
                ],
                [
                    "2025-05-22",
                    -0.04
                ],
                [
                    "2025-05-23",
                    1.43
                ],
                [
                    "2025-05-26",
                    5.20
                ],
                [
                    "2025-05-27",
                    5.40
                ],
                [
                    "2025-05-28",
                    3.85
                ],
                [
                    "2025-05-29",
                    -5.08
                ],
                [
                    "2025-05-30",
                    1.38
                ],
                [
                    "2025-06-03",
                    -6.01
                ],
                [
                    "2025-06-04",
                    7.63
                ],
                [
                    "2025-06-05",
                    7.23
                ],
                [
                    "2025-06-06",
                    5.87
                ],
                [
                    "2025-06-09",
                    1.68
                ],
                [
                    "2025-06-10",
                    1.21
                ],
                [
                    "2025-06-11",
                    -0.89
                ],
                [
                    "2025-06-12",
                    -2.06
                ],
                [
                    "2025-06-13",
                    9.58
                ],
                [
                    "2025-06-16",
                    -4.79
                ],
                [
                    "2025-06-17",
                    3.03
                ],
                [
                    "2025-06-18",
                    1.12
                ],
                [
                    "2025-06-19",
                    1.29
                ],
                [
                    "2025-06-20",
                    -7.56
                ],
                [
                    "2025-06-23",
                    -2.98
                ],
                [
                    "2025-06-24",
                    -2.44
                ],
                [
                    "2025-06-25",
                    -2.28
                ],
                [
                    "2025-06-26",
                    -9.05
                ],
                [
                    "2025-06-27",
                    5.66
                ],
                [
                    "2025-06-30",
                    1.11
                ],
                [
                    "2025-07-01",
                    -3.59
                ],
                [
                    "2025-07-02",
                    4.09
                ],
                [
                    "2025-07-03",
                    1.74
                ],
                [
                    "2025-07-04",
                    -5.98
                ],
                [
                    "2025-07-07",
                    -8.91
                ],
                [
                    "2025-07-08",
                    0.62
                ],
                [
                    "2025-07-09",
                    -2.67
                ],
                [
                    "2025-07-10",
                    -6.85
                ],
                [
                    "2025-07-11",
                    4.60
                ],
                [
                    "2025-07-14",
                    5.40
                ],
                [
                    "2025-07-15",
                    10.69
                ],
                [
                    "2025-07-16",
                    3.07
                ],
                [
                    "2025-07-17",
                    4.61
                ],
                [
                    "2025-07-18",
                    -1.03
                ],
                [
                    "2025-07-21",
                    -3.25
                ],
                [
                    "2025-07-22",
                    5.97
                ],
                [
                    "2025-07-23",
                    0.42
                ],
                [
                    "2025-07-24",
                    -3.41
                ],
                [
                    "2025-07-25",
                    0.40
                ],
                [
                    "2025-07-28",
                    0.68
                ],
                [
                    "2025-07-29",
                    1.43
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-04",
                    4.37
                ],
                [
                    "2025-03-05",
                    15.65
                ],
                [
                    "2025-03-06",
                    -8.50
                ],
                [
                    "2025-03-07",
                    5.56
                ],
                [
                    "2025-03-11",
                    -1.96
                ],
                [
                    "2025-03-14",
                    10.01
                ],
                [
                    "2025-04-15",
                    2.08
                ],
                [
                    "2025-04-16",
                    1.31
                ],
                [
                    "2025-04-17",
                    -2.42
                ],
                [
                    "2025-05-07",
                    11.15
                ],
                [
                    "2025-05-08",
                    7.63
                ],
                [
                    "2025-05-09",
                    1.08
                ],
                [
                    "2025-05-12",
                    -0.25
                ],
                [
                    "2025-05-13",
                    8.71
                ],
                [
                    "2025-05-14",
                    -2.28
                ],
                [
                    "2025-06-03",
                    5.91
                ],
                [
                    "2025-06-20",
                    12.62
                ],
                [
                    "2025-06-23",
                    0.88
                ],
                [
                    "2025-06-24",
                    -4.97
                ],
                [
                    "2025-06-25",
                    -3.35
                ],
                [
                    "2025-06-26",
                    12.19
                ],
                [
                    "2025-07-01",
                    6.76
                ],
                [
                    "2025-07-02",
                    -5.49
                ],
                [
                    "2025-07-04",
                    6.98
                ],
                [
                    "2025-07-07",
                    13.85
                ],
                [
                    "2025-07-08",
                    -1.77
                ],
                [
                    "2025-07-09",
                    4.49
                ],
                [
                    "2025-07-10",
                    9.67
                ],
                [
                    "2025-07-11",
                    -6.26
                ],
                [
                    "2025-07-29",
                    -1.93
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-28",
                    3.30
                ],
                [
                    "2025-03-03",
                    1.38
                ],
                [
                    "2025-04-29",
                    9.27
                ],
                [
                    "2025-05-06",
                    7.90
                ],
                [
                    "2025-05-07",
                    -3.40
                ],
                [
                    "2025-05-08",
                    4.18
                ],
                [
                    "2025-05-09",
                    -4.88
                ],
                [
                    "2025-05-12",
                    -0.97
                ],
                [
                    "2025-06-24",
                    7.40
                ],
                [
                    "2025-06-25",
                    5.63
                ],
                [
                    "2025-06-26",
                    -3.14
                ],
                [
                    "2025-06-27",
                    1.43
                ],
                [
                    "2025-06-30",
                    3.90
                ],
                [
                    "2025-07-01",
                    -3.16
                ],
                [
                    "2025-07-02",
                    1.40
                ],
                [
                    "2025-07-04",
                    -1.00
                ],
                [
                    "2025-07-24",
                    1.11
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    -0.59
                ],
                [
                    "2025-02-11",
                    7.01
                ],
                [
                    "2025-02-12",
                    2.88
                ],
                [
                    "2025-02-13",
                    2.12
                ],
                [
                    "2025-02-14",
                    3.42
                ],
                [
                    "2025-02-17",
                    0.87
                ],
                [
                    "2025-02-18",
                    5.33
                ],
                [
                    "2025-02-19",
                    -3.20
                ],
                [
                    "2025-02-20",
                    3.15
                ],
                [
                    "2025-02-21",
                    5.89
                ],
                [
                    "2025-02-24",
                    -2.65
                ],
                [
                    "2025-02-25",
                    7.36
                ],
                [
                    "2025-02-26",
                    -1.78
                ],
                [
                    "2025-02-27",
                    -6.19
                ],
                [
                    "2025-03-12",
                    -2.35
                ],
                [
                    "2025-03-18",
                    2.69
                ],
                [
                    "2025-03-19",
                    -0.34
                ],
                [
                    "2025-03-20",
                    2.01
                ],
                [
                    "2025-03-21",
                    7.65
                ],
                [
                    "2025-03-24",
                    7.13
                ],
                [
                    "2025-03-25",
                    1.25
                ],
                [
                    "2025-03-26",
                    3.92
                ],
                [
                    "2025-03-27",
                    -0.39
                ],
                [
                    "2025-03-28",
                    4.28
                ],
                [
                    "2025-03-31",
                    6.83
                ],
                [
                    "2025-04-01",
                    6.21
                ],
                [
                    "2025-04-02",
                    -5.06
                ],
                [
                    "2025-04-03",
                    5.83
                ],
                [
                    "2025-04-07",
                    12.86
                ],
                [
                    "2025-04-08",
                    10.82
                ],
                [
                    "2025-04-09",
                    3.96
                ],
                [
                    "2025-04-10",
                    -0.59
                ],
                [
                    "2025-04-11",
                    -2.88
                ],
                [
                    "2025-04-14",
                    -4.23
                ],
                [
                    "2025-04-21",
                    1.70
                ],
                [
                    "2025-04-22",
                    6.22
                ],
                [
                    "2025-04-23",
                    3.02
                ],
                [
                    "2025-04-24",
                    1.88
                ],
                [
                    "2025-04-25",
                    -1.68
                ],
                [
                    "2025-04-28",
                    2.91
                ],
                [
                    "2025-04-30",
                    4.35
                ],
                [
                    "2025-05-20",
                    -1.36
                ],
                [
                    "2025-05-21",
                    -1.34
                ],
                [
                    "2025-05-22",
                    -0.04
                ],
                [
                    "2025-05-23",
                    1.43
                ],
                [
                    "2025-05-26",
                    5.20
                ],
                [
                    "2025-05-27",
                    5.40
                ],
                [
                    "2025-05-28",
                    3.85
                ],
                [
                    "2025-05-29",
                    -5.08
                ],
                [
                    "2025-05-30",
                    1.38
                ],
                [
                    "2025-06-06",
                    5.87
                ],
                [
                    "2025-06-09",
                    1.68
                ],
                [
                    "2025-06-10",
                    1.21
                ],
                [
                    "2025-06-11",
                    -0.89
                ],
                [
                    "2025-06-12",
                    -2.06
                ],
                [
                    "2025-06-13",
                    9.58
                ],
                [
                    "2025-06-16",
                    -4.79
                ],
                [
                    "2025-06-17",
                    3.03
                ],
                [
                    "2025-06-18",
                    1.12
                ],
                [
                    "2025-06-19",
                    1.29
                ],
                [
                    "2025-07-03",
                    1.74
                ],
                [
                    "2025-07-14",
                    5.40
                ],
                [
                    "2025-07-15",
                    10.69
                ],
                [
                    "2025-07-16",
                    3.07
                ],
                [
                    "2025-07-17",
                    4.61
                ],
                [
                    "2025-07-18",
                    -1.03
                ],
                [
                    "2025-07-21",
                    -3.25
                ],
                [
                    "2025-07-22",
                    5.97
                ],
                [
                    "2025-07-23",
                    0.42
                ],
                [
                    "2025-07-25",
                    0.40
                ],
                [
                    "2025-07-28",
                    0.68
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "000001 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_40e1656b325a451aa2816ada0fb57491.setOption(option_40e1656b325a451aa2816ada0fb57491);
            window.addEventListener('resize', function(){
                chart_40e1656b325a451aa2816ada0fb57491.resize();
            })
    </script>
</body>
</html>
