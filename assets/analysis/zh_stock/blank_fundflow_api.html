<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行业板块资金流向分析 (API版)</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header .subtitle {
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .control-section {
            margin-bottom: 15px;
        }
        
        .control-section h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 1.1em;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 8px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            background: white;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 5px;
            border-radius: 3px;
            transition: background-color 0.2s;
        }
        
        .checkbox-item:hover {
            background-color: #f8f9fa;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.1);
        }
        
        .checkbox-item label {
            cursor: pointer;
            font-size: 13px;
            color: #495057;
        }
        
        .chart-container {
            padding: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
            font-size: 1.2em;
        }
        
        .error {
            text-align: center;
            padding: 50px;
            color: #dc3545;
            font-size: 1.2em;
        }
        
        #main {
            width: 100%;
            height: 600px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 行业板块资金流向分析</h1>
            <div class="subtitle" id="subtitle">近60天主力资金流向 | 数据更新时间: <span id="updateTime">加载中...</span></div>
        </div>
        
        <div class="controls">
            <div class="control-section">
                <h3>🎛️ 快速选择板块:</h3>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="updateChart()">🔄 更新图表</button>
                    <button class="btn btn-success" onclick="selectAll()">✅ 全选</button>
                    <button class="btn btn-danger" onclick="selectNone()">❌ 全不选</button>
                    <button class="btn btn-warning" onclick="selectTop10()">🔟 选择前10</button>
                </div>
            </div>
            
            <div class="control-section">
                <h3>📋 选择显示的板块:</h3>
                <div id="blankCheckboxes" class="checkbox-grid">
                    <div class="loading">正在加载板块数据...</div>
                </div>
            </div>
        </div>
        
        <div class="chart-container">
            <div id="main"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let chartData = null;
        let myChart = null;

        // 颜色配置
        const colors = [
            "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
            "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
            "#F8C471", "#82E0AA", "#F1948A", "#85C1E9", "#D7BDE2",
            "#A3E4D7", "#F9E79F", "#FADBD8", "#D5DBDB", "#AED6F1"
        ];

        // 初始化ECharts
        function initChart() {
            // 检查ECharts是否加载
            if (typeof echarts === 'undefined') {
                console.error('ECharts未加载，请检查CDN连接');
                document.getElementById('blankCheckboxes').innerHTML =
                    '<div class="error">ECharts库加载失败，请检查网络连接</div>';
                return false;
            }
            myChart = echarts.init(document.getElementById('main'));
            return true;
        }
        
        // 从API获取数据
        async function fetchData() {
            try {
                const response = await fetch('http://localhost:9999/api/v1/analyze/zh/blank/fund-flow?days=60');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('获取数据失败:', error);
                throw error;
            }
        }
        
        // 渲染板块选择框
        function renderCheckboxes(blankNames) {
            const container = document.getElementById('blankCheckboxes');
            container.innerHTML = '';
            
            blankNames.forEach((name, index) => {
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = `blank_${index}`;
                checkbox.value = name;
                
                const label = document.createElement('label');
                label.htmlFor = `blank_${index}`;
                label.textContent = name;
                
                div.appendChild(checkbox);
                div.appendChild(label);
                container.appendChild(div);
            });
        }
        
        // 更新图表
        function updateChart() {
            const checkboxes = document.querySelectorAll('#blankCheckboxes input[type="checkbox"]');
            const selectedBlanks = [];
            
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedBlanks.push(checkbox.value);
                }
            });
            
            if (selectedBlanks.length === 0) {
                alert('请至少选择一个板块！');
                return;
            }
            
            renderChart(selectedBlanks);
        }
        
        // 渲染图表
        function renderChart(selectedBlanks) {
            if (!chartData) return;
            
            const series = [];
            
            selectedBlanks.forEach((blankName, index) => {
                if (chartData.blank_data[blankName]) {
                    series.push({
                        name: blankName,
                        type: 'line',
                        data: chartData.blank_data[blankName].major,
                        lineStyle: {
                            width: 2,
                            color: colors[index % colors.length]
                        },
                        symbol: 'circle',
                        symbolSize: 4,
                        markPoint: {
                            data: [
                                {type: 'max', name: '最大值'},
                                {type: 'min', name: '最小值'}
                            ]
                        }
                    });
                }
            });
            
            const option = {
                title: {
                    text: '行业板块资金流向图 (近60天)',
                    subtext: '主力净流入 (万元)',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    type: 'scroll',
                    left: 'left',
                    top: '10%',
                    orient: 'vertical'
                },
                grid: {
                    left: '15%',
                    right: '10%',
                    bottom: '15%',
                    top: '20%'
                },
                xAxis: {
                    type: 'category',
                    data: chartData.dates,
                    boundaryGap: false,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '资金流入(万元)',
                    axisLabel: {
                        formatter: '{value}万'
                    }
                },
                series: series,
                dataZoom: [
                    {
                        type: 'slider',
                        show: true,
                        bottom: '5%',
                        start: 0,
                        end: 100
                    },
                    {
                        type: 'inside'
                    }
                ],
                toolbox: {
                    show: true,
                    feature: {
                        saveAsImage: {title: '保存图片'},
                        dataZoom: {title: {zoom: '区域缩放', back: '区域缩放还原'}},
                        restore: {title: '还原'},
                        magicType: {title: {line: '折线图', bar: '柱状图'}}
                    }
                }
            };
            
            myChart.setOption(option);
        }
        
        // 选择控制函数
        function selectAll() {
            const checkboxes = document.querySelectorAll('#blankCheckboxes input[type="checkbox"]');
            checkboxes.forEach(checkbox => checkbox.checked = true);
            updateChart();
        }
        
        function selectNone() {
            const checkboxes = document.querySelectorAll('#blankCheckboxes input[type="checkbox"]');
            checkboxes.forEach(checkbox => checkbox.checked = false);
            myChart.clear();
        }
        
        function selectTop10() {
            const checkboxes = document.querySelectorAll('#blankCheckboxes input[type="checkbox"]');
            checkboxes.forEach((checkbox, index) => {
                checkbox.checked = index < 10;
            });
            updateChart();
        }
        
        // 页面初始化
        async function init() {
            try {
                // 初始化图表
                if (!initChart()) {
                    return; // ECharts加载失败，直接返回
                }

                // 获取数据
                chartData = await fetchData();

                // 渲染复选框
                renderCheckboxes(chartData.blank_names);

                // 更新时间
                document.getElementById('updateTime').textContent = new Date().toLocaleString();

                // 默认选择前10个
                selectTop10();

            } catch (error) {
                document.getElementById('blankCheckboxes').innerHTML =
                    '<div class="error">数据加载失败，请检查API服务是否正常运行<br>错误信息: ' + error.message + '</div>';
                console.error('初始化失败:', error);
            }
        }
        
        // 响应式处理
        window.addEventListener('resize', function() {
            if (myChart) {
                myChart.resize();
            }
        });
        
        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
