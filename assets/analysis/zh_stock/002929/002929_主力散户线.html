<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="fc854f547f914db982ccf41a570700ac" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_fc854f547f914db982ccf41a570700ac = echarts.init(
            document.getElementById('fc854f547f914db982ccf41a570700ac'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_fc854f547f914db982ccf41a570700ac = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    33.4,
                    32.12,
                    31.66,
                    33.5
                ],
                [
                    32.99,
                    33.34,
                    32.91,
                    33.83
                ],
                [
                    32.97,
                    34.63,
                    32.78,
                    35.13
                ],
                [
                    35.17,
                    35.47,
                    34.76,
                    36.21
                ],
                [
                    39.01,
                    39.02,
                    39.01,
                    39.02
                ],
                [
                    40.98,
                    42.92,
                    40.51,
                    42.92
                ],
                [
                    47.21,
                    47.21,
                    47.21,
                    47.21
                ],
                [
                    51.93,
                    50.2,
                    46.52,
                    51.93
                ],
                [
                    48.41,
                    55.22,
                    45.58,
                    55.22
                ],
                [
                    53.0,
                    51.65,
                    50.02,
                    54.9
                ],
                [
                    50.97,
                    51.44,
                    50.9,
                    54.55
                ],
                [
                    53.02,
                    56.58,
                    52.1,
                    56.58
                ],
                [
                    58.81,
                    62.24,
                    58.8,
                    62.24
                ],
                [
                    68.46,
                    68.46,
                    68.46,
                    68.46
                ],
                [
                    74.0,
                    70.95,
                    68.86,
                    75.31
                ],
                [
                    64.11,
                    64.99,
                    63.86,
                    69.0
                ],
                [
                    64.84,
                    63.48,
                    61.59,
                    65.5
                ],
                [
                    63.0,
                    60.99,
                    59.4,
                    64.58
                ],
                [
                    59.5,
                    56.3,
                    56.01,
                    60.0
                ],
                [
                    57.5,
                    57.99,
                    56.0,
                    59.1
                ],
                [
                    55.1,
                    57.78,
                    55.1,
                    58.3
                ],
                [
                    60.2,
                    61.52,
                    58.46,
                    62.9
                ],
                [
                    63.72,
                    62.0,
                    60.48,
                    65.0
                ],
                [
                    60.1,
                    62.49,
                    59.38,
                    64.93
                ],
                [
                    60.8,
                    58.62,
                    56.6,
                    61.0
                ],
                [
                    59.02,
                    62.06,
                    58.26,
                    63.23
                ],
                [
                    62.06,
                    63.1,
                    61.9,
                    68.11
                ],
                [
                    62.5,
                    61.57,
                    59.82,
                    64.99
                ],
                [
                    61.0,
                    60.31,
                    58.3,
                    61.4
                ],
                [
                    60.19,
                    62.26,
                    58.96,
                    62.75
                ],
                [
                    66.9,
                    65.26,
                    64.02,
                    68.49
                ],
                [
                    65.5,
                    63.82,
                    63.28,
                    67.0
                ],
                [
                    62.94,
                    60.01,
                    60.01,
                    63.8
                ],
                [
                    59.48,
                    58.6,
                    57.0,
                    60.35
                ],
                [
                    58.38,
                    56.33,
                    54.83,
                    58.98
                ],
                [
                    56.3,
                    52.55,
                    52.02,
                    56.76
                ],
                [
                    52.56,
                    52.17,
                    52.17,
                    53.7
                ],
                [
                    52.17,
                    51.15,
                    50.99,
                    53.33
                ],
                [
                    51.0,
                    51.21,
                    51.0,
                    52.7
                ],
                [
                    50.31,
                    54.04,
                    49.6,
                    55.05
                ],
                [
                    54.0,
                    52.88,
                    51.99,
                    54.18
                ],
                [
                    52.62,
                    53.09,
                    52.62,
                    54.66
                ],
                [
                    52.07,
                    52.26,
                    51.65,
                    54.13
                ],
                [
                    47.04,
                    47.03,
                    47.03,
                    48.58
                ],
                [
                    44.4,
                    42.44,
                    42.33,
                    45.91
                ],
                [
                    41.68,
                    44.55,
                    39.21,
                    45.5
                ],
                [
                    46.28,
                    47.25,
                    45.18,
                    48.98
                ],
                [
                    46.15,
                    47.69,
                    46.15,
                    48.45
                ],
                [
                    48.3,
                    48.0,
                    47.4,
                    48.78
                ],
                [
                    47.52,
                    47.4,
                    46.94,
                    48.39
                ],
                [
                    47.01,
                    45.83,
                    44.79,
                    47.66
                ],
                [
                    45.93,
                    45.75,
                    45.75,
                    47.15
                ],
                [
                    45.94,
                    46.3,
                    45.5,
                    47.25
                ],
                [
                    46.31,
                    50.93,
                    45.73,
                    50.93
                ],
                [
                    50.42,
                    49.3,
                    48.9,
                    50.9
                ],
                [
                    49.77,
                    49.9,
                    48.97,
                    50.6
                ],
                [
                    49.3,
                    46.25,
                    45.75,
                    49.3
                ],
                [
                    46.25,
                    46.26,
                    46.04,
                    47.4
                ],
                [
                    45.8,
                    46.37,
                    45.51,
                    47.65
                ],
                [
                    46.83,
                    48.86,
                    46.53,
                    50.3
                ],
                [
                    48.96,
                    50.2,
                    48.0,
                    50.8
                ],
                [
                    51.09,
                    53.72,
                    50.28,
                    54.08
                ],
                [
                    54.15,
                    51.95,
                    51.37,
                    54.38
                ],
                [
                    51.8,
                    51.5,
                    51.3,
                    52.52
                ],
                [
                    51.25,
                    49.9,
                    49.75,
                    51.5
                ],
                [
                    50.19,
                    50.99,
                    49.89,
                    51.0
                ],
                [
                    51.63,
                    52.2,
                    51.63,
                    54.0
                ],
                [
                    52.0,
                    52.31,
                    51.71,
                    53.99
                ],
                [
                    52.0,
                    50.34,
                    50.0,
                    52.31
                ],
                [
                    48.37,
                    48.88,
                    48.37,
                    49.85
                ],
                [
                    48.5,
                    47.67,
                    46.9,
                    48.8
                ],
                [
                    48.18,
                    51.76,
                    47.51,
                    52.44
                ],
                [
                    53.24,
                    51.38,
                    51.35,
                    53.92
                ],
                [
                    51.17,
                    49.9,
                    49.7,
                    51.5
                ],
                [
                    49.91,
                    47.36,
                    47.36,
                    50.02
                ],
                [
                    47.4,
                    49.81,
                    47.36,
                    51.06
                ],
                [
                    50.5,
                    49.08,
                    48.73,
                    51.2
                ],
                [
                    49.5,
                    49.11,
                    48.16,
                    50.98
                ],
                [
                    48.6,
                    48.99,
                    47.89,
                    49.5
                ],
                [
                    48.5,
                    46.19,
                    46.01,
                    48.55
                ],
                [
                    45.81,
                    45.86,
                    45.65,
                    46.46
                ],
                [
                    46.31,
                    48.76,
                    46.0,
                    50.45
                ],
                [
                    47.86,
                    49.64,
                    46.8,
                    50.31
                ],
                [
                    49.5,
                    49.93,
                    48.51,
                    50.22
                ],
                [
                    49.93,
                    52.78,
                    49.7,
                    53.77
                ],
                [
                    52.27,
                    51.35,
                    51.02,
                    54.08
                ],
                [
                    51.31,
                    50.35,
                    49.68,
                    51.58
                ],
                [
                    50.35,
                    49.3,
                    49.0,
                    50.9
                ],
                [
                    48.61,
                    47.38,
                    46.91,
                    49.19
                ],
                [
                    47.15,
                    47.49,
                    46.97,
                    48.68
                ],
                [
                    47.69,
                    46.15,
                    45.81,
                    47.69
                ],
                [
                    46.31,
                    46.66,
                    44.9,
                    46.88
                ],
                [
                    46.52,
                    45.3,
                    45.02,
                    46.84
                ],
                [
                    45.3,
                    43.89,
                    43.85,
                    45.75
                ],
                [
                    43.08,
                    43.69,
                    42.91,
                    43.8
                ],
                [
                    44.09,
                    45.13,
                    43.86,
                    45.35
                ],
                [
                    46.76,
                    46.38,
                    45.36,
                    47.09
                ],
                [
                    46.6,
                    46.62,
                    45.62,
                    47.98
                ],
                [
                    46.62,
                    47.15,
                    45.81,
                    47.97
                ],
                [
                    47.0,
                    47.26,
                    46.82,
                    47.48
                ],
                [
                    47.27,
                    46.55,
                    46.0,
                    47.27
                ],
                [
                    46.16,
                    45.55,
                    45.3,
                    46.74
                ],
                [
                    45.41,
                    45.29,
                    45.12,
                    46.38
                ],
                [
                    45.29,
                    45.51,
                    44.9,
                    46.48
                ],
                [
                    45.0,
                    44.71,
                    44.57,
                    45.35
                ],
                [
                    44.6,
                    46.05,
                    44.56,
                    46.71
                ],
                [
                    46.29,
                    46.03,
                    45.16,
                    46.29
                ],
                [
                    45.8,
                    45.22,
                    44.95,
                    45.96
                ],
                [
                    45.3,
                    45.14,
                    44.63,
                    45.5
                ],
                [
                    45.13,
                    45.6,
                    44.81,
                    45.99
                ],
                [
                    45.71,
                    47.87,
                    45.58,
                    48.0
                ],
                [
                    47.0,
                    47.7,
                    46.6,
                    48.5
                ],
                [
                    46.9,
                    46.72,
                    46.4,
                    47.5
                ],
                [
                    46.84,
                    46.39,
                    46.33,
                    47.5
                ],
                [
                    46.4,
                    46.49,
                    46.0,
                    46.74
                ],
                [
                    46.6,
                    45.03,
                    44.7,
                    46.6
                ],
                [
                    44.77,
                    45.17,
                    44.55,
                    45.99
                ],
                [
                    45.19,
                    45.37,
                    44.88,
                    45.44
                ],
                [
                    45.37,
                    46.16,
                    45.29,
                    46.23
                ],
                [
                    46.65,
                    45.44,
                    45.16,
                    46.7
                ],
                [
                    45.38,
                    44.81,
                    44.5,
                    45.4
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -13.06
                ],
                [
                    "2025-02-05",
                    1.88
                ],
                [
                    "2025-02-06",
                    1.59
                ],
                [
                    "2025-02-07",
                    0.17
                ],
                [
                    "2025-02-10",
                    27.93
                ],
                [
                    "2025-02-11",
                    10.63
                ],
                [
                    "2025-02-12",
                    19.59
                ],
                [
                    "2025-02-13",
                    -6.13
                ],
                [
                    "2025-02-14",
                    2.57
                ],
                [
                    "2025-02-17",
                    -0.96
                ],
                [
                    "2025-02-18",
                    -0.42
                ],
                [
                    "2025-02-19",
                    13.98
                ],
                [
                    "2025-02-20",
                    18.23
                ],
                [
                    "2025-02-21",
                    36.94
                ],
                [
                    "2025-02-24",
                    -8.63
                ],
                [
                    "2025-02-25",
                    -6.15
                ],
                [
                    "2025-02-26",
                    0.25
                ],
                [
                    "2025-02-27",
                    -10.19
                ],
                [
                    "2025-02-28",
                    -12.94
                ],
                [
                    "2025-03-03",
                    -0.35
                ],
                [
                    "2025-03-04",
                    -5.21
                ],
                [
                    "2025-03-05",
                    9.16
                ],
                [
                    "2025-03-06",
                    0.79
                ],
                [
                    "2025-03-07",
                    5.64
                ],
                [
                    "2025-03-10",
                    -8.62
                ],
                [
                    "2025-03-11",
                    -2.34
                ],
                [
                    "2025-03-12",
                    0.70
                ],
                [
                    "2025-03-13",
                    -6.66
                ],
                [
                    "2025-03-14",
                    -9.92
                ],
                [
                    "2025-03-17",
                    6.15
                ],
                [
                    "2025-03-18",
                    2.39
                ],
                [
                    "2025-03-19",
                    -0.43
                ],
                [
                    "2025-03-20",
                    -12.21
                ],
                [
                    "2025-03-21",
                    -5.22
                ],
                [
                    "2025-03-24",
                    -18.68
                ],
                [
                    "2025-03-25",
                    -12.69
                ],
                [
                    "2025-03-26",
                    -1.61
                ],
                [
                    "2025-03-27",
                    -15.93
                ],
                [
                    "2025-03-28",
                    1.74
                ],
                [
                    "2025-03-31",
                    5.35
                ],
                [
                    "2025-04-01",
                    7.68
                ],
                [
                    "2025-04-02",
                    4.69
                ],
                [
                    "2025-04-03",
                    -4.39
                ],
                [
                    "2025-04-07",
                    -15.09
                ],
                [
                    "2025-04-08",
                    -8.49
                ],
                [
                    "2025-04-09",
                    -11.86
                ],
                [
                    "2025-04-10",
                    5.33
                ],
                [
                    "2025-04-11",
                    -2.20
                ],
                [
                    "2025-04-14",
                    -7.66
                ],
                [
                    "2025-04-15",
                    -3.23
                ],
                [
                    "2025-04-16",
                    -16.99
                ],
                [
                    "2025-04-17",
                    -0.80
                ],
                [
                    "2025-04-18",
                    5.14
                ],
                [
                    "2025-04-21",
                    25.95
                ],
                [
                    "2025-04-22",
                    -7.00
                ],
                [
                    "2025-04-23",
                    5.03
                ],
                [
                    "2025-04-24",
                    -12.39
                ],
                [
                    "2025-04-25",
                    -3.59
                ],
                [
                    "2025-04-28",
                    3.71
                ],
                [
                    "2025-04-29",
                    3.63
                ],
                [
                    "2025-04-30",
                    9.42
                ],
                [
                    "2025-05-06",
                    11.78
                ],
                [
                    "2025-05-07",
                    -2.83
                ],
                [
                    "2025-05-08",
                    -2.76
                ],
                [
                    "2025-05-09",
                    -21.79
                ],
                [
                    "2025-05-12",
                    4.68
                ],
                [
                    "2025-05-13",
                    6.75
                ],
                [
                    "2025-05-14",
                    -3.62
                ],
                [
                    "2025-05-15",
                    0.21
                ],
                [
                    "2025-05-16",
                    -4.25
                ],
                [
                    "2025-05-19",
                    -14.87
                ],
                [
                    "2025-05-20",
                    20.63
                ],
                [
                    "2025-05-21",
                    -6.68
                ],
                [
                    "2025-05-22",
                    -1.48
                ],
                [
                    "2025-05-23",
                    -1.75
                ],
                [
                    "2025-05-26",
                    6.60
                ],
                [
                    "2025-05-27",
                    5.61
                ],
                [
                    "2025-05-28",
                    4.14
                ],
                [
                    "2025-05-29",
                    -1.37
                ],
                [
                    "2025-05-30",
                    -10.73
                ],
                [
                    "2025-06-03",
                    -5.03
                ],
                [
                    "2025-06-04",
                    20.52
                ],
                [
                    "2025-06-05",
                    -10.81
                ],
                [
                    "2025-06-06",
                    -5.62
                ],
                [
                    "2025-06-09",
                    -0.30
                ],
                [
                    "2025-06-10",
                    -4.27
                ],
                [
                    "2025-06-11",
                    -0.88
                ],
                [
                    "2025-06-12",
                    -6.15
                ],
                [
                    "2025-06-13",
                    -16.45
                ],
                [
                    "2025-06-16",
                    6.01
                ],
                [
                    "2025-06-17",
                    -10.25
                ],
                [
                    "2025-06-18",
                    4.00
                ],
                [
                    "2025-06-19",
                    -11.43
                ],
                [
                    "2025-06-20",
                    -5.79
                ],
                [
                    "2025-06-23",
                    -8.36
                ],
                [
                    "2025-06-24",
                    2.33
                ],
                [
                    "2025-06-25",
                    4.86
                ],
                [
                    "2025-06-26",
                    2.02
                ],
                [
                    "2025-06-27",
                    2.88
                ],
                [
                    "2025-06-30",
                    -12.25
                ],
                [
                    "2025-07-01",
                    -10.91
                ],
                [
                    "2025-07-02",
                    -11.87
                ],
                [
                    "2025-07-03",
                    -9.55
                ],
                [
                    "2025-07-04",
                    4.06
                ],
                [
                    "2025-07-07",
                    -14.44
                ],
                [
                    "2025-07-08",
                    -0.99
                ],
                [
                    "2025-07-09",
                    0.79
                ],
                [
                    "2025-07-10",
                    -4.75
                ],
                [
                    "2025-07-11",
                    -11.90
                ],
                [
                    "2025-07-14",
                    4.65
                ],
                [
                    "2025-07-15",
                    7.50
                ],
                [
                    "2025-07-16",
                    -2.96
                ],
                [
                    "2025-07-17",
                    -4.52
                ],
                [
                    "2025-07-18",
                    -11.07
                ],
                [
                    "2025-07-21",
                    -5.92
                ],
                [
                    "2025-07-22",
                    -11.87
                ],
                [
                    "2025-07-23",
                    -3.86
                ],
                [
                    "2025-07-24",
                    -4.12
                ],
                [
                    "2025-07-25",
                    11.15
                ],
                [
                    "2025-07-28",
                    -3.36
                ],
                [
                    "2025-07-29",
                    -20.68
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    2.59
                ],
                [
                    "2025-02-05",
                    -0.97
                ],
                [
                    "2025-02-06",
                    -3.19
                ],
                [
                    "2025-02-07",
                    -0.54
                ],
                [
                    "2025-02-10",
                    -14.10
                ],
                [
                    "2025-02-11",
                    -8.53
                ],
                [
                    "2025-02-12",
                    -11.86
                ],
                [
                    "2025-02-13",
                    0.13
                ],
                [
                    "2025-02-14",
                    -1.45
                ],
                [
                    "2025-02-17",
                    1.91
                ],
                [
                    "2025-02-18",
                    -1.02
                ],
                [
                    "2025-02-19",
                    -7.92
                ],
                [
                    "2025-02-20",
                    -9.70
                ],
                [
                    "2025-02-21",
                    -18.33
                ],
                [
                    "2025-02-24",
                    1.34
                ],
                [
                    "2025-02-25",
                    0.61
                ],
                [
                    "2025-02-26",
                    -4.17
                ],
                [
                    "2025-02-27",
                    -1.64
                ],
                [
                    "2025-02-28",
                    0.25
                ],
                [
                    "2025-03-03",
                    -2.73
                ],
                [
                    "2025-03-04",
                    0.47
                ],
                [
                    "2025-03-05",
                    3.51
                ],
                [
                    "2025-03-06",
                    2.12
                ],
                [
                    "2025-03-07",
                    -4.41
                ],
                [
                    "2025-03-10",
                    2.18
                ],
                [
                    "2025-03-11",
                    4.74
                ],
                [
                    "2025-03-12",
                    -1.89
                ],
                [
                    "2025-03-13",
                    1.65
                ],
                [
                    "2025-03-14",
                    -3.13
                ],
                [
                    "2025-03-17",
                    0.45
                ],
                [
                    "2025-03-18",
                    -3.91
                ],
                [
                    "2025-03-19",
                    1.64
                ],
                [
                    "2025-03-20",
                    1.75
                ],
                [
                    "2025-03-21",
                    2.54
                ],
                [
                    "2025-03-24",
                    5.63
                ],
                [
                    "2025-03-25",
                    -1.46
                ],
                [
                    "2025-03-26",
                    -0.98
                ],
                [
                    "2025-03-27",
                    1.74
                ],
                [
                    "2025-03-28",
                    1.13
                ],
                [
                    "2025-03-31",
                    1.93
                ],
                [
                    "2025-04-01",
                    3.39
                ],
                [
                    "2025-04-02",
                    1.29
                ],
                [
                    "2025-04-03",
                    -3.10
                ],
                [
                    "2025-04-07",
                    2.01
                ],
                [
                    "2025-04-08",
                    3.03
                ],
                [
                    "2025-04-09",
                    -3.06
                ],
                [
                    "2025-04-10",
                    -0.21
                ],
                [
                    "2025-04-11",
                    -1.14
                ],
                [
                    "2025-04-14",
                    -2.02
                ],
                [
                    "2025-04-15",
                    -0.99
                ],
                [
                    "2025-04-16",
                    -0.94
                ],
                [
                    "2025-04-17",
                    -0.95
                ],
                [
                    "2025-04-18",
                    2.51
                ],
                [
                    "2025-04-21",
                    -10.31
                ],
                [
                    "2025-04-22",
                    0.22
                ],
                [
                    "2025-04-23",
                    1.53
                ],
                [
                    "2025-04-24",
                    3.38
                ],
                [
                    "2025-04-25",
                    -4.37
                ],
                [
                    "2025-04-28",
                    -2.30
                ],
                [
                    "2025-04-29",
                    -5.77
                ],
                [
                    "2025-04-30",
                    -1.47
                ],
                [
                    "2025-05-06",
                    -1.31
                ],
                [
                    "2025-05-07",
                    4.76
                ],
                [
                    "2025-05-08",
                    4.67
                ],
                [
                    "2025-05-09",
                    4.80
                ],
                [
                    "2025-05-12",
                    -2.77
                ],
                [
                    "2025-05-13",
                    -6.46
                ],
                [
                    "2025-05-14",
                    -1.09
                ],
                [
                    "2025-05-15",
                    1.75
                ],
                [
                    "2025-05-16",
                    4.20
                ],
                [
                    "2025-05-19",
                    2.30
                ],
                [
                    "2025-05-20",
                    -11.10
                ],
                [
                    "2025-05-21",
                    -2.42
                ],
                [
                    "2025-05-22",
                    -4.33
                ],
                [
                    "2025-05-23",
                    0.33
                ],
                [
                    "2025-05-26",
                    0.63
                ],
                [
                    "2025-05-27",
                    -0.18
                ],
                [
                    "2025-05-28",
                    1.59
                ],
                [
                    "2025-05-29",
                    -3.99
                ],
                [
                    "2025-05-30",
                    3.72
                ],
                [
                    "2025-06-03",
                    -0.21
                ],
                [
                    "2025-06-04",
                    -13.04
                ],
                [
                    "2025-06-05",
                    2.16
                ],
                [
                    "2025-06-06",
                    -1.76
                ],
                [
                    "2025-06-09",
                    -0.60
                ],
                [
                    "2025-06-10",
                    2.19
                ],
                [
                    "2025-06-11",
                    2.08
                ],
                [
                    "2025-06-12",
                    3.73
                ],
                [
                    "2025-06-13",
                    6.62
                ],
                [
                    "2025-06-16",
                    2.55
                ],
                [
                    "2025-06-17",
                    5.19
                ],
                [
                    "2025-06-18",
                    5.27
                ],
                [
                    "2025-06-19",
                    5.75
                ],
                [
                    "2025-06-20",
                    -0.06
                ],
                [
                    "2025-06-23",
                    3.36
                ],
                [
                    "2025-06-24",
                    4.19
                ],
                [
                    "2025-06-25",
                    -3.40
                ],
                [
                    "2025-06-26",
                    -0.79
                ],
                [
                    "2025-06-27",
                    -2.83
                ],
                [
                    "2025-06-30",
                    2.94
                ],
                [
                    "2025-07-01",
                    -0.91
                ],
                [
                    "2025-07-02",
                    -0.55
                ],
                [
                    "2025-07-03",
                    5.31
                ],
                [
                    "2025-07-04",
                    1.99
                ],
                [
                    "2025-07-07",
                    4.04
                ],
                [
                    "2025-07-08",
                    -0.87
                ],
                [
                    "2025-07-09",
                    -1.32
                ],
                [
                    "2025-07-10",
                    3.35
                ],
                [
                    "2025-07-11",
                    -0.34
                ],
                [
                    "2025-07-14",
                    1.75
                ],
                [
                    "2025-07-15",
                    -0.12
                ],
                [
                    "2025-07-16",
                    -0.65
                ],
                [
                    "2025-07-17",
                    0.27
                ],
                [
                    "2025-07-18",
                    8.22
                ],
                [
                    "2025-07-21",
                    1.87
                ],
                [
                    "2025-07-22",
                    10.02
                ],
                [
                    "2025-07-23",
                    2.56
                ],
                [
                    "2025-07-24",
                    3.98
                ],
                [
                    "2025-07-25",
                    -2.12
                ],
                [
                    "2025-07-28",
                    -0.39
                ],
                [
                    "2025-07-29",
                    7.44
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    10.47
                ],
                [
                    "2025-02-05",
                    -0.91
                ],
                [
                    "2025-02-06",
                    1.60
                ],
                [
                    "2025-02-07",
                    0.37
                ],
                [
                    "2025-02-10",
                    -13.82
                ],
                [
                    "2025-02-11",
                    -2.09
                ],
                [
                    "2025-02-12",
                    -7.73
                ],
                [
                    "2025-02-13",
                    5.99
                ],
                [
                    "2025-02-14",
                    -1.13
                ],
                [
                    "2025-02-17",
                    -0.95
                ],
                [
                    "2025-02-18",
                    1.43
                ],
                [
                    "2025-02-19",
                    -6.06
                ],
                [
                    "2025-02-20",
                    -8.53
                ],
                [
                    "2025-02-21",
                    -18.61
                ],
                [
                    "2025-02-24",
                    7.29
                ],
                [
                    "2025-02-25",
                    5.54
                ],
                [
                    "2025-02-26",
                    3.92
                ],
                [
                    "2025-02-27",
                    11.84
                ],
                [
                    "2025-02-28",
                    12.69
                ],
                [
                    "2025-03-03",
                    3.08
                ],
                [
                    "2025-03-04",
                    4.74
                ],
                [
                    "2025-03-05",
                    -12.67
                ],
                [
                    "2025-03-06",
                    -2.92
                ],
                [
                    "2025-03-07",
                    -1.24
                ],
                [
                    "2025-03-10",
                    6.44
                ],
                [
                    "2025-03-11",
                    -2.40
                ],
                [
                    "2025-03-12",
                    1.18
                ],
                [
                    "2025-03-13",
                    5.01
                ],
                [
                    "2025-03-14",
                    13.05
                ],
                [
                    "2025-03-17",
                    -6.60
                ],
                [
                    "2025-03-18",
                    1.52
                ],
                [
                    "2025-03-19",
                    -1.20
                ],
                [
                    "2025-03-20",
                    10.46
                ],
                [
                    "2025-03-21",
                    2.68
                ],
                [
                    "2025-03-24",
                    13.04
                ],
                [
                    "2025-03-25",
                    14.15
                ],
                [
                    "2025-03-26",
                    2.60
                ],
                [
                    "2025-03-27",
                    14.19
                ],
                [
                    "2025-03-28",
                    -2.87
                ],
                [
                    "2025-03-31",
                    -7.29
                ],
                [
                    "2025-04-01",
                    -11.07
                ],
                [
                    "2025-04-02",
                    -5.98
                ],
                [
                    "2025-04-03",
                    7.50
                ],
                [
                    "2025-04-07",
                    13.07
                ],
                [
                    "2025-04-08",
                    5.46
                ],
                [
                    "2025-04-09",
                    14.92
                ],
                [
                    "2025-04-10",
                    -5.12
                ],
                [
                    "2025-04-11",
                    3.35
                ],
                [
                    "2025-04-14",
                    9.68
                ],
                [
                    "2025-04-15",
                    4.21
                ],
                [
                    "2025-04-16",
                    17.94
                ],
                [
                    "2025-04-17",
                    1.74
                ],
                [
                    "2025-04-18",
                    -7.64
                ],
                [
                    "2025-04-21",
                    -15.64
                ],
                [
                    "2025-04-22",
                    6.77
                ],
                [
                    "2025-04-23",
                    -6.56
                ],
                [
                    "2025-04-24",
                    9.01
                ],
                [
                    "2025-04-25",
                    7.96
                ],
                [
                    "2025-04-28",
                    -1.41
                ],
                [
                    "2025-04-29",
                    2.14
                ],
                [
                    "2025-04-30",
                    -7.94
                ],
                [
                    "2025-05-06",
                    -10.47
                ],
                [
                    "2025-05-07",
                    -1.93
                ],
                [
                    "2025-05-08",
                    -1.90
                ],
                [
                    "2025-05-09",
                    17.00
                ],
                [
                    "2025-05-12",
                    -1.91
                ],
                [
                    "2025-05-13",
                    -0.28
                ],
                [
                    "2025-05-14",
                    4.71
                ],
                [
                    "2025-05-15",
                    -1.97
                ],
                [
                    "2025-05-16",
                    0.05
                ],
                [
                    "2025-05-19",
                    12.57
                ],
                [
                    "2025-05-20",
                    -9.52
                ],
                [
                    "2025-05-21",
                    9.10
                ],
                [
                    "2025-05-22",
                    5.81
                ],
                [
                    "2025-05-23",
                    1.42
                ],
                [
                    "2025-05-26",
                    -7.23
                ],
                [
                    "2025-05-27",
                    -5.43
                ],
                [
                    "2025-05-28",
                    -5.73
                ],
                [
                    "2025-05-29",
                    5.36
                ],
                [
                    "2025-05-30",
                    7.02
                ],
                [
                    "2025-06-03",
                    5.23
                ],
                [
                    "2025-06-04",
                    -7.48
                ],
                [
                    "2025-06-05",
                    8.64
                ],
                [
                    "2025-06-06",
                    7.39
                ],
                [
                    "2025-06-09",
                    0.90
                ],
                [
                    "2025-06-10",
                    2.08
                ],
                [
                    "2025-06-11",
                    -1.20
                ],
                [
                    "2025-06-12",
                    2.42
                ],
                [
                    "2025-06-13",
                    9.83
                ],
                [
                    "2025-06-16",
                    -8.56
                ],
                [
                    "2025-06-17",
                    5.05
                ],
                [
                    "2025-06-18",
                    -9.27
                ],
                [
                    "2025-06-19",
                    5.68
                ],
                [
                    "2025-06-20",
                    5.85
                ],
                [
                    "2025-06-23",
                    5.01
                ],
                [
                    "2025-06-24",
                    -6.52
                ],
                [
                    "2025-06-25",
                    -1.46
                ],
                [
                    "2025-06-26",
                    -1.23
                ],
                [
                    "2025-06-27",
                    -0.04
                ],
                [
                    "2025-06-30",
                    9.31
                ],
                [
                    "2025-07-01",
                    11.82
                ],
                [
                    "2025-07-02",
                    12.41
                ],
                [
                    "2025-07-03",
                    4.24
                ],
                [
                    "2025-07-04",
                    -6.05
                ],
                [
                    "2025-07-07",
                    10.41
                ],
                [
                    "2025-07-08",
                    1.86
                ],
                [
                    "2025-07-09",
                    0.53
                ],
                [
                    "2025-07-10",
                    1.41
                ],
                [
                    "2025-07-11",
                    12.24
                ],
                [
                    "2025-07-14",
                    -6.41
                ],
                [
                    "2025-07-15",
                    -7.38
                ],
                [
                    "2025-07-16",
                    3.61
                ],
                [
                    "2025-07-17",
                    4.25
                ],
                [
                    "2025-07-18",
                    2.86
                ],
                [
                    "2025-07-21",
                    4.04
                ],
                [
                    "2025-07-22",
                    1.85
                ],
                [
                    "2025-07-23",
                    1.30
                ],
                [
                    "2025-07-24",
                    0.14
                ],
                [
                    "2025-07-25",
                    -9.03
                ],
                [
                    "2025-07-28",
                    3.76
                ],
                [
                    "2025-07-29",
                    13.23
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    27.93
                ],
                [
                    "2025-02-11",
                    10.63
                ],
                [
                    "2025-02-12",
                    19.59
                ],
                [
                    "2025-02-13",
                    -6.13
                ],
                [
                    "2025-02-14",
                    2.57
                ],
                [
                    "2025-02-17",
                    -0.96
                ],
                [
                    "2025-02-18",
                    -0.42
                ],
                [
                    "2025-02-19",
                    13.98
                ],
                [
                    "2025-02-20",
                    18.23
                ],
                [
                    "2025-02-21",
                    36.94
                ],
                [
                    "2025-02-24",
                    -8.63
                ],
                [
                    "2025-02-25",
                    -6.15
                ],
                [
                    "2025-02-26",
                    0.25
                ],
                [
                    "2025-03-07",
                    5.64
                ],
                [
                    "2025-03-10",
                    -8.62
                ],
                [
                    "2025-03-11",
                    -2.34
                ],
                [
                    "2025-04-02",
                    4.69
                ],
                [
                    "2025-04-03",
                    -4.39
                ],
                [
                    "2025-04-23",
                    5.03
                ],
                [
                    "2025-04-24",
                    -12.39
                ],
                [
                    "2025-05-06",
                    11.78
                ],
                [
                    "2025-05-07",
                    -2.83
                ],
                [
                    "2025-05-08",
                    -2.76
                ],
                [
                    "2025-05-26",
                    6.60
                ],
                [
                    "2025-05-28",
                    4.14
                ],
                [
                    "2025-05-29",
                    -1.37
                ],
                [
                    "2025-05-30",
                    -10.73
                ],
                [
                    "2025-06-27",
                    2.88
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-10",
                    2.18
                ],
                [
                    "2025-03-11",
                    4.74
                ],
                [
                    "2025-04-01",
                    3.39
                ],
                [
                    "2025-04-02",
                    1.29
                ],
                [
                    "2025-04-03",
                    -3.10
                ],
                [
                    "2025-04-07",
                    2.01
                ],
                [
                    "2025-05-08",
                    4.67
                ],
                [
                    "2025-05-09",
                    4.80
                ],
                [
                    "2025-05-30",
                    3.72
                ],
                [
                    "2025-06-18",
                    5.27
                ],
                [
                    "2025-06-20",
                    -0.06
                ],
                [
                    "2025-06-27",
                    -2.83
                ],
                [
                    "2025-07-18",
                    8.22
                ],
                [
                    "2025-07-25",
                    -2.12
                ],
                [
                    "2025-07-28",
                    -0.39
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-28",
                    12.69
                ],
                [
                    "2025-03-03",
                    3.08
                ],
                [
                    "2025-03-04",
                    4.74
                ],
                [
                    "2025-03-05",
                    -12.67
                ],
                [
                    "2025-03-06",
                    -2.92
                ],
                [
                    "2025-03-12",
                    1.18
                ],
                [
                    "2025-03-13",
                    5.01
                ],
                [
                    "2025-03-14",
                    13.05
                ],
                [
                    "2025-03-17",
                    -6.60
                ],
                [
                    "2025-03-18",
                    1.52
                ],
                [
                    "2025-03-19",
                    -1.20
                ],
                [
                    "2025-03-20",
                    10.46
                ],
                [
                    "2025-03-21",
                    2.68
                ],
                [
                    "2025-03-24",
                    13.04
                ],
                [
                    "2025-03-25",
                    14.15
                ],
                [
                    "2025-03-26",
                    2.60
                ],
                [
                    "2025-03-27",
                    14.19
                ],
                [
                    "2025-03-28",
                    -2.87
                ],
                [
                    "2025-03-31",
                    -7.29
                ],
                [
                    "2025-04-08",
                    5.46
                ],
                [
                    "2025-04-09",
                    14.92
                ],
                [
                    "2025-04-10",
                    -5.12
                ],
                [
                    "2025-04-11",
                    3.35
                ],
                [
                    "2025-04-14",
                    9.68
                ],
                [
                    "2025-04-15",
                    4.21
                ],
                [
                    "2025-04-16",
                    17.94
                ],
                [
                    "2025-04-17",
                    1.74
                ],
                [
                    "2025-04-18",
                    -7.64
                ],
                [
                    "2025-04-28",
                    -1.41
                ],
                [
                    "2025-04-29",
                    2.14
                ],
                [
                    "2025-05-12",
                    -1.91
                ],
                [
                    "2025-05-13",
                    -0.28
                ],
                [
                    "2025-05-14",
                    4.71
                ],
                [
                    "2025-05-15",
                    -1.97
                ],
                [
                    "2025-05-19",
                    12.57
                ],
                [
                    "2025-05-20",
                    -9.52
                ],
                [
                    "2025-05-21",
                    9.10
                ],
                [
                    "2025-05-22",
                    5.81
                ],
                [
                    "2025-05-23",
                    1.42
                ],
                [
                    "2025-06-03",
                    5.23
                ],
                [
                    "2025-06-05",
                    8.64
                ],
                [
                    "2025-06-06",
                    7.39
                ],
                [
                    "2025-06-09",
                    0.90
                ],
                [
                    "2025-06-10",
                    2.08
                ],
                [
                    "2025-06-11",
                    -1.20
                ],
                [
                    "2025-06-12",
                    2.42
                ],
                [
                    "2025-06-13",
                    9.83
                ],
                [
                    "2025-06-16",
                    -8.56
                ],
                [
                    "2025-06-17",
                    5.05
                ],
                [
                    "2025-06-19",
                    5.68
                ],
                [
                    "2025-06-23",
                    5.01
                ],
                [
                    "2025-06-24",
                    -6.52
                ],
                [
                    "2025-06-25",
                    -1.46
                ],
                [
                    "2025-06-26",
                    -1.23
                ],
                [
                    "2025-06-30",
                    9.31
                ],
                [
                    "2025-07-01",
                    11.82
                ],
                [
                    "2025-07-02",
                    12.41
                ],
                [
                    "2025-07-03",
                    4.24
                ],
                [
                    "2025-07-04",
                    -6.05
                ],
                [
                    "2025-07-07",
                    10.41
                ],
                [
                    "2025-07-08",
                    1.86
                ],
                [
                    "2025-07-09",
                    0.53
                ],
                [
                    "2025-07-10",
                    1.41
                ],
                [
                    "2025-07-11",
                    12.24
                ],
                [
                    "2025-07-14",
                    -6.41
                ],
                [
                    "2025-07-15",
                    -7.38
                ],
                [
                    "2025-07-16",
                    3.61
                ],
                [
                    "2025-07-17",
                    4.25
                ],
                [
                    "2025-07-21",
                    4.04
                ],
                [
                    "2025-07-22",
                    1.85
                ],
                [
                    "2025-07-23",
                    1.30
                ],
                [
                    "2025-07-24",
                    0.14
                ],
                [
                    "2025-07-29",
                    13.23
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002929 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_fc854f547f914db982ccf41a570700ac.setOption(option_fc854f547f914db982ccf41a570700ac);
            window.addEventListener('resize', function(){
                chart_fc854f547f914db982ccf41a570700ac.resize();
            })
    </script>
</body>
</html>
