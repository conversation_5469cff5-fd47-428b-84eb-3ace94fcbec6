<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="e92cd416895f48cd8ea2e218e3e8acc9" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_e92cd416895f48cd8ea2e218e3e8acc9 = echarts.init(
            document.getElementById('e92cd416895f48cd8ea2e218e3e8acc9'), 'white', {renderer: 'canvas'});
        var option_e92cd416895f48cd8ea2e218e3e8acc9 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    33.40,
                    32.12,
                    31.66,
                    33.50
                ],
                [
                    32.99,
                    33.34,
                    32.91,
                    33.83
                ],
                [
                    32.97,
                    34.63,
                    32.78,
                    35.13
                ],
                [
                    35.17,
                    35.47,
                    34.76,
                    36.21
                ],
                [
                    39.01,
                    39.02,
                    39.01,
                    39.02
                ],
                [
                    40.98,
                    42.92,
                    40.51,
                    42.92
                ],
                [
                    47.21,
                    47.21,
                    47.21,
                    47.21
                ],
                [
                    51.93,
                    50.20,
                    46.52,
                    51.93
                ],
                [
                    48.41,
                    55.22,
                    45.58,
                    55.22
                ],
                [
                    53.00,
                    51.65,
                    50.02,
                    54.90
                ],
                [
                    50.97,
                    51.44,
                    50.90,
                    54.55
                ],
                [
                    53.02,
                    56.58,
                    52.10,
                    56.58
                ],
                [
                    58.81,
                    62.24,
                    58.80,
                    62.24
                ],
                [
                    68.46,
                    68.46,
                    68.46,
                    68.46
                ],
                [
                    74.00,
                    70.95,
                    68.86,
                    75.31
                ],
                [
                    64.11,
                    64.99,
                    63.86,
                    69.00
                ],
                [
                    64.84,
                    63.48,
                    61.59,
                    65.50
                ],
                [
                    63.00,
                    60.99,
                    59.40,
                    64.58
                ],
                [
                    59.50,
                    56.30,
                    56.01,
                    60.00
                ],
                [
                    57.50,
                    57.99,
                    56.00,
                    59.10
                ],
                [
                    55.10,
                    57.78,
                    55.10,
                    58.30
                ],
                [
                    60.20,
                    61.52,
                    58.46,
                    62.90
                ],
                [
                    63.72,
                    62.00,
                    60.48,
                    65.00
                ],
                [
                    60.10,
                    62.49,
                    59.38,
                    64.93
                ],
                [
                    60.80,
                    58.62,
                    56.60,
                    61.00
                ],
                [
                    59.02,
                    62.06,
                    58.26,
                    63.23
                ],
                [
                    62.06,
                    63.10,
                    61.90,
                    68.11
                ],
                [
                    62.50,
                    61.57,
                    59.82,
                    64.99
                ],
                [
                    61.00,
                    60.31,
                    58.30,
                    61.40
                ],
                [
                    60.19,
                    62.26,
                    58.96,
                    62.75
                ],
                [
                    66.90,
                    65.26,
                    64.02,
                    68.49
                ],
                [
                    65.50,
                    63.82,
                    63.28,
                    67.00
                ],
                [
                    62.94,
                    60.01,
                    60.01,
                    63.80
                ],
                [
                    59.48,
                    58.60,
                    57.00,
                    60.35
                ],
                [
                    58.38,
                    56.33,
                    54.83,
                    58.98
                ],
                [
                    56.30,
                    52.55,
                    52.02,
                    56.76
                ],
                [
                    52.56,
                    52.17,
                    52.17,
                    53.70
                ],
                [
                    52.17,
                    51.15,
                    50.99,
                    53.33
                ],
                [
                    51.00,
                    51.21,
                    51.00,
                    52.70
                ],
                [
                    50.31,
                    54.04,
                    49.60,
                    55.05
                ],
                [
                    54.00,
                    52.88,
                    51.99,
                    54.18
                ],
                [
                    52.62,
                    53.09,
                    52.62,
                    54.66
                ],
                [
                    52.07,
                    52.26,
                    51.65,
                    54.13
                ],
                [
                    47.04,
                    47.03,
                    47.03,
                    48.58
                ],
                [
                    44.40,
                    42.44,
                    42.33,
                    45.91
                ],
                [
                    41.68,
                    44.55,
                    39.21,
                    45.50
                ],
                [
                    46.28,
                    47.25,
                    45.18,
                    48.98
                ],
                [
                    46.15,
                    47.69,
                    46.15,
                    48.45
                ],
                [
                    48.30,
                    48.00,
                    47.40,
                    48.78
                ],
                [
                    47.52,
                    47.40,
                    46.94,
                    48.39
                ],
                [
                    47.01,
                    45.83,
                    44.79,
                    47.66
                ],
                [
                    45.93,
                    45.75,
                    45.75,
                    47.15
                ],
                [
                    45.94,
                    46.30,
                    45.50,
                    47.25
                ],
                [
                    46.31,
                    50.93,
                    45.73,
                    50.93
                ],
                [
                    50.42,
                    49.30,
                    48.90,
                    50.90
                ],
                [
                    49.77,
                    49.90,
                    48.97,
                    50.60
                ],
                [
                    49.30,
                    46.25,
                    45.75,
                    49.30
                ],
                [
                    46.25,
                    46.26,
                    46.04,
                    47.40
                ],
                [
                    45.80,
                    46.37,
                    45.51,
                    47.65
                ],
                [
                    46.83,
                    48.86,
                    46.53,
                    50.30
                ],
                [
                    48.96,
                    50.20,
                    48.00,
                    50.80
                ],
                [
                    51.09,
                    53.72,
                    50.28,
                    54.08
                ],
                [
                    54.15,
                    51.95,
                    51.37,
                    54.38
                ],
                [
                    51.80,
                    51.50,
                    51.30,
                    52.52
                ],
                [
                    51.25,
                    49.90,
                    49.75,
                    51.50
                ],
                [
                    50.19,
                    50.99,
                    49.89,
                    51.00
                ],
                [
                    51.63,
                    52.20,
                    51.63,
                    54.00
                ],
                [
                    52.00,
                    52.31,
                    51.71,
                    53.99
                ],
                [
                    52.00,
                    50.34,
                    50.00,
                    52.31
                ],
                [
                    48.37,
                    48.88,
                    48.37,
                    49.85
                ],
                [
                    48.50,
                    47.67,
                    46.90,
                    48.80
                ],
                [
                    48.18,
                    51.76,
                    47.51,
                    52.44
                ],
                [
                    53.24,
                    51.38,
                    51.35,
                    53.92
                ],
                [
                    51.17,
                    49.90,
                    49.70,
                    51.50
                ],
                [
                    49.91,
                    47.36,
                    47.36,
                    50.02
                ],
                [
                    47.40,
                    49.81,
                    47.36,
                    51.06
                ],
                [
                    50.50,
                    49.08,
                    48.73,
                    51.20
                ],
                [
                    49.50,
                    49.11,
                    48.16,
                    50.98
                ],
                [
                    48.60,
                    48.99,
                    47.89,
                    49.50
                ],
                [
                    48.50,
                    46.19,
                    46.01,
                    48.55
                ],
                [
                    45.81,
                    45.86,
                    45.65,
                    46.46
                ],
                [
                    46.31,
                    48.76,
                    46.00,
                    50.45
                ],
                [
                    47.86,
                    49.64,
                    46.80,
                    50.31
                ],
                [
                    49.50,
                    49.93,
                    48.51,
                    50.22
                ],
                [
                    49.93,
                    52.78,
                    49.70,
                    53.77
                ],
                [
                    52.27,
                    51.35,
                    51.02,
                    54.08
                ],
                [
                    51.31,
                    50.35,
                    49.68,
                    51.58
                ],
                [
                    50.35,
                    49.30,
                    49.00,
                    50.90
                ],
                [
                    48.61,
                    47.38,
                    46.91,
                    49.19
                ],
                [
                    47.15,
                    47.49,
                    46.97,
                    48.68
                ],
                [
                    47.69,
                    46.15,
                    45.81,
                    47.69
                ],
                [
                    46.31,
                    46.66,
                    44.90,
                    46.88
                ],
                [
                    46.52,
                    45.30,
                    45.02,
                    46.84
                ],
                [
                    45.30,
                    43.89,
                    43.85,
                    45.75
                ],
                [
                    43.08,
                    43.69,
                    42.91,
                    43.80
                ],
                [
                    44.09,
                    45.13,
                    43.86,
                    45.35
                ],
                [
                    46.76,
                    46.38,
                    45.36,
                    47.09
                ],
                [
                    46.60,
                    46.62,
                    45.62,
                    47.98
                ],
                [
                    46.62,
                    47.15,
                    45.81,
                    47.97
                ],
                [
                    47.00,
                    47.26,
                    46.82,
                    47.48
                ],
                [
                    47.27,
                    46.55,
                    46.00,
                    47.27
                ],
                [
                    46.16,
                    45.55,
                    45.30,
                    46.74
                ],
                [
                    45.41,
                    45.29,
                    45.12,
                    46.38
                ],
                [
                    45.29,
                    45.51,
                    44.90,
                    46.48
                ],
                [
                    45.00,
                    44.71,
                    44.57,
                    45.35
                ],
                [
                    44.60,
                    46.05,
                    44.56,
                    46.71
                ],
                [
                    46.29,
                    46.03,
                    45.16,
                    46.29
                ],
                [
                    45.80,
                    45.22,
                    44.95,
                    45.96
                ],
                [
                    45.30,
                    45.14,
                    44.63,
                    45.50
                ],
                [
                    45.13,
                    45.60,
                    44.81,
                    45.99
                ],
                [
                    45.71,
                    47.87,
                    45.58,
                    48.00
                ],
                [
                    47.00,
                    47.70,
                    46.60,
                    48.50
                ],
                [
                    46.90,
                    46.72,
                    46.40,
                    47.50
                ],
                [
                    46.84,
                    46.39,
                    46.33,
                    47.50
                ],
                [
                    46.40,
                    46.49,
                    46.00,
                    46.74
                ],
                [
                    46.60,
                    45.03,
                    44.70,
                    46.60
                ],
                [
                    44.77,
                    45.17,
                    44.55,
                    45.99
                ],
                [
                    45.19,
                    45.37,
                    44.88,
                    45.44
                ],
                [
                    45.37,
                    46.16,
                    45.29,
                    46.23
                ],
                [
                    46.65,
                    45.44,
                    45.16,
                    46.70
                ],
                [
                    45.38,
                    44.81,
                    44.50,
                    45.40
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "symbol": "diamond",
            "symbolSize": 16,
            "data": [
                [
                    "2025-06-05",
                    50.31
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002929 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_e92cd416895f48cd8ea2e218e3e8acc9.setOption(option_e92cd416895f48cd8ea2e218e3e8acc9);
            window.addEventListener('resize', function(){
                chart_e92cd416895f48cd8ea2e218e3e8acc9.resize();
            })
    </script>
</body>
</html>
