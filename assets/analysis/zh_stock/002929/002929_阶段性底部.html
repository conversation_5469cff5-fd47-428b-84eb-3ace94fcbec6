<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="2d10d9a13784479eb73902df3bf43521" class="chart-container" style="width:100%; height:500px; "></div>
    <script>
        var chart_2d10d9a13784479eb73902df3bf43521 = echarts.init(
            document.getElementById('2d10d9a13784479eb73902df3bf43521'), 'white', {renderer: 'canvas'});
        var option_2d10d9a13784479eb73902df3bf43521 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    33.4,
                    32.12,
                    31.66,
                    33.5
                ],
                [
                    32.99,
                    33.34,
                    32.91,
                    33.83
                ],
                [
                    32.97,
                    34.63,
                    32.78,
                    35.13
                ],
                [
                    35.17,
                    35.47,
                    34.76,
                    36.21
                ],
                [
                    39.01,
                    39.02,
                    39.01,
                    39.02
                ],
                [
                    40.98,
                    42.92,
                    40.51,
                    42.92
                ],
                [
                    47.21,
                    47.21,
                    47.21,
                    47.21
                ],
                [
                    51.93,
                    50.2,
                    46.52,
                    51.93
                ],
                [
                    48.41,
                    55.22,
                    45.58,
                    55.22
                ],
                [
                    53.0,
                    51.65,
                    50.02,
                    54.9
                ],
                [
                    50.97,
                    51.44,
                    50.9,
                    54.55
                ],
                [
                    53.02,
                    56.58,
                    52.1,
                    56.58
                ],
                [
                    58.81,
                    62.24,
                    58.8,
                    62.24
                ],
                [
                    68.46,
                    68.46,
                    68.46,
                    68.46
                ],
                [
                    74.0,
                    70.95,
                    68.86,
                    75.31
                ],
                [
                    64.11,
                    64.99,
                    63.86,
                    69.0
                ],
                [
                    64.84,
                    63.48,
                    61.59,
                    65.5
                ],
                [
                    63.0,
                    60.99,
                    59.4,
                    64.58
                ],
                [
                    59.5,
                    56.3,
                    56.01,
                    60.0
                ],
                [
                    57.5,
                    57.99,
                    56.0,
                    59.1
                ],
                [
                    55.1,
                    57.78,
                    55.1,
                    58.3
                ],
                [
                    60.2,
                    61.52,
                    58.46,
                    62.9
                ],
                [
                    63.72,
                    62.0,
                    60.48,
                    65.0
                ],
                [
                    60.1,
                    62.49,
                    59.38,
                    64.93
                ],
                [
                    60.8,
                    58.62,
                    56.6,
                    61.0
                ],
                [
                    59.02,
                    62.06,
                    58.26,
                    63.23
                ],
                [
                    62.06,
                    63.1,
                    61.9,
                    68.11
                ],
                [
                    62.5,
                    61.57,
                    59.82,
                    64.99
                ],
                [
                    61.0,
                    60.31,
                    58.3,
                    61.4
                ],
                [
                    60.19,
                    62.26,
                    58.96,
                    62.75
                ],
                [
                    66.9,
                    65.26,
                    64.02,
                    68.49
                ],
                [
                    65.5,
                    63.82,
                    63.28,
                    67.0
                ],
                [
                    62.94,
                    60.01,
                    60.01,
                    63.8
                ],
                [
                    59.48,
                    58.6,
                    57.0,
                    60.35
                ],
                [
                    58.38,
                    56.33,
                    54.83,
                    58.98
                ],
                [
                    56.3,
                    52.55,
                    52.02,
                    56.76
                ],
                [
                    52.56,
                    52.17,
                    52.17,
                    53.7
                ],
                [
                    52.17,
                    51.15,
                    50.99,
                    53.33
                ],
                [
                    51.0,
                    51.21,
                    51.0,
                    52.7
                ],
                [
                    50.31,
                    54.04,
                    49.6,
                    55.05
                ],
                [
                    54.0,
                    52.88,
                    51.99,
                    54.18
                ],
                [
                    52.62,
                    53.09,
                    52.62,
                    54.66
                ],
                [
                    52.07,
                    52.26,
                    51.65,
                    54.13
                ],
                [
                    47.04,
                    47.03,
                    47.03,
                    48.58
                ],
                [
                    44.4,
                    42.44,
                    42.33,
                    45.91
                ],
                [
                    41.68,
                    44.55,
                    39.21,
                    45.5
                ],
                [
                    46.28,
                    47.25,
                    45.18,
                    48.98
                ],
                [
                    46.15,
                    47.69,
                    46.15,
                    48.45
                ],
                [
                    48.3,
                    48.0,
                    47.4,
                    48.78
                ],
                [
                    47.52,
                    47.4,
                    46.94,
                    48.39
                ],
                [
                    47.01,
                    45.83,
                    44.79,
                    47.66
                ],
                [
                    45.93,
                    45.75,
                    45.75,
                    47.15
                ],
                [
                    45.94,
                    46.3,
                    45.5,
                    47.25
                ],
                [
                    46.31,
                    50.93,
                    45.73,
                    50.93
                ],
                [
                    50.42,
                    49.3,
                    48.9,
                    50.9
                ],
                [
                    49.77,
                    49.9,
                    48.97,
                    50.6
                ],
                [
                    49.3,
                    46.25,
                    45.75,
                    49.3
                ],
                [
                    46.25,
                    46.26,
                    46.04,
                    47.4
                ],
                [
                    45.8,
                    46.37,
                    45.51,
                    47.65
                ],
                [
                    46.83,
                    48.86,
                    46.53,
                    50.3
                ],
                [
                    48.96,
                    50.2,
                    48.0,
                    50.8
                ],
                [
                    51.09,
                    53.72,
                    50.28,
                    54.08
                ],
                [
                    54.15,
                    51.95,
                    51.37,
                    54.38
                ],
                [
                    51.8,
                    51.5,
                    51.3,
                    52.52
                ],
                [
                    51.25,
                    49.9,
                    49.75,
                    51.5
                ],
                [
                    50.19,
                    50.99,
                    49.89,
                    51.0
                ],
                [
                    51.63,
                    52.2,
                    51.63,
                    54.0
                ],
                [
                    52.0,
                    52.31,
                    51.71,
                    53.99
                ],
                [
                    52.0,
                    50.34,
                    50.0,
                    52.31
                ],
                [
                    48.37,
                    48.88,
                    48.37,
                    49.85
                ],
                [
                    48.5,
                    47.67,
                    46.9,
                    48.8
                ],
                [
                    48.18,
                    51.76,
                    47.51,
                    52.44
                ],
                [
                    53.24,
                    51.38,
                    51.35,
                    53.92
                ],
                [
                    51.17,
                    49.9,
                    49.7,
                    51.5
                ],
                [
                    49.91,
                    47.36,
                    47.36,
                    50.02
                ],
                [
                    47.4,
                    49.81,
                    47.36,
                    51.06
                ],
                [
                    50.5,
                    49.08,
                    48.73,
                    51.2
                ],
                [
                    49.5,
                    49.11,
                    48.16,
                    50.98
                ],
                [
                    48.6,
                    48.99,
                    47.89,
                    49.5
                ],
                [
                    48.5,
                    46.19,
                    46.01,
                    48.55
                ],
                [
                    45.81,
                    45.86,
                    45.65,
                    46.46
                ],
                [
                    46.31,
                    48.76,
                    46.0,
                    50.45
                ],
                [
                    47.86,
                    49.64,
                    46.8,
                    50.31
                ],
                [
                    49.5,
                    49.93,
                    48.51,
                    50.22
                ],
                [
                    49.93,
                    52.78,
                    49.7,
                    53.77
                ],
                [
                    52.27,
                    51.35,
                    51.02,
                    54.08
                ],
                [
                    51.31,
                    50.35,
                    49.68,
                    51.58
                ],
                [
                    50.35,
                    49.3,
                    49.0,
                    50.9
                ],
                [
                    48.61,
                    47.38,
                    46.91,
                    49.19
                ],
                [
                    47.15,
                    47.49,
                    46.97,
                    48.68
                ],
                [
                    47.69,
                    46.15,
                    45.81,
                    47.69
                ],
                [
                    46.31,
                    46.66,
                    44.9,
                    46.88
                ],
                [
                    46.52,
                    45.3,
                    45.02,
                    46.84
                ],
                [
                    45.3,
                    43.89,
                    43.85,
                    45.75
                ],
                [
                    43.08,
                    43.69,
                    42.91,
                    43.8
                ],
                [
                    44.09,
                    45.13,
                    43.86,
                    45.35
                ],
                [
                    46.76,
                    46.38,
                    45.36,
                    47.09
                ],
                [
                    46.6,
                    46.62,
                    45.62,
                    47.98
                ],
                [
                    46.62,
                    47.15,
                    45.81,
                    47.97
                ],
                [
                    47.0,
                    47.26,
                    46.82,
                    47.48
                ],
                [
                    47.27,
                    46.55,
                    46.0,
                    47.27
                ],
                [
                    46.16,
                    45.55,
                    45.3,
                    46.74
                ],
                [
                    45.41,
                    45.29,
                    45.12,
                    46.38
                ],
                [
                    45.29,
                    45.51,
                    44.9,
                    46.48
                ],
                [
                    45.0,
                    44.71,
                    44.57,
                    45.35
                ],
                [
                    44.6,
                    46.05,
                    44.56,
                    46.71
                ],
                [
                    46.29,
                    46.03,
                    45.16,
                    46.29
                ],
                [
                    45.8,
                    45.22,
                    44.95,
                    45.96
                ],
                [
                    45.3,
                    45.14,
                    44.63,
                    45.5
                ],
                [
                    45.13,
                    45.6,
                    44.81,
                    45.99
                ],
                [
                    45.71,
                    47.87,
                    45.58,
                    48.0
                ],
                [
                    47.0,
                    47.7,
                    46.6,
                    48.5
                ],
                [
                    46.9,
                    46.72,
                    46.4,
                    47.5
                ],
                [
                    46.84,
                    46.39,
                    46.33,
                    47.5
                ],
                [
                    46.4,
                    46.49,
                    46.0,
                    46.74
                ],
                [
                    46.6,
                    45.03,
                    44.7,
                    46.6
                ],
                [
                    44.77,
                    45.17,
                    44.55,
                    45.99
                ],
                [
                    45.19,
                    45.37,
                    44.88,
                    45.44
                ],
                [
                    45.37,
                    46.16,
                    45.29,
                    46.23
                ],
                [
                    46.65,
                    45.44,
                    45.16,
                    46.7
                ],
                [
                    45.38,
                    44.81,
                    44.5,
                    45.4
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u9636\u6bb5\u6027\u5e95\u90e8",
            "symbol": "triangle",
            "symbolSize": 20,
            "data": [
                [
                    "2025-04-03",
                    50.62
                ],
                [
                    "2025-04-07",
                    46.09
                ],
                [
                    "2025-04-08",
                    41.48
                ],
                [
                    "2025-04-09",
                    38.43
                ],
                [
                    "2025-04-24",
                    44.84
                ],
                [
                    "2025-04-25",
                    45.12
                ],
                [
                    "2025-04-28",
                    44.6
                ],
                [
                    "2025-06-03",
                    44.74
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#FF6B35",
                "borderColor": "#CC5529",
                "borderWidth": 2,
                "opacity": 0.9
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf",
                "\u9636\u6bb5\u6027\u5e95\u90e8"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002929 \u9636\u6bb5\u6027\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_2d10d9a13784479eb73902df3bf43521.setOption(option_2d10d9a13784479eb73902df3bf43521);
            window.addEventListener('resize', function(){
                chart_2d10d9a13784479eb73902df3bf43521.resize();
            })
    </script>
</body>
</html>
