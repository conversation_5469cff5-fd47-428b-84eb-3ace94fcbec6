<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="1547d1f6aad74ae58ae389914d49a593" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_1547d1f6aad74ae58ae389914d49a593 = echarts.init(
            document.getElementById('1547d1f6aad74ae58ae389914d49a593'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_1547d1f6aad74ae58ae389914d49a593 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    10.46,
                    10.51,
                    10.4,
                    10.88
                ],
                [
                    10.56,
                    11.25,
                    10.51,
                    11.56
                ],
                [
                    11.15,
                    11.84,
                    11.1,
                    11.94
                ],
                [
                    11.94,
                    13.02,
                    11.9,
                    13.02
                ],
                [
                    12.68,
                    12.8,
                    12.4,
                    12.94
                ],
                [
                    12.7,
                    12.04,
                    11.83,
                    12.8
                ],
                [
                    11.98,
                    12.22,
                    11.94,
                    12.27
                ],
                [
                    12.19,
                    12.03,
                    12.0,
                    12.36
                ],
                [
                    11.99,
                    12.23,
                    11.96,
                    12.63
                ],
                [
                    12.2,
                    12.9,
                    12.2,
                    13.07
                ],
                [
                    12.86,
                    12.74,
                    12.67,
                    13.43
                ],
                [
                    12.61,
                    13.78,
                    12.59,
                    13.94
                ],
                [
                    13.57,
                    13.35,
                    13.26,
                    13.74
                ],
                [
                    13.35,
                    13.18,
                    12.81,
                    13.54
                ],
                [
                    13.05,
                    13.59,
                    12.85,
                    13.71
                ],
                [
                    13.4,
                    13.49,
                    13.32,
                    14.24
                ],
                [
                    13.67,
                    14.27,
                    13.32,
                    14.5
                ],
                [
                    14.13,
                    13.79,
                    13.5,
                    14.45
                ],
                [
                    13.77,
                    13.16,
                    13.06,
                    14.09
                ],
                [
                    13.18,
                    13.49,
                    13.16,
                    13.99
                ],
                [
                    13.36,
                    13.69,
                    13.16,
                    13.79
                ],
                [
                    13.78,
                    13.39,
                    13.1,
                    13.83
                ],
                [
                    13.5,
                    13.8,
                    13.38,
                    14.0
                ],
                [
                    13.68,
                    14.14,
                    13.14,
                    14.4
                ],
                [
                    14.06,
                    14.16,
                    13.93,
                    14.48
                ],
                [
                    13.87,
                    14.0,
                    13.55,
                    14.1
                ],
                [
                    13.96,
                    13.92,
                    13.76,
                    14.28
                ],
                [
                    13.92,
                    13.2,
                    13.08,
                    14.0
                ],
                [
                    13.1,
                    13.65,
                    13.1,
                    13.8
                ],
                [
                    13.9,
                    14.01,
                    13.82,
                    14.29
                ],
                [
                    14.01,
                    14.29,
                    13.89,
                    14.48
                ],
                [
                    14.21,
                    13.73,
                    13.65,
                    14.24
                ],
                [
                    13.73,
                    13.48,
                    13.44,
                    13.79
                ],
                [
                    13.46,
                    13.13,
                    12.95,
                    13.65
                ],
                [
                    13.2,
                    13.32,
                    12.81,
                    13.43
                ],
                [
                    13.25,
                    13.02,
                    13.0,
                    13.54
                ],
                [
                    13.01,
                    13.46,
                    12.98,
                    13.95
                ],
                [
                    13.3,
                    13.05,
                    13.03,
                    13.46
                ],
                [
                    13.04,
                    12.61,
                    12.5,
                    13.33
                ],
                [
                    12.54,
                    12.18,
                    12.02,
                    12.6
                ],
                [
                    12.23,
                    12.14,
                    11.92,
                    12.48
                ],
                [
                    12.05,
                    12.09,
                    11.98,
                    12.26
                ],
                [
                    11.88,
                    11.95,
                    11.7,
                    12.28
                ],
                [
                    11.1,
                    10.76,
                    10.76,
                    11.32
                ],
                [
                    10.44,
                    10.06,
                    9.71,
                    10.83
                ],
                [
                    9.88,
                    10.02,
                    9.2,
                    10.15
                ],
                [
                    10.38,
                    10.35,
                    10.26,
                    10.48
                ],
                [
                    10.27,
                    10.25,
                    10.19,
                    10.48
                ],
                [
                    10.41,
                    10.46,
                    10.41,
                    10.75
                ],
                [
                    10.51,
                    10.3,
                    10.26,
                    10.74
                ],
                [
                    10.3,
                    9.84,
                    9.68,
                    10.3
                ],
                [
                    9.73,
                    9.86,
                    9.73,
                    9.98
                ],
                [
                    9.9,
                    9.69,
                    9.55,
                    9.92
                ],
                [
                    9.71,
                    9.96,
                    9.64,
                    10.01
                ],
                [
                    9.98,
                    10.2,
                    9.92,
                    10.53
                ],
                [
                    10.21,
                    10.16,
                    10.08,
                    10.47
                ],
                [
                    10.2,
                    9.94,
                    9.83,
                    10.22
                ],
                [
                    9.9,
                    9.96,
                    9.89,
                    10.09
                ],
                [
                    10.0,
                    10.1,
                    9.95,
                    10.3
                ],
                [
                    10.12,
                    9.9,
                    9.9,
                    10.39
                ],
                [
                    10.15,
                    10.51,
                    10.09,
                    10.89
                ],
                [
                    10.65,
                    10.89,
                    10.5,
                    10.97
                ],
                [
                    11.1,
                    10.83,
                    10.7,
                    11.16
                ],
                [
                    10.79,
                    10.9,
                    10.72,
                    11.01
                ],
                [
                    10.89,
                    10.51,
                    10.47,
                    10.97
                ],
                [
                    10.72,
                    11.36,
                    10.72,
                    11.45
                ],
                [
                    11.36,
                    11.47,
                    11.29,
                    11.95
                ],
                [
                    11.36,
                    11.44,
                    11.16,
                    11.57
                ],
                [
                    11.45,
                    11.27,
                    11.1,
                    11.46
                ],
                [
                    11.23,
                    11.31,
                    11.18,
                    11.41
                ],
                [
                    11.31,
                    11.54,
                    11.11,
                    11.62
                ],
                [
                    11.55,
                    11.59,
                    11.35,
                    11.77
                ],
                [
                    11.7,
                    11.54,
                    11.49,
                    11.87
                ],
                [
                    11.38,
                    11.46,
                    11.34,
                    11.57
                ],
                [
                    11.46,
                    11.63,
                    11.37,
                    11.87
                ],
                [
                    11.82,
                    11.38,
                    11.33,
                    11.83
                ],
                [
                    11.38,
                    11.39,
                    11.05,
                    11.42
                ],
                [
                    11.39,
                    11.03,
                    10.98,
                    11.45
                ],
                [
                    11.03,
                    11.23,
                    11.01,
                    11.28
                ],
                [
                    11.14,
                    11.08,
                    11.03,
                    11.25
                ],
                [
                    11.0,
                    10.78,
                    10.75,
                    11.24
                ],
                [
                    10.88,
                    11.05,
                    10.86,
                    11.49
                ],
                [
                    11.07,
                    10.95,
                    10.83,
                    11.11
                ],
                [
                    11.0,
                    10.93,
                    10.83,
                    11.02
                ],
                [
                    10.95,
                    11.38,
                    10.89,
                    11.5
                ],
                [
                    11.33,
                    11.23,
                    11.14,
                    11.42
                ],
                [
                    11.5,
                    12.12,
                    11.48,
                    12.35
                ],
                [
                    11.81,
                    11.71,
                    11.69,
                    12.01
                ],
                [
                    11.91,
                    11.6,
                    11.57,
                    12.01
                ],
                [
                    11.57,
                    11.91,
                    11.4,
                    11.99
                ],
                [
                    11.88,
                    11.96,
                    11.81,
                    12.2
                ],
                [
                    11.93,
                    12.17,
                    11.85,
                    12.35
                ],
                [
                    12.17,
                    11.96,
                    11.86,
                    12.3
                ],
                [
                    12.09,
                    12.05,
                    12.02,
                    12.44
                ],
                [
                    11.88,
                    11.89,
                    11.58,
                    11.93
                ],
                [
                    11.85,
                    12.07,
                    11.78,
                    12.15
                ],
                [
                    12.06,
                    12.06,
                    11.89,
                    12.1
                ],
                [
                    12.08,
                    11.89,
                    11.88,
                    12.27
                ],
                [
                    12.04,
                    12.41,
                    11.98,
                    12.6
                ],
                [
                    13.01,
                    13.1,
                    12.64,
                    13.4
                ],
                [
                    13.01,
                    13.11,
                    12.81,
                    13.24
                ],
                [
                    13.25,
                    14.14,
                    13.1,
                    14.42
                ],
                [
                    13.92,
                    13.99,
                    13.69,
                    14.19
                ],
                [
                    14.25,
                    13.7,
                    13.55,
                    14.3
                ],
                [
                    13.75,
                    13.89,
                    13.7,
                    14.18
                ],
                [
                    13.84,
                    14.63,
                    13.76,
                    14.95
                ],
                [
                    14.4,
                    14.55,
                    14.4,
                    14.85
                ],
                [
                    14.5,
                    14.76,
                    14.49,
                    15.0
                ],
                [
                    15.28,
                    14.65,
                    14.13,
                    15.47
                ],
                [
                    14.5,
                    14.74,
                    14.36,
                    14.92
                ],
                [
                    14.85,
                    14.83,
                    14.6,
                    15.09
                ],
                [
                    14.76,
                    15.39,
                    14.4,
                    15.42
                ],
                [
                    15.61,
                    15.18,
                    15.03,
                    15.88
                ],
                [
                    15.01,
                    14.66,
                    14.53,
                    15.35
                ],
                [
                    14.66,
                    14.78,
                    14.5,
                    15.03
                ],
                [
                    14.65,
                    14.88,
                    14.44,
                    15.05
                ],
                [
                    14.86,
                    14.51,
                    14.42,
                    15.17
                ],
                [
                    14.4,
                    14.96,
                    14.35,
                    15.0
                ],
                [
                    14.95,
                    14.77,
                    14.73,
                    14.97
                ],
                [
                    14.55,
                    14.85,
                    14.43,
                    14.96
                ],
                [
                    14.85,
                    14.98,
                    14.74,
                    15.1
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -2.39
                ],
                [
                    "2025-02-05",
                    18.25
                ],
                [
                    "2025-02-06",
                    5.34
                ],
                [
                    "2025-02-07",
                    12.43
                ],
                [
                    "2025-02-10",
                    -5.71
                ],
                [
                    "2025-02-11",
                    -12.23
                ],
                [
                    "2025-02-12",
                    0.17
                ],
                [
                    "2025-02-13",
                    -9.96
                ],
                [
                    "2025-02-14",
                    4.06
                ],
                [
                    "2025-02-17",
                    15.38
                ],
                [
                    "2025-02-18",
                    5.46
                ],
                [
                    "2025-02-19",
                    18.12
                ],
                [
                    "2025-02-20",
                    -1.05
                ],
                [
                    "2025-02-21",
                    0.98
                ],
                [
                    "2025-02-24",
                    9.99
                ],
                [
                    "2025-02-25",
                    -0.62
                ],
                [
                    "2025-02-26",
                    10.55
                ],
                [
                    "2025-02-27",
                    -6.86
                ],
                [
                    "2025-02-28",
                    -13.06
                ],
                [
                    "2025-03-03",
                    -0.63
                ],
                [
                    "2025-03-04",
                    4.24
                ],
                [
                    "2025-03-05",
                    -4.46
                ],
                [
                    "2025-03-06",
                    -0.54
                ],
                [
                    "2025-03-07",
                    11.41
                ],
                [
                    "2025-03-10",
                    1.15
                ],
                [
                    "2025-03-11",
                    -2.39
                ],
                [
                    "2025-03-12",
                    3.81
                ],
                [
                    "2025-03-13",
                    -19.74
                ],
                [
                    "2025-03-14",
                    4.55
                ],
                [
                    "2025-03-17",
                    0.41
                ],
                [
                    "2025-03-18",
                    5.11
                ],
                [
                    "2025-03-19",
                    -13.49
                ],
                [
                    "2025-03-20",
                    -10.19
                ],
                [
                    "2025-03-21",
                    -11.45
                ],
                [
                    "2025-03-24",
                    0.63
                ],
                [
                    "2025-03-25",
                    -4.86
                ],
                [
                    "2025-03-26",
                    5.25
                ],
                [
                    "2025-03-27",
                    -11.25
                ],
                [
                    "2025-03-28",
                    -18.03
                ],
                [
                    "2025-03-31",
                    -11.27
                ],
                [
                    "2025-04-01",
                    -11.36
                ],
                [
                    "2025-04-02",
                    -15.75
                ],
                [
                    "2025-04-03",
                    -9.34
                ],
                [
                    "2025-04-07",
                    -17.00
                ],
                [
                    "2025-04-08",
                    -7.57
                ],
                [
                    "2025-04-09",
                    -13.39
                ],
                [
                    "2025-04-10",
                    -2.65
                ],
                [
                    "2025-04-11",
                    -10.90
                ],
                [
                    "2025-04-14",
                    -5.82
                ],
                [
                    "2025-04-15",
                    3.68
                ],
                [
                    "2025-04-16",
                    -22.67
                ],
                [
                    "2025-04-17",
                    -9.81
                ],
                [
                    "2025-04-18",
                    -26.87
                ],
                [
                    "2025-04-21",
                    0.50
                ],
                [
                    "2025-04-22",
                    13.90
                ],
                [
                    "2025-04-23",
                    -5.73
                ],
                [
                    "2025-04-24",
                    -3.23
                ],
                [
                    "2025-04-25",
                    -6.94
                ],
                [
                    "2025-04-28",
                    7.73
                ],
                [
                    "2025-04-29",
                    -3.66
                ],
                [
                    "2025-04-30",
                    9.49
                ],
                [
                    "2025-05-06",
                    -7.77
                ],
                [
                    "2025-05-07",
                    -6.91
                ],
                [
                    "2025-05-08",
                    -3.67
                ],
                [
                    "2025-05-09",
                    -11.68
                ],
                [
                    "2025-05-12",
                    10.44
                ],
                [
                    "2025-05-13",
                    -4.03
                ],
                [
                    "2025-05-14",
                    -11.70
                ],
                [
                    "2025-05-15",
                    -3.26
                ],
                [
                    "2025-05-16",
                    4.52
                ],
                [
                    "2025-05-19",
                    -3.27
                ],
                [
                    "2025-05-20",
                    -0.79
                ],
                [
                    "2025-05-21",
                    -0.19
                ],
                [
                    "2025-05-22",
                    -17.12
                ],
                [
                    "2025-05-23",
                    -4.03
                ],
                [
                    "2025-05-26",
                    -1.60
                ],
                [
                    "2025-05-27",
                    -3.67
                ],
                [
                    "2025-05-28",
                    -7.58
                ],
                [
                    "2025-05-29",
                    -1.21
                ],
                [
                    "2025-05-30",
                    -13.28
                ],
                [
                    "2025-06-03",
                    -15.41
                ],
                [
                    "2025-06-04",
                    7.35
                ],
                [
                    "2025-06-05",
                    -13.44
                ],
                [
                    "2025-06-06",
                    -10.37
                ],
                [
                    "2025-06-09",
                    -3.94
                ],
                [
                    "2025-06-10",
                    7.43
                ],
                [
                    "2025-06-11",
                    19.44
                ],
                [
                    "2025-06-12",
                    -17.08
                ],
                [
                    "2025-06-13",
                    3.22
                ],
                [
                    "2025-06-16",
                    0.62
                ],
                [
                    "2025-06-17",
                    12.82
                ],
                [
                    "2025-06-18",
                    5.75
                ],
                [
                    "2025-06-19",
                    -9.86
                ],
                [
                    "2025-06-20",
                    -4.18
                ],
                [
                    "2025-06-23",
                    -12.01
                ],
                [
                    "2025-06-24",
                    -4.65
                ],
                [
                    "2025-06-25",
                    -4.24
                ],
                [
                    "2025-06-26",
                    0.68
                ],
                [
                    "2025-06-27",
                    3.08
                ],
                [
                    "2025-06-30",
                    2.24
                ],
                [
                    "2025-07-01",
                    6.89
                ],
                [
                    "2025-07-02",
                    14.56
                ],
                [
                    "2025-07-03",
                    -9.60
                ],
                [
                    "2025-07-04",
                    -13.09
                ],
                [
                    "2025-07-07",
                    -5.36
                ],
                [
                    "2025-07-08",
                    6.99
                ],
                [
                    "2025-07-09",
                    -4.26
                ],
                [
                    "2025-07-10",
                    4.56
                ],
                [
                    "2025-07-11",
                    -8.09
                ],
                [
                    "2025-07-14",
                    -1.54
                ],
                [
                    "2025-07-15",
                    0.90
                ],
                [
                    "2025-07-16",
                    9.69
                ],
                [
                    "2025-07-17",
                    -11.32
                ],
                [
                    "2025-07-18",
                    -24.01
                ],
                [
                    "2025-07-21",
                    -0.71
                ],
                [
                    "2025-07-22",
                    -3.68
                ],
                [
                    "2025-07-23",
                    -20.23
                ],
                [
                    "2025-07-24",
                    -8.53
                ],
                [
                    "2025-07-25",
                    -18.53
                ],
                [
                    "2025-07-28",
                    -7.48
                ],
                [
                    "2025-07-29",
                    -1.35
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    0.65
                ],
                [
                    "2025-02-05",
                    -10.09
                ],
                [
                    "2025-02-06",
                    -2.69
                ],
                [
                    "2025-02-07",
                    -5.90
                ],
                [
                    "2025-02-10",
                    1.43
                ],
                [
                    "2025-02-11",
                    2.95
                ],
                [
                    "2025-02-12",
                    -1.82
                ],
                [
                    "2025-02-13",
                    0.33
                ],
                [
                    "2025-02-14",
                    3.36
                ],
                [
                    "2025-02-17",
                    -4.13
                ],
                [
                    "2025-02-18",
                    0.57
                ],
                [
                    "2025-02-19",
                    -4.44
                ],
                [
                    "2025-02-20",
                    -0.02
                ],
                [
                    "2025-02-21",
                    -2.52
                ],
                [
                    "2025-02-24",
                    -4.25
                ],
                [
                    "2025-02-25",
                    -0.55
                ],
                [
                    "2025-02-26",
                    -5.88
                ],
                [
                    "2025-02-27",
                    2.44
                ],
                [
                    "2025-02-28",
                    6.85
                ],
                [
                    "2025-03-03",
                    -0.43
                ],
                [
                    "2025-03-04",
                    0.26
                ],
                [
                    "2025-03-05",
                    1.31
                ],
                [
                    "2025-03-06",
                    -1.26
                ],
                [
                    "2025-03-07",
                    -2.51
                ],
                [
                    "2025-03-10",
                    1.48
                ],
                [
                    "2025-03-11",
                    2.69
                ],
                [
                    "2025-03-12",
                    0.47
                ],
                [
                    "2025-03-13",
                    4.07
                ],
                [
                    "2025-03-14",
                    -3.52
                ],
                [
                    "2025-03-17",
                    -1.20
                ],
                [
                    "2025-03-18",
                    -3.93
                ],
                [
                    "2025-03-19",
                    8.41
                ],
                [
                    "2025-03-20",
                    5.65
                ],
                [
                    "2025-03-21",
                    3.28
                ],
                [
                    "2025-03-24",
                    -0.94
                ],
                [
                    "2025-03-25",
                    2.37
                ],
                [
                    "2025-03-26",
                    -5.09
                ],
                [
                    "2025-03-27",
                    5.75
                ],
                [
                    "2025-03-28",
                    4.49
                ],
                [
                    "2025-03-31",
                    -0.73
                ],
                [
                    "2025-04-01",
                    -0.08
                ],
                [
                    "2025-04-02",
                    3.75
                ],
                [
                    "2025-04-03",
                    2.24
                ],
                [
                    "2025-04-07",
                    2.47
                ],
                [
                    "2025-04-08",
                    -1.59
                ],
                [
                    "2025-04-09",
                    2.86
                ],
                [
                    "2025-04-10",
                    -1.06
                ],
                [
                    "2025-04-11",
                    -3.40
                ],
                [
                    "2025-04-14",
                    0.17
                ],
                [
                    "2025-04-15",
                    0.72
                ],
                [
                    "2025-04-16",
                    5.62
                ],
                [
                    "2025-04-17",
                    -2.79
                ],
                [
                    "2025-04-18",
                    9.08
                ],
                [
                    "2025-04-21",
                    -1.40
                ],
                [
                    "2025-04-22",
                    -2.70
                ],
                [
                    "2025-04-23",
                    1.32
                ],
                [
                    "2025-04-24",
                    -0.60
                ],
                [
                    "2025-04-25",
                    0.00
                ],
                [
                    "2025-04-28",
                    1.69
                ],
                [
                    "2025-04-29",
                    5.20
                ],
                [
                    "2025-04-30",
                    -6.94
                ],
                [
                    "2025-05-06",
                    0.54
                ],
                [
                    "2025-05-07",
                    2.34
                ],
                [
                    "2025-05-08",
                    1.24
                ],
                [
                    "2025-05-09",
                    0.56
                ],
                [
                    "2025-05-12",
                    -3.89
                ],
                [
                    "2025-05-13",
                    0.47
                ],
                [
                    "2025-05-14",
                    0.57
                ],
                [
                    "2025-05-15",
                    11.34
                ],
                [
                    "2025-05-16",
                    8.16
                ],
                [
                    "2025-05-19",
                    1.62
                ],
                [
                    "2025-05-20",
                    -1.02
                ],
                [
                    "2025-05-21",
                    0.61
                ],
                [
                    "2025-05-22",
                    -2.19
                ],
                [
                    "2025-05-23",
                    -4.33
                ],
                [
                    "2025-05-26",
                    -4.85
                ],
                [
                    "2025-05-27",
                    -0.61
                ],
                [
                    "2025-05-28",
                    3.13
                ],
                [
                    "2025-05-29",
                    -0.63
                ],
                [
                    "2025-05-30",
                    5.48
                ],
                [
                    "2025-06-03",
                    4.99
                ],
                [
                    "2025-06-04",
                    -1.83
                ],
                [
                    "2025-06-05",
                    7.35
                ],
                [
                    "2025-06-06",
                    4.67
                ],
                [
                    "2025-06-09",
                    0.62
                ],
                [
                    "2025-06-10",
                    -1.70
                ],
                [
                    "2025-06-11",
                    -8.77
                ],
                [
                    "2025-06-12",
                    8.08
                ],
                [
                    "2025-06-13",
                    5.62
                ],
                [
                    "2025-06-16",
                    0.62
                ],
                [
                    "2025-06-17",
                    -2.77
                ],
                [
                    "2025-06-18",
                    1.69
                ],
                [
                    "2025-06-19",
                    5.72
                ],
                [
                    "2025-06-20",
                    2.29
                ],
                [
                    "2025-06-23",
                    1.59
                ],
                [
                    "2025-06-24",
                    2.86
                ],
                [
                    "2025-06-25",
                    3.93
                ],
                [
                    "2025-06-26",
                    1.95
                ],
                [
                    "2025-06-27",
                    -1.09
                ],
                [
                    "2025-06-30",
                    -3.93
                ],
                [
                    "2025-07-01",
                    -2.23
                ],
                [
                    "2025-07-02",
                    -3.31
                ],
                [
                    "2025-07-03",
                    5.33
                ],
                [
                    "2025-07-04",
                    4.78
                ],
                [
                    "2025-07-07",
                    4.80
                ],
                [
                    "2025-07-08",
                    -4.40
                ],
                [
                    "2025-07-09",
                    -0.26
                ],
                [
                    "2025-07-10",
                    -7.03
                ],
                [
                    "2025-07-11",
                    5.05
                ],
                [
                    "2025-07-14",
                    -0.80
                ],
                [
                    "2025-07-15",
                    -1.07
                ],
                [
                    "2025-07-16",
                    -5.26
                ],
                [
                    "2025-07-17",
                    2.76
                ],
                [
                    "2025-07-18",
                    5.35
                ],
                [
                    "2025-07-21",
                    -3.20
                ],
                [
                    "2025-07-22",
                    2.15
                ],
                [
                    "2025-07-23",
                    1.44
                ],
                [
                    "2025-07-24",
                    -1.90
                ],
                [
                    "2025-07-25",
                    8.14
                ],
                [
                    "2025-07-28",
                    0.68
                ],
                [
                    "2025-07-29",
                    3.30
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    1.74
                ],
                [
                    "2025-02-05",
                    -8.16
                ],
                [
                    "2025-02-06",
                    -2.65
                ],
                [
                    "2025-02-07",
                    -6.54
                ],
                [
                    "2025-02-10",
                    4.27
                ],
                [
                    "2025-02-11",
                    9.28
                ],
                [
                    "2025-02-12",
                    1.65
                ],
                [
                    "2025-02-13",
                    9.63
                ],
                [
                    "2025-02-14",
                    -7.42
                ],
                [
                    "2025-02-17",
                    -11.25
                ],
                [
                    "2025-02-18",
                    -6.03
                ],
                [
                    "2025-02-19",
                    -13.69
                ],
                [
                    "2025-02-20",
                    1.07
                ],
                [
                    "2025-02-21",
                    1.54
                ],
                [
                    "2025-02-24",
                    -5.74
                ],
                [
                    "2025-02-25",
                    1.16
                ],
                [
                    "2025-02-26",
                    -4.67
                ],
                [
                    "2025-02-27",
                    4.42
                ],
                [
                    "2025-02-28",
                    6.20
                ],
                [
                    "2025-03-03",
                    1.07
                ],
                [
                    "2025-03-04",
                    -4.50
                ],
                [
                    "2025-03-05",
                    3.15
                ],
                [
                    "2025-03-06",
                    1.81
                ],
                [
                    "2025-03-07",
                    -8.91
                ],
                [
                    "2025-03-10",
                    -2.63
                ],
                [
                    "2025-03-11",
                    -0.30
                ],
                [
                    "2025-03-12",
                    -4.28
                ],
                [
                    "2025-03-13",
                    15.67
                ],
                [
                    "2025-03-14",
                    -1.03
                ],
                [
                    "2025-03-17",
                    0.79
                ],
                [
                    "2025-03-18",
                    -1.19
                ],
                [
                    "2025-03-19",
                    5.08
                ],
                [
                    "2025-03-20",
                    4.53
                ],
                [
                    "2025-03-21",
                    8.17
                ],
                [
                    "2025-03-24",
                    0.30
                ],
                [
                    "2025-03-25",
                    2.49
                ],
                [
                    "2025-03-26",
                    -0.16
                ],
                [
                    "2025-03-27",
                    5.50
                ],
                [
                    "2025-03-28",
                    13.54
                ],
                [
                    "2025-03-31",
                    12.00
                ],
                [
                    "2025-04-01",
                    11.44
                ],
                [
                    "2025-04-02",
                    12.00
                ],
                [
                    "2025-04-03",
                    7.09
                ],
                [
                    "2025-04-07",
                    14.53
                ],
                [
                    "2025-04-08",
                    9.16
                ],
                [
                    "2025-04-09",
                    10.53
                ],
                [
                    "2025-04-10",
                    3.70
                ],
                [
                    "2025-04-11",
                    14.30
                ],
                [
                    "2025-04-14",
                    5.64
                ],
                [
                    "2025-04-15",
                    -4.40
                ],
                [
                    "2025-04-16",
                    17.05
                ],
                [
                    "2025-04-17",
                    12.60
                ],
                [
                    "2025-04-18",
                    17.79
                ],
                [
                    "2025-04-21",
                    0.90
                ],
                [
                    "2025-04-22",
                    -11.19
                ],
                [
                    "2025-04-23",
                    4.41
                ],
                [
                    "2025-04-24",
                    3.82
                ],
                [
                    "2025-04-25",
                    6.94
                ],
                [
                    "2025-04-28",
                    -9.42
                ],
                [
                    "2025-04-29",
                    -1.54
                ],
                [
                    "2025-04-30",
                    -2.56
                ],
                [
                    "2025-05-06",
                    7.23
                ],
                [
                    "2025-05-07",
                    4.57
                ],
                [
                    "2025-05-08",
                    2.43
                ],
                [
                    "2025-05-09",
                    11.12
                ],
                [
                    "2025-05-12",
                    -6.55
                ],
                [
                    "2025-05-13",
                    3.55
                ],
                [
                    "2025-05-14",
                    11.13
                ],
                [
                    "2025-05-15",
                    -8.09
                ],
                [
                    "2025-05-16",
                    -12.69
                ],
                [
                    "2025-05-19",
                    1.65
                ],
                [
                    "2025-05-20",
                    1.81
                ],
                [
                    "2025-05-21",
                    -0.42
                ],
                [
                    "2025-05-22",
                    19.32
                ],
                [
                    "2025-05-23",
                    8.36
                ],
                [
                    "2025-05-26",
                    6.45
                ],
                [
                    "2025-05-27",
                    4.28
                ],
                [
                    "2025-05-28",
                    4.45
                ],
                [
                    "2025-05-29",
                    1.84
                ],
                [
                    "2025-05-30",
                    7.80
                ],
                [
                    "2025-06-03",
                    10.42
                ],
                [
                    "2025-06-04",
                    -5.53
                ],
                [
                    "2025-06-05",
                    6.10
                ],
                [
                    "2025-06-06",
                    5.70
                ],
                [
                    "2025-06-09",
                    3.32
                ],
                [
                    "2025-06-10",
                    -5.73
                ],
                [
                    "2025-06-11",
                    -10.67
                ],
                [
                    "2025-06-12",
                    9.00
                ],
                [
                    "2025-06-13",
                    -8.84
                ],
                [
                    "2025-06-16",
                    -1.24
                ],
                [
                    "2025-06-17",
                    -10.05
                ],
                [
                    "2025-06-18",
                    -7.44
                ],
                [
                    "2025-06-19",
                    4.15
                ],
                [
                    "2025-06-20",
                    1.89
                ],
                [
                    "2025-06-23",
                    10.42
                ],
                [
                    "2025-06-24",
                    1.79
                ],
                [
                    "2025-06-25",
                    0.31
                ],
                [
                    "2025-06-26",
                    -2.63
                ],
                [
                    "2025-06-27",
                    -1.99
                ],
                [
                    "2025-06-30",
                    1.70
                ],
                [
                    "2025-07-01",
                    -4.66
                ],
                [
                    "2025-07-02",
                    -11.25
                ],
                [
                    "2025-07-03",
                    4.28
                ],
                [
                    "2025-07-04",
                    8.31
                ],
                [
                    "2025-07-07",
                    0.55
                ],
                [
                    "2025-07-08",
                    -2.59
                ],
                [
                    "2025-07-09",
                    4.52
                ],
                [
                    "2025-07-10",
                    2.47
                ],
                [
                    "2025-07-11",
                    3.04
                ],
                [
                    "2025-07-14",
                    2.34
                ],
                [
                    "2025-07-15",
                    0.16
                ],
                [
                    "2025-07-16",
                    -4.43
                ],
                [
                    "2025-07-17",
                    8.55
                ],
                [
                    "2025-07-18",
                    18.66
                ],
                [
                    "2025-07-21",
                    3.91
                ],
                [
                    "2025-07-22",
                    1.53
                ],
                [
                    "2025-07-23",
                    18.79
                ],
                [
                    "2025-07-24",
                    10.43
                ],
                [
                    "2025-07-25",
                    10.38
                ],
                [
                    "2025-07-28",
                    6.79
                ],
                [
                    "2025-07-29",
                    -1.95
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    -5.71
                ],
                [
                    "2025-02-11",
                    -12.23
                ],
                [
                    "2025-02-18",
                    5.46
                ],
                [
                    "2025-02-19",
                    18.12
                ],
                [
                    "2025-02-20",
                    -1.05
                ],
                [
                    "2025-02-21",
                    0.98
                ],
                [
                    "2025-02-24",
                    9.99
                ],
                [
                    "2025-02-25",
                    -0.62
                ],
                [
                    "2025-02-26",
                    10.55
                ],
                [
                    "2025-02-27",
                    -6.86
                ],
                [
                    "2025-03-07",
                    11.41
                ],
                [
                    "2025-03-10",
                    1.15
                ],
                [
                    "2025-03-11",
                    -2.39
                ],
                [
                    "2025-03-12",
                    3.81
                ],
                [
                    "2025-04-28",
                    7.73
                ],
                [
                    "2025-04-30",
                    9.49
                ],
                [
                    "2025-06-13",
                    3.22
                ],
                [
                    "2025-06-16",
                    0.62
                ],
                [
                    "2025-06-17",
                    12.82
                ],
                [
                    "2025-06-18",
                    5.75
                ],
                [
                    "2025-06-19",
                    -9.86
                ],
                [
                    "2025-06-20",
                    -4.18
                ],
                [
                    "2025-07-01",
                    6.89
                ],
                [
                    "2025-07-02",
                    14.56
                ],
                [
                    "2025-07-03",
                    -9.60
                ],
                [
                    "2025-07-04",
                    -13.09
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-11",
                    2.69
                ],
                [
                    "2025-03-12",
                    0.47
                ],
                [
                    "2025-03-13",
                    4.07
                ],
                [
                    "2025-05-07",
                    2.34
                ],
                [
                    "2025-05-16",
                    8.16
                ],
                [
                    "2025-05-19",
                    1.62
                ],
                [
                    "2025-05-20",
                    -1.02
                ],
                [
                    "2025-05-21",
                    0.61
                ],
                [
                    "2025-06-11",
                    -8.77
                ],
                [
                    "2025-06-13",
                    5.62
                ],
                [
                    "2025-06-16",
                    0.62
                ],
                [
                    "2025-06-17",
                    -2.77
                ],
                [
                    "2025-06-18",
                    1.69
                ],
                [
                    "2025-06-19",
                    5.72
                ],
                [
                    "2025-06-20",
                    2.29
                ],
                [
                    "2025-06-23",
                    1.59
                ],
                [
                    "2025-06-30",
                    -3.93
                ],
                [
                    "2025-07-04",
                    4.78
                ],
                [
                    "2025-07-07",
                    4.80
                ],
                [
                    "2025-07-08",
                    -4.40
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-12",
                    1.65
                ],
                [
                    "2025-02-13",
                    9.63
                ],
                [
                    "2025-02-14",
                    -7.42
                ],
                [
                    "2025-02-17",
                    -11.25
                ],
                [
                    "2025-02-28",
                    6.20
                ],
                [
                    "2025-03-03",
                    1.07
                ],
                [
                    "2025-03-04",
                    -4.50
                ],
                [
                    "2025-03-05",
                    3.15
                ],
                [
                    "2025-03-06",
                    1.81
                ],
                [
                    "2025-03-14",
                    -1.03
                ],
                [
                    "2025-03-17",
                    0.79
                ],
                [
                    "2025-03-18",
                    -1.19
                ],
                [
                    "2025-03-19",
                    5.08
                ],
                [
                    "2025-03-20",
                    4.53
                ],
                [
                    "2025-03-21",
                    8.17
                ],
                [
                    "2025-03-24",
                    0.30
                ],
                [
                    "2025-03-25",
                    2.49
                ],
                [
                    "2025-03-26",
                    -0.16
                ],
                [
                    "2025-03-27",
                    5.50
                ],
                [
                    "2025-03-28",
                    13.54
                ],
                [
                    "2025-03-31",
                    12.00
                ],
                [
                    "2025-04-01",
                    11.44
                ],
                [
                    "2025-04-02",
                    12.00
                ],
                [
                    "2025-04-03",
                    7.09
                ],
                [
                    "2025-04-07",
                    14.53
                ],
                [
                    "2025-04-08",
                    9.16
                ],
                [
                    "2025-04-09",
                    10.53
                ],
                [
                    "2025-04-10",
                    3.70
                ],
                [
                    "2025-04-11",
                    14.30
                ],
                [
                    "2025-04-14",
                    5.64
                ],
                [
                    "2025-04-15",
                    -4.40
                ],
                [
                    "2025-04-16",
                    17.05
                ],
                [
                    "2025-04-17",
                    12.60
                ],
                [
                    "2025-04-18",
                    17.79
                ],
                [
                    "2025-04-21",
                    0.90
                ],
                [
                    "2025-04-22",
                    -11.19
                ],
                [
                    "2025-04-23",
                    4.41
                ],
                [
                    "2025-04-24",
                    3.82
                ],
                [
                    "2025-04-25",
                    6.94
                ],
                [
                    "2025-04-29",
                    -1.54
                ],
                [
                    "2025-05-06",
                    7.23
                ],
                [
                    "2025-05-08",
                    2.43
                ],
                [
                    "2025-05-09",
                    11.12
                ],
                [
                    "2025-05-12",
                    -6.55
                ],
                [
                    "2025-05-13",
                    3.55
                ],
                [
                    "2025-05-14",
                    11.13
                ],
                [
                    "2025-05-15",
                    -8.09
                ],
                [
                    "2025-05-22",
                    19.32
                ],
                [
                    "2025-05-23",
                    8.36
                ],
                [
                    "2025-05-26",
                    6.45
                ],
                [
                    "2025-05-27",
                    4.28
                ],
                [
                    "2025-05-28",
                    4.45
                ],
                [
                    "2025-05-29",
                    1.84
                ],
                [
                    "2025-05-30",
                    7.80
                ],
                [
                    "2025-06-03",
                    10.42
                ],
                [
                    "2025-06-04",
                    -5.53
                ],
                [
                    "2025-06-05",
                    6.10
                ],
                [
                    "2025-06-06",
                    5.70
                ],
                [
                    "2025-06-09",
                    3.32
                ],
                [
                    "2025-06-10",
                    -5.73
                ],
                [
                    "2025-06-12",
                    9.00
                ],
                [
                    "2025-06-24",
                    1.79
                ],
                [
                    "2025-06-25",
                    0.31
                ],
                [
                    "2025-06-26",
                    -2.63
                ],
                [
                    "2025-06-27",
                    -1.99
                ],
                [
                    "2025-07-09",
                    4.52
                ],
                [
                    "2025-07-10",
                    2.47
                ],
                [
                    "2025-07-11",
                    3.04
                ],
                [
                    "2025-07-14",
                    2.34
                ],
                [
                    "2025-07-15",
                    0.16
                ],
                [
                    "2025-07-17",
                    8.55
                ],
                [
                    "2025-07-18",
                    18.66
                ],
                [
                    "2025-07-21",
                    3.91
                ],
                [
                    "2025-07-22",
                    1.53
                ],
                [
                    "2025-07-23",
                    18.79
                ],
                [
                    "2025-07-24",
                    10.43
                ],
                [
                    "2025-07-25",
                    10.38
                ],
                [
                    "2025-07-28",
                    6.79
                ],
                [
                    "2025-07-29",
                    -1.95
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600732 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_1547d1f6aad74ae58ae389914d49a593.setOption(option_1547d1f6aad74ae58ae389914d49a593);
            window.addEventListener('resize', function(){
                chart_1547d1f6aad74ae58ae389914d49a593.resize();
            })
    </script>
</body>
</html>
