<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="cf19d81b48e946329fc2359bb9d9ad12" class="chart-container" style="width:100%; height:480px; "></div>
    <script>
        var chart_cf19d81b48e946329fc2359bb9d9ad12 = echarts.init(
            document.getElementById('cf19d81b48e946329fc2359bb9d9ad12'), 'white', {renderer: 'canvas'});
        var option_cf19d81b48e946329fc2359bb9d9ad12 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    10.46,
                    10.51,
                    10.40,
                    10.88
                ],
                [
                    10.56,
                    11.25,
                    10.51,
                    11.56
                ],
                [
                    11.15,
                    11.84,
                    11.10,
                    11.94
                ],
                [
                    11.94,
                    13.02,
                    11.90,
                    13.02
                ],
                [
                    12.68,
                    12.80,
                    12.40,
                    12.94
                ],
                [
                    12.70,
                    12.04,
                    11.83,
                    12.80
                ],
                [
                    11.98,
                    12.22,
                    11.94,
                    12.27
                ],
                [
                    12.19,
                    12.03,
                    12.00,
                    12.36
                ],
                [
                    11.99,
                    12.23,
                    11.96,
                    12.63
                ],
                [
                    12.20,
                    12.90,
                    12.20,
                    13.07
                ],
                [
                    12.86,
                    12.74,
                    12.67,
                    13.43
                ],
                [
                    12.61,
                    13.78,
                    12.59,
                    13.94
                ],
                [
                    13.57,
                    13.35,
                    13.26,
                    13.74
                ],
                [
                    13.35,
                    13.18,
                    12.81,
                    13.54
                ],
                [
                    13.05,
                    13.59,
                    12.85,
                    13.71
                ],
                [
                    13.40,
                    13.49,
                    13.32,
                    14.24
                ],
                [
                    13.67,
                    14.27,
                    13.32,
                    14.50
                ],
                [
                    14.13,
                    13.79,
                    13.50,
                    14.45
                ],
                [
                    13.77,
                    13.16,
                    13.06,
                    14.09
                ],
                [
                    13.18,
                    13.49,
                    13.16,
                    13.99
                ],
                [
                    13.36,
                    13.69,
                    13.16,
                    13.79
                ],
                [
                    13.78,
                    13.39,
                    13.10,
                    13.83
                ],
                [
                    13.50,
                    13.80,
                    13.38,
                    14.00
                ],
                [
                    13.68,
                    14.14,
                    13.14,
                    14.40
                ],
                [
                    14.06,
                    14.16,
                    13.93,
                    14.48
                ],
                [
                    13.87,
                    14.00,
                    13.55,
                    14.10
                ],
                [
                    13.96,
                    13.92,
                    13.76,
                    14.28
                ],
                [
                    13.92,
                    13.20,
                    13.08,
                    14.00
                ],
                [
                    13.10,
                    13.65,
                    13.10,
                    13.80
                ],
                [
                    13.90,
                    14.01,
                    13.82,
                    14.29
                ],
                [
                    14.01,
                    14.29,
                    13.89,
                    14.48
                ],
                [
                    14.21,
                    13.73,
                    13.65,
                    14.24
                ],
                [
                    13.73,
                    13.48,
                    13.44,
                    13.79
                ],
                [
                    13.46,
                    13.13,
                    12.95,
                    13.65
                ],
                [
                    13.20,
                    13.32,
                    12.81,
                    13.43
                ],
                [
                    13.25,
                    13.02,
                    13.00,
                    13.54
                ],
                [
                    13.01,
                    13.46,
                    12.98,
                    13.95
                ],
                [
                    13.30,
                    13.05,
                    13.03,
                    13.46
                ],
                [
                    13.04,
                    12.61,
                    12.50,
                    13.33
                ],
                [
                    12.54,
                    12.18,
                    12.02,
                    12.60
                ],
                [
                    12.23,
                    12.14,
                    11.92,
                    12.48
                ],
                [
                    12.05,
                    12.09,
                    11.98,
                    12.26
                ],
                [
                    11.88,
                    11.95,
                    11.70,
                    12.28
                ],
                [
                    11.10,
                    10.76,
                    10.76,
                    11.32
                ],
                [
                    10.44,
                    10.06,
                    9.71,
                    10.83
                ],
                [
                    9.88,
                    10.02,
                    9.20,
                    10.15
                ],
                [
                    10.38,
                    10.35,
                    10.26,
                    10.48
                ],
                [
                    10.27,
                    10.25,
                    10.19,
                    10.48
                ],
                [
                    10.41,
                    10.46,
                    10.41,
                    10.75
                ],
                [
                    10.51,
                    10.30,
                    10.26,
                    10.74
                ],
                [
                    10.30,
                    9.84,
                    9.68,
                    10.30
                ],
                [
                    9.73,
                    9.86,
                    9.73,
                    9.98
                ],
                [
                    9.90,
                    9.69,
                    9.55,
                    9.92
                ],
                [
                    9.71,
                    9.96,
                    9.64,
                    10.01
                ],
                [
                    9.98,
                    10.20,
                    9.92,
                    10.53
                ],
                [
                    10.21,
                    10.16,
                    10.08,
                    10.47
                ],
                [
                    10.20,
                    9.94,
                    9.83,
                    10.22
                ],
                [
                    9.90,
                    9.96,
                    9.89,
                    10.09
                ],
                [
                    10.00,
                    10.10,
                    9.95,
                    10.30
                ],
                [
                    10.12,
                    9.90,
                    9.90,
                    10.39
                ],
                [
                    10.15,
                    10.51,
                    10.09,
                    10.89
                ],
                [
                    10.65,
                    10.89,
                    10.50,
                    10.97
                ],
                [
                    11.10,
                    10.83,
                    10.70,
                    11.16
                ],
                [
                    10.79,
                    10.90,
                    10.72,
                    11.01
                ],
                [
                    10.89,
                    10.51,
                    10.47,
                    10.97
                ],
                [
                    10.72,
                    11.36,
                    10.72,
                    11.45
                ],
                [
                    11.36,
                    11.47,
                    11.29,
                    11.95
                ],
                [
                    11.36,
                    11.44,
                    11.16,
                    11.57
                ],
                [
                    11.45,
                    11.27,
                    11.10,
                    11.46
                ],
                [
                    11.23,
                    11.31,
                    11.18,
                    11.41
                ],
                [
                    11.31,
                    11.54,
                    11.11,
                    11.62
                ],
                [
                    11.55,
                    11.59,
                    11.35,
                    11.77
                ],
                [
                    11.70,
                    11.54,
                    11.49,
                    11.87
                ],
                [
                    11.38,
                    11.46,
                    11.34,
                    11.57
                ],
                [
                    11.46,
                    11.63,
                    11.37,
                    11.87
                ],
                [
                    11.82,
                    11.38,
                    11.33,
                    11.83
                ],
                [
                    11.38,
                    11.39,
                    11.05,
                    11.42
                ],
                [
                    11.39,
                    11.03,
                    10.98,
                    11.45
                ],
                [
                    11.03,
                    11.23,
                    11.01,
                    11.28
                ],
                [
                    11.14,
                    11.08,
                    11.03,
                    11.25
                ],
                [
                    11.00,
                    10.78,
                    10.75,
                    11.24
                ],
                [
                    10.88,
                    11.05,
                    10.86,
                    11.49
                ],
                [
                    11.07,
                    10.95,
                    10.83,
                    11.11
                ],
                [
                    11.00,
                    10.93,
                    10.83,
                    11.02
                ],
                [
                    10.95,
                    11.38,
                    10.89,
                    11.50
                ],
                [
                    11.33,
                    11.23,
                    11.14,
                    11.42
                ],
                [
                    11.50,
                    12.12,
                    11.48,
                    12.35
                ],
                [
                    11.81,
                    11.71,
                    11.69,
                    12.01
                ],
                [
                    11.91,
                    11.60,
                    11.57,
                    12.01
                ],
                [
                    11.57,
                    11.91,
                    11.40,
                    11.99
                ],
                [
                    11.88,
                    11.96,
                    11.81,
                    12.20
                ],
                [
                    11.93,
                    12.17,
                    11.85,
                    12.35
                ],
                [
                    12.17,
                    11.96,
                    11.86,
                    12.30
                ],
                [
                    12.09,
                    12.05,
                    12.02,
                    12.44
                ],
                [
                    11.88,
                    11.89,
                    11.58,
                    11.93
                ],
                [
                    11.85,
                    12.07,
                    11.78,
                    12.15
                ],
                [
                    12.06,
                    12.06,
                    11.89,
                    12.10
                ],
                [
                    12.08,
                    11.89,
                    11.88,
                    12.27
                ],
                [
                    12.04,
                    12.41,
                    11.98,
                    12.60
                ],
                [
                    13.01,
                    13.10,
                    12.64,
                    13.40
                ],
                [
                    13.01,
                    13.11,
                    12.81,
                    13.24
                ],
                [
                    13.25,
                    14.14,
                    13.10,
                    14.42
                ],
                [
                    13.92,
                    13.99,
                    13.69,
                    14.19
                ],
                [
                    14.25,
                    13.70,
                    13.55,
                    14.30
                ],
                [
                    13.75,
                    13.89,
                    13.70,
                    14.18
                ],
                [
                    13.84,
                    14.63,
                    13.76,
                    14.95
                ],
                [
                    14.40,
                    14.55,
                    14.40,
                    14.85
                ],
                [
                    14.50,
                    14.76,
                    14.49,
                    15.00
                ],
                [
                    15.28,
                    14.65,
                    14.13,
                    15.47
                ],
                [
                    14.50,
                    14.74,
                    14.36,
                    14.92
                ],
                [
                    14.85,
                    14.83,
                    14.60,
                    15.09
                ],
                [
                    14.76,
                    15.39,
                    14.40,
                    15.42
                ],
                [
                    15.61,
                    15.18,
                    15.03,
                    15.88
                ],
                [
                    15.01,
                    14.66,
                    14.53,
                    15.35
                ],
                [
                    14.66,
                    14.78,
                    14.50,
                    15.03
                ],
                [
                    14.65,
                    14.88,
                    14.44,
                    15.05
                ],
                [
                    14.86,
                    14.51,
                    14.42,
                    15.17
                ],
                [
                    14.40,
                    14.96,
                    14.35,
                    15.00
                ],
                [
                    14.95,
                    14.77,
                    14.73,
                    14.97
                ],
                [
                    14.55,
                    14.85,
                    14.43,
                    14.96
                ],
                [
                    14.85,
                    14.98,
                    14.74,
                    15.10
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "symbol": "diamond",
            "symbolSize": 16,
            "data": [
                [
                    "2025-05-13",
                    11.95
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600732 \u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_cf19d81b48e946329fc2359bb9d9ad12.setOption(option_cf19d81b48e946329fc2359bb9d9ad12);
            window.addEventListener('resize', function(){
                chart_cf19d81b48e946329fc2359bb9d9ad12.resize();
            })
    </script>
</body>
</html>
