<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="ca2a8b2151ce43edb9aa02b8f1f686ea" class="chart-container" style="width:100%; height:500px; "></div>
    <script>
        var chart_ca2a8b2151ce43edb9aa02b8f1f686ea = echarts.init(
            document.getElementById('ca2a8b2151ce43edb9aa02b8f1f686ea'), 'white', {renderer: 'canvas'});
        var option_ca2a8b2151ce43edb9aa02b8f1f686ea = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    13.96,
                    13.6,
                    13.59,
                    14.24
                ],
                [
                    14.96,
                    14.96,
                    14.8,
                    14.96
                ],
                [
                    15.7,
                    15.54,
                    15.01,
                    15.85
                ],
                [
                    15.46,
                    16.38,
                    15.45,
                    17.09
                ],
                [
                    17.34,
                    17.21,
                    16.88,
                    17.91
                ],
                [
                    16.66,
                    17.0,
                    16.62,
                    18.18
                ],
                [
                    17.44,
                    17.67,
                    17.22,
                    18.0
                ],
                [
                    17.65,
                    17.45,
                    17.01,
                    17.9
                ],
                [
                    17.82,
                    19.2,
                    17.3,
                    19.2
                ],
                [
                    21.12,
                    21.12,
                    19.88,
                    21.12
                ],
                [
                    21.4,
                    23.23,
                    20.29,
                    23.23
                ],
                [
                    24.0,
                    23.01,
                    22.06,
                    24.5
                ],
                [
                    22.06,
                    22.07,
                    21.7,
                    22.95
                ],
                [
                    22.5,
                    24.28,
                    22.3,
                    24.28
                ],
                [
                    26.71,
                    26.71,
                    26.71,
                    26.71
                ],
                [
                    26.0,
                    28.8,
                    25.5,
                    29.38
                ],
                [
                    28.52,
                    27.51,
                    26.56,
                    28.6
                ],
                [
                    27.07,
                    24.99,
                    24.76,
                    27.2
                ],
                [
                    24.0,
                    22.49,
                    22.49,
                    25.1
                ],
                [
                    22.49,
                    21.87,
                    21.56,
                    22.77
                ],
                [
                    21.64,
                    22.3,
                    21.5,
                    22.64
                ],
                [
                    22.63,
                    23.79,
                    21.82,
                    24.24
                ],
                [
                    23.81,
                    24.69,
                    23.41,
                    25.45
                ],
                [
                    24.69,
                    27.16,
                    23.63,
                    27.16
                ],
                [
                    26.5,
                    27.64,
                    25.2,
                    28.8
                ],
                [
                    26.96,
                    28.76,
                    26.61,
                    30.26
                ],
                [
                    28.77,
                    27.89,
                    27.8,
                    29.88
                ],
                [
                    27.0,
                    26.0,
                    25.1,
                    27.07
                ],
                [
                    25.2,
                    25.49,
                    24.49,
                    25.93
                ],
                [
                    25.45,
                    26.25,
                    25.18,
                    26.66
                ],
                [
                    26.43,
                    25.69,
                    25.61,
                    26.66
                ],
                [
                    25.6,
                    25.79,
                    25.01,
                    26.55
                ],
                [
                    25.3,
                    26.12,
                    24.68,
                    27.5
                ],
                [
                    25.83,
                    24.27,
                    24.08,
                    26.35
                ],
                [
                    24.48,
                    23.61,
                    23.1,
                    24.8
                ],
                [
                    24.2,
                    22.53,
                    22.36,
                    24.2
                ],
                [
                    22.5,
                    22.72,
                    22.41,
                    23.25
                ],
                [
                    23.91,
                    23.01,
                    22.88,
                    24.26
                ],
                [
                    22.6,
                    22.8,
                    22.51,
                    23.71
                ],
                [
                    22.16,
                    24.07,
                    21.81,
                    25.0
                ],
                [
                    24.07,
                    23.16,
                    23.01,
                    24.07
                ],
                [
                    23.05,
                    23.02,
                    22.9,
                    23.69
                ],
                [
                    22.6,
                    23.29,
                    22.51,
                    23.61
                ],
                [
                    21.5,
                    20.96,
                    20.96,
                    22.1
                ],
                [
                    20.27,
                    19.44,
                    18.86,
                    20.87
                ],
                [
                    18.99,
                    20.43,
                    17.84,
                    20.88
                ],
                [
                    20.95,
                    21.14,
                    20.68,
                    21.88
                ],
                [
                    21.0,
                    21.46,
                    20.87,
                    21.85
                ],
                [
                    21.91,
                    21.48,
                    21.37,
                    21.97
                ],
                [
                    21.58,
                    21.4,
                    21.13,
                    21.75
                ],
                [
                    21.28,
                    20.85,
                    20.56,
                    21.48
                ],
                [
                    20.8,
                    20.93,
                    20.67,
                    21.48
                ],
                [
                    21.09,
                    21.07,
                    20.86,
                    21.56
                ],
                [
                    21.07,
                    22.35,
                    20.87,
                    22.69
                ],
                [
                    22.23,
                    21.63,
                    21.41,
                    22.31
                ],
                [
                    22.0,
                    22.37,
                    21.6,
                    22.63
                ],
                [
                    22.24,
                    21.55,
                    21.19,
                    22.3
                ],
                [
                    22.0,
                    22.76,
                    22.0,
                    23.39
                ],
                [
                    22.85,
                    23.18,
                    22.85,
                    23.98
                ],
                [
                    23.1,
                    23.19,
                    22.57,
                    23.5
                ],
                [
                    23.19,
                    23.48,
                    22.31,
                    23.68
                ],
                [
                    23.8,
                    24.3,
                    23.49,
                    24.55
                ],
                [
                    24.59,
                    23.73,
                    23.36,
                    24.59
                ],
                [
                    23.4,
                    23.34,
                    23.3,
                    23.83
                ],
                [
                    23.34,
                    22.66,
                    22.66,
                    23.34
                ],
                [
                    22.8,
                    22.92,
                    22.66,
                    22.98
                ],
                [
                    23.15,
                    22.71,
                    22.67,
                    23.21
                ],
                [
                    22.7,
                    22.58,
                    22.35,
                    22.88
                ],
                [
                    22.45,
                    21.58,
                    21.55,
                    22.49
                ],
                [
                    21.49,
                    21.65,
                    21.31,
                    21.83
                ],
                [
                    21.79,
                    21.61,
                    21.18,
                    21.8
                ],
                [
                    21.47,
                    21.53,
                    21.28,
                    21.75
                ],
                [
                    21.43,
                    21.21,
                    21.12,
                    21.54
                ],
                [
                    21.14,
                    21.0,
                    20.96,
                    21.36
                ],
                [
                    21.0,
                    20.55,
                    20.5,
                    21.13
                ],
                [
                    20.54,
                    20.79,
                    20.4,
                    20.95
                ],
                [
                    20.72,
                    20.37,
                    20.3,
                    20.78
                ],
                [
                    20.29,
                    20.14,
                    20.13,
                    20.5
                ],
                [
                    20.2,
                    20.49,
                    20.15,
                    20.55
                ],
                [
                    20.42,
                    20.06,
                    20.01,
                    20.42
                ],
                [
                    19.93,
                    20.07,
                    19.85,
                    20.21
                ],
                [
                    20.1,
                    20.19,
                    20.01,
                    20.53
                ],
                [
                    20.2,
                    20.89,
                    20.2,
                    20.97
                ],
                [
                    20.92,
                    20.91,
                    20.66,
                    21.37
                ],
                [
                    20.82,
                    20.93,
                    20.82,
                    21.15
                ],
                [
                    21.05,
                    20.38,
                    20.13,
                    21.11
                ],
                [
                    20.4,
                    20.29,
                    20.27,
                    20.56
                ],
                [
                    20.21,
                    20.19,
                    20.11,
                    20.36
                ],
                [
                    20.11,
                    19.66,
                    19.44,
                    20.12
                ],
                [
                    19.47,
                    19.74,
                    19.43,
                    19.8
                ],
                [
                    19.74,
                    19.56,
                    19.43,
                    19.74
                ],
                [
                    19.51,
                    19.5,
                    19.25,
                    19.67
                ],
                [
                    19.5,
                    19.13,
                    19.09,
                    19.64
                ],
                [
                    19.1,
                    18.7,
                    18.67,
                    19.24
                ],
                [
                    18.5,
                    18.81,
                    18.36,
                    18.9
                ],
                [
                    18.86,
                    19.68,
                    18.79,
                    20.16
                ],
                [
                    19.7,
                    20.06,
                    19.61,
                    20.08
                ],
                [
                    20.07,
                    19.94,
                    19.82,
                    20.4
                ],
                [
                    19.86,
                    19.93,
                    19.76,
                    20.18
                ],
                [
                    19.89,
                    20.17,
                    19.88,
                    20.28
                ],
                [
                    20.2,
                    20.02,
                    19.85,
                    20.22
                ],
                [
                    19.96,
                    19.58,
                    19.49,
                    19.98
                ],
                [
                    19.52,
                    19.58,
                    19.5,
                    19.87
                ],
                [
                    19.55,
                    19.99,
                    19.52,
                    20.5
                ],
                [
                    19.81,
                    19.62,
                    19.55,
                    19.87
                ],
                [
                    19.63,
                    20.05,
                    19.49,
                    20.09
                ],
                [
                    20.05,
                    19.96,
                    19.9,
                    20.2
                ],
                [
                    19.9,
                    20.14,
                    19.68,
                    20.5
                ],
                [
                    20.03,
                    21.28,
                    19.86,
                    22.0
                ],
                [
                    21.94,
                    21.68,
                    21.45,
                    22.22
                ],
                [
                    21.4,
                    22.74,
                    21.32,
                    23.0
                ],
                [
                    22.6,
                    22.27,
                    22.1,
                    23.17
                ],
                [
                    22.07,
                    22.32,
                    21.78,
                    22.6
                ],
                [
                    22.45,
                    23.14,
                    22.3,
                    23.46
                ],
                [
                    22.63,
                    22.28,
                    22.16,
                    22.95
                ],
                [
                    22.01,
                    21.88,
                    21.8,
                    22.46
                ],
                [
                    21.6,
                    22.65,
                    21.5,
                    22.92
                ],
                [
                    22.4,
                    23.0,
                    22.4,
                    23.32
                ],
                [
                    23.57,
                    23.47,
                    23.18,
                    24.06
                ],
                [
                    23.47,
                    23.13,
                    22.8,
                    23.6
                ],
                [
                    22.8,
                    22.56,
                    22.25,
                    22.96
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u9636\u6bb5\u6027\u5e95\u90e8",
            "symbol": "triangle",
            "symbolSize": 20,
            "data": [
                [
                    "2025-03-20",
                    24.19
                ],
                [
                    "2025-04-01",
                    22.55
                ],
                [
                    "2025-04-02",
                    22.44
                ],
                [
                    "2025-06-03",
                    19.45
                ],
                [
                    "2025-06-04",
                    19.61
                ],
                [
                    "2025-06-12",
                    19.71
                ],
                [
                    "2025-07-07",
                    19.16
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#FF6B35",
                "borderColor": "#CC5529",
                "borderWidth": 2,
                "opacity": 0.9
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf",
                "\u9636\u6bb5\u6027\u5e95\u90e8"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600602 \u9636\u6bb5\u6027\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_ca2a8b2151ce43edb9aa02b8f1f686ea.setOption(option_ca2a8b2151ce43edb9aa02b8f1f686ea);
            window.addEventListener('resize', function(){
                chart_ca2a8b2151ce43edb9aa02b8f1f686ea.resize();
            })
    </script>
</body>
</html>
