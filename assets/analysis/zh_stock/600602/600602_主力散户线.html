<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="dcce1ec6a99044da82f17b152df9c206" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_dcce1ec6a99044da82f17b152df9c206 = echarts.init(
            document.getElementById('dcce1ec6a99044da82f17b152df9c206'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_dcce1ec6a99044da82f17b152df9c206 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    13.96,
                    13.6,
                    13.59,
                    14.24
                ],
                [
                    14.96,
                    14.96,
                    14.8,
                    14.96
                ],
                [
                    15.7,
                    15.54,
                    15.01,
                    15.85
                ],
                [
                    15.46,
                    16.38,
                    15.45,
                    17.09
                ],
                [
                    17.34,
                    17.21,
                    16.88,
                    17.91
                ],
                [
                    16.66,
                    17.0,
                    16.62,
                    18.18
                ],
                [
                    17.44,
                    17.67,
                    17.22,
                    18.0
                ],
                [
                    17.65,
                    17.45,
                    17.01,
                    17.9
                ],
                [
                    17.82,
                    19.2,
                    17.3,
                    19.2
                ],
                [
                    21.12,
                    21.12,
                    19.88,
                    21.12
                ],
                [
                    21.4,
                    23.23,
                    20.29,
                    23.23
                ],
                [
                    24.0,
                    23.01,
                    22.06,
                    24.5
                ],
                [
                    22.06,
                    22.07,
                    21.7,
                    22.95
                ],
                [
                    22.5,
                    24.28,
                    22.3,
                    24.28
                ],
                [
                    26.71,
                    26.71,
                    26.71,
                    26.71
                ],
                [
                    26.0,
                    28.8,
                    25.5,
                    29.38
                ],
                [
                    28.52,
                    27.51,
                    26.56,
                    28.6
                ],
                [
                    27.07,
                    24.99,
                    24.76,
                    27.2
                ],
                [
                    24.0,
                    22.49,
                    22.49,
                    25.1
                ],
                [
                    22.49,
                    21.87,
                    21.56,
                    22.77
                ],
                [
                    21.64,
                    22.3,
                    21.5,
                    22.64
                ],
                [
                    22.63,
                    23.79,
                    21.82,
                    24.24
                ],
                [
                    23.81,
                    24.69,
                    23.41,
                    25.45
                ],
                [
                    24.69,
                    27.16,
                    23.63,
                    27.16
                ],
                [
                    26.5,
                    27.64,
                    25.2,
                    28.8
                ],
                [
                    26.96,
                    28.76,
                    26.61,
                    30.26
                ],
                [
                    28.77,
                    27.89,
                    27.8,
                    29.88
                ],
                [
                    27.0,
                    26.0,
                    25.1,
                    27.07
                ],
                [
                    25.2,
                    25.49,
                    24.49,
                    25.93
                ],
                [
                    25.45,
                    26.25,
                    25.18,
                    26.66
                ],
                [
                    26.43,
                    25.69,
                    25.61,
                    26.66
                ],
                [
                    25.6,
                    25.79,
                    25.01,
                    26.55
                ],
                [
                    25.3,
                    26.12,
                    24.68,
                    27.5
                ],
                [
                    25.83,
                    24.27,
                    24.08,
                    26.35
                ],
                [
                    24.48,
                    23.61,
                    23.1,
                    24.8
                ],
                [
                    24.2,
                    22.53,
                    22.36,
                    24.2
                ],
                [
                    22.5,
                    22.72,
                    22.41,
                    23.25
                ],
                [
                    23.91,
                    23.01,
                    22.88,
                    24.26
                ],
                [
                    22.6,
                    22.8,
                    22.51,
                    23.71
                ],
                [
                    22.16,
                    24.07,
                    21.81,
                    25.0
                ],
                [
                    24.07,
                    23.16,
                    23.01,
                    24.07
                ],
                [
                    23.05,
                    23.02,
                    22.9,
                    23.69
                ],
                [
                    22.6,
                    23.29,
                    22.51,
                    23.61
                ],
                [
                    21.5,
                    20.96,
                    20.96,
                    22.1
                ],
                [
                    20.27,
                    19.44,
                    18.86,
                    20.87
                ],
                [
                    18.99,
                    20.43,
                    17.84,
                    20.88
                ],
                [
                    20.95,
                    21.14,
                    20.68,
                    21.88
                ],
                [
                    21.0,
                    21.46,
                    20.87,
                    21.85
                ],
                [
                    21.91,
                    21.48,
                    21.37,
                    21.97
                ],
                [
                    21.58,
                    21.4,
                    21.13,
                    21.75
                ],
                [
                    21.28,
                    20.85,
                    20.56,
                    21.48
                ],
                [
                    20.8,
                    20.93,
                    20.67,
                    21.48
                ],
                [
                    21.09,
                    21.07,
                    20.86,
                    21.56
                ],
                [
                    21.07,
                    22.35,
                    20.87,
                    22.69
                ],
                [
                    22.23,
                    21.63,
                    21.41,
                    22.31
                ],
                [
                    22.0,
                    22.37,
                    21.6,
                    22.63
                ],
                [
                    22.24,
                    21.55,
                    21.19,
                    22.3
                ],
                [
                    22.0,
                    22.76,
                    22.0,
                    23.39
                ],
                [
                    22.85,
                    23.18,
                    22.85,
                    23.98
                ],
                [
                    23.1,
                    23.19,
                    22.57,
                    23.5
                ],
                [
                    23.19,
                    23.48,
                    22.31,
                    23.68
                ],
                [
                    23.8,
                    24.3,
                    23.49,
                    24.55
                ],
                [
                    24.59,
                    23.73,
                    23.36,
                    24.59
                ],
                [
                    23.4,
                    23.34,
                    23.3,
                    23.83
                ],
                [
                    23.34,
                    22.66,
                    22.66,
                    23.34
                ],
                [
                    22.8,
                    22.92,
                    22.66,
                    22.98
                ],
                [
                    23.15,
                    22.71,
                    22.67,
                    23.21
                ],
                [
                    22.7,
                    22.58,
                    22.35,
                    22.88
                ],
                [
                    22.45,
                    21.58,
                    21.55,
                    22.49
                ],
                [
                    21.49,
                    21.65,
                    21.31,
                    21.83
                ],
                [
                    21.79,
                    21.61,
                    21.18,
                    21.8
                ],
                [
                    21.47,
                    21.53,
                    21.28,
                    21.75
                ],
                [
                    21.43,
                    21.21,
                    21.12,
                    21.54
                ],
                [
                    21.14,
                    21.0,
                    20.96,
                    21.36
                ],
                [
                    21.0,
                    20.55,
                    20.5,
                    21.13
                ],
                [
                    20.54,
                    20.79,
                    20.4,
                    20.95
                ],
                [
                    20.72,
                    20.37,
                    20.3,
                    20.78
                ],
                [
                    20.29,
                    20.14,
                    20.13,
                    20.5
                ],
                [
                    20.2,
                    20.49,
                    20.15,
                    20.55
                ],
                [
                    20.42,
                    20.06,
                    20.01,
                    20.42
                ],
                [
                    19.93,
                    20.07,
                    19.85,
                    20.21
                ],
                [
                    20.1,
                    20.19,
                    20.01,
                    20.53
                ],
                [
                    20.2,
                    20.89,
                    20.2,
                    20.97
                ],
                [
                    20.92,
                    20.91,
                    20.66,
                    21.37
                ],
                [
                    20.82,
                    20.93,
                    20.82,
                    21.15
                ],
                [
                    21.05,
                    20.38,
                    20.13,
                    21.11
                ],
                [
                    20.4,
                    20.29,
                    20.27,
                    20.56
                ],
                [
                    20.21,
                    20.19,
                    20.11,
                    20.36
                ],
                [
                    20.11,
                    19.66,
                    19.44,
                    20.12
                ],
                [
                    19.47,
                    19.74,
                    19.43,
                    19.8
                ],
                [
                    19.74,
                    19.56,
                    19.43,
                    19.74
                ],
                [
                    19.51,
                    19.5,
                    19.25,
                    19.67
                ],
                [
                    19.5,
                    19.13,
                    19.09,
                    19.64
                ],
                [
                    19.1,
                    18.7,
                    18.67,
                    19.24
                ],
                [
                    18.5,
                    18.81,
                    18.36,
                    18.9
                ],
                [
                    18.86,
                    19.68,
                    18.79,
                    20.16
                ],
                [
                    19.7,
                    20.06,
                    19.61,
                    20.08
                ],
                [
                    20.07,
                    19.94,
                    19.82,
                    20.4
                ],
                [
                    19.86,
                    19.93,
                    19.76,
                    20.18
                ],
                [
                    19.89,
                    20.17,
                    19.88,
                    20.28
                ],
                [
                    20.2,
                    20.02,
                    19.85,
                    20.22
                ],
                [
                    19.96,
                    19.58,
                    19.49,
                    19.98
                ],
                [
                    19.52,
                    19.58,
                    19.5,
                    19.87
                ],
                [
                    19.55,
                    19.99,
                    19.52,
                    20.5
                ],
                [
                    19.81,
                    19.62,
                    19.55,
                    19.87
                ],
                [
                    19.63,
                    20.05,
                    19.49,
                    20.09
                ],
                [
                    20.05,
                    19.96,
                    19.9,
                    20.2
                ],
                [
                    19.9,
                    20.14,
                    19.68,
                    20.5
                ],
                [
                    20.03,
                    21.28,
                    19.86,
                    22.0
                ],
                [
                    21.94,
                    21.68,
                    21.45,
                    22.22
                ],
                [
                    21.4,
                    22.74,
                    21.32,
                    23.0
                ],
                [
                    22.6,
                    22.27,
                    22.1,
                    23.17
                ],
                [
                    22.07,
                    22.32,
                    21.78,
                    22.6
                ],
                [
                    22.45,
                    23.14,
                    22.3,
                    23.46
                ],
                [
                    22.63,
                    22.28,
                    22.16,
                    22.95
                ],
                [
                    22.01,
                    21.88,
                    21.8,
                    22.46
                ],
                [
                    21.6,
                    22.65,
                    21.5,
                    22.92
                ],
                [
                    22.4,
                    23.0,
                    22.4,
                    23.32
                ],
                [
                    23.57,
                    23.47,
                    23.18,
                    24.06
                ],
                [
                    23.47,
                    23.13,
                    22.8,
                    23.6
                ],
                [
                    22.8,
                    22.56,
                    22.25,
                    22.96
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -0.99
                ],
                [
                    "2025-02-05",
                    35.28
                ],
                [
                    "2025-02-06",
                    -1.18
                ],
                [
                    "2025-02-07",
                    10.38
                ],
                [
                    "2025-02-10",
                    -3.42
                ],
                [
                    "2025-02-11",
                    -1.53
                ],
                [
                    "2025-02-12",
                    -6.99
                ],
                [
                    "2025-02-13",
                    -1.24
                ],
                [
                    "2025-02-14",
                    14.05
                ],
                [
                    "2025-02-17",
                    -6.03
                ],
                [
                    "2025-02-18",
                    -11.59
                ],
                [
                    "2025-02-19",
                    -5.16
                ],
                [
                    "2025-02-20",
                    -6.92
                ],
                [
                    "2025-02-21",
                    12.13
                ],
                [
                    "2025-02-24",
                    39.77
                ],
                [
                    "2025-02-25",
                    -6.68
                ],
                [
                    "2025-02-26",
                    -6.68
                ],
                [
                    "2025-02-27",
                    -11.17
                ],
                [
                    "2025-02-28",
                    -9.29
                ],
                [
                    "2025-03-03",
                    -1.36
                ],
                [
                    "2025-03-04",
                    -2.35
                ],
                [
                    "2025-03-05",
                    -3.33
                ],
                [
                    "2025-03-06",
                    0.63
                ],
                [
                    "2025-03-07",
                    21.76
                ],
                [
                    "2025-03-10",
                    -5.75
                ],
                [
                    "2025-03-11",
                    -0.90
                ],
                [
                    "2025-03-12",
                    -4.86
                ],
                [
                    "2025-03-13",
                    -15.80
                ],
                [
                    "2025-03-14",
                    -6.83
                ],
                [
                    "2025-03-17",
                    3.12
                ],
                [
                    "2025-03-18",
                    -8.52
                ],
                [
                    "2025-03-19",
                    -4.78
                ],
                [
                    "2025-03-20",
                    7.62
                ],
                [
                    "2025-03-21",
                    -14.61
                ],
                [
                    "2025-03-24",
                    -10.54
                ],
                [
                    "2025-03-25",
                    -9.12
                ],
                [
                    "2025-03-26",
                    -0.46
                ],
                [
                    "2025-03-27",
                    -3.50
                ],
                [
                    "2025-03-28",
                    -1.34
                ],
                [
                    "2025-03-31",
                    5.24
                ],
                [
                    "2025-04-01",
                    -4.25
                ],
                [
                    "2025-04-02",
                    -1.48
                ],
                [
                    "2025-04-03",
                    4.61
                ],
                [
                    "2025-04-07",
                    -14.02
                ],
                [
                    "2025-04-08",
                    -12.52
                ],
                [
                    "2025-04-09",
                    -0.95
                ],
                [
                    "2025-04-10",
                    -1.99
                ],
                [
                    "2025-04-11",
                    1.47
                ],
                [
                    "2025-04-14",
                    -1.57
                ],
                [
                    "2025-04-15",
                    -0.83
                ],
                [
                    "2025-04-16",
                    -10.13
                ],
                [
                    "2025-04-17",
                    -3.57
                ],
                [
                    "2025-04-18",
                    7.51
                ],
                [
                    "2025-04-21",
                    9.08
                ],
                [
                    "2025-04-22",
                    -6.76
                ],
                [
                    "2025-04-23",
                    4.11
                ],
                [
                    "2025-04-24",
                    -10.61
                ],
                [
                    "2025-04-25",
                    10.74
                ],
                [
                    "2025-04-28",
                    -2.87
                ],
                [
                    "2025-04-29",
                    -1.37
                ],
                [
                    "2025-04-30",
                    -6.63
                ],
                [
                    "2025-05-06",
                    3.46
                ],
                [
                    "2025-05-07",
                    -6.70
                ],
                [
                    "2025-05-08",
                    -8.68
                ],
                [
                    "2025-05-09",
                    -11.43
                ],
                [
                    "2025-05-12",
                    -3.24
                ],
                [
                    "2025-05-13",
                    -14.28
                ],
                [
                    "2025-05-14",
                    -16.34
                ],
                [
                    "2025-05-15",
                    -20.71
                ],
                [
                    "2025-05-16",
                    0.38
                ],
                [
                    "2025-05-19",
                    -2.11
                ],
                [
                    "2025-05-20",
                    -0.48
                ],
                [
                    "2025-05-21",
                    -11.39
                ],
                [
                    "2025-05-22",
                    -7.08
                ],
                [
                    "2025-05-23",
                    -7.44
                ],
                [
                    "2025-05-26",
                    1.40
                ],
                [
                    "2025-05-27",
                    -7.94
                ],
                [
                    "2025-05-28",
                    -6.12
                ],
                [
                    "2025-05-29",
                    2.12
                ],
                [
                    "2025-05-30",
                    -13.20
                ],
                [
                    "2025-06-03",
                    -2.36
                ],
                [
                    "2025-06-04",
                    3.59
                ],
                [
                    "2025-06-05",
                    8.59
                ],
                [
                    "2025-06-06",
                    -5.61
                ],
                [
                    "2025-06-09",
                    -3.67
                ],
                [
                    "2025-06-10",
                    -13.31
                ],
                [
                    "2025-06-11",
                    -9.76
                ],
                [
                    "2025-06-12",
                    -1.72
                ],
                [
                    "2025-06-13",
                    -21.48
                ],
                [
                    "2025-06-16",
                    -10.72
                ],
                [
                    "2025-06-17",
                    -15.83
                ],
                [
                    "2025-06-18",
                    -4.19
                ],
                [
                    "2025-06-19",
                    -9.50
                ],
                [
                    "2025-06-20",
                    -15.25
                ],
                [
                    "2025-06-23",
                    -4.73
                ],
                [
                    "2025-06-24",
                    6.79
                ],
                [
                    "2025-06-25",
                    -1.21
                ],
                [
                    "2025-06-26",
                    -5.21
                ],
                [
                    "2025-06-27",
                    -12.09
                ],
                [
                    "2025-06-30",
                    2.96
                ],
                [
                    "2025-07-01",
                    -0.04
                ],
                [
                    "2025-07-02",
                    -11.88
                ],
                [
                    "2025-07-03",
                    -2.07
                ],
                [
                    "2025-07-04",
                    11.79
                ],
                [
                    "2025-07-07",
                    -12.53
                ],
                [
                    "2025-07-08",
                    3.28
                ],
                [
                    "2025-07-09",
                    -3.52
                ],
                [
                    "2025-07-10",
                    3.11
                ],
                [
                    "2025-07-11",
                    11.06
                ],
                [
                    "2025-07-14",
                    -5.60
                ],
                [
                    "2025-07-15",
                    5.66
                ],
                [
                    "2025-07-16",
                    -10.26
                ],
                [
                    "2025-07-17",
                    -2.43
                ],
                [
                    "2025-07-18",
                    14.48
                ],
                [
                    "2025-07-21",
                    -17.95
                ],
                [
                    "2025-07-22",
                    -6.15
                ],
                [
                    "2025-07-23",
                    8.81
                ],
                [
                    "2025-07-24",
                    7.68
                ],
                [
                    "2025-07-25",
                    1.28
                ],
                [
                    "2025-07-28",
                    -13.96
                ],
                [
                    "2025-07-29",
                    -15.00
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    1.74
                ],
                [
                    "2025-02-05",
                    -20.34
                ],
                [
                    "2025-02-06",
                    -1.79
                ],
                [
                    "2025-02-07",
                    -6.14
                ],
                [
                    "2025-02-10",
                    1.27
                ],
                [
                    "2025-02-11",
                    1.10
                ],
                [
                    "2025-02-12",
                    1.36
                ],
                [
                    "2025-02-13",
                    -0.67
                ],
                [
                    "2025-02-14",
                    -6.01
                ],
                [
                    "2025-02-17",
                    0.83
                ],
                [
                    "2025-02-18",
                    2.96
                ],
                [
                    "2025-02-19",
                    -2.55
                ],
                [
                    "2025-02-20",
                    0.46
                ],
                [
                    "2025-02-21",
                    -6.15
                ],
                [
                    "2025-02-24",
                    -21.31
                ],
                [
                    "2025-02-25",
                    1.13
                ],
                [
                    "2025-02-26",
                    1.50
                ],
                [
                    "2025-02-27",
                    0.90
                ],
                [
                    "2025-02-28",
                    -1.73
                ],
                [
                    "2025-03-03",
                    -2.76
                ],
                [
                    "2025-03-04",
                    1.89
                ],
                [
                    "2025-03-05",
                    2.67
                ],
                [
                    "2025-03-06",
                    -0.92
                ],
                [
                    "2025-03-07",
                    -13.26
                ],
                [
                    "2025-03-10",
                    1.63
                ],
                [
                    "2025-03-11",
                    -0.66
                ],
                [
                    "2025-03-12",
                    -1.85
                ],
                [
                    "2025-03-13",
                    2.83
                ],
                [
                    "2025-03-14",
                    1.22
                ],
                [
                    "2025-03-17",
                    -0.71
                ],
                [
                    "2025-03-18",
                    -0.80
                ],
                [
                    "2025-03-19",
                    0.11
                ],
                [
                    "2025-03-20",
                    -3.33
                ],
                [
                    "2025-03-21",
                    0.97
                ],
                [
                    "2025-03-24",
                    -4.97
                ],
                [
                    "2025-03-25",
                    -4.69
                ],
                [
                    "2025-03-26",
                    -1.44
                ],
                [
                    "2025-03-27",
                    3.03
                ],
                [
                    "2025-03-28",
                    1.64
                ],
                [
                    "2025-03-31",
                    -1.83
                ],
                [
                    "2025-04-01",
                    -0.76
                ],
                [
                    "2025-04-02",
                    -2.18
                ],
                [
                    "2025-04-03",
                    -2.66
                ],
                [
                    "2025-04-07",
                    1.28
                ],
                [
                    "2025-04-08",
                    1.07
                ],
                [
                    "2025-04-09",
                    -2.80
                ],
                [
                    "2025-04-10",
                    -1.70
                ],
                [
                    "2025-04-11",
                    -5.69
                ],
                [
                    "2025-04-14",
                    -6.50
                ],
                [
                    "2025-04-15",
                    1.45
                ],
                [
                    "2025-04-16",
                    -7.22
                ],
                [
                    "2025-04-17",
                    -0.85
                ],
                [
                    "2025-04-18",
                    0.07
                ],
                [
                    "2025-04-21",
                    -1.47
                ],
                [
                    "2025-04-22",
                    4.13
                ],
                [
                    "2025-04-23",
                    -2.49
                ],
                [
                    "2025-04-24",
                    6.11
                ],
                [
                    "2025-04-25",
                    -7.67
                ],
                [
                    "2025-04-28",
                    -1.37
                ],
                [
                    "2025-04-29",
                    -2.32
                ],
                [
                    "2025-04-30",
                    6.24
                ],
                [
                    "2025-05-06",
                    2.17
                ],
                [
                    "2025-05-07",
                    0.09
                ],
                [
                    "2025-05-08",
                    -0.93
                ],
                [
                    "2025-05-09",
                    -2.11
                ],
                [
                    "2025-05-12",
                    -1.44
                ],
                [
                    "2025-05-13",
                    2.82
                ],
                [
                    "2025-05-14",
                    3.29
                ],
                [
                    "2025-05-15",
                    -1.30
                ],
                [
                    "2025-05-16",
                    -3.65
                ],
                [
                    "2025-05-19",
                    -0.18
                ],
                [
                    "2025-05-20",
                    -3.00
                ],
                [
                    "2025-05-21",
                    1.68
                ],
                [
                    "2025-05-22",
                    -3.75
                ],
                [
                    "2025-05-23",
                    -4.31
                ],
                [
                    "2025-05-26",
                    -6.04
                ],
                [
                    "2025-05-27",
                    -0.66
                ],
                [
                    "2025-05-28",
                    -3.50
                ],
                [
                    "2025-05-29",
                    -2.71
                ],
                [
                    "2025-05-30",
                    0.87
                ],
                [
                    "2025-06-03",
                    -7.12
                ],
                [
                    "2025-06-04",
                    0.08
                ],
                [
                    "2025-06-05",
                    0.40
                ],
                [
                    "2025-06-06",
                    3.58
                ],
                [
                    "2025-06-09",
                    1.62
                ],
                [
                    "2025-06-10",
                    -0.28
                ],
                [
                    "2025-06-11",
                    -2.29
                ],
                [
                    "2025-06-12",
                    -2.74
                ],
                [
                    "2025-06-13",
                    1.05
                ],
                [
                    "2025-06-16",
                    -5.44
                ],
                [
                    "2025-06-17",
                    1.38
                ],
                [
                    "2025-06-18",
                    0.15
                ],
                [
                    "2025-06-19",
                    -2.52
                ],
                [
                    "2025-06-20",
                    -6.32
                ],
                [
                    "2025-06-23",
                    -1.62
                ],
                [
                    "2025-06-24",
                    1.09
                ],
                [
                    "2025-06-25",
                    -0.42
                ],
                [
                    "2025-06-26",
                    0.49
                ],
                [
                    "2025-06-27",
                    1.07
                ],
                [
                    "2025-06-30",
                    -0.49
                ],
                [
                    "2025-07-01",
                    0.71
                ],
                [
                    "2025-07-02",
                    -1.12
                ],
                [
                    "2025-07-03",
                    -2.37
                ],
                [
                    "2025-07-04",
                    -3.58
                ],
                [
                    "2025-07-07",
                    1.18
                ],
                [
                    "2025-07-08",
                    -1.61
                ],
                [
                    "2025-07-09",
                    -1.88
                ],
                [
                    "2025-07-10",
                    -2.07
                ],
                [
                    "2025-07-11",
                    -4.69
                ],
                [
                    "2025-07-14",
                    2.96
                ],
                [
                    "2025-07-15",
                    -0.77
                ],
                [
                    "2025-07-16",
                    2.47
                ],
                [
                    "2025-07-17",
                    -0.25
                ],
                [
                    "2025-07-18",
                    -5.86
                ],
                [
                    "2025-07-21",
                    2.79
                ],
                [
                    "2025-07-22",
                    -1.77
                ],
                [
                    "2025-07-23",
                    -4.08
                ],
                [
                    "2025-07-24",
                    -3.36
                ],
                [
                    "2025-07-25",
                    -0.71
                ],
                [
                    "2025-07-28",
                    5.28
                ],
                [
                    "2025-07-29",
                    2.90
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -0.74
                ],
                [
                    "2025-02-05",
                    -14.94
                ],
                [
                    "2025-02-06",
                    2.97
                ],
                [
                    "2025-02-07",
                    -4.23
                ],
                [
                    "2025-02-10",
                    2.15
                ],
                [
                    "2025-02-11",
                    0.43
                ],
                [
                    "2025-02-12",
                    5.63
                ],
                [
                    "2025-02-13",
                    1.91
                ],
                [
                    "2025-02-14",
                    -8.04
                ],
                [
                    "2025-02-17",
                    5.20
                ],
                [
                    "2025-02-18",
                    8.62
                ],
                [
                    "2025-02-19",
                    7.71
                ],
                [
                    "2025-02-20",
                    6.47
                ],
                [
                    "2025-02-21",
                    -5.98
                ],
                [
                    "2025-02-24",
                    -18.47
                ],
                [
                    "2025-02-25",
                    5.54
                ],
                [
                    "2025-02-26",
                    5.19
                ],
                [
                    "2025-02-27",
                    10.26
                ],
                [
                    "2025-02-28",
                    11.02
                ],
                [
                    "2025-03-03",
                    4.12
                ],
                [
                    "2025-03-04",
                    0.46
                ],
                [
                    "2025-03-05",
                    0.66
                ],
                [
                    "2025-03-06",
                    0.30
                ],
                [
                    "2025-03-07",
                    -8.51
                ],
                [
                    "2025-03-10",
                    4.13
                ],
                [
                    "2025-03-11",
                    1.56
                ],
                [
                    "2025-03-12",
                    6.71
                ],
                [
                    "2025-03-13",
                    12.97
                ],
                [
                    "2025-03-14",
                    5.61
                ],
                [
                    "2025-03-17",
                    -2.41
                ],
                [
                    "2025-03-18",
                    9.32
                ],
                [
                    "2025-03-19",
                    4.67
                ],
                [
                    "2025-03-20",
                    -4.30
                ],
                [
                    "2025-03-21",
                    13.64
                ],
                [
                    "2025-03-24",
                    15.51
                ],
                [
                    "2025-03-25",
                    13.80
                ],
                [
                    "2025-03-26",
                    1.90
                ],
                [
                    "2025-03-27",
                    0.47
                ],
                [
                    "2025-03-28",
                    -0.30
                ],
                [
                    "2025-03-31",
                    -3.41
                ],
                [
                    "2025-04-01",
                    5.01
                ],
                [
                    "2025-04-02",
                    3.66
                ],
                [
                    "2025-04-03",
                    -1.95
                ],
                [
                    "2025-04-07",
                    12.74
                ],
                [
                    "2025-04-08",
                    11.45
                ],
                [
                    "2025-04-09",
                    3.75
                ],
                [
                    "2025-04-10",
                    3.68
                ],
                [
                    "2025-04-11",
                    4.22
                ],
                [
                    "2025-04-14",
                    8.06
                ],
                [
                    "2025-04-15",
                    -0.61
                ],
                [
                    "2025-04-16",
                    17.35
                ],
                [
                    "2025-04-17",
                    4.42
                ],
                [
                    "2025-04-18",
                    -7.57
                ],
                [
                    "2025-04-21",
                    -7.61
                ],
                [
                    "2025-04-22",
                    2.64
                ],
                [
                    "2025-04-23",
                    -1.61
                ],
                [
                    "2025-04-24",
                    4.50
                ],
                [
                    "2025-04-25",
                    -3.08
                ],
                [
                    "2025-04-28",
                    4.25
                ],
                [
                    "2025-04-29",
                    3.69
                ],
                [
                    "2025-04-30",
                    0.39
                ],
                [
                    "2025-05-06",
                    -5.63
                ],
                [
                    "2025-05-07",
                    6.60
                ],
                [
                    "2025-05-08",
                    9.61
                ],
                [
                    "2025-05-09",
                    13.54
                ],
                [
                    "2025-05-12",
                    4.68
                ],
                [
                    "2025-05-13",
                    11.47
                ],
                [
                    "2025-05-14",
                    13.05
                ],
                [
                    "2025-05-15",
                    22.01
                ],
                [
                    "2025-05-16",
                    3.27
                ],
                [
                    "2025-05-19",
                    2.30
                ],
                [
                    "2025-05-20",
                    3.48
                ],
                [
                    "2025-05-21",
                    9.70
                ],
                [
                    "2025-05-22",
                    10.83
                ],
                [
                    "2025-05-23",
                    11.75
                ],
                [
                    "2025-05-26",
                    4.63
                ],
                [
                    "2025-05-27",
                    8.60
                ],
                [
                    "2025-05-28",
                    9.62
                ],
                [
                    "2025-05-29",
                    0.60
                ],
                [
                    "2025-05-30",
                    12.33
                ],
                [
                    "2025-06-03",
                    9.48
                ],
                [
                    "2025-06-04",
                    -3.67
                ],
                [
                    "2025-06-05",
                    -8.99
                ],
                [
                    "2025-06-06",
                    2.02
                ],
                [
                    "2025-06-09",
                    2.05
                ],
                [
                    "2025-06-10",
                    13.59
                ],
                [
                    "2025-06-11",
                    12.05
                ],
                [
                    "2025-06-12",
                    4.46
                ],
                [
                    "2025-06-13",
                    20.42
                ],
                [
                    "2025-06-16",
                    16.15
                ],
                [
                    "2025-06-17",
                    14.45
                ],
                [
                    "2025-06-18",
                    4.03
                ],
                [
                    "2025-06-19",
                    12.02
                ],
                [
                    "2025-06-20",
                    21.57
                ],
                [
                    "2025-06-23",
                    6.35
                ],
                [
                    "2025-06-24",
                    -7.88
                ],
                [
                    "2025-06-25",
                    1.63
                ],
                [
                    "2025-06-26",
                    4.72
                ],
                [
                    "2025-06-27",
                    11.03
                ],
                [
                    "2025-06-30",
                    -2.47
                ],
                [
                    "2025-07-01",
                    -0.68
                ],
                [
                    "2025-07-02",
                    13.00
                ],
                [
                    "2025-07-03",
                    4.45
                ],
                [
                    "2025-07-04",
                    -8.21
                ],
                [
                    "2025-07-07",
                    11.35
                ],
                [
                    "2025-07-08",
                    -1.67
                ],
                [
                    "2025-07-09",
                    5.40
                ],
                [
                    "2025-07-10",
                    -1.04
                ],
                [
                    "2025-07-11",
                    -6.36
                ],
                [
                    "2025-07-14",
                    2.64
                ],
                [
                    "2025-07-15",
                    -4.89
                ],
                [
                    "2025-07-16",
                    7.79
                ],
                [
                    "2025-07-17",
                    2.67
                ],
                [
                    "2025-07-18",
                    -8.63
                ],
                [
                    "2025-07-21",
                    15.15
                ],
                [
                    "2025-07-22",
                    7.92
                ],
                [
                    "2025-07-23",
                    -4.73
                ],
                [
                    "2025-07-24",
                    -4.32
                ],
                [
                    "2025-07-25",
                    -0.56
                ],
                [
                    "2025-07-28",
                    8.68
                ],
                [
                    "2025-07-29",
                    12.09
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    -3.42
                ],
                [
                    "2025-02-11",
                    -1.53
                ],
                [
                    "2025-02-24",
                    39.77
                ],
                [
                    "2025-02-25",
                    -6.68
                ],
                [
                    "2025-02-26",
                    -6.68
                ],
                [
                    "2025-02-27",
                    -11.17
                ],
                [
                    "2025-03-07",
                    21.76
                ],
                [
                    "2025-03-10",
                    -5.75
                ],
                [
                    "2025-03-11",
                    -0.90
                ],
                [
                    "2025-04-23",
                    4.11
                ],
                [
                    "2025-04-24",
                    -10.61
                ],
                [
                    "2025-04-25",
                    10.74
                ],
                [
                    "2025-05-06",
                    3.46
                ],
                [
                    "2025-07-14",
                    -5.60
                ],
                [
                    "2025-07-15",
                    5.66
                ],
                [
                    "2025-07-16",
                    -10.26
                ],
                [
                    "2025-07-18",
                    14.48
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-04-24",
                    6.11
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-12",
                    5.63
                ],
                [
                    "2025-02-13",
                    1.91
                ],
                [
                    "2025-02-17",
                    5.20
                ],
                [
                    "2025-02-18",
                    8.62
                ],
                [
                    "2025-02-19",
                    7.71
                ],
                [
                    "2025-02-20",
                    6.47
                ],
                [
                    "2025-02-21",
                    -5.98
                ],
                [
                    "2025-03-03",
                    4.12
                ],
                [
                    "2025-03-04",
                    0.46
                ],
                [
                    "2025-03-05",
                    0.66
                ],
                [
                    "2025-03-06",
                    0.30
                ],
                [
                    "2025-03-13",
                    12.97
                ],
                [
                    "2025-03-14",
                    5.61
                ],
                [
                    "2025-03-17",
                    -2.41
                ],
                [
                    "2025-03-18",
                    9.32
                ],
                [
                    "2025-03-19",
                    4.67
                ],
                [
                    "2025-03-20",
                    -4.30
                ],
                [
                    "2025-03-21",
                    13.64
                ],
                [
                    "2025-03-24",
                    15.51
                ],
                [
                    "2025-03-25",
                    13.80
                ],
                [
                    "2025-03-26",
                    1.90
                ],
                [
                    "2025-03-27",
                    0.47
                ],
                [
                    "2025-03-28",
                    -0.30
                ],
                [
                    "2025-03-31",
                    -3.41
                ],
                [
                    "2025-04-01",
                    5.01
                ],
                [
                    "2025-04-02",
                    3.66
                ],
                [
                    "2025-04-07",
                    12.74
                ],
                [
                    "2025-04-08",
                    11.45
                ],
                [
                    "2025-04-09",
                    3.75
                ],
                [
                    "2025-04-10",
                    3.68
                ],
                [
                    "2025-04-11",
                    4.22
                ],
                [
                    "2025-04-14",
                    8.06
                ],
                [
                    "2025-04-15",
                    -0.61
                ],
                [
                    "2025-04-16",
                    17.35
                ],
                [
                    "2025-04-17",
                    4.42
                ],
                [
                    "2025-04-18",
                    -7.57
                ],
                [
                    "2025-04-22",
                    2.64
                ],
                [
                    "2025-04-28",
                    4.25
                ],
                [
                    "2025-04-29",
                    3.69
                ],
                [
                    "2025-04-30",
                    0.39
                ],
                [
                    "2025-05-07",
                    6.60
                ],
                [
                    "2025-05-08",
                    9.61
                ],
                [
                    "2025-05-09",
                    13.54
                ],
                [
                    "2025-05-12",
                    4.68
                ],
                [
                    "2025-05-13",
                    11.47
                ],
                [
                    "2025-05-14",
                    13.05
                ],
                [
                    "2025-05-15",
                    22.01
                ],
                [
                    "2025-05-16",
                    3.27
                ],
                [
                    "2025-05-19",
                    2.30
                ],
                [
                    "2025-05-20",
                    3.48
                ],
                [
                    "2025-05-21",
                    9.70
                ],
                [
                    "2025-05-22",
                    10.83
                ],
                [
                    "2025-05-23",
                    11.75
                ],
                [
                    "2025-05-26",
                    4.63
                ],
                [
                    "2025-05-27",
                    8.60
                ],
                [
                    "2025-05-28",
                    9.62
                ],
                [
                    "2025-05-29",
                    0.60
                ],
                [
                    "2025-05-30",
                    12.33
                ],
                [
                    "2025-06-03",
                    9.48
                ],
                [
                    "2025-06-04",
                    -3.67
                ],
                [
                    "2025-06-05",
                    -8.99
                ],
                [
                    "2025-06-06",
                    2.02
                ],
                [
                    "2025-06-10",
                    13.59
                ],
                [
                    "2025-06-11",
                    12.05
                ],
                [
                    "2025-06-12",
                    4.46
                ],
                [
                    "2025-06-13",
                    20.42
                ],
                [
                    "2025-06-16",
                    16.15
                ],
                [
                    "2025-06-17",
                    14.45
                ],
                [
                    "2025-06-18",
                    4.03
                ],
                [
                    "2025-06-19",
                    12.02
                ],
                [
                    "2025-06-20",
                    21.57
                ],
                [
                    "2025-06-23",
                    6.35
                ],
                [
                    "2025-06-24",
                    -7.88
                ],
                [
                    "2025-06-25",
                    1.63
                ],
                [
                    "2025-06-26",
                    4.72
                ],
                [
                    "2025-06-27",
                    11.03
                ],
                [
                    "2025-06-30",
                    -2.47
                ],
                [
                    "2025-07-01",
                    -0.68
                ],
                [
                    "2025-07-02",
                    13.00
                ],
                [
                    "2025-07-03",
                    4.45
                ],
                [
                    "2025-07-07",
                    11.35
                ],
                [
                    "2025-07-08",
                    -1.67
                ],
                [
                    "2025-07-09",
                    5.40
                ],
                [
                    "2025-07-17",
                    2.67
                ],
                [
                    "2025-07-21",
                    15.15
                ],
                [
                    "2025-07-22",
                    7.92
                ],
                [
                    "2025-07-23",
                    -4.73
                ],
                [
                    "2025-07-25",
                    -0.56
                ],
                [
                    "2025-07-28",
                    8.68
                ],
                [
                    "2025-07-29",
                    12.09
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600602 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_dcce1ec6a99044da82f17b152df9c206.setOption(option_dcce1ec6a99044da82f17b152df9c206);
            window.addEventListener('resize', function(){
                chart_dcce1ec6a99044da82f17b152df9c206.resize();
            })
    </script>
</body>
</html>
