<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="24723104adb94696a44fc3f367b9ef31" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_24723104adb94696a44fc3f367b9ef31 = echarts.init(
            document.getElementById('24723104adb94696a44fc3f367b9ef31'), 'white', {renderer: 'canvas'});
            
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        
        var option_24723104adb94696a44fc3f367b9ef31 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    8.43,
                    8.22,
                    8.22,
                    8.43
                ],
                [
                    8.27,
                    8.21,
                    8.2,
                    8.31
                ],
                [
                    8.22,
                    8.34,
                    8.18,
                    8.36
                ],
                [
                    8.37,
                    8.83,
                    8.36,
                    9.14
                ],
                [
                    8.71,
                    8.89,
                    8.71,
                    8.92
                ],
                [
                    8.9,
                    8.81,
                    8.71,
                    8.91
                ],
                [
                    8.77,
                    8.97,
                    8.76,
                    9.0
                ],
                [
                    8.97,
                    8.87,
                    8.83,
                    8.99
                ],
                [
                    8.82,
                    8.84,
                    8.77,
                    8.9
                ],
                [
                    8.95,
                    8.85,
                    8.81,
                    8.96
                ],
                [
                    8.82,
                    8.64,
                    8.58,
                    8.83
                ],
                [
                    8.61,
                    8.65,
                    8.57,
                    8.71
                ],
                [
                    8.63,
                    8.61,
                    8.56,
                    8.68
                ],
                [
                    8.65,
                    8.9,
                    8.58,
                    8.99
                ],
                [
                    8.89,
                    8.76,
                    8.7,
                    8.89
                ],
                [
                    8.65,
                    8.65,
                    8.63,
                    8.72
                ],
                [
                    8.66,
                    8.85,
                    8.62,
                    8.86
                ],
                [
                    8.77,
                    8.79,
                    8.63,
                    8.88
                ],
                [
                    8.74,
                    8.52,
                    8.5,
                    8.88
                ],
                [
                    8.52,
                    8.48,
                    8.44,
                    8.61
                ],
                [
                    8.45,
                    8.55,
                    8.41,
                    8.58
                ],
                [
                    8.56,
                    8.54,
                    8.42,
                    8.56
                ],
                [
                    8.6,
                    8.68,
                    8.57,
                    8.71
                ],
                [
                    8.63,
                    8.55,
                    8.53,
                    8.65
                ],
                [
                    8.53,
                    8.54,
                    8.46,
                    8.56
                ],
                [
                    8.46,
                    8.57,
                    8.44,
                    8.58
                ],
                [
                    8.61,
                    8.61,
                    8.56,
                    8.73
                ],
                [
                    8.62,
                    8.62,
                    8.54,
                    8.7
                ],
                [
                    8.63,
                    8.83,
                    8.61,
                    8.91
                ],
                [
                    8.83,
                    8.77,
                    8.74,
                    8.84
                ],
                [
                    8.8,
                    8.77,
                    8.74,
                    8.85
                ],
                [
                    8.76,
                    8.78,
                    8.72,
                    8.82
                ],
                [
                    8.76,
                    8.69,
                    8.66,
                    8.78
                ],
                [
                    8.68,
                    8.57,
                    8.53,
                    8.72
                ],
                [
                    8.55,
                    8.54,
                    8.45,
                    8.58
                ],
                [
                    8.54,
                    8.6,
                    8.5,
                    8.61
                ],
                [
                    8.57,
                    8.54,
                    8.54,
                    8.62
                ],
                [
                    8.54,
                    8.54,
                    8.47,
                    8.6
                ],
                [
                    8.54,
                    8.5,
                    8.47,
                    8.58
                ],
                [
                    8.47,
                    8.36,
                    8.32,
                    8.52
                ],
                [
                    8.36,
                    8.42,
                    8.35,
                    8.46
                ],
                [
                    8.4,
                    8.49,
                    8.39,
                    8.52
                ],
                [
                    8.45,
                    8.48,
                    8.42,
                    8.54
                ],
                [
                    8.04,
                    7.63,
                    7.63,
                    8.22
                ],
                [
                    7.68,
                    7.73,
                    7.61,
                    7.83
                ],
                [
                    7.7,
                    7.85,
                    7.54,
                    7.94
                ],
                [
                    7.94,
                    7.97,
                    7.86,
                    8.06
                ],
                [
                    7.95,
                    7.94,
                    7.9,
                    8.02
                ],
                [
                    7.99,
                    7.97,
                    7.93,
                    8.01
                ],
                [
                    7.97,
                    7.95,
                    7.89,
                    7.97
                ],
                [
                    7.92,
                    7.9,
                    7.79,
                    7.94
                ],
                [
                    7.88,
                    7.88,
                    7.85,
                    7.93
                ],
                [
                    7.88,
                    7.94,
                    7.85,
                    7.97
                ],
                [
                    7.92,
                    7.97,
                    7.87,
                    7.99
                ],
                [
                    7.97,
                    7.97,
                    7.95,
                    8.04
                ],
                [
                    8.0,
                    7.96,
                    7.94,
                    8.04
                ],
                [
                    7.96,
                    7.92,
                    7.88,
                    7.99
                ],
                [
                    8.04,
                    8.19,
                    8.02,
                    8.29
                ],
                [
                    8.16,
                    8.15,
                    8.07,
                    8.2
                ],
                [
                    8.13,
                    8.13,
                    8.11,
                    8.22
                ],
                [
                    8.13,
                    8.12,
                    8.11,
                    8.19
                ],
                [
                    8.16,
                    8.2,
                    8.13,
                    8.22
                ],
                [
                    8.35,
                    8.21,
                    8.19,
                    8.51
                ],
                [
                    8.19,
                    8.23,
                    8.15,
                    8.27
                ],
                [
                    8.23,
                    8.12,
                    8.07,
                    8.24
                ],
                [
                    8.18,
                    8.3,
                    8.16,
                    8.3
                ],
                [
                    8.39,
                    8.29,
                    8.26,
                    8.39
                ],
                [
                    8.33,
                    8.51,
                    8.27,
                    8.64
                ],
                [
                    8.45,
                    8.43,
                    8.38,
                    8.51
                ],
                [
                    8.41,
                    8.37,
                    8.34,
                    8.43
                ],
                [
                    8.38,
                    8.35,
                    8.31,
                    8.39
                ],
                [
                    8.38,
                    8.36,
                    8.32,
                    8.39
                ],
                [
                    8.36,
                    8.36,
                    8.34,
                    8.39
                ],
                [
                    8.33,
                    8.31,
                    8.29,
                    8.35
                ],
                [
                    8.29,
                    8.23,
                    8.23,
                    8.36
                ],
                [
                    8.22,
                    8.22,
                    8.17,
                    8.24
                ],
                [
                    8.22,
                    8.23,
                    8.17,
                    8.25
                ],
                [
                    8.23,
                    8.19,
                    8.18,
                    8.25
                ],
                [
                    8.21,
                    8.34,
                    8.2,
                    8.34
                ],
                [
                    8.29,
                    8.33,
                    8.25,
                    8.35
                ],
                [
                    8.28,
                    8.38,
                    8.27,
                    8.41
                ],
                [
                    8.36,
                    8.47,
                    8.35,
                    8.5
                ],
                [
                    8.47,
                    8.51,
                    8.45,
                    8.52
                ],
                [
                    8.52,
                    8.48,
                    8.45,
                    8.53
                ],
                [
                    8.55,
                    8.54,
                    8.5,
                    8.61
                ],
                [
                    8.56,
                    8.44,
                    8.39,
                    8.56
                ],
                [
                    8.48,
                    8.55,
                    8.44,
                    8.65
                ],
                [
                    8.51,
                    8.55,
                    8.5,
                    8.59
                ],
                [
                    8.52,
                    8.46,
                    8.42,
                    8.55
                ],
                [
                    8.43,
                    8.54,
                    8.42,
                    8.55
                ],
                [
                    8.54,
                    8.55,
                    8.5,
                    8.56
                ],
                [
                    8.54,
                    8.44,
                    8.36,
                    8.56
                ],
                [
                    8.41,
                    8.34,
                    8.29,
                    8.44
                ],
                [
                    8.32,
                    8.3,
                    8.29,
                    8.38
                ],
                [
                    8.27,
                    8.37,
                    8.24,
                    8.41
                ],
                [
                    8.4,
                    8.63,
                    8.38,
                    8.63
                ],
                [
                    8.63,
                    8.89,
                    8.61,
                    8.93
                ],
                [
                    8.96,
                    8.81,
                    8.78,
                    9.0
                ],
                [
                    8.82,
                    8.77,
                    8.76,
                    9.04
                ],
                [
                    8.79,
                    8.77,
                    8.7,
                    8.81
                ],
                [
                    8.78,
                    8.73,
                    8.67,
                    8.79
                ],
                [
                    8.73,
                    8.77,
                    8.71,
                    8.81
                ],
                [
                    8.78,
                    8.87,
                    8.75,
                    8.89
                ],
                [
                    8.87,
                    8.83,
                    8.77,
                    8.95
                ],
                [
                    8.8,
                    8.85,
                    8.78,
                    8.89
                ],
                [
                    9.11,
                    9.2,
                    9.02,
                    9.3
                ],
                [
                    9.0,
                    9.02,
                    8.98,
                    9.09
                ],
                [
                    8.97,
                    9.08,
                    8.95,
                    9.15
                ],
                [
                    9.14,
                    9.56,
                    9.13,
                    9.86
                ],
                [
                    9.68,
                    9.43,
                    9.35,
                    9.68
                ],
                [
                    9.4,
                    9.32,
                    9.28,
                    9.47
                ],
                [
                    9.35,
                    9.28,
                    9.23,
                    9.35
                ],
                [
                    9.25,
                    9.35,
                    9.22,
                    9.35
                ],
                [
                    9.33,
                    9.34,
                    9.29,
                    9.37
                ],
                [
                    9.34,
                    9.39,
                    9.3,
                    9.4
                ],
                [
                    9.39,
                    9.39,
                    9.27,
                    9.41
                ],
                [
                    9.44,
                    9.39,
                    9.37,
                    9.59
                ],
                [
                    9.35,
                    9.65,
                    9.33,
                    9.66
                ],
                [
                    9.65,
                    9.56,
                    9.54,
                    9.68
                ],
                [
                    9.59,
                    9.58,
                    9.49,
                    9.7
                ],
                [
                    9.53,
                    9.61,
                    9.41,
                    9.62
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -5.29
                ],
                [
                    "2025-02-05",
                    -12.94
                ],
                [
                    "2025-02-06",
                    -0.95
                ],
                [
                    "2025-02-07",
                    10.56
                ],
                [
                    "2025-02-10",
                    -11.33
                ],
                [
                    "2025-02-11",
                    -3.09
                ],
                [
                    "2025-02-12",
                    6.95
                ],
                [
                    "2025-02-13",
                    -6.45
                ],
                [
                    "2025-02-14",
                    -21.06
                ],
                [
                    "2025-02-17",
                    -6.89
                ],
                [
                    "2025-02-18",
                    -5.14
                ],
                [
                    "2025-02-19",
                    -15.21
                ],
                [
                    "2025-02-20",
                    -0.82
                ],
                [
                    "2025-02-21",
                    5.13
                ],
                [
                    "2025-02-24",
                    -7.58
                ],
                [
                    "2025-02-25",
                    -9.42
                ],
                [
                    "2025-02-26",
                    7.44
                ],
                [
                    "2025-02-27",
                    0.26
                ],
                [
                    "2025-02-28",
                    -8.09
                ],
                [
                    "2025-03-03",
                    -4.35
                ],
                [
                    "2025-03-04",
                    6.51
                ],
                [
                    "2025-03-05",
                    -5.06
                ],
                [
                    "2025-03-06",
                    5.01
                ],
                [
                    "2025-03-07",
                    -13.09
                ],
                [
                    "2025-03-10",
                    -4.96
                ],
                [
                    "2025-03-11",
                    6.03
                ],
                [
                    "2025-03-12",
                    2.23
                ],
                [
                    "2025-03-13",
                    1.85
                ],
                [
                    "2025-03-14",
                    6.22
                ],
                [
                    "2025-03-17",
                    -15.96
                ],
                [
                    "2025-03-18",
                    -5.84
                ],
                [
                    "2025-03-19",
                    -4.59
                ],
                [
                    "2025-03-20",
                    -17.11
                ],
                [
                    "2025-03-21",
                    -15.90
                ],
                [
                    "2025-03-24",
                    -5.68
                ],
                [
                    "2025-03-25",
                    -0.68
                ],
                [
                    "2025-03-26",
                    -2.92
                ],
                [
                    "2025-03-27",
                    -14.07
                ],
                [
                    "2025-03-28",
                    -0.36
                ],
                [
                    "2025-03-31",
                    -12.50
                ],
                [
                    "2025-04-01",
                    -1.47
                ],
                [
                    "2025-04-02",
                    -2.23
                ],
                [
                    "2025-04-03",
                    -10.09
                ],
                [
                    "2025-04-07",
                    -6.29
                ],
                [
                    "2025-04-08",
                    -9.26
                ],
                [
                    "2025-04-09",
                    -2.31
                ],
                [
                    "2025-04-10",
                    -4.42
                ],
                [
                    "2025-04-11",
                    -0.73
                ],
                [
                    "2025-04-14",
                    -1.49
                ],
                [
                    "2025-04-15",
                    2.90
                ],
                [
                    "2025-04-16",
                    0.16
                ],
                [
                    "2025-04-17",
                    -3.42
                ],
                [
                    "2025-04-18",
                    -9.25
                ],
                [
                    "2025-04-21",
                    0.15
                ],
                [
                    "2025-04-22",
                    3.89
                ],
                [
                    "2025-04-23",
                    1.07
                ],
                [
                    "2025-04-24",
                    -10.84
                ],
                [
                    "2025-04-25",
                    3.05
                ],
                [
                    "2025-04-28",
                    -12.16
                ],
                [
                    "2025-04-29",
                    7.59
                ],
                [
                    "2025-04-30",
                    0.79
                ],
                [
                    "2025-05-06",
                    -0.32
                ],
                [
                    "2025-05-07",
                    7.38
                ],
                [
                    "2025-05-08",
                    -12.05
                ],
                [
                    "2025-05-09",
                    -18.62
                ],
                [
                    "2025-05-12",
                    3.53
                ],
                [
                    "2025-05-13",
                    -15.09
                ],
                [
                    "2025-05-14",
                    5.33
                ],
                [
                    "2025-05-15",
                    -12.60
                ],
                [
                    "2025-05-16",
                    -4.37
                ],
                [
                    "2025-05-19",
                    2.55
                ],
                [
                    "2025-05-20",
                    5.76
                ],
                [
                    "2025-05-21",
                    -14.99
                ],
                [
                    "2025-05-22",
                    -14.06
                ],
                [
                    "2025-05-23",
                    -10.90
                ],
                [
                    "2025-05-26",
                    -0.97
                ],
                [
                    "2025-05-27",
                    7.36
                ],
                [
                    "2025-05-28",
                    6.15
                ],
                [
                    "2025-05-29",
                    0.38
                ],
                [
                    "2025-05-30",
                    -16.46
                ],
                [
                    "2025-06-03",
                    -7.92
                ],
                [
                    "2025-06-04",
                    -0.28
                ],
                [
                    "2025-06-05",
                    -3.27
                ],
                [
                    "2025-06-06",
                    -1.27
                ],
                [
                    "2025-06-09",
                    -1.73
                ],
                [
                    "2025-06-10",
                    -13.54
                ],
                [
                    "2025-06-11",
                    9.40
                ],
                [
                    "2025-06-12",
                    -6.86
                ],
                [
                    "2025-06-13",
                    -14.76
                ],
                [
                    "2025-06-16",
                    -3.85
                ],
                [
                    "2025-06-17",
                    -7.20
                ],
                [
                    "2025-06-18",
                    -7.05
                ],
                [
                    "2025-06-19",
                    -11.02
                ],
                [
                    "2025-06-20",
                    0.86
                ],
                [
                    "2025-06-23",
                    -17.06
                ],
                [
                    "2025-06-24",
                    2.45
                ],
                [
                    "2025-06-25",
                    6.34
                ],
                [
                    "2025-06-26",
                    -10.79
                ],
                [
                    "2025-06-27",
                    2.30
                ],
                [
                    "2025-06-30",
                    -2.78
                ],
                [
                    "2025-07-01",
                    -5.75
                ],
                [
                    "2025-07-02",
                    -10.48
                ],
                [
                    "2025-07-03",
                    3.99
                ],
                [
                    "2025-07-04",
                    1.17
                ],
                [
                    "2025-07-07",
                    -10.53
                ],
                [
                    "2025-07-08",
                    6.69
                ],
                [
                    "2025-07-09",
                    -11.44
                ],
                [
                    "2025-07-10",
                    0.05
                ],
                [
                    "2025-07-11",
                    12.75
                ],
                [
                    "2025-07-14",
                    -7.16
                ],
                [
                    "2025-07-15",
                    3.91
                ],
                [
                    "2025-07-16",
                    -4.28
                ],
                [
                    "2025-07-17",
                    7.84
                ],
                [
                    "2025-07-18",
                    2.39
                ],
                [
                    "2025-07-21",
                    -18.29
                ],
                [
                    "2025-07-22",
                    -2.54
                ],
                [
                    "2025-07-23",
                    -0.96
                ],
                [
                    "2025-07-24",
                    -6.25
                ],
                [
                    "2025-07-25",
                    -8.51
                ],
                [
                    "2025-07-28",
                    2.75
                ],
                [
                    "2025-07-29",
                    -5.88
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    11.59
                ],
                [
                    "2025-02-05",
                    5.31
                ],
                [
                    "2025-02-06",
                    -3.81
                ],
                [
                    "2025-02-07",
                    -5.29
                ],
                [
                    "2025-02-10",
                    1.93
                ],
                [
                    "2025-02-11",
                    1.48
                ],
                [
                    "2025-02-12",
                    -4.77
                ],
                [
                    "2025-02-13",
                    3.03
                ],
                [
                    "2025-02-14",
                    5.45
                ],
                [
                    "2025-02-17",
                    7.85
                ],
                [
                    "2025-02-18",
                    2.68
                ],
                [
                    "2025-02-19",
                    11.46
                ],
                [
                    "2025-02-20",
                    2.77
                ],
                [
                    "2025-02-21",
                    0.12
                ],
                [
                    "2025-02-24",
                    2.09
                ],
                [
                    "2025-02-25",
                    4.71
                ],
                [
                    "2025-02-26",
                    -0.33
                ],
                [
                    "2025-02-27",
                    0.25
                ],
                [
                    "2025-02-28",
                    7.09
                ],
                [
                    "2025-03-03",
                    0.93
                ],
                [
                    "2025-03-04",
                    -7.92
                ],
                [
                    "2025-03-05",
                    4.88
                ],
                [
                    "2025-03-06",
                    -2.02
                ],
                [
                    "2025-03-07",
                    7.30
                ],
                [
                    "2025-03-10",
                    4.96
                ],
                [
                    "2025-03-11",
                    -3.40
                ],
                [
                    "2025-03-12",
                    1.86
                ],
                [
                    "2025-03-13",
                    -2.89
                ],
                [
                    "2025-03-14",
                    -2.82
                ],
                [
                    "2025-03-17",
                    8.78
                ],
                [
                    "2025-03-18",
                    2.12
                ],
                [
                    "2025-03-19",
                    -1.99
                ],
                [
                    "2025-03-20",
                    7.99
                ],
                [
                    "2025-03-21",
                    5.02
                ],
                [
                    "2025-03-24",
                    -0.01
                ],
                [
                    "2025-03-25",
                    -0.08
                ],
                [
                    "2025-03-26",
                    4.48
                ],
                [
                    "2025-03-27",
                    5.12
                ],
                [
                    "2025-03-28",
                    2.68
                ],
                [
                    "2025-03-31",
                    5.65
                ],
                [
                    "2025-04-01",
                    -8.92
                ],
                [
                    "2025-04-02",
                    -6.70
                ],
                [
                    "2025-04-03",
                    2.20
                ],
                [
                    "2025-04-07",
                    -1.82
                ],
                [
                    "2025-04-08",
                    -1.79
                ],
                [
                    "2025-04-09",
                    -2.72
                ],
                [
                    "2025-04-10",
                    -3.46
                ],
                [
                    "2025-04-11",
                    -1.82
                ],
                [
                    "2025-04-14",
                    3.25
                ],
                [
                    "2025-04-15",
                    4.80
                ],
                [
                    "2025-04-16",
                    -4.23
                ],
                [
                    "2025-04-17",
                    -1.67
                ],
                [
                    "2025-04-18",
                    1.16
                ],
                [
                    "2025-04-21",
                    -8.98
                ],
                [
                    "2025-04-22",
                    -0.29
                ],
                [
                    "2025-04-23",
                    -3.71
                ],
                [
                    "2025-04-24",
                    2.82
                ],
                [
                    "2025-04-25",
                    -3.73
                ],
                [
                    "2025-04-28",
                    -0.20
                ],
                [
                    "2025-04-29",
                    -0.99
                ],
                [
                    "2025-04-30",
                    1.76
                ],
                [
                    "2025-05-06",
                    -0.52
                ],
                [
                    "2025-05-07",
                    2.26
                ],
                [
                    "2025-05-08",
                    0.58
                ],
                [
                    "2025-05-09",
                    13.72
                ],
                [
                    "2025-05-12",
                    -7.58
                ],
                [
                    "2025-05-13",
                    8.59
                ],
                [
                    "2025-05-14",
                    -4.32
                ],
                [
                    "2025-05-15",
                    2.28
                ],
                [
                    "2025-05-16",
                    -2.30
                ],
                [
                    "2025-05-19",
                    0.64
                ],
                [
                    "2025-05-20",
                    -3.36
                ],
                [
                    "2025-05-21",
                    2.61
                ],
                [
                    "2025-05-22",
                    6.05
                ],
                [
                    "2025-05-23",
                    0.97
                ],
                [
                    "2025-05-26",
                    7.29
                ],
                [
                    "2025-05-27",
                    0.31
                ],
                [
                    "2025-05-28",
                    2.06
                ],
                [
                    "2025-05-29",
                    -10.77
                ],
                [
                    "2025-05-30",
                    4.40
                ],
                [
                    "2025-06-03",
                    1.94
                ],
                [
                    "2025-06-04",
                    -5.07
                ],
                [
                    "2025-06-05",
                    -0.64
                ],
                [
                    "2025-06-06",
                    2.53
                ],
                [
                    "2025-06-09",
                    -2.51
                ],
                [
                    "2025-06-10",
                    3.65
                ],
                [
                    "2025-06-11",
                    -5.48
                ],
                [
                    "2025-06-12",
                    2.96
                ],
                [
                    "2025-06-13",
                    6.43
                ],
                [
                    "2025-06-16",
                    -1.45
                ],
                [
                    "2025-06-17",
                    -1.88
                ],
                [
                    "2025-06-18",
                    10.83
                ],
                [
                    "2025-06-19",
                    2.62
                ],
                [
                    "2025-06-20",
                    -2.46
                ],
                [
                    "2025-06-23",
                    -2.27
                ],
                [
                    "2025-06-24",
                    -6.23
                ],
                [
                    "2025-06-25",
                    -3.67
                ],
                [
                    "2025-06-26",
                    4.61
                ],
                [
                    "2025-06-27",
                    -3.45
                ],
                [
                    "2025-06-30",
                    1.57
                ],
                [
                    "2025-07-01",
                    13.57
                ],
                [
                    "2025-07-02",
                    -0.57
                ],
                [
                    "2025-07-03",
                    -0.28
                ],
                [
                    "2025-07-04",
                    -2.31
                ],
                [
                    "2025-07-07",
                    -3.97
                ],
                [
                    "2025-07-08",
                    -0.03
                ],
                [
                    "2025-07-09",
                    2.22
                ],
                [
                    "2025-07-10",
                    -0.23
                ],
                [
                    "2025-07-11",
                    -4.64
                ],
                [
                    "2025-07-14",
                    2.99
                ],
                [
                    "2025-07-15",
                    0.07
                ],
                [
                    "2025-07-16",
                    2.45
                ],
                [
                    "2025-07-17",
                    -9.93
                ],
                [
                    "2025-07-18",
                    4.94
                ],
                [
                    "2025-07-21",
                    13.72
                ],
                [
                    "2025-07-22",
                    0.06
                ],
                [
                    "2025-07-23",
                    0.25
                ],
                [
                    "2025-07-24",
                    -0.48
                ],
                [
                    "2025-07-25",
                    2.30
                ],
                [
                    "2025-07-28",
                    0.14
                ],
                [
                    "2025-07-29",
                    -1.19
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-27",
                    -6.31
                ],
                [
                    "2025-02-05",
                    7.63
                ],
                [
                    "2025-02-06",
                    4.76
                ],
                [
                    "2025-02-07",
                    -5.27
                ],
                [
                    "2025-02-10",
                    9.40
                ],
                [
                    "2025-02-11",
                    1.60
                ],
                [
                    "2025-02-12",
                    -2.18
                ],
                [
                    "2025-02-13",
                    3.42
                ],
                [
                    "2025-02-14",
                    15.60
                ],
                [
                    "2025-02-17",
                    -0.97
                ],
                [
                    "2025-02-18",
                    2.47
                ],
                [
                    "2025-02-19",
                    3.75
                ],
                [
                    "2025-02-20",
                    -1.95
                ],
                [
                    "2025-02-21",
                    -5.25
                ],
                [
                    "2025-02-24",
                    5.48
                ],
                [
                    "2025-02-25",
                    4.71
                ],
                [
                    "2025-02-26",
                    -7.10
                ],
                [
                    "2025-02-27",
                    -0.51
                ],
                [
                    "2025-02-28",
                    1.00
                ],
                [
                    "2025-03-03",
                    3.42
                ],
                [
                    "2025-03-04",
                    1.41
                ],
                [
                    "2025-03-05",
                    0.18
                ],
                [
                    "2025-03-06",
                    -3.00
                ],
                [
                    "2025-03-07",
                    5.79
                ],
                [
                    "2025-03-10",
                    0.00
                ],
                [
                    "2025-03-11",
                    -2.64
                ],
                [
                    "2025-03-12",
                    -4.09
                ],
                [
                    "2025-03-13",
                    1.04
                ],
                [
                    "2025-03-14",
                    -3.39
                ],
                [
                    "2025-03-17",
                    7.18
                ],
                [
                    "2025-03-18",
                    3.72
                ],
                [
                    "2025-03-19",
                    6.58
                ],
                [
                    "2025-03-20",
                    9.12
                ],
                [
                    "2025-03-21",
                    10.88
                ],
                [
                    "2025-03-24",
                    5.69
                ],
                [
                    "2025-03-25",
                    0.76
                ],
                [
                    "2025-03-26",
                    -1.56
                ],
                [
                    "2025-03-27",
                    8.96
                ],
                [
                    "2025-03-28",
                    -2.31
                ],
                [
                    "2025-03-31",
                    6.85
                ],
                [
                    "2025-04-01",
                    10.39
                ],
                [
                    "2025-04-02",
                    8.93
                ],
                [
                    "2025-04-03",
                    7.89
                ],
                [
                    "2025-04-07",
                    8.12
                ],
                [
                    "2025-04-08",
                    11.05
                ],
                [
                    "2025-04-09",
                    5.03
                ],
                [
                    "2025-04-10",
                    7.87
                ],
                [
                    "2025-04-11",
                    2.55
                ],
                [
                    "2025-04-14",
                    -1.75
                ],
                [
                    "2025-04-15",
                    -7.70
                ],
                [
                    "2025-04-16",
                    4.07
                ],
                [
                    "2025-04-17",
                    5.09
                ],
                [
                    "2025-04-18",
                    8.09
                ],
                [
                    "2025-04-21",
                    8.83
                ],
                [
                    "2025-04-22",
                    -3.60
                ],
                [
                    "2025-04-23",
                    2.64
                ],
                [
                    "2025-04-24",
                    8.02
                ],
                [
                    "2025-04-25",
                    0.69
                ],
                [
                    "2025-04-28",
                    12.35
                ],
                [
                    "2025-04-29",
                    -6.60
                ],
                [
                    "2025-04-30",
                    -2.56
                ],
                [
                    "2025-05-06",
                    0.83
                ],
                [
                    "2025-05-07",
                    -9.64
                ],
                [
                    "2025-05-08",
                    11.46
                ],
                [
                    "2025-05-09",
                    4.90
                ],
                [
                    "2025-05-12",
                    4.05
                ],
                [
                    "2025-05-13",
                    6.51
                ],
                [
                    "2025-05-14",
                    -1.01
                ],
                [
                    "2025-05-15",
                    10.32
                ],
                [
                    "2025-05-16",
                    6.67
                ],
                [
                    "2025-05-19",
                    -3.19
                ],
                [
                    "2025-05-20",
                    -2.40
                ],
                [
                    "2025-05-21",
                    12.38
                ],
                [
                    "2025-05-22",
                    8.00
                ],
                [
                    "2025-05-23",
                    9.93
                ],
                [
                    "2025-05-26",
                    -6.32
                ],
                [
                    "2025-05-27",
                    -7.68
                ],
                [
                    "2025-05-28",
                    -8.21
                ],
                [
                    "2025-05-29",
                    10.38
                ],
                [
                    "2025-05-30",
                    12.06
                ],
                [
                    "2025-06-03",
                    5.99
                ],
                [
                    "2025-06-04",
                    5.35
                ],
                [
                    "2025-06-05",
                    3.90
                ],
                [
                    "2025-06-06",
                    -1.26
                ],
                [
                    "2025-06-09",
                    4.24
                ],
                [
                    "2025-06-10",
                    9.88
                ],
                [
                    "2025-06-11",
                    -3.92
                ],
                [
                    "2025-06-12",
                    3.91
                ],
                [
                    "2025-06-13",
                    8.33
                ],
                [
                    "2025-06-16",
                    5.29
                ],
                [
                    "2025-06-17",
                    9.07
                ],
                [
                    "2025-06-18",
                    -3.78
                ],
                [
                    "2025-06-19",
                    8.39
                ],
                [
                    "2025-06-20",
                    1.60
                ],
                [
                    "2025-06-23",
                    19.33
                ],
                [
                    "2025-06-24",
                    3.78
                ],
                [
                    "2025-06-25",
                    -2.67
                ],
                [
                    "2025-06-26",
                    6.18
                ],
                [
                    "2025-06-27",
                    1.15
                ],
                [
                    "2025-06-30",
                    1.21
                ],
                [
                    "2025-07-01",
                    -7.82
                ],
                [
                    "2025-07-02",
                    11.05
                ],
                [
                    "2025-07-03",
                    -3.71
                ],
                [
                    "2025-07-04",
                    1.14
                ],
                [
                    "2025-07-07",
                    14.50
                ],
                [
                    "2025-07-08",
                    -6.66
                ],
                [
                    "2025-07-09",
                    9.22
                ],
                [
                    "2025-07-10",
                    0.18
                ],
                [
                    "2025-07-11",
                    -8.11
                ],
                [
                    "2025-07-14",
                    4.16
                ],
                [
                    "2025-07-15",
                    -3.98
                ],
                [
                    "2025-07-16",
                    1.83
                ],
                [
                    "2025-07-17",
                    2.10
                ],
                [
                    "2025-07-18",
                    -7.33
                ],
                [
                    "2025-07-21",
                    4.57
                ],
                [
                    "2025-07-22",
                    2.48
                ],
                [
                    "2025-07-23",
                    0.71
                ],
                [
                    "2025-07-24",
                    6.73
                ],
                [
                    "2025-07-25",
                    6.21
                ],
                [
                    "2025-07-28",
                    -2.90
                ],
                [
                    "2025-07-29",
                    7.08
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-03-04",
                    6.51
                ],
                [
                    "2025-03-14",
                    6.22
                ],
                [
                    "2025-03-17",
                    -15.96
                ],
                [
                    "2025-05-07",
                    7.38
                ],
                [
                    "2025-05-08",
                    -12.05
                ],
                [
                    "2025-05-29",
                    0.38
                ],
                [
                    "2025-07-14",
                    -7.16
                ],
                [
                    "2025-07-16",
                    -4.28
                ],
                [
                    "2025-07-17",
                    7.84
                ],
                [
                    "2025-07-18",
                    2.39
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-21",
                    0.12
                ],
                [
                    "2025-02-26",
                    -0.33
                ],
                [
                    "2025-02-27",
                    0.25
                ],
                [
                    "2025-03-04",
                    -7.92
                ],
                [
                    "2025-03-12",
                    1.86
                ],
                [
                    "2025-03-17",
                    8.78
                ],
                [
                    "2025-05-07",
                    2.26
                ],
                [
                    "2025-05-08",
                    0.58
                ],
                [
                    "2025-05-28",
                    2.06
                ],
                [
                    "2025-07-01",
                    13.57
                ],
                [
                    "2025-07-14",
                    2.99
                ],
                [
                    "2025-07-16",
                    2.45
                ],
                [
                    "2025-07-18",
                    4.94
                ],
                [
                    "2025-07-21",
                    13.72
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-10",
                    9.40
                ],
                [
                    "2025-02-11",
                    1.60
                ],
                [
                    "2025-02-13",
                    3.42
                ],
                [
                    "2025-02-14",
                    15.60
                ],
                [
                    "2025-02-17",
                    -0.97
                ],
                [
                    "2025-02-18",
                    2.47
                ],
                [
                    "2025-02-19",
                    3.75
                ],
                [
                    "2025-02-20",
                    -1.95
                ],
                [
                    "2025-02-24",
                    5.48
                ],
                [
                    "2025-02-25",
                    4.71
                ],
                [
                    "2025-02-28",
                    1.00
                ],
                [
                    "2025-03-03",
                    3.42
                ],
                [
                    "2025-03-05",
                    0.18
                ],
                [
                    "2025-03-06",
                    -3.00
                ],
                [
                    "2025-03-07",
                    5.79
                ],
                [
                    "2025-03-10",
                    0.00
                ],
                [
                    "2025-03-11",
                    -2.64
                ],
                [
                    "2025-03-13",
                    1.04
                ],
                [
                    "2025-03-18",
                    3.72
                ],
                [
                    "2025-03-19",
                    6.58
                ],
                [
                    "2025-03-20",
                    9.12
                ],
                [
                    "2025-03-21",
                    10.88
                ],
                [
                    "2025-03-24",
                    5.69
                ],
                [
                    "2025-03-25",
                    0.76
                ],
                [
                    "2025-03-26",
                    -1.56
                ],
                [
                    "2025-03-27",
                    8.96
                ],
                [
                    "2025-03-28",
                    -2.31
                ],
                [
                    "2025-03-31",
                    6.85
                ],
                [
                    "2025-04-01",
                    10.39
                ],
                [
                    "2025-04-02",
                    8.93
                ],
                [
                    "2025-04-03",
                    7.89
                ],
                [
                    "2025-04-07",
                    8.12
                ],
                [
                    "2025-04-08",
                    11.05
                ],
                [
                    "2025-04-09",
                    5.03
                ],
                [
                    "2025-04-10",
                    7.87
                ],
                [
                    "2025-04-11",
                    2.55
                ],
                [
                    "2025-04-14",
                    -1.75
                ],
                [
                    "2025-04-15",
                    -7.70
                ],
                [
                    "2025-04-16",
                    4.07
                ],
                [
                    "2025-04-17",
                    5.09
                ],
                [
                    "2025-04-18",
                    8.09
                ],
                [
                    "2025-04-21",
                    8.83
                ],
                [
                    "2025-04-22",
                    -3.60
                ],
                [
                    "2025-04-23",
                    2.64
                ],
                [
                    "2025-04-24",
                    8.02
                ],
                [
                    "2025-04-25",
                    0.69
                ],
                [
                    "2025-04-28",
                    12.35
                ],
                [
                    "2025-04-29",
                    -6.60
                ],
                [
                    "2025-04-30",
                    -2.56
                ],
                [
                    "2025-05-06",
                    0.83
                ],
                [
                    "2025-05-09",
                    4.90
                ],
                [
                    "2025-05-12",
                    4.05
                ],
                [
                    "2025-05-13",
                    6.51
                ],
                [
                    "2025-05-14",
                    -1.01
                ],
                [
                    "2025-05-15",
                    10.32
                ],
                [
                    "2025-05-16",
                    6.67
                ],
                [
                    "2025-05-19",
                    -3.19
                ],
                [
                    "2025-05-20",
                    -2.40
                ],
                [
                    "2025-05-21",
                    12.38
                ],
                [
                    "2025-05-22",
                    8.00
                ],
                [
                    "2025-05-23",
                    9.93
                ],
                [
                    "2025-05-26",
                    -6.32
                ],
                [
                    "2025-05-27",
                    -7.68
                ],
                [
                    "2025-05-30",
                    12.06
                ],
                [
                    "2025-06-03",
                    5.99
                ],
                [
                    "2025-06-04",
                    5.35
                ],
                [
                    "2025-06-05",
                    3.90
                ],
                [
                    "2025-06-06",
                    -1.26
                ],
                [
                    "2025-06-09",
                    4.24
                ],
                [
                    "2025-06-10",
                    9.88
                ],
                [
                    "2025-06-11",
                    -3.92
                ],
                [
                    "2025-06-12",
                    3.91
                ],
                [
                    "2025-06-13",
                    8.33
                ],
                [
                    "2025-06-16",
                    5.29
                ],
                [
                    "2025-06-17",
                    9.07
                ],
                [
                    "2025-06-18",
                    -3.78
                ],
                [
                    "2025-06-19",
                    8.39
                ],
                [
                    "2025-06-20",
                    1.60
                ],
                [
                    "2025-06-23",
                    19.33
                ],
                [
                    "2025-06-24",
                    3.78
                ],
                [
                    "2025-06-25",
                    -2.67
                ],
                [
                    "2025-06-26",
                    6.18
                ],
                [
                    "2025-06-27",
                    1.15
                ],
                [
                    "2025-06-30",
                    1.21
                ],
                [
                    "2025-07-02",
                    11.05
                ],
                [
                    "2025-07-03",
                    -3.71
                ],
                [
                    "2025-07-04",
                    1.14
                ],
                [
                    "2025-07-07",
                    14.50
                ],
                [
                    "2025-07-08",
                    -6.66
                ],
                [
                    "2025-07-09",
                    9.22
                ],
                [
                    "2025-07-10",
                    0.18
                ],
                [
                    "2025-07-11",
                    -8.11
                ],
                [
                    "2025-07-15",
                    -3.98
                ],
                [
                    "2025-07-22",
                    2.48
                ],
                [
                    "2025-07-23",
                    0.71
                ],
                [
                    "2025-07-24",
                    6.73
                ],
                [
                    "2025-07-25",
                    6.21
                ],
                [
                    "2025-07-28",
                    -2.90
                ],
                [
                    "2025-07-29",
                    7.08
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600109 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_24723104adb94696a44fc3f367b9ef31.setOption(option_24723104adb94696a44fc3f367b9ef31);
            window.addEventListener('resize', function(){
                chart_24723104adb94696a44fc3f367b9ef31.resize();
            })
    </script>
</body>
</html>
