<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="bea4bd74054a4cebab8ebc6398a003e1" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_bea4bd74054a4cebab8ebc6398a003e1 = echarts.init(
            document.getElementById('bea4bd74054a4cebab8ebc6398a003e1'), 'white', {renderer: 'canvas'});
        var option_bea4bd74054a4cebab8ebc6398a003e1 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    8.43,
                    8.22,
                    8.22,
                    8.43
                ],
                [
                    8.27,
                    8.21,
                    8.2,
                    8.31
                ],
                [
                    8.22,
                    8.34,
                    8.18,
                    8.36
                ],
                [
                    8.37,
                    8.83,
                    8.36,
                    9.14
                ],
                [
                    8.71,
                    8.89,
                    8.71,
                    8.92
                ],
                [
                    8.9,
                    8.81,
                    8.71,
                    8.91
                ],
                [
                    8.77,
                    8.97,
                    8.76,
                    9.0
                ],
                [
                    8.97,
                    8.87,
                    8.83,
                    8.99
                ],
                [
                    8.82,
                    8.84,
                    8.77,
                    8.9
                ],
                [
                    8.95,
                    8.85,
                    8.81,
                    8.96
                ],
                [
                    8.82,
                    8.64,
                    8.58,
                    8.83
                ],
                [
                    8.61,
                    8.65,
                    8.57,
                    8.71
                ],
                [
                    8.63,
                    8.61,
                    8.56,
                    8.68
                ],
                [
                    8.65,
                    8.9,
                    8.58,
                    8.99
                ],
                [
                    8.89,
                    8.76,
                    8.7,
                    8.89
                ],
                [
                    8.65,
                    8.65,
                    8.63,
                    8.72
                ],
                [
                    8.66,
                    8.85,
                    8.62,
                    8.86
                ],
                [
                    8.77,
                    8.79,
                    8.63,
                    8.88
                ],
                [
                    8.74,
                    8.52,
                    8.5,
                    8.88
                ],
                [
                    8.52,
                    8.48,
                    8.44,
                    8.61
                ],
                [
                    8.45,
                    8.55,
                    8.41,
                    8.58
                ],
                [
                    8.56,
                    8.54,
                    8.42,
                    8.56
                ],
                [
                    8.6,
                    8.68,
                    8.57,
                    8.71
                ],
                [
                    8.63,
                    8.55,
                    8.53,
                    8.65
                ],
                [
                    8.53,
                    8.54,
                    8.46,
                    8.56
                ],
                [
                    8.46,
                    8.57,
                    8.44,
                    8.58
                ],
                [
                    8.61,
                    8.61,
                    8.56,
                    8.73
                ],
                [
                    8.62,
                    8.62,
                    8.54,
                    8.7
                ],
                [
                    8.63,
                    8.83,
                    8.61,
                    8.91
                ],
                [
                    8.83,
                    8.77,
                    8.74,
                    8.84
                ],
                [
                    8.8,
                    8.77,
                    8.74,
                    8.85
                ],
                [
                    8.76,
                    8.78,
                    8.72,
                    8.82
                ],
                [
                    8.76,
                    8.69,
                    8.66,
                    8.78
                ],
                [
                    8.68,
                    8.57,
                    8.53,
                    8.72
                ],
                [
                    8.55,
                    8.54,
                    8.45,
                    8.58
                ],
                [
                    8.54,
                    8.6,
                    8.5,
                    8.61
                ],
                [
                    8.57,
                    8.54,
                    8.54,
                    8.62
                ],
                [
                    8.54,
                    8.54,
                    8.47,
                    8.6
                ],
                [
                    8.54,
                    8.5,
                    8.47,
                    8.58
                ],
                [
                    8.47,
                    8.36,
                    8.32,
                    8.52
                ],
                [
                    8.36,
                    8.42,
                    8.35,
                    8.46
                ],
                [
                    8.4,
                    8.49,
                    8.39,
                    8.52
                ],
                [
                    8.45,
                    8.48,
                    8.42,
                    8.54
                ],
                [
                    8.04,
                    7.63,
                    7.63,
                    8.22
                ],
                [
                    7.68,
                    7.73,
                    7.61,
                    7.83
                ],
                [
                    7.7,
                    7.85,
                    7.54,
                    7.94
                ],
                [
                    7.94,
                    7.97,
                    7.86,
                    8.06
                ],
                [
                    7.95,
                    7.94,
                    7.9,
                    8.02
                ],
                [
                    7.99,
                    7.97,
                    7.93,
                    8.01
                ],
                [
                    7.97,
                    7.95,
                    7.89,
                    7.97
                ],
                [
                    7.92,
                    7.9,
                    7.79,
                    7.94
                ],
                [
                    7.88,
                    7.88,
                    7.85,
                    7.93
                ],
                [
                    7.88,
                    7.94,
                    7.85,
                    7.97
                ],
                [
                    7.92,
                    7.97,
                    7.87,
                    7.99
                ],
                [
                    7.97,
                    7.97,
                    7.95,
                    8.04
                ],
                [
                    8.0,
                    7.96,
                    7.94,
                    8.04
                ],
                [
                    7.96,
                    7.92,
                    7.88,
                    7.99
                ],
                [
                    8.04,
                    8.19,
                    8.02,
                    8.29
                ],
                [
                    8.16,
                    8.15,
                    8.07,
                    8.2
                ],
                [
                    8.13,
                    8.13,
                    8.11,
                    8.22
                ],
                [
                    8.13,
                    8.12,
                    8.11,
                    8.19
                ],
                [
                    8.16,
                    8.2,
                    8.13,
                    8.22
                ],
                [
                    8.35,
                    8.21,
                    8.19,
                    8.51
                ],
                [
                    8.19,
                    8.23,
                    8.15,
                    8.27
                ],
                [
                    8.23,
                    8.12,
                    8.07,
                    8.24
                ],
                [
                    8.18,
                    8.3,
                    8.16,
                    8.3
                ],
                [
                    8.39,
                    8.29,
                    8.26,
                    8.39
                ],
                [
                    8.33,
                    8.51,
                    8.27,
                    8.64
                ],
                [
                    8.45,
                    8.43,
                    8.38,
                    8.51
                ],
                [
                    8.41,
                    8.37,
                    8.34,
                    8.43
                ],
                [
                    8.38,
                    8.35,
                    8.31,
                    8.39
                ],
                [
                    8.38,
                    8.36,
                    8.32,
                    8.39
                ],
                [
                    8.36,
                    8.36,
                    8.34,
                    8.39
                ],
                [
                    8.33,
                    8.31,
                    8.29,
                    8.35
                ],
                [
                    8.29,
                    8.23,
                    8.23,
                    8.36
                ],
                [
                    8.22,
                    8.22,
                    8.17,
                    8.24
                ],
                [
                    8.22,
                    8.23,
                    8.17,
                    8.25
                ],
                [
                    8.23,
                    8.19,
                    8.18,
                    8.25
                ],
                [
                    8.21,
                    8.34,
                    8.2,
                    8.34
                ],
                [
                    8.29,
                    8.33,
                    8.25,
                    8.35
                ],
                [
                    8.28,
                    8.38,
                    8.27,
                    8.41
                ],
                [
                    8.36,
                    8.47,
                    8.35,
                    8.5
                ],
                [
                    8.47,
                    8.51,
                    8.45,
                    8.52
                ],
                [
                    8.52,
                    8.48,
                    8.45,
                    8.53
                ],
                [
                    8.55,
                    8.54,
                    8.5,
                    8.61
                ],
                [
                    8.56,
                    8.44,
                    8.39,
                    8.56
                ],
                [
                    8.48,
                    8.55,
                    8.44,
                    8.65
                ],
                [
                    8.51,
                    8.55,
                    8.5,
                    8.59
                ],
                [
                    8.52,
                    8.46,
                    8.42,
                    8.55
                ],
                [
                    8.43,
                    8.54,
                    8.42,
                    8.55
                ],
                [
                    8.54,
                    8.55,
                    8.5,
                    8.56
                ],
                [
                    8.54,
                    8.44,
                    8.36,
                    8.56
                ],
                [
                    8.41,
                    8.34,
                    8.29,
                    8.44
                ],
                [
                    8.32,
                    8.3,
                    8.29,
                    8.38
                ],
                [
                    8.27,
                    8.37,
                    8.24,
                    8.41
                ],
                [
                    8.4,
                    8.63,
                    8.38,
                    8.63
                ],
                [
                    8.63,
                    8.89,
                    8.61,
                    8.93
                ],
                [
                    8.96,
                    8.81,
                    8.78,
                    9.0
                ],
                [
                    8.82,
                    8.77,
                    8.76,
                    9.04
                ],
                [
                    8.79,
                    8.77,
                    8.7,
                    8.81
                ],
                [
                    8.78,
                    8.73,
                    8.67,
                    8.79
                ],
                [
                    8.73,
                    8.77,
                    8.71,
                    8.81
                ],
                [
                    8.78,
                    8.87,
                    8.75,
                    8.89
                ],
                [
                    8.87,
                    8.83,
                    8.77,
                    8.95
                ],
                [
                    8.8,
                    8.85,
                    8.78,
                    8.89
                ],
                [
                    9.11,
                    9.2,
                    9.02,
                    9.3
                ],
                [
                    9.0,
                    9.02,
                    8.98,
                    9.09
                ],
                [
                    8.97,
                    9.08,
                    8.95,
                    9.15
                ],
                [
                    9.14,
                    9.56,
                    9.13,
                    9.86
                ],
                [
                    9.68,
                    9.43,
                    9.35,
                    9.68
                ],
                [
                    9.4,
                    9.32,
                    9.28,
                    9.47
                ],
                [
                    9.35,
                    9.28,
                    9.23,
                    9.35
                ],
                [
                    9.25,
                    9.35,
                    9.22,
                    9.35
                ],
                [
                    9.33,
                    9.34,
                    9.29,
                    9.37
                ],
                [
                    9.34,
                    9.39,
                    9.3,
                    9.4
                ],
                [
                    9.39,
                    9.39,
                    9.27,
                    9.41
                ],
                [
                    9.44,
                    9.39,
                    9.37,
                    9.59
                ],
                [
                    9.35,
                    9.65,
                    9.33,
                    9.66
                ],
                [
                    9.65,
                    9.56,
                    9.54,
                    9.68
                ],
                [
                    9.59,
                    9.58,
                    9.49,
                    9.7
                ],
                [
                    9.53,
                    9.61,
                    9.41,
                    9.62
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#880000",
                "borderColor0": "#008F28"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5438\u7b79",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 18,
            "data": [
                [
                    "2025-03-04",
                    8.41
                ],
                [
                    "2025-03-11",
                    8.44
                ],
                [
                    "2025-04-22",
                    7.95
                ],
                [
                    "2025-04-29",
                    8.11
                ],
                [
                    "2025-05-07",
                    8.19
                ],
                [
                    "2025-05-20",
                    8.32
                ],
                [
                    "2025-05-27",
                    8.17
                ],
                [
                    "2025-05-28",
                    8.18
                ],
                [
                    "2025-06-11",
                    8.44
                ],
                [
                    "2025-07-03",
                    8.75
                ],
                [
                    "2025-07-08",
                    9.02
                ],
                [
                    "2025-07-15",
                    9.28
                ],
                [
                    "2025-07-17",
                    9.22
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#F4B400"
            }
        },
        {
            "type": "scatter",
            "name": "\u52a0\u4ed3",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 18,
            "data": [
                [
                    "2025-02-21",
                    8.58
                ],
                [
                    "2025-04-25",
                    8.02
                ],
                [
                    "2025-06-25",
                    8.61
                ],
                [
                    "2025-07-08",
                    9.02
                ],
                [
                    "2025-07-11",
                    9.13
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4285F4"
            }
        },
        {
            "type": "scatter",
            "name": "\u6d17\u76d8",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "rect",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#16A085"
            }
        },
        {
            "type": "scatter",
            "name": "\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "circle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#DB4437"
            }
        },
        {
            "type": "scatter",
            "name": "\u9690\u5f62\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "pin",
            "symbolSize": 18,
            "data": [
                [
                    "2025-05-09",
                    8.07
                ],
                [
                    "2025-05-22",
                    8.29
                ],
                [
                    "2025-05-23",
                    8.23
                ],
                [
                    "2025-05-26",
                    8.17
                ],
                [
                    "2025-06-09",
                    8.5
                ],
                [
                    "2025-06-10",
                    8.39
                ],
                [
                    "2025-06-13",
                    8.42
                ],
                [
                    "2025-06-16",
                    8.42
                ],
                [
                    "2025-06-17",
                    8.5
                ],
                [
                    "2025-06-18",
                    8.36
                ],
                [
                    "2025-06-19",
                    8.29
                ],
                [
                    "2025-07-01",
                    8.67
                ],
                [
                    "2025-07-02",
                    8.71
                ],
                [
                    "2025-07-22",
                    9.27
                ],
                [
                    "2025-07-23",
                    9.37
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#8E44AD"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5438\u7b79",
                "\u52a0\u4ed3",
                "\u6d17\u76d8",
                "\u51fa\u8d27",
                "\u9690\u5f62\u51fa\u8d27"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10,
            "top": "2%"
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "interval": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 0
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600109 \u4e3b\u529b\u4fe1\u53f7 (\u542b\u6d17\u76d8)",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "90%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_bea4bd74054a4cebab8ebc6398a003e1.setOption(option_bea4bd74054a4cebab8ebc6398a003e1);
            window.addEventListener('resize', function(){
                chart_bea4bd74054a4cebab8ebc6398a003e1.resize();
            })
    </script>
</body>
</html>
