<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="891dfe05e31f4969bcba8eda01552423" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_891dfe05e31f4969bcba8eda01552423 = echarts.init(
            document.getElementById('891dfe05e31f4969bcba8eda01552423'), 'white', {renderer: 'canvas'});
        var option_891dfe05e31f4969bcba8eda01552423 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    23.81,
                    22.85,
                    22.85,
                    23.91
                ],
                [
                    23.32,
                    23.11,
                    23.0,
                    23.57
                ],
                [
                    22.96,
                    24.19,
                    22.82,
                    24.45
                ],
                [
                    24.48,
                    24.39,
                    23.96,
                    24.84
                ],
                [
                    24.57,
                    24.96,
                    24.3,
                    25.22
                ],
                [
                    24.96,
                    24.41,
                    24.31,
                    25.0
                ],
                [
                    24.27,
                    25.3,
                    24.2,
                    25.32
                ],
                [
                    25.4,
                    24.72,
                    24.7,
                    25.5
                ],
                [
                    24.71,
                    24.65,
                    24.41,
                    24.82
                ],
                [
                    24.76,
                    24.48,
                    24.27,
                    24.85
                ],
                [
                    24.46,
                    23.89,
                    23.75,
                    24.66
                ],
                [
                    23.98,
                    25.64,
                    23.89,
                    25.98
                ],
                [
                    25.64,
                    25.62,
                    25.42,
                    26.05
                ],
                [
                    25.55,
                    26.07,
                    25.3,
                    26.14
                ],
                [
                    26.01,
                    25.75,
                    25.5,
                    26.04
                ],
                [
                    25.43,
                    26.32,
                    25.15,
                    27.04
                ],
                [
                    26.49,
                    26.6,
                    26.04,
                    26.78
                ],
                [
                    26.9,
                    26.38,
                    25.83,
                    26.9
                ],
                [
                    26.36,
                    25.23,
                    25.12,
                    26.36
                ],
                [
                    25.29,
                    25.03,
                    24.8,
                    25.54
                ],
                [
                    24.78,
                    25.72,
                    24.7,
                    26.08
                ],
                [
                    25.75,
                    25.59,
                    25.25,
                    25.97
                ],
                [
                    25.79,
                    25.75,
                    25.6,
                    26.08
                ],
                [
                    25.57,
                    25.16,
                    24.99,
                    25.63
                ],
                [
                    25.19,
                    25.01,
                    24.85,
                    25.38
                ],
                [
                    24.71,
                    25.04,
                    24.7,
                    25.28
                ],
                [
                    26.5,
                    25.56,
                    25.53,
                    27.54
                ],
                [
                    25.3,
                    24.57,
                    24.38,
                    25.59
                ],
                [
                    24.57,
                    24.85,
                    24.39,
                    24.88
                ],
                [
                    24.85,
                    24.75,
                    24.61,
                    25.05
                ],
                [
                    24.89,
                    25.3,
                    24.84,
                    26.32
                ],
                [
                    25.26,
                    24.91,
                    24.8,
                    25.29
                ],
                [
                    24.85,
                    24.81,
                    24.61,
                    25.03
                ],
                [
                    24.66,
                    24.26,
                    24.18,
                    24.79
                ],
                [
                    24.2,
                    24.05,
                    23.66,
                    24.31
                ],
                [
                    24.14,
                    23.91,
                    23.78,
                    24.27
                ],
                [
                    23.83,
                    23.91,
                    23.77,
                    24.13
                ],
                [
                    23.7,
                    23.93,
                    23.52,
                    24.33
                ],
                [
                    23.91,
                    23.73,
                    23.7,
                    24.32
                ],
                [
                    23.51,
                    23.6,
                    23.07,
                    23.75
                ],
                [
                    23.59,
                    23.86,
                    23.56,
                    23.9
                ],
                [
                    23.99,
                    23.73,
                    23.67,
                    24.1
                ],
                [
                    23.38,
                    23.48,
                    23.3,
                    24.07
                ],
                [
                    22.0,
                    21.13,
                    21.13,
                    22.5
                ],
                [
                    21.0,
                    20.52,
                    20.3,
                    21.48
                ],
                [
                    20.24,
                    21.04,
                    19.5,
                    21.23
                ],
                [
                    21.54,
                    21.35,
                    21.32,
                    21.84
                ],
                [
                    21.33,
                    22.08,
                    21.2,
                    22.77
                ],
                [
                    22.4,
                    22.23,
                    22.14,
                    22.6
                ],
                [
                    22.15,
                    21.91,
                    21.84,
                    22.18
                ],
                [
                    21.86,
                    21.7,
                    21.21,
                    22.04
                ],
                [
                    21.5,
                    21.62,
                    21.5,
                    22.05
                ],
                [
                    21.63,
                    21.46,
                    21.28,
                    21.73
                ],
                [
                    22.2,
                    23.61,
                    22.07,
                    23.61
                ],
                [
                    24.56,
                    25.96,
                    23.73,
                    25.97
                ],
                [
                    25.07,
                    24.62,
                    23.98,
                    25.07
                ],
                [
                    24.4,
                    25.79,
                    24.19,
                    26.26
                ],
                [
                    25.2,
                    24.9,
                    24.64,
                    25.44
                ],
                [
                    24.54,
                    23.97,
                    23.96,
                    25.15
                ],
                [
                    23.48,
                    24.07,
                    23.25,
                    24.75
                ],
                [
                    24.35,
                    25.95,
                    23.88,
                    26.48
                ],
                [
                    25.35,
                    26.19,
                    25.35,
                    26.24
                ],
                [
                    26.19,
                    25.8,
                    25.58,
                    26.41
                ],
                [
                    25.72,
                    25.81,
                    25.58,
                    26.04
                ],
                [
                    25.7,
                    24.63,
                    24.6,
                    25.7
                ],
                [
                    24.9,
                    24.7,
                    24.6,
                    25.0
                ],
                [
                    24.95,
                    24.54,
                    24.26,
                    24.99
                ],
                [
                    24.54,
                    24.6,
                    24.41,
                    24.75
                ],
                [
                    24.6,
                    23.54,
                    23.53,
                    24.6
                ],
                [
                    23.58,
                    23.79,
                    23.5,
                    23.91
                ],
                [
                    23.98,
                    24.09,
                    23.61,
                    24.16
                ],
                [
                    24.09,
                    24.08,
                    23.89,
                    24.23
                ],
                [
                    24.0,
                    23.85,
                    23.75,
                    24.07
                ],
                [
                    23.8,
                    23.88,
                    23.73,
                    24.23
                ],
                [
                    23.78,
                    23.06,
                    23.05,
                    23.9
                ],
                [
                    23.1,
                    23.31,
                    23.1,
                    23.47
                ],
                [
                    23.28,
                    23.39,
                    23.06,
                    23.44
                ],
                [
                    23.4,
                    22.93,
                    22.9,
                    23.52
                ],
                [
                    22.9,
                    23.61,
                    22.9,
                    23.72
                ],
                [
                    23.45,
                    23.68,
                    23.2,
                    23.8
                ],
                [
                    23.4,
                    24.1,
                    23.38,
                    24.11
                ],
                [
                    23.96,
                    23.79,
                    23.77,
                    24.02
                ],
                [
                    23.82,
                    23.87,
                    23.62,
                    23.96
                ],
                [
                    23.9,
                    23.69,
                    23.68,
                    23.95
                ],
                [
                    23.64,
                    23.83,
                    23.6,
                    23.95
                ],
                [
                    23.83,
                    23.3,
                    23.08,
                    23.9
                ],
                [
                    23.3,
                    23.24,
                    23.23,
                    23.62
                ],
                [
                    23.2,
                    22.76,
                    22.7,
                    23.2
                ],
                [
                    22.66,
                    22.28,
                    22.18,
                    22.95
                ],
                [
                    22.18,
                    22.45,
                    22.15,
                    22.62
                ],
                [
                    22.31,
                    22.48,
                    22.31,
                    22.59
                ],
                [
                    22.43,
                    22.53,
                    22.29,
                    22.63
                ],
                [
                    22.5,
                    22.36,
                    22.25,
                    22.68
                ],
                [
                    22.34,
                    22.31,
                    22.24,
                    22.59
                ],
                [
                    22.14,
                    22.7,
                    22.03,
                    22.75
                ],
                [
                    22.8,
                    23.06,
                    22.71,
                    23.08
                ],
                [
                    23.15,
                    23.24,
                    22.84,
                    23.25
                ],
                [
                    23.24,
                    22.96,
                    22.94,
                    23.25
                ],
                [
                    23.16,
                    23.1,
                    23.04,
                    23.28
                ],
                [
                    23.26,
                    23.3,
                    23.13,
                    23.39
                ],
                [
                    23.25,
                    23.33,
                    23.06,
                    23.54
                ],
                [
                    23.35,
                    23.09,
                    22.96,
                    23.35
                ],
                [
                    23.11,
                    23.1,
                    22.96,
                    23.19
                ],
                [
                    23.12,
                    22.74,
                    22.66,
                    23.16
                ],
                [
                    22.7,
                    22.82,
                    22.67,
                    22.95
                ],
                [
                    22.8,
                    23.4,
                    22.72,
                    23.47
                ],
                [
                    23.41,
                    23.19,
                    23.11,
                    23.41
                ],
                [
                    23.2,
                    23.33,
                    23.13,
                    23.76
                ],
                [
                    23.4,
                    23.44,
                    23.16,
                    23.53
                ],
                [
                    23.48,
                    23.45,
                    23.33,
                    23.53
                ],
                [
                    23.2,
                    23.08,
                    22.9,
                    23.43
                ],
                [
                    23.06,
                    23.09,
                    23.0,
                    23.44
                ],
                [
                    22.94,
                    23.14,
                    22.82,
                    23.17
                ],
                [
                    23.22,
                    23.29,
                    23.06,
                    23.39
                ],
                [
                    23.31,
                    23.3,
                    23.21,
                    23.41
                ],
                [
                    23.3,
                    23.38,
                    23.23,
                    23.47
                ],
                [
                    23.41,
                    23.29,
                    23.25,
                    23.65
                ],
                [
                    23.29,
                    23.73,
                    23.27,
                    23.76
                ],
                [
                    24.3,
                    24.9,
                    24.29,
                    25.4
                ],
                [
                    24.81,
                    24.73,
                    24.45,
                    24.9
                ],
                [
                    24.5,
                    24.74,
                    24.46,
                    25.0
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c",
                "borderColor": "#880000",
                "borderColor0": "#008F28"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5438\u7b79",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 18,
            "data": [
                [
                    "2025-02-26",
                    26.04
                ],
                [
                    "2025-03-03",
                    24.8
                ],
                [
                    "2025-03-18",
                    24.84
                ],
                [
                    "2025-03-20",
                    24.61
                ],
                [
                    "2025-04-29",
                    23.25
                ],
                [
                    "2025-05-19",
                    23.61
                ],
                [
                    "2025-05-22",
                    23.73
                ],
                [
                    "2025-07-10",
                    23.13
                ],
                [
                    "2025-07-11",
                    23.16
                ],
                [
                    "2025-07-14",
                    23.33
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#F4B400"
            }
        },
        {
            "type": "scatter",
            "name": "\u52a0\u4ed3",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "triangle",
            "symbolSize": 18,
            "data": [
                [
                    "2025-02-19",
                    23.89
                ],
                [
                    "2025-04-21",
                    22.07
                ],
                [
                    "2025-04-22",
                    23.73
                ],
                [
                    "2025-04-24",
                    24.19
                ],
                [
                    "2025-07-25",
                    24.29
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4285F4"
            }
        },
        {
            "type": "scatter",
            "name": "\u6d17\u76d8",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "rect",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#16A085"
            }
        },
        {
            "type": "scatter",
            "name": "\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "circle",
            "symbolSize": 18,
            "data": [],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#DB4437"
            }
        },
        {
            "type": "scatter",
            "name": "\u9690\u5f62\u51fa\u8d27",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "pin",
            "symbolSize": 18,
            "data": [
                [
                    "2025-05-08",
                    25.58
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#8E44AD"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5438\u7b79",
                "\u52a0\u4ed3",
                "\u6d17\u76d8",
                "\u51fa\u8d27",
                "\u9690\u5f62\u51fa\u8d27"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10,
            "top": "2%"
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "interval": 10,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14",
                "2025-07-15",
                "2025-07-16",
                "2025-07-17",
                "2025-07-18",
                "2025-07-21",
                "2025-07-22",
                "2025-07-23",
                "2025-07-24",
                "2025-07-25",
                "2025-07-28",
                "2025-07-29"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 0
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "605358 \u4e3b\u529b\u4fe1\u53f7 (\u542b\u6d17\u76d8)",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "90%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_891dfe05e31f4969bcba8eda01552423.setOption(option_891dfe05e31f4969bcba8eda01552423);
            window.addEventListener('resize', function(){
                chart_891dfe05e31f4969bcba8eda01552423.resize();
            })
    </script>
</body>
</html>
