{"stocks": [{"code": "000001", "name": "平安银行"}, {"code": "000547", "name": "航天发展"}, {"code": "000863", "name": "三湘印象"}, {"code": "002049", "name": "紫光国微"}, {"code": "002050", "name": "三花智控"}, {"code": "002229", "name": "鸿博股份"}, {"code": "002410", "name": "广联达"}, {"code": "002459", "name": "晶澳科技"}, {"code": "002544", "name": "普天科技"}, {"code": "002664", "name": "信质集团"}, {"code": "002929", "name": "润建股份"}, {"code": "600064", "name": "南京高科"}, {"code": "600109", "name": "国金证券"}, {"code": "600416", "name": "湘电股份"}, {"code": "600510", "name": "黑牡丹"}, {"code": "600517", "name": "国网英大"}, {"code": "600597", "name": "光明乳业"}, {"code": "600602", "name": "云赛智联"}, {"code": "600661", "name": "昂立教育"}, {"code": "600732", "name": "爱旭股份"}, {"code": "600745", "name": "闻泰科技"}, {"code": "600880", "name": "博瑞传播"}, {"code": "601888", "name": "中国中免"}, {"code": "603058", "name": "永吉股份"}, {"code": "603212", "name": "赛伍技术"}, {"code": "603267", "name": "鸿远电子"}, {"code": "603421", "name": "鼎信通讯"}, {"code": "603429", "name": "集友股份"}, {"code": "603708", "name": "家家悦"}, {"code": "603899", "name": "晨光股份"}, {"code": "605358", "name": "立昂微"}], "chartTypes": [{"name": "主力入场信号", "icon": "🎯", "color": "#e74c3c"}, {"name": "主力散户线", "icon": "📊", "color": "#3498db"}, {"name": "量能堆积", "icon": "📈", "color": "#2ecc71"}, {"name": "阶段性底部", "icon": "🔍", "color": "#f39c12"}], "lastUpdated": "2025-07-29T19:01:28.146484", "totalStocks": 31, "totalChartTypes": 4, "generatedBy": "generate_stock_config_advanced.py", "cacheStats": {"totalCached": 31, "cacheFile": "/Users/<USER>/Desktop/TT/code/trading-system/cache/stock_names_cache.json"}, "analysisUpdateStats": {"success": ["000001", "000547", "000863", "002049", "002050", "002229", "002410", "002459", "002544", "002664", "002929", "600064", "600109", "600416", "600510", "600517", "600597", "600602", "600661", "600732", "600745", "600880", "601888", "603058", "603212", "603267", "603421", "603429", "603708", "603899", "605358"], "failed": [], "skipped": []}}