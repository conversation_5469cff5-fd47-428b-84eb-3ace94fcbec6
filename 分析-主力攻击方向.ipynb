#%% md
### Stage 0 运行完，你现在得到什么？

当你在 Jupyter 里执行：

```python
df = preprocess('板块价格.csv', '板块资金.csv')
```

---

#### 1. 返回值 `df`（同时也写入 `stage0_output.parquet`）

| 列类别        | 代表字段                                  | 说明                         |
| ---------- | ------------------------------------- | -------------------------- |
| **主键**     | `name`, `date`                        | 板块名称 + 交易日                 |
| **原始行情**   | `open`, `high`, `low`, `close`, `vol` | 从价格 CSV 直接保留               |
| **原始资金**   | `major_net_inflow` → `F1`             | 当日主力净流入（元）                 |
| **滚动资金指标** | `F5`, `F10`                           | 近 5 / 10 日净流入累和（不足天数记 NaN） |
|            | `FlowSlope`                           | 资金回流加速度＝(F5 – F10)/\|F10\| |
| **位置/形态**  | `52W_pct_rank`                        | 当前价 ÷ 近 52 周最高价            |
|            | `drawdown_1y`                         | 距一年高点的跌幅比例                 |
|            | `Slope_40d`                           | 40 日对数价格斜率（≈0 → 横盘）        |
| **波动/量能**  | `ATR_pct`                             | ATR20 ÷ 收盘价——近 1 月平均振幅占比   |
|            | `BB_width`                            | 20 日布林带宽 ÷ 收盘价——波动压缩程度     |
|            | `vol_ratio_5`                         | 当日量 ÷ 近 5 日均量（量比 5）        |

> **行数**
> \= *价格 CSV 行数*（≈ 3 × 10⁴）——因为是 **left join**，哪天无资金数据也保留，`major_net_inflow` 自动填 0。

---

#### 2. 数据示例（概念）

| name | date       | close | F1         | F5         | F10        | FlowSlope | 52W\_pct\_rank | Slope\_40d | ATR\_pct | BB\_width | vol\_ratio\_5 |
| ---- | ---------- | ----- | ---------- | ---------- | ---------- | --------- | -------------- | ---------- | -------- | --------- | ------------- |
| 光伏设备 | 2025-06-20 | 1523  | 12 345 678 | 45 678 910 | -1 234 567 | 0.84      | 0.62           | 0.0008     | 0.024    | 0.031     | 1.28          |

* **F1** 为正 → 当天净流入
* **F5** 已转正、**F10** 仍小负 → 处于零轴临界
* **FlowSlope 0.84** → 资金回流速度高
* **52W\_pct\_rank 0.62** 且 **Slope\_40d ≈ 0**、**ATR\_pct** 与 **BB\_width** 都很低 → 低位横盘压缩
  → 这类记录在后续阶段 1–4 中就可能进入 **爆发前夕池**。

---

#### 3. 你现在可以做的

* **浏览**：`df.head()`, `df.describe()` ——直观查看指标分布
* **保存**：`df.to_csv('stage0_debug.csv', index=False)` 若想手动核对
* **下一步**：直接把 `df` 传给 **阶段 1** 的区段划分逻辑（A/B/C/D）。

如果还想添加额外列名映射或指标，只要改 `preprocess()` 前几行即可，其他计算链会自动继承。

#%%
# csv
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Union
import importlib
from typing import Optional



demo_price = Path("/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/all_industries_daily_data.csv")
demo_fund  = Path("/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/blank_fund.csv")
stage0_path = "/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/stage0_output.parquet"
#%%
# ───── Helper utils ──────────────────────────────────────

def _norm_date(s: pd.Series) -> pd.Series:
    """Robust date parser for yyyy‑mm‑dd / yyyymmdd / datetime."""
    return pd.to_datetime(s.astype(str).str.replace(r"[^0-9]", "", regex=True))


def _rolling_sum(series: pd.Series, window: int):
    return series.rolling(window, min_periods=window).sum()


def _slope(arr: np.ndarray):
    y = np.log(arr)
    x = np.arange(len(y))
    if np.any(np.isnan(y)):
        return np.nan
    return (np.cov(x, y, bias=True)[0, 1] / np.var(x))


# ───── Core function ─────────────────────────────────────

def preprocess(price_csv: Union[str, Path],
               fund_csv: Union[str, Path],
               *,
               price_name_col: Optional[Union[int, str]] = None,
               out_path: Union[str, Path] | None = None,
               save_format: str | None = "parquet",
               fund_has_name_first: bool = True) -> pd.DataFrame:
    """Compute Stage‑0 indicators and return wide DataFrame.

    Parameters
    ----------
    price_csv, fund_csv : str or Path — 行情 / 资金 CSV 路径
    price_name_col      : int | str | None — 价格 CSV 中“板块名称”列
    out_path            : 保存文件名；None → "stage0_output.{fmt}"
    save_format         : 'parquet' | 'csv' | None
    fund_has_name_first : True → fund 列顺序 (name,date,…)，False → (date,name,…)
    """

    # ── 1. 读文件 ─────────────────────────
    price = pd.read_csv(price_csv)
    fund  = pd.read_csv(fund_csv)

    # ── 2. 日期列标准化 ────────────────────
    price["date"] = _norm_date(price.iloc[:, 0])
    fund_date_col = 1 if fund_has_name_first else 0
    fund["date"]  = _norm_date(fund.iloc[:, fund_date_col])

    # ── 3. 确定名称列 index ─────────────────
    if price_name_col is None:
        # try auto match by keyword
        kw = ["name", "板块", "行业"]
        matched = [c for c in price.columns if any(k in str(c) for k in kw)]
        if matched:
            price_name_col = matched[0]
        else:
            price_name_col = 1  # fallback: 第二列

    # resolve col name
    if isinstance(price_name_col, int):
        name_col = price.columns[price_name_col]
    else:
        name_col = price_name_col

    # ── 4. 重命名核心列 ───────────────────
    rename_map = {name_col: "name"}
    # 把其余五列按相对位置映射
    core_cols = [c for c in price.columns if c != name_col][:6]  # 防御性切片
    if len(core_cols) < 6:
        raise ValueError("price_csv 缺少必须的 open/high/low/close/vol 列")
    rename_map.update({
        core_cols[1]: "open",
        core_cols[2]: "high",
        core_cols[3]: "low",
        core_cols[4]: "close",
        core_cols[5]: "vol",
    })
    price = price.rename(columns=rename_map)

    fund = fund.rename(columns={"name": "name", "major_net_inflow": "major_net_inflow"})

    # ── 5. 字段类型 & 去重合并 ─────────────
    price["name"] = price["name"].astype(str).str.strip()
    fund["name"]  = fund["name"].astype(str).str.strip()
    fund = fund.groupby(["name", "date"], as_index=False)["major_net_inflow"].sum()
    price = price.drop_duplicates(subset=["name", "date"])
    df = price.merge(fund, on=["name", "date"], how="left")
    df["major_net_inflow"] = df["major_net_inflow"].fillna(0)

    # ── 6. 资金指标 ───────────────────────
    df = df.sort_values(["name", "date"])
    g = df.groupby("name", group_keys=False)
    df["F1"]  = df["major_net_inflow"]
    df["F5"]  = g["F1"].apply(lambda x: _rolling_sum(x, 5))
    df["F10"] = g["F1"].apply(lambda x: _rolling_sum(x, 10))
    df["FlowSlope"] = (df["F5"] - df["F10"]) / (df["F10"].abs() + 1e-9)

    # ── 7. 形态 / 波动指标 ─────────────────
    df["high_52w"] = g["close"].apply(lambda x: x.rolling(252, min_periods=1).max())
    df["52W_pct_rank"] = df["close"] / df["high_52w"]
    df["drawdown_1y"]  = (df["close"] - df["high_52w"]) / df["high_52w"]
    df["Slope_40d"]    = g["close"].apply(lambda x: x.rolling(40, min_periods=40).apply(_slope, raw=True))
    df["TR"]           = df["high"] - df["low"]
    df["ATR_20"]       = g["TR"].apply(lambda x: x.rolling(20, min_periods=20).mean())
    df["ATR_pct"]      = df["ATR_20"] / df["close"]
    df["BB_width"]     = 2 * g["close"].apply(lambda x: x.rolling(20, min_periods=20).std()) / df["close"]
    df["vol_mean_5"]   = g["vol"].apply(lambda x: x.rolling(5, min_periods=5).mean())
    df["vol_ratio_5"]  = df["vol"] / df["vol_mean_5"]

    # ── 8. 保存 ───────────────────────────
    if save_format is not None:
        if out_path is None:
            out_path = Path(f"stage0_output.{save_format}")
        else:
            out_path = Path(out_path)

        if save_format == "parquet":
            if importlib.util.find_spec("pyarrow") or importlib.util.find_spec("fastparquet"):
                df.to_parquet(out_path, index=False)
    return df
#%%
df0 = preprocess(demo_price, demo_fund, out_path=stage0_path, price_name_col="板块名称")

#%%
df0['name'].head()
#%% md
### Stage 1 的作用是什么？

> **一句话**：
> Stage 1 把 Stage 0 算好的资金指标 — **F1 / F5 / F10 / FlowSlope** — 按资金动能状态切成 **四大区段 (A/B/C/D)**，为后面“形态过滤、5 日评分、状态机迁移”做第一层筛选。

---

## 1️⃣  资金四分区的含义

| 区段       | 判定逻辑（简化）                       | 资金行为         | 在整套流程中的角色                 |             |                         |
| -------- | ------------------------------ | ------------ | ------------------------- | ----------- | ----------------------- |
| **A 撤离** | F10 ≪ 0 且 F5 ≪ 0，FlowSlope≈0   | 主力持续流出       | 直接忽略，不入任何池                |             |                         |
| **B 吸筹** | F10、F5 都< 0 但绝对值缩小，FlowSlope>0 | 流出在收敛 → 回流试探 | 进入 **观察池**（低频跟踪）          |             |                         |
| **C 临界** | F10< 0 且 \\                    | F10\\        | < **M**，FlowSlope≥阈值，F1>0 | 资金贴近零轴并首次转正 | 进入 **爆发前夕池**，等待形态+短评分确认 |
| **D 上拐** | F5、F10 都≥ 0                    | 资金已全面由负转正    | 直接视为强势趋势，可选跟踪             |             |                         |

> **M（动态阈值）**：当天全部板块 \\|F10\\| 的中位数；样本短时≈1.6 亿元，后续随数据增多自动更新。

---

## 2️⃣  Stage 1 输入 & 输出

* **输入**：Stage 0 的宽表

  * 至少要有 `name, date, F1, F5, F10, FlowSlope`
* **输出**：

  * 在原 DataFrame 上新增一列 `zone` ∈ {A, B, C, D, None}
  * **辅助函数** `latest_snapshot()`：快速看**最新交易日**各板块所属区段 + 核心资金指标，方便当日决策

```python
df1 = classify(df0)            # 给每行打上 zone
latest_snapshot(df1, top_n=20) # 看当天资金动向最活跃的前 20 个板块
```

---

## 3️⃣  为什么要先做 Stage 1？

1. **快速过滤掉明显弱势**（A 撤离），节省后续运算和注意力。
2. **把焦点集中到资金已经明显回流**（B）和 **零轴临界**（C）的板块。
3. **为后续阶段建立状态机**：

   * 只有 **C → 形态合格** 的才会进入 “爆发前夕池” 并触发 5 日评分；
   * B 区只是“观察”，不会浪费高频评分计算。
4. **动态阈值** 让算法随样本扩充自动调节，不必频繁手动改参数。

---

### 🚩 接下来

* **Stage 2**：对 `zone == 'C'` 的行跑一年级别 “低位 + 横盘/压缩” 形态过滤；
* **Stage 3**：对通过过滤的板块启动 5 日评分；
* **Stage 4**：根据评分结果在各池之间迁移（爆发确认 / 失败冷却）。

把 Stage 1 跑通后，你就能每天得到“资金动能层面”的第一批候选板块，为下一步形态验证打地基。

#%%
"""
Stage 3 – 5 日日度评分与状态机迁移  (v1.0 中文注释版)
=====================================================

本文件负责：
1. 对 `phase == 'pre_burst'`（爆发前夕池）做 5 日滚动评分
2. 达到阈值 → 升级 `strong_trend`；跌破阈值或大流出 → `cooldown`
3. 持久化状态到 `stage3_state.parquet`，次日继续累积

核心参数（中文解释）
--------------------
```python
SCORE_WIN   = 5      # 评分窗口：连续 5 个交易日
COOLDOWN_N  = 10     # 失败后冷却天数
BIG_OUTFLOW = 1e9    # 10 亿元：单日净流出超过此值直接失败
```

日度评分规则（分值写在 `_daily_score()`）：
* **+2**  当日 F1 > 0  （仍有主力净流入）
* **+1**  当日 F1 比昨增  （回流速度提升）
* **+2**  当日涨幅>1% 且量比5≥1.2  （量价共振）
* **−3**  连续两日阴线且连续净流出  （弱势+撤资）
* **−2**  收盘价跌破 MA20          （短趋势走弱）

迁移逻辑：
* 5 日累计分 **≥+5**    → `strong_trend`  ✅ 爆发确认
* 5 日累计分 **≤−3** 或单日流出>10亿 → `cooldown` 冷却10天 ❌ 失败
* 满 5 日但未触阈值 → 清零重计下一轮

函数接口
---------
```python
update_scores(df2, state_path='stage3_state.parquet')
    # 输入 Stage‑2 当日宽表，返回更新后的状态 DataFrame

get_watchlist(state_df)
    # 返回 strong_trend 池、pre_burst 池两个 DataFrame
```
"""


from __future__ import annotations
import pandas as pd
from typing import Optional

# ───── internal helper ───────────────────────────────────

def _get_F10_threshold(df: pd.DataFrame, date: pd.Timestamp) -> float:
    """Median of |F10| on the given date."""
    day = df[df["date"] == date]
    return day["F10"].abs().median(skipna=True)


# ───── classification core ───────────────────────────────

def classify(
    df: pd.DataFrame,
    *,
    slope_cut: float = 0.25,
    F10_threshold: Optional[float] = None,
) -> pd.DataFrame:
    """Add `zone` column (A/B/C/D/None) according to资金动能规则."""

    df = df.copy()
    if not pd.api.types.is_datetime64_any_dtype(df["date"]):
        df["date"] = pd.to_datetime(df["date"])

    zones = []
    for _, row in df.iterrows():
        F1, F5, F10, slope = row["F1"], row["F5"], row["F10"], row["FlowSlope"]
        if pd.isna(F10) or pd.isna(F5):
            zones.append(None)
            continue
        M = F10_threshold or _get_F10_threshold(df, row["date"])
        # ── rule tree ──────────────────
        if (F10 < 0) and (F5 < 0) and abs(slope) < 0.05:
            zones.append("A")
        elif (F10 < 0) and (F5 < 0) and (slope > 0):
            zones.append("B")
        elif (F10 < 0) and (abs(F10) < M) and (slope >= slope_cut) and (F1 > 0):
            zones.append("C")
        elif (F5 >= 0) and (F10 >= 0):
            zones.append("D")
        else:
            zones.append(None)
    df["zone"] = zones
    return df


# ───── snapshot helper ──────────────────────────────────

def latest_snapshot(
    df: pd.DataFrame,
    top_n: Optional[int] = None,
    *,
    sort_by: str = "FlowSlope",
) -> pd.DataFrame:
    """Return latest‑day table sorted by `sort_by` (default FlowSlope ↓)."""

    last_date = df["date"].max()
    snap = df[df["date"] == last_date].copy()

    # 对指定列做绝对值排序，如 sort_by=='F1' 用 abs(F1)；其他列直接降序
    if sort_by == "F1":
        snap = snap.sort_values(sort_by, key=lambda s: s.abs(), ascending=False)
    else:
        snap = snap.sort_values(sort_by, ascending=False)

    if top_n:
        snap = snap.head(top_n)

    cols = ["name", "F1", "F5", "F10", "FlowSlope", "zone"]
    return snap[cols].reset_index(drop=True)

#%%
df0 = pd.read_parquet(stage0_path)
df1 = classify(df0)
df1.head(20)
#%% md
### FlowSlope 是什么？

| 项目            | 公式                    | 单位      | 含义                                        |
| ------------- | --------------------- | ------- | ----------------------------------------- |
| **FlowSlope** | \[F5 − F10] ÷ \|F10\| | **无量纲** | **资金回流加速度**<br>（近 5 日净流入变化量 ÷ 过去 10 日的基数） |

> * **F1** ＝ 当日主力净流入
> * **F5** ＝ 最近 5 个交易日主力净流入之和
> * **F10** ＝ 最近 10 个交易日主力净流入之和

#### ① 公式拆解

1. **(F5 − F10)**

   * *正值* → 近 5 日流入比前 10 日“好转”
     *说明资金在加速回流或减速流出*
   * *负值* → 近 5 日流入状况比 10 日更差
     *资金流出正在恶化*
2. **÷ |F10|**

   * 把不同板块的资金体量归一化
   * 得到 0.0 – 数十之间的小数或百分比

#### ② 解读区间（短样本默认阈值）

| FlowSlope 值 | 资金状态       | 在 Stage 1 的作用           |
| ----------- | ---------- | ----------------------- |
| **≥ +0.30** | 回流速度“明显加快” | **C 区** 与 **B 区** 的核心判据 |
| 0 → +0.30   | 轻微好转       | 仍属 B 区（吸筹但尚不强）          |
| −0.05 → 0   | 基本持平       | A 区（仍流出）                |
| ≤ −0.05     | 恶化         | A 区，直接过滤                |

> 0.30 这个 cut 来自我们的短历史样本；随着资金数据积累，可以用整体分位（如 70% 分位）动态调整。

#### ③ Stage 1 中如何用 FlowSlope

1. **B 吸筹区**

   * 条件：F10、F5 都 < 0，但 `FlowSlope > 0`
   * 说明流出正在收敛，进入“减速回流”阶段
2. **C 临界区（爆发前夕候选）**

   * 条件之一：`FlowSlope ≥ 0.25~0.30`
   * * F10 贴近 0 且 F1 首次为正
   * 代表“资金加速度足够大”——零轴拐点
3. **A / D**

   * `FlowSlope` 决定 A（≈0 或负）还是 B/C
   * D 区已不看 FlowSlope，因为 F5、F10 都 ≥ 0

#### ④ 使用小贴士

* **分母加 1e-9**：避免 F10 = 0 时除零；但数值极小时 FlowSlope 会非常大，要与 “|F10| 小于阈值” 搭配使用。
* **回测调参**：

  * 数据越长，FlowSlope 分布越稳定；可用 60 日或 90 日滚动分位自适应 cut。
  * 若你觉得 0.30 太苛刻，可暂改 0.25 观察命中率。
* **可视化**：在 notebook 里画 `FlowSlope` vs `F10` 散点，一眼能看到哪些板块正从左下（流出偏弱）向右上（回流加速）。

---

> **总结**
> FlowSlope = *“近 5 日资金相比近 10 日的* **增速比例**”\*。
> 它让我们**量化**“资金是否在快速掉头”，是 Stage 1 判断 B/C 区的关键因素，直接影响后续是否进入“爆发前夕池”。

#%%
pd.set_option('display.max_rows', None)  

stage1_df = latest_snapshot(df1)
stage1_df_b = stage1_df[stage1_df["zone"] == "B"]
stage1_df_b
#%%
stage1_df_c = stage1_df[stage1_df["zone"] == "C"]
stage1_df_c

#%%
stage1_df_d = stage1_df[stage1_df["zone"] == "D"]
stage1_df_d
#%%
stage1_df_d = stage1_df[stage1_df["zone"] == "A"]
stage1_df_d
#%% md
### Stage 2 —— “一年级别形态过滤” 当前使用的全部硬性条件

| 维度     | 指标 & 公式                                           | **阈值**                  | 判定通过          |
| ------ | ------------------------------------------------- | ----------------------- | ------------- |
| **低位** | 1) 52 W%Rank ＝ `close / high_52w`                 | ≤ **0.70**              | 距一年高点 ≥ 30 %  |
|        | 2) Drawdown\_1y ＝ `(close − high_52w) / high_52w` | ≤ **−25 %**             | 一年最大回撤 ≥ 25 % |
| **横盘** | \|Slope\_40d\| （40 日对数价格斜率）                       | ≤ **0.001**  ≈ ±0.1 %/日 | 近两个月基本走平      |
| **压缩** | *满足其一即可*                                          |                         |               |
|        | a) ATR\_pct ＝ `ATR_20 / close`                    | ≤ **3 %**               | 20 日平均振幅极窄    |
|        | b) BB\_width\_rank ＝ `BB_width` 在最近 1 年分位         | ≤ **20 %**              | 布林带收口到最窄 20 % |

> **全部 3 组条件同时满足** → `shape_ok = True` → 板块当日可进入 **爆发前夕池**
> 任何一项不满足 → `shape_ok = False` → 当日直接淘汰

---

#### 备注

* “压缩”只需 **a 或 b** 成立即可；其余维度必须同时满足。
* 阈值可以按你的市场环境微调：

  * 多头高波动市：ATR\_pct 放宽至 4 %
  * 防御型行业：Drawdown\_1y 放宽至 −20 %
* 数据不足 252 日时，可把 **BB\_width\_rank** 改用近 100 日分位。

#%% md
好的，把形态闸值整体 **放宽半档**，同时保持“三道闸”结构不变。
下面是 **更新后的 Stage 2 筛选条件**（已放宽的数值用粗体标出）：

| 维度     | 指标                                             | 原阈值     | **放宽后阈值**    | 判定说明                 |
| ------ | ---------------------------------------------- | ------- | ------------ | -------------------- |
| **低位** | 52 W%Rank = `close / high_52w`                 | ≤ 0.70  | **≤ 0.75**   | 允许距年高点缩小到 25 %       |
|        | Drawdown\_1y = `(close − high_52w) / high_52w` | ≤ −25 % | **≤ −20 %**  | 最大回撤放宽 5 pct         |
| **横盘** | \|Slope\_40d\|（40 日 log 斜率）                    | ≤ 0.001 | **≤ 0.0015** | 近 2 月日均涨跌幅可到 ±0.15 % |
| **压缩** | **满足其一即可**                                     |         |              |                      |
|        | a) ATR\_pct = `ATR_20 / close`                 | ≤ 3 %   | **≤ 4 %**    | 波幅阈值 +1 pct          |
|        | b) BB\_width\_rank = 布林带宽在 1 年分位               | ≤ 20 %  | **≤ 25 %**   | 允许收口到最窄 25 %         |

> **通过规则**
>
> * 三大维度都要满足：
>
>   * “低位”组 2 条 **同时**达标
>   * “横盘”组 1 条
>   * “压缩”组二选一
> * 满足即 `shape_ok = True` → 当天纳入 **爆发前夕池**。

---

### 何时再微调

| 观察现象             | 动作建议                                  |
| ---------------- | ------------------------------------- |
| `shape_ok` 板块仍过少 | 可继续放宽到 Rank ≤ 0.80 或 ATR\_pct ≤ 4.5 % |
| 信号很多但 5 日评分爆发率低  | 适当收紧 ATR\_pct 或 BB\_rank              |
| 行业间差异显著          | 对高波动行业单独再+0.5 % ATR\_pct；防御板块保持 3 %   |

如果这一版阈值感觉合适，我再按此写出 **Stage 2 形态过滤代码**；如还想微调数值或逻辑，先告诉我再定稿。

#%%
"""
Stage 2 – 一年级别形态过滤 (低位 + 横盘/压缩)  **v1.1**
========================================================

修复
----
* **去掉自写 rolling‑apply Rank** → 改为 *向量化* 方式：
  直接比较 `BB_width` 与同板块过去 `rank_window` 日的
  `quantile(bb_rank_cut)`，避免 `index out‑of‑bounds`。
* 运行速度更快，且兼容短历史（<252 日）板块。

核心阈值 (已放宽)
-----------------
* 低位
  * 52W_pct_rank ≤ 0.75
  * drawdown_1y    ≤ −0.20
* 横盘
  * |Slope_40d|     ≤ 0.0015
* 压缩 (满足其一)
  * ATR_pct      ≤ 0.04
  * BB_width ≤ rolling_quantile(0.25)

Use
---
```python
from stage2_shape_filter import shape_filter, today_candidates

df2 = shape_filter(df1)          # df1 已含 zone
cands = today_candidates(df2)    # 最新交易日 爆发前夕池
```
"""


def shape_filter(
    df: pd.DataFrame,
    *,
    rank_window: int = 252,
    low_rank_cut: float = 0.75,
    drawdown_cut: float = -0.20,
    slope_cut: float = 0.0015,
    atr_cut: float = 0.04,
    bb_rank_cut: float = 0.25,
) -> pd.DataFrame:
    """Append `shape_ok` and `phase` columns.

    phase:
        'pre_burst'   – zone=='C' & shape_ok
        'early_accum' – zone=='B' & shape_ok
        None          – others
    """

    df = df.copy()
    need = ["52W_pct_rank", "drawdown_1y", "Slope_40d", "ATR_pct", "BB_width", "zone"]
    miss = [c for c in need if c not in df.columns]
    if miss:
        raise KeyError(f"Stage‑2 missing columns: {miss}")

    df = df.sort_values(["name", "date"])

    # rolling BB threshold per board (vectorised)
    def _roll_q(s: pd.Series):
        w = rank_window if len(s) >= rank_window else max(len(s), 100)
        return s.rolling(w, min_periods=max(20, w//4)).quantile(bb_rank_cut)

    df["BB_thresh"] = df.groupby("name", group_keys=False)["BB_width"].transform(_roll_q)

    # boolean gates
    cond_low   = (df["52W_pct_rank"] <= low_rank_cut) & (df["drawdown_1y"] <= drawdown_cut)
    cond_flat  = df["Slope_40d"].abs() <= slope_cut
    cond_tight = (df["ATR_pct"] <= atr_cut) | (df["BB_width"] <= df["BB_thresh"])

    df["shape_ok"] = cond_low & cond_flat & cond_tight

    # phase assignment
    phase = np.where((df["zone"] == "C") & df["shape_ok"], "pre_burst",
             np.where((df["zone"] == "B") & df["shape_ok"], "early_accum", None))
    df["phase"] = phase
    return df


# ─────────────────────────────────────────────────────────
# helper
# ─────────────────────────────────────────────────────────

def today_candidates(
    df: pd.DataFrame,
    *,
    date: Optional[pd.Timestamp] = None,
    mode: str = "pre_burst",  # 'pre_burst' | 'all'
    cols=None,
):
    """Latest‑day watchlist.

    mode='pre_burst' → 仅 zone=='C' & shape_ok
    mode='all'       → 同时包含 early_accum（B 区 + shape_ok）
    """
    if date is None:
        date = df["date"].max()

    if mode == "pre_burst":
        mask = (df["date"] == date) & (df["phase"] == "pre_burst")
    elif mode == "all":
        mask = (df["date"] == date) & (df["phase"].isin(["pre_burst", "early_accum"]))
    else:
        raise ValueError("mode must be 'pre_burst' or 'all'")

    default_cols = ["name", "phase", "F1", "F5", "F10", "FlowSlope"]
    return df.loc[mask, cols or default_cols].reset_index(drop=True)

#%%
df2 = shape_filter(df1)
#%%
df2.loc[df2["shape_ok"] & (df2["zone"] == "B"), ["name", "FlowSlope","date"]]
#%%
print("爆发前夕:\n", today_candidates(df2).head(10))
#%%
print("all watch (incl. 早期吸筹):\n", today_candidates(df2, mode="all").head(10))
#%%

#%% md
# Stage3
---

## 1️⃣ 输入与目标

| 项目       | 说明                                                                          |
| -------- | --------------------------------------------------------------------------- |
| **输入**   | Stage 2 结果 `df2`（含 `phase` 列：`pre_burst`, `early_accum`）                    |
| **追踪对象** | 仅 `phase == 'pre_burst'` 的板块（C 区 + 形态OK）                                    |
| **目标**   | 在 **连续 5 个交易日** 内累计评分：<br>• **≥ +5** → “爆发确认”<br>• **≤ −3** 或单日大流出 → “蓄势失败” |

---

## 2️⃣ 日度评分体系

| 当日事件                     | + / − 分 | 说明      |
| ------------------------ | ------- | ------- |
| **F1 > 0**（仍净流入）         | +2      | 资金延续回流  |
| **F1 环比增加**              | +1      | 回流速度再提速 |
| **涨幅 > 1 % 且 量比5 ≥ 1.2** | +2      | 量价共振    |
| **连续两日阴线且连续净流出**         | −3      | 资金撤退警示  |
| **收盘跌破 MA20**            | −2      | 短趋势转弱   |

> **计分周期** 固定为 5 个交易日；每天收盘后更新 `score` 与 `days_cnt`。

---

## 3️⃣ 状态判定与迁移

| 5 日累计得分                     | 结论       | 下一状态                            |
| --------------------------- | -------- | ------------------------------- |
| **≥ +5**                    | **爆发确认** | → `phase='strong_trend'`（进入强势池） |
| **≤ −3** **或** 单日净流出 > 10 亿 | **蓄势失败** | → `phase='cooldown'`, 冷却 N=10 日 |
| 其它                          | 仍在评分中    | 保持 `pre_burst`，等待周期结束           |

* 若 5 日后得分介于 −3 \~ +4 → 继续留在 `pre_burst` 再开启新一轮 5 日计分。
* `early_accum` 板块一旦 **资金指标升级到 C 区**（F10 贴零轴 & FlowSlope ≥ 0.25 & F1>0）→ 自动改 `phase='pre_burst'` 并开启评分。

---

## 4️⃣ 数据表字段（建议）

| 字段              | 类型  | 说明                                                      |
| --------------- | --- | ------------------------------------------------------- |
| `phase`         | str | `pre_burst / strong_trend / cooldown / early_accum / …` |
| `score`         | int | 当前 5 日累计分                                               |
| `days_cnt`      | int | 当前评分周期已走天数 (1-5)                                        |
| `cooldown_days` | int | 冷却倒计时；0 表示解冻可重新评估                                       |

---

## 5️⃣ 调度流程

```
收盘 → ① 更新今日资金 / 行情 → Stage 0 指标刷新
       ② 重新判 zone & shape_ok → Stage 1 + Stage 2
       ③ 对 phase=='pre_burst' 板块:
            score += 今日增减分
            days_cnt += 1
            若(得分>=+5) → strong_trend
            elif(得分<=-3 or 单日大流出) → cooldown (cooldown_days=10)
            elif(days_cnt==5) → 清零 score, days_cnt 重新计分
       ④ cooldown_days >0 的板块 → cooldown_days -=1
```

---

## 6️⃣ 成功 / 失败后如何跟进？

| 状态                | 监控重点           | 退出条件                          |
| ----------------- | -------------- | ----------------------------- |
| **strong\_trend** | 涨幅、成交量、最大回撤    | 回撤＞−10 % 或 20 日资金再翻负          |
| **cooldown**      | 等待资金重新进入 B/C 区 | `cooldown_days` 归 0 且资金信号再度向好 |

---

## 7️⃣ 动态参数建议

* **单日大流出阈值**：可设为 `3 × 当日上证成交额分位`，或固定 10 亿。
* **冷却期 N**：目前样本短，可先用 10 日；数据够 1 年后再调至 20 日。
* **评分阈值**：

  * `>=+5` 爆发线可回测调成 `+4` 或 `+6`，看命中率-风险比。
  * `<=−3` 失败线同理。

---

### 🚀 Stage 3 的作用

1. **把形态 + 资金拐点的“可能性”变成“确认信号”**。
2. **避免假突破**：5 日窗口确保资金、量价至少保持一周同步。
3. **状态机闭环**：成功转 `strong_trend`，失败自动冷却，流程收束无人工干预。

若方案无异议，我可以接着写 Stage 3 的实现代码（计分逻辑、状态字段维护、每日调度函数）。

#%%
"""
Stage 3 – 5 日日度评分与状态机迁移  (v1.0 中文注释版)
=====================================================

本文件负责：
1. 对 `phase == 'pre_burst'`（爆发前夕池）做 5 日滚动评分
2. 达到阈值 → 升级 `strong_trend`；跌破阈值或大流出 → `cooldown`
3. 持久化状态到 `stage3_state.parquet`，次日继续累积

核心参数（中文解释）
--------------------
```python
SCORE_WIN   = 5      # 评分窗口：连续 5 个交易日
COOLDOWN_N  = 10     # 失败后冷却天数
BIG_OUTFLOW = 1e9    # 10 亿元：单日净流出超过此值直接失败
```

日度评分规则（分值写在 `_daily_score()`）：
* **+2**  当日 F1 > 0  （仍有主力净流入）
* **+1**  当日 F1 比昨增  （回流速度提升）
* **+2**  当日涨幅>1% 且量比5≥1.2  （量价共振）
* **−3**  连续两日阴线且连续净流出  （弱势+撤资）
* **−2**  收盘价跌破 MA20          （短趋势走弱）

迁移逻辑：
* 5 日累计分 **≥+5**    → `strong_trend`  ✅ 爆发确认
* 5 日累计分 **≤−3** 或单日流出>10亿 → `cooldown` 冷却10天 ❌ 失败
* 满 5 日但未触阈值 → 清零重计下一轮

函数接口
---------
```python
update_scores(df2, state_path='stage3_state.parquet')
    # 输入 Stage‑2 当日宽表，返回更新后的状态 DataFrame

get_watchlist(state_df)
    # 返回 strong_trend 池、pre_burst 池两个 DataFrame
```
"""

from __future__ import annotations
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Tuple, Optional

# ─────────────────────────────────────────────────────────
# 全局参数（如需调参仅改此处）
# ─────────────────────────────────────────────────────────
SCORE_WIN   = 5      # ★ 评分窗口 = 5 天
COOLDOWN_N  = 10     # ★ 失败后冷却天数
BIG_OUTFLOW = 1e9    # ★ 判定“大流出”阈值（10 亿元）

# 保存文件名，可统一在调度脚本中改
# stage0_path = "/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/stage0_output.parquet"
STATE_FILE  = "/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/stage3_state.parquet"

# ─────────────────────────────────────────────────────────
# 计算单日得分
# ─────────────────────────────────────────────────────────

def _daily_score(row: pd.Series, prev_row: Optional[pd.Series]) -> int:
    """根据今日行 & 昨日行计算增减分。"""
    pts = 0
    # -------- 正分 --------
    if row["F1"] > 0:                         # 主力仍净流入
        pts += 2
    if prev_row is not None and row["F1"] > prev_row["F1"]:  # 流入加速
        pts += 1
    if (row.get("pct_chg", 0) > 0.01) and (row.get("vol_ratio_5", 1) >= 1.2):  # 放量上涨
        pts += 2
    # -------- 负分 --------
    if prev_row is not None:
        two_down = (row.get("pct_chg", 0) < 0) and (prev_row.get("pct_chg", 0) < 0)
        two_out  = (row["F1"] < 0) and (prev_row["F1"] < 0)
        if two_down and two_out:             # 连阴 + 连续流出
            pts -= 3
    if row.get("close_below_ma20", False):  # 跌破 MA20
        pts -= 2
    return pts

# ─────────────────────────────────────────────────────────
# 每日更新主函数 (修正空状态初始化 KeyError)
# ─────────────────────────────────────────────────────────

def update_scores(df2: pd.DataFrame, state_path: str | Path = STATE_FILE) -> pd.DataFrame:
    """根据当日 df2 更新评分状态，并写回 parquet。"""

    today = df2["date"].max()
    today_rows = df2[df2["date"] == today].set_index("name")  # 当日全部板块，按 name 设为索引

    # 读取或初始化历史状态 -------------------------------------------------
    if Path(state_path).exists():
        state_prev = pd.read_parquet(state_path).set_index("name")
    else:
        # 初始化空表时必须先含有 name 列，否则 set_index 会报 KeyError
        state_prev = (
            pd.DataFrame(
                columns=["name", "phase", "score", "days_cnt", "cooldown_days", "last_date"]
            )
            .set_index("name")
        )

    new_records = []

    for name, cur in today_rows.iterrows():
        prev = state_prev.loc[name] if name in state_prev.index else None

        # ------- 初始化字段 -------
        phase      = cur.get("phase")
        score      = 0
        days_cnt   = 0
        cooldown   = 0
        last_date  = today

        if prev is not None:
            phase      = prev["phase"] if pd.notna(prev["phase"]) else phase
            score      = int(prev["score"])
            days_cnt   = int(prev["days_cnt"])
            cooldown   = max(int(prev["cooldown_days"]) - 1, 0)

        # -------- 冷却池逻辑 --------
        if phase == "cooldown":
            if cooldown == 0 and cur["phase"] in ["pre_burst", "early_accum"]:
                phase = cur["phase"]  # 冷却结束，重新参与评分
            else:
                new_records.append([name, phase, score, days_cnt, cooldown, last_date])
                continue  # 冷却中不打分

        # -------- 仅 pre_burst 参与 5 日评分 --------
        if phase == "pre_burst":
            # 找昨日行用于环比
            prev_row = None
            if prev is not None and prev["last_date"] != today:
                prev_row_q = df2[(df2["name"] == name) & (df2["date"] == prev["last_date"])]
                prev_row = prev_row_q.iloc[0] if not prev_row_q.empty else None

            score += _daily_score(cur, prev_row)
            days_cnt += 1

            # ---------- 状态迁移 ----------
            if (cur["F1"] < -BIG_OUTFLOW) or (score <= -3):
                phase, score, days_cnt, cooldown = "cooldown", 0, 0, COOLDOWN_N
            elif score >= 5:
                phase, score, days_cnt = "strong_trend", 0, 0
            elif days_cnt == SCORE_WIN:
                score, days_cnt = 0, 0  # 重启下一轮

        # early_accum 由 Stage‑2 升级，无需在此打分
        new_records.append([name, phase, score, days_cnt, cooldown, last_date])

    # 补回今天缺席但仍在状态表里的板块（如停牌）
    absent_prev = state_prev[~state_prev.index.isin(today_rows.index)].reset_index()
    combined = pd.concat(
        [pd.DataFrame(new_records, columns=["name", "phase", "score", "days_cnt", "cooldown_days", "last_date"]), absent_prev]
    )
    combined.to_parquet(state_path, index=False)
    return combined.set_index("name")

def get_watchlist(state_df: pd.DataFrame,
                  mode: str = "core") -> tuple:
    """
    根据 state_df['phase'] 返回若干池子 DataFrame。

    Parameters
    ----------
    state_df : DataFrame
        update_scores / update_strong_pool 之后的一张全局状态表，
        必须至少含列 ["phase"]，并已 index.set_index("name")。
    mode : {"core", "all"}, default "core"
        * "core" → 只返回 (strong_pool, pre_burst_pool)
        * "all"  → 返回 (strong_pool, pre_burst_pool,
                         early_accum_pool, cooldown_pool)

    Returns
    -------
    tuple
        DataFrame 顺序与 mode 对应；若某池为空则返回空表。
    """
    def _sel(p):  # 内部小助手
        return state_df[state_df["phase"] == p].reset_index()

    strong   = _sel("strong_trend")
    pre      = _sel("pre_burst")

    if mode == "core":
        return strong, pre

    early    = _sel("early_accum")
    cooldown = _sel("cooldown")
    return strong, pre, early, cooldown



#%%
# ─────────────────────────────────────────────────────────
# CLI demo
# ─────────────────────────────────────────────────────────

df3 = update_scores(df2)
strong, pre, early, cooldown = get_watchlist(df3, mode="all")

print("今日强势池:", strong.head(10))
print()
print("⏬ 转弱 / 冷却池 cooldown", cooldown.head(10))
print()

print("今日爆发前夕:", pre.head(10))
print()

print("今日早起吸筹:", early.head(10))

#%%
df3
#%%

#%% md
### Stage 4 — **强势池跟踪 & 退场状态机**

> Stage 3 已把「爆发确认」的板块升格为
> `phase = 'strong_trend'`，并把失败者丢进 `cooldown`。
> Stage 4 的职责是：
>
> 1. **动态维护强势池**（盈利保护、出场条件）
> 2. **管理冷却期结束后的再评估**
> 3. 一并写回同一张 `stage3_state.parquet`，让全流程闭环。

---

## 1️⃣  输入 / 输出

| 项目     | 说明                                                                      |
| ------ | ----------------------------------------------------------------------- |
| **输入** | • 最新 **`state_today`**（Stage 3 更新后）<br>• 当日 **`df2_today`**（含价格 & 资金指标） |
| **输出** | 回写后的 **state\_today**（新增/更新字段）                                          |

---

## 2️⃣  新增字段

| 字段                  | 类型    | 作用                               |
| ------------------- | ----- | -------------------------------- |
| `high_since_strong` | float | 进入 `strong_trend` 后的最高收盘价（动态更新）  |
| `drawdown_pct`      | float | 当日收盘距 `high_since_strong` 的回撤百分比 |
| `strong_days`       | int   | 处于 `strong_trend` 累计天数           |

---

## 3️⃣  强势池每日检查逻辑

| 规则       | 默认阈值                              | 动作                                             | 备注        |
| -------- | --------------------------------- | ---------------------------------------------- | --------- |
| **更新高点** | `close > high_since_strong`       | `high_since_strong = close`，`drawdown_pct = 0` | ——        |
| **回撤止盈** | `drawdown_pct ≤ −10 %`            | → `phase='cooldown'`, `cooldown_days = 10`     | 可调 8–15 % |
| **资金转弱** | 连续 **3 日** F1<0 **且** FlowSlope<0 | 同上                                             | 捕捉主力撤退    |
| **弱量破位** | 收盘价 < MA20 **且** 成交额 < 5 日均额      | 同上                                             | 防跌破上升通道   |
| **强势续航** | 无违规                               | `strong_days += 1`                             | ——        |

> *所有条件可多选满足其一即退场*；
> 阈值放在参数区，和 Stage 3 同样易改。

---

## 4️⃣  冷却池管理

* `cooldown_days` 已在 Stage 3 每日 `−1`
* **当倒计时到 0** 时：

  * 先把 `phase` 设为 `None`（或 `'cooled'`）
  * 下一轮 Stage 1–2–3 会把它重新评估：

    * 若资金转好 → 可能进入 `early_accum` / `pre_burst`
    * 否则继续保持普通状态，不占用池子

---

## 5️⃣  与现有状态文件的交互

```
state_today (含 strong_trend/cooldown)
         │
         ▼
Stage 4 update_strong_pool()
    • 遍历 phase=='strong_trend'
    • 计算 drawdown_pct 等
    • 如触退场条件 → phase='cooldown', cooldown_days=10
    • else 更新 high_since_strong, strong_days+1
写回 stage3_state.parquet
```

---

## 6️⃣  关键参数（集中放头部）

```python
MAX_DRAWDOWN   = 0.10   # 10% 回撤止盈
COOLDOWN_N2    = 10     # 强势退场后的冷却期
OUTFLOW_STREAK = 3      # 连续 3 日资金流出
```

---

## 7️⃣  示例时序

| 日期  | 操作               | phase                          | drawdown | high\_since\_strong |
| --- | ---------------- | ------------------------------ | -------- | ------------------- |
| D0  | 5 日评分达 +6        | strong\_trend                  | 0        | 12.3                |
| D2  | 继续新高             | strong\_trend                  | 0        | 12.8                |
| D5  | 回撤 −5 %          | strong\_trend                  | −5 %     | 12.8                |
| D7  | 连续第 3 日 F1<0     | → cooldown(10)                 | —        | —                   |
| D17 | cooldown\_days=0 | phase=None → 等待 Stage 1–2 重新评估 |          |                     |

---

## 8️⃣  下一步

* **确认**：阈值（10 % 回撤 / 3 日流出）是否符合你的风险偏好？
* **代码**：将写一个 `update_strong_pool(df2_today, state_path)`：

  * 读取 `state_path` → 更新强势池 → 保存
  * 放在 Stage 3 之后调用，或与 Stage 3 合并成 `daily_update()` 也可。
* **可选拓展**：

  * 对强势池再做 **收益跟踪**（累计涨幅、胜率统计）
  * 或设 `super_trend` 阶段（涨幅≥30 % 且回撤≤5 %）

若以上方案 OK，我就照此写 Stage 4 的实现代码。

#%%
"""
Stage 4 – 强势池跟踪 & 退场状态机  (v1.0)
========================================

作用
----
1. **每日巡检** phase=='strong_trend' 的板块：
   * 更新 `high_since_strong`, `drawdown_pct`, `strong_days`, `outflow_streak`
   * 若触发退场条件 → phase='cooldown', cooldown_days=COOLDOWN_N2
2. 结果回写到同一个 `stage3_state.parquet`，供下一轮 Stage‑1/2/3 使用。

核心参数
---------
```python
MAX_DRAWDOWN     = 0.10   # 强势期最大容忍回撤 10 %
MAX_OUT_STREAK   = 3      # 连续 3 日主力净流出即退场
COOLDOWN_N2      = 10     # 退场后冷却天数
VOLUME_WEAK_MA20 = True   # 跌破 MA20 且成交<5日均额 也退场
```

接口
-----
```python
update_strong_pool(df2_today,
                   state_path='outputs/stage3_state.parquet')
    -> 返回更新后的 state DataFrame
```

依赖列
-------
`df2_today` 必须至少包含：
`name, close, MA20, vol, vol_mean_5, F1, FlowSlope`
（MA20 / vol_mean_5 建议在 Stage‑0 就已计算）
"""

from __future__ import annotations
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Optional

# ─────────────────────────────────────────────────────────
# 全局参数（如需调参仅改此处）
# ─────────────────────────────────────────────────────────
MAX_DRAWDOWN   = 0.10   # 10 % 回撤止盈
MAX_OUT_STREAK = 3      # 连续 3 日资金净流出触发退场
COOLDOWN_N2    = 10     # 退场后冷却天数
VOLUME_WEAK_MA20 = True # 开启量价弱破位规则
STATE_FILE      = "/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/stage3_state.parquet"

# ─────────────────────────────────────────────────────────
# 主函数
# ─────────────────────────────────────────────────────────

def update_strong_pool(df2_today: pd.DataFrame,
                       state_path: str | Path = STATE_FILE) -> pd.DataFrame:
    """检查强势池，更新高点 & 回撤，满足退场条件则冷却。"""

    if not Path(state_path).exists():
        raise FileNotFoundError("stage3_state.parquet 不存在，请先跑 Stage‑3")

    state = pd.read_parquet(state_path).set_index("name")

    today = df2_today["date"].max()
    today_rows = df2_today[df2_today["date"] == today].set_index("name", drop=False)

    # 初始化潜在新列
    for col, default in [
        ("high_since_strong", np.nan),
        ("drawdown_pct", np.nan),
        ("strong_days", 0),
        ("outflow_streak", 0),
    ]:
        if col not in state.columns:
            state[col] = default

    # 遍历强势池
    for name, board_state in state[state["phase"] == "strong_trend"].iterrows():
        if name not in today_rows.index:
            # 停牌或无数据：强势天数+1, drawdown保持
            state.at[name, "strong_days"] = board_state["strong_days"] + 1
            continue

        cur = today_rows.loc[name]
        close = cur["close"]
        high_prev = board_state["high_since_strong"] if not np.isnan(board_state["high_since_strong"]) else close
        high_new  = max(high_prev, close)
        drawdown  = (close - high_new) / high_new

        # 更新基本字段
        state.at[name, "high_since_strong"] = high_new
        state.at[name, "drawdown_pct"]      = drawdown
        state.at[name, "strong_days"]        = board_state["strong_days"] + 1

        # 资金流出连续计数
        streak = board_state["outflow_streak"]
        streak = streak + 1 if cur["F1"] < 0 else 0
        state.at[name, "outflow_streak"] = streak

        # ---------- 退场判定 ----------
        exit_flag = False
        if drawdown <= -MAX_DRAWDOWN:
            exit_flag = True
        elif streak >= MAX_OUT_STREAK and cur["FlowSlope"] < 0:
            exit_flag = True
        elif VOLUME_WEAK_MA20 and (close < cur.get("MA20", close)) and (cur["vol"] < cur.get("vol_mean_5", cur["vol"])):
            exit_flag = True

        if exit_flag:
            state.at[name, ["phase", "score", "days_cnt", "outflow_streak"]] = [
                "cooldown", 0, 0, 0,
            ]
            state.at[name, "cooldown_days"] = COOLDOWN_N2
            # high_since_strong / drawdown_pct 保留以便复盘

        # 更新 last_date
        state.at[name, "last_date"] = today

    # 写回 parquet
    state.reset_index().to_parquet(state_path, index=False)
    return state



#%%
# ─────────────────────────────────────────────────────────
# CLI demo (optional)
# ─────────────────────────────────────────────────────────
state_after = update_strong_pool(df2)
print("更新后强势池: ")
print(state_after[state_after["phase"] == "strong_trend"][["drawdown_pct", "strong_days"]].head())
#%%
df_strong   = state_after[state_after["phase"] == "strong_trend"]
df_cooldown = state_after[state_after["phase"] == "cooldown"]
df_pre      = state_after[state_after["phase"] == "pre_burst"]
df_early    = state_after[state_after["phase"] == "early_accum"]
#%%
df_strong
#%%
df_cooldown
#%%
df_pre
#%%
df_early
#%%
