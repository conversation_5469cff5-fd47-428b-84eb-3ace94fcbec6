#%%
from pathlib import Path
CODE = "002410"
ROOT = Path("/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/")  
#%%
# -*- coding: utf-8 -*-
"""signals_kline_complete.py
--------------------------------------------------
纯 K 线 + 主力信号 (吸筹 / 加仓 / 出货 / 隐形出货) 可视化脚本
--------------------------------------------------
使用方式：
1. 修改 ROOT & CODE 两个常量为你的数据路径、股票代码。
2. 依次准备三张 CSV：
   - {CODE}_stock_daily.csv   (日线 OHLCV)
   - {CODE}_stock_fund.csv    (主力资金流数据：major_net_inflow, major_rate)
   - {CODE}_stock_uma.csv     (大单成交：up_vol, down_vol)
3. 运行脚本即可在同目录生成 HTML，可在 Jupyter 内联或浏览器查看。
"""

from pathlib import Path
import pandas as pd
from pyecharts.charts import Kline, Scatter, Grid
from pyecharts import options as opts
from pyecharts.globals import CurrentConfig, NotebookType

# ------------------------------------------------------------------
# 0. 基本参数 (按需修改)
# ------------------------------------------------------------------
# ROOT = Path("./data")          # 数据目录
# CODE = "600000"                # 股票代码
WIN_HIGH = 60                  # “相对高位”滚动窗口 (日)

# Notebook 内联显示 (可选)
CurrentConfig.NOTEBOOK_TYPE = NotebookType.JUPYTER_NOTEBOOK  # Lab 用户请改成 JUPYTER_LAB

# ------------------------------------------------------------------
# 1. 读取三张表
# ------------------------------------------------------------------
daily = pd.read_csv(ROOT / f"{CODE}_stock_daily.csv", parse_dates=["date"])
fund  = pd.read_csv(ROOT / f"{CODE}_stock_fund.csv",  parse_dates=["date"])
anom  = pd.read_csv(ROOT / f"{CODE}_stock_uma.csv",   parse_dates=["date"])

# ------------------------------------------------------------------
# 2. 预处理
# ------------------------------------------------------------------
daily = daily.sort_values("date").reset_index(drop=True)

daily["pct_chg"]   = daily["close"].pct_change().mul(100)

daily["vol_ma5"]   = daily["volume"].rolling(5).mean()

daily["range_pct"] = (daily["high"] - daily["low"]) / daily["close"] * 100

daily["body_pct"]  = (daily["close"] - daily["open"]).abs() / (
    daily["high"] - daily["low"] + 1e-6
) * 100

# 合并资金与大单信息
df = (
    daily.merge(
        fund[["code", "date", "major_net_inflow", "major_rate"]],
        on=["code", "date"], how="left"
    ).merge(
        anom[["code", "date", "up_vol", "down_vol"]],
        on=["code", "date"], how="left"
    )
)

# ------------------------------------------------------------------
# 3. 三类传统主力信号
# ------------------------------------------------------------------
# 3.1 吸筹
df["吸筹"] = (
    (df.major_net_inflow > 0)
    & (df.major_rate > 3)
    & (
        (df.pct_chg.abs() <= 1.5)
        | ((df.low < df.close) & (df.range_pct > 3) & (df.body_pct < 40))
    )
)

# 3.2 加仓
df["加仓"] = (
    (df.major_net_inflow > 0)
    & (df.major_rate > 3)
    & (df.pct_chg >= 3)
    & (df.volume > df.vol_ma5 * 1.3)
)

# 3.3 出货 (显式长上影 / 价涨量缩)
df["出货"] = (
    (df.major_net_inflow < 0)
    & (df.major_rate < -3)
    & (
        # 高位长上影
        (
            (df.close > df.open)
            & (df.body_pct < 50)
            & (df.range_pct > 3)
            & ((df.high - df.close) / df.close > 0.03)
        )
        |
        # 量缩价涨 + 主力流出
        ((df.pct_chg > 0) & (df.volume < df.vol_ma5) & (df.major_rate < 0))
    )
    & (df.down_vol > df.up_vol)
)

# ------------------------------------------------------------------
# 4. 隐形出货 (无长上影 / 下跌派发)
# ------------------------------------------------------------------
# 4.1 连续主力流出 + 价格乏力
df["连续流出天数"] = (
    (df.major_net_inflow < 0)
    .groupby((df.major_net_inflow >= 0).cumsum())
    .cumcount()
    + 1
)
cond_fund  = df["连续流出天数"] >= 3
cond_price = df["pct_chg"].rolling(3).sum() < 1

# 4.2 雪崩式：长阴放量
cond_drop  = (df.pct_chg <= -3) & (df.volume > df.vol_ma5 * 1.5)
cond_fund2 = df.major_net_inflow < 0

# 4.3 滚筒洗衣：窄幅放量
cond_narrow  = df.range_pct < 2
cond_bigvol  = df.volume > df.vol_ma5 * 2
cond_fund3   = df.major_net_inflow < 0

# 4.4 "相对高位"过滤
df["near_high"] = df.close >= df.high.rolling(WIN_HIGH).max() * 0.9

# 4.5 综合判定
df["隐形出货"] = (
    ((cond_fund & cond_price) | (cond_drop & cond_fund2) | (cond_bigvol & cond_narrow & cond_fund3))
    & df["near_high"]
)

# ------------------------------------------------------------------
# 5. 可视化构建
# ------------------------------------------------------------------

def make_scatter_series(flag, name, symbol, color):
    """返回带颜色的 scatter 系列"""
    sel = df.loc[flag]
    if sel.empty:
        # 避免 pyecharts 报空数据错误
        return Scatter().add_xaxis([]).add_yaxis(name, [])
    return (
        Scatter()
        .add_xaxis(sel.date.dt.strftime("%Y-%m-%d").tolist())
        .add_yaxis(
            name,
            sel.low.tolist(),
            symbol=symbol,
            symbol_size=18,
            itemstyle_opts=opts.ItemStyleOpts(color=color),
            label_opts=opts.LabelOpts(is_show=False),
        )
    )

# X 轴 & K 线数据
x_axis = df.date.dt.strftime("%Y-%m-%d").tolist()
k_data = df[["open", "close", "low", "high"]].round(2).values.tolist()

kline = (
    Kline()
    .add_xaxis(x_axis)
    .add_yaxis(
        "K线",
        k_data,
        itemstyle_opts=opts.ItemStyleOpts(
            color="#ec0000",      # 阳线
            color0="#00da3c",     # 阴线
            border_color="#880000",
            border_color0="#008F28",
        ),
    )
    .set_global_opts(
        title_opts=opts.TitleOpts(title=f"{CODE} 主力信号 (含隐形出货)"),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
        xaxis_opts=opts.AxisOpts(
            type_="category",
            is_scale=True,
            axislabel_opts=opts.LabelOpts(rotate=45, interval=10),
        ),
        yaxis_opts=opts.AxisOpts(is_scale=True, splitarea_opts=opts.SplitAreaOpts(is_show=True)),
        datazoom_opts=[opts.DataZoomOpts(type_="inside"), opts.DataZoomOpts(type_="slider")],
        legend_opts=opts.LegendOpts(pos_top="2%"),
    )
)

panel = (
    kline
    .overlap(make_scatter_series(df["吸筹"],     "吸筹",     "diamond",  "#F4B400"))  # 金黄
    .overlap(make_scatter_series(df["加仓"],     "加仓",     "triangle", "#4285F4"))  # 亮蓝
    .overlap(make_scatter_series(df["出货"],     "出货",     "circle",   "#DB4437"))  # 鲜红
    .overlap(make_scatter_series(df["隐形出货"], "隐形出货", "pin",      "#8E44AD"))  # 紫色图钉
)

grid = Grid(init_opts=opts.InitOpts(width="100%", height="800px"))
grid.add(panel, grid_opts=opts.GridOpts(pos_left="5%", pos_right="5%", pos_top="5%", height="90%"))

# ------------------------------------------------------------------
# 6. 渲染
# ------------------------------------------------------------------
# 在 Jupyter 想要内联→取消下一行注释
# grid.render_notebook()

# 导出 HTML
output_path = ROOT / f"{CODE}_signals_kline.html"
grid.render(output_path)
print(f"✅ 图表已导出: {output_path.relative_to(Path.cwd())}")

#%%
