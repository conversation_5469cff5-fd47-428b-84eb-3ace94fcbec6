#!/usr/bin/env python3
"""
测试新的MA5/MA15逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from src.domain.analyze.aggregate.analysis.zh_blank import _Stage4MigrateByScoreAggregate
import pandas as pd
from loguru import logger

def test_ma5_ma15_consolidation_logic():
    """测试基于MA5/MA15的consolidation逻辑"""
    logger.info("开始测试MA5/MA15的consolidation逻辑...")
    
    stage4 = _Stage4MigrateByScoreAggregate(is_concept=False)
    
    # 测试场景
    test_scenarios = [
        {
            "name": "场景1: MA5上方很强",
            "price": 100,
            "ma5": 98,
            "ma15": 95,
            "pct_chg": -1.0,
            "vol_ratio_5": 0.9,
            "expected": "不触发consolidation（仍很强）"
        },
        {
            "name": "场景2: 跌破MA5但在MA15上方（调整）",
            "price": 96,
            "ma5": 98,
            "ma15": 95,
            "pct_chg": -2.0,
            "vol_ratio_5": 0.7,  # 缩量
            "expected": "触发consolidation（调整性质）"
        },
        {
            "name": "场景3: MA5上方大跌但缩量（强势洗盘）",
            "price": 99,
            "ma5": 98,
            "ma15": 95,
            "pct_chg": -4.0,  # 大跌
            "vol_ratio_5": 0.6,  # 缩量
            "expected": "触发consolidation（强势洗盘）"
        },
        {
            "name": "场景4: 跌破MA15且大幅下跌（转弱）",
            "price": 93,
            "ma5": 98,
            "ma15": 95,
            "pct_chg": -6.0,  # 大幅下跌
            "vol_ratio_5": 1.5,
            "expected": "不触发consolidation（真正转弱）"
        },
        {
            "name": "场景5: 跌破MA15但跌幅不大且资金流出不多",
            "price": 94,
            "ma5": 98,
            "ma15": 95,
            "pct_chg": -2.0,  # 小幅下跌
            "vol_ratio_5": 0.8,
            "f1": -3e9,  # 小额流出
            "expected": "可能触发consolidation（需看资金流）"
        }
    ]
    
    for scenario in test_scenarios:
        logger.info(f"\n{scenario['name']}:")
        logger.info(f"  价格: {scenario['price']}, MA5: {scenario['ma5']}, MA15: {scenario['ma15']}")
        logger.info(f"  涨跌幅: {scenario['pct_chg']}%, 量比: {scenario['vol_ratio_5']}")
        
        # 创建模拟数据
        state = pd.Series({
            "phase": "strong_trend",
            "strong_days": 10,
            "high_since_strong": 105.0,
        }, name="测试板块")
        
        today_row = pd.Series({
            "close": scenario["price"],
            "MA5": scenario["ma5"],
            "MA15": scenario["ma15"],
            "pct_chg": scenario["pct_chg"],
            "vol_ratio_5": scenario["vol_ratio_5"],
            "FlowSlope": -0.1,
            "F5": 5.0,
            "F1": scenario.get("f1", -5e9),
        })
        
        prev_row = pd.Series({
            "pct_chg": -1.0,
            "F1": -2e9,
        })
        
        should_enter = stage4._should_enter_consolidation(
            state, today_row, prev_row, 15e9
        )
        
        logger.info(f"  结果: {'触发consolidation' if should_enter else '不触发consolidation'}")
        logger.info(f"  预期: {scenario['expected']}")
        
        # 简单的验证逻辑
        if scenario["name"] == "场景2" or scenario["name"] == "场景3":
            if should_enter:
                logger.info("  ✓ 符合预期")
            else:
                logger.warning("  ⚠️ 可能不符合预期")
        elif scenario["name"] == "场景4":
            if not should_enter:
                logger.info("  ✓ 符合预期")
            else:
                logger.warning("  ⚠️ 可能不符合预期")

def test_consolidation_scoring():
    """测试consolidation状态的评分机制"""
    logger.info("\n开始测试consolidation评分机制...")
    
    stage4 = _Stage4MigrateByScoreAggregate(is_concept=False)
    
    # 测试评分场景
    scoring_scenarios = [
        {
            "name": "重新站上MA5（很强）",
            "price": 100,
            "ma5": 98,
            "ma15": 95,
            "flow_slope": 0.2,
            "f1": 5e9,
            "f5": 10e9,
            "expected_score": "高分（二次启动）"
        },
        {
            "name": "站上MA15但未到MA5（调整结束）",
            "price": 96,
            "ma5": 98,
            "ma15": 95,
            "flow_slope": 0.1,
            "f1": 2e9,
            "f5": 5e9,
            "expected_score": "中等分数"
        },
        {
            "name": "跌破MA15（转弱信号）",
            "price": 93,
            "ma5": 98,
            "ma15": 95,
            "flow_slope": -0.4,
            "f1": -8e9,
            "f5": -5e9,
            "expected_score": "负分（确认转弱）"
        }
    ]
    
    for scenario in scoring_scenarios:
        logger.info(f"\n{scenario['name']}:")
        
        # 创建consolidation状态
        state = pd.Series({
            "phase": "consolidation",
            "consolidation_days": 3,
            "consolidation_high": 99.0,
            "consolidation_low": 94.0,
            "outflow_streak": 0,
        }, name="测试板块")
        
        today_row = pd.Series({
            "close": scenario["price"],
            "MA5": scenario["ma5"],
            "MA15": scenario["ma15"],
            "FlowSlope": scenario["flow_slope"],
            "F1": scenario["f1"],
            "F5": scenario["f5"],
            "vol_ratio_5": 1.0,
        })
        
        # 手动计算评分（模拟_update_consolidation_state的评分逻辑）
        consolidation_score = 0
        
        # 正分
        if scenario["flow_slope"] > 0:
            consolidation_score += 2
        if scenario["price"] > scenario["ma5"]:
            consolidation_score += 3
        elif scenario["price"] > scenario["ma15"]:
            consolidation_score += 1
        if scenario["f1"] > 0 and scenario["f5"] > 0:
            consolidation_score += 2
            
        # 负分
        if scenario["flow_slope"] < -0.3:
            consolidation_score -= 2
        if scenario["price"] < scenario["ma15"]:
            consolidation_score -= 3
        if scenario["f1"] < 0 and scenario["f5"] < 0:
            consolidation_score -= 3
            
        logger.info(f"  计算得分: {consolidation_score}")
        logger.info(f"  预期: {scenario['expected_score']}")
        
        if consolidation_score >= 5:
            logger.info("  → 应该回到strong_trend")
        elif consolidation_score <= -5:
            logger.info("  → 应该进入cooldown")
        else:
            logger.info("  → 继续观察")

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("测试基于MA5/MA15的新逻辑")
    logger.info("=" * 60)
    
    test_ma5_ma15_consolidation_logic()
    test_consolidation_scoring()
    
    logger.info("\n" + "=" * 60)
    logger.info("新的MA5/MA15逻辑总结:")
    logger.info("=" * 60)
    logger.info("🔥 MA5上方: 很强，一般不会触发consolidation")
    logger.info("📊 MA5-MA15之间: 调整性质，容易触发consolidation")
    logger.info("⚠️ 跌破MA15: 转弱信号，需要大幅下跌或大额流出才直接退场")
    logger.info("")
    logger.info("评分机制:")
    logger.info("✅ 重新站上MA5: +3分（很强）")
    logger.info("✅ 站上MA15: +1分（调整结束）")
    logger.info("❌ 跌破MA15: -3分（转弱信号）")
