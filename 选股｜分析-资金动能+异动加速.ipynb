#%%
import pandas as pd
code = "600704"
daily_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_daily_data.csv"
fund_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_individual_fund_flow.csv"
up_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_uma.csv"
daily_data = pd.read_csv(daily_path)
# 读取资金流数据
funds_data = pd.read_csv(fund_path)
#%%
# coding: utf-8
"""
A 股版   资金动能 (Momentum) + up_vol 异动加速度  信号计算
-------------------------------------------------------
◎ 动能持续： ΔFlow 连续 `flow_days` 天 > 0
◎ 动能放大： 近 `flow_days` ΔFlow 总和 > (滚动 N 日 ΔFlow 均值 + k_sigma × σ)
◎ 异动放大： 当日 up_vol > (rolling N 日均值 + up_z × σ) 且 Accel_up 连续 up_days 天 > 0
满足三项 → momentum_signal = 1
-------------------------------------------------------
作者：ChatGPT
"""

import pandas as pd
import numpy as np
from pathlib import Path

# ============== 用户参数 ============== #
# daily_path = Path("daily_kline.csv")        # 日线 CSV：trade_date, open, high, low, close, vol
# fund_path  = Path("moneyflow.csv")          # 资金流 CSV：日期, 大单净流入-净额, 超大单净流入-净额
# up_path    = Path("up_vol.csv")             # 异动量 CSV：date, up_vol

N_window   = 15        # 历史窗口长度 (日)
flow_days  = 2        # ΔFlow 连续天数
k_sigma    = 0.618       # 动能放大 Z σ 系数
up_days    = 2         # Accel_up 连续天数
up_z       = 0.8       # up_vol 放大 Z σ 系数
# ====================================== #

# ---------- 1. 读取 & 统一索引 ---------- #
daily = (pd.read_csv(daily_path, dtype={"trade_date": str})
           .rename(columns={"trade_date": "date"})
           .assign(date=lambda d: pd.to_datetime(d["date"]))
           .set_index("date")
           .sort_index())

fund = (pd.read_csv(fund_path, dtype={"日期": str})
          .rename(columns={
              "日期": "date",
              "大单净流入-净额":   "large_amt",
              "超大单净流入-净额": "elg_amt"
          })
          .assign(date=lambda f: pd.to_datetime(f["date"]))
          .set_index("date")
          .sort_index())

up = (pd.read_csv(up_path, dtype={"date": str})
        .rename(columns={"date": "date", "up_vol": "up_vol"})
        .assign(date=lambda u: pd.to_datetime(u["date"]),
                up_vol=lambda u: u["up_vol"].astype(str)
                                     .str.replace(",", "", regex=False)
                                     .replace("", "0").astype(float))
        .set_index("date")
        .sort_index())

# ---------- 2. 合并 ---------- #
# 主力净流入 = 大单 + 超大单
fund["main_net_amt"] = fund["large_amt"] + fund["elg_amt"]

df = (daily[["open", "high", "low", "close", "vol"]]
        .join(fund[["main_net_amt"]], how="inner")
        .join(up[["up_vol"]],        how="inner")
        .dropna())

# ---------- 3. 指标计算 ---------- #
# ΔFlow
df["dFlow"] = df["main_net_amt"].diff()

# ΔFlow rolling 均值 + σ
df["dFlow_mu"]  = df["dFlow"].rolling(N_window).mean()
df["dFlow_sigma"] = df["dFlow"].rolling(N_window).std()

# 近 flow_days ΔFlow 总和
sum_cols = [f"dFlow_lag{i}" for i in range(flow_days)]
for i in range(1, flow_days):
    df[f"dFlow_lag{i}"] = df["dFlow"].shift(i)
df["sum_dFlow"] = df[["dFlow"] + sum_cols[1:]].sum(axis=1)

# up_vol rolling 均值 + σ
df["up_mu"]  = df["up_vol"].rolling(N_window).mean()
df["up_sigma"] = df["up_vol"].rolling(N_window).std()

# up_vol 3 日平均 & 加速度
df["up_3"]      = df["up_vol"].rolling(3).mean()
df["Accel_up"]  = df["up_3"] - df["up_3"].shift(1)

# ---------- 4. 信号条件 ---------- #
# (1) ΔFlow 连续 flow_days 天 > 0
cond1 = (df["dFlow"] > 0)
for i in range(1, flow_days):
    cond1 &= (df["dFlow"].shift(i) > 0)

# (2) 近 flow_days ΔFlow 总和 > 均值 + k_sigma × σ
cond2 = df["sum_dFlow"] > (df["dFlow_mu"] + k_sigma * df["dFlow_sigma"])

# (3) up_vol 放大 ＆ Accel_up 连续 up_days 天 > 0
cond3 = (df["up_vol"] > (df["up_mu"] + up_z * df["up_sigma"]))
for i in range(up_days):
    cond3 &= (df["Accel_up"].shift(i) > 0)

df["momentum_signal"] = np.where(cond1 & cond2 & cond3, 1, 0)

# ---------- 5. 结果输出 ---------- #
sig = df[df["momentum_signal"] == 1].copy()
print(f"\n★ 共触发 {sig.shape[0]} 个 momentum 信号\n")
if not sig.empty:
    print(1)
    show_cols = ["close", "vol", "main_net_amt", "up_vol",
                 "dFlow", "sum_dFlow", "Accel_up"]
    df = sig[show_cols].tail(15).round(2)

else:
    print("当前参数下暂无满足条件的交易日。")

# 也可保存为 csv 方便回测
# sig.to_csv("momentum_signals.csv")
df

#%% md
为什么 “动能 / 资金流” 一票都触发不了？
* 绝大多数时候是 阈值“过硬” + 数据本身噪声大 造成的。
* A 股里只有强势题材龙头、或极端底部吸筹阶段，才会同时满足
* ΔFlow 连续 3 天 > 0 ＆ Σ3ΔFlow > 历史均值×1.5 ＆ Accel_up 连续 2 天。
* 如果你把样本拉到 2000 只股票，大概每天也就 0~3 只满足 —— 所以单票没撞上很正常。
#%% md
## 我觉得这个是可以拿来做选股用的
#%%
