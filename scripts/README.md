# 主力攻击方向分析脚本

本目录包含用于主力攻击方向分析的自动化脚本。

## 📁 脚本说明

### 1. `daily_zh_blank_analysis.py` - 每日分析脚本

**功能**：
- 运行完整的四阶段主力攻击方向分析
- 生成详细的分析报告
- 自动保存结果到数据库
- 生成Markdown格式的报告文件

**用法**：
```bash
# 手动运行（指定截止日期）
python scripts/daily_zh_blank_analysis.py 2025-06-20

# 使用昨日作为截止日期
# Linux/WSL:
python scripts/daily_zh_blank_analysis.py $(date -d "yesterday" +%Y-%m-%d)
# macOS:
python scripts/daily_zh_blank_analysis.py $(date -v-1d +%Y-%m-%d)

# 设置定时任务（每个交易日下午4点运行，使用昨日数据）
# Linux/WSL:
0 16 * * 1-5 cd /path/to/trading-system && /path/to/trading-system/.venv/bin/python3 scripts/daily_zh_blank_analysis.py $(date -d "yesterday" +\%Y-\%m-\%d)
# macOS:
0 16 * * 1-5 cd /path/to/trading-system && /path/to/trading-system/.venv/bin/python3 scripts/daily_zh_blank_analysis.py $(date -v-1d +\%Y-\%m-\%d)
```

**输出**：
- 控制台日志
- 日志文件：`logs/daily_analysis_YYYYMMDD.log`
- 分析报告：`reports/daily_analysis_YYYYMMDD.md`

### 2. `quick_check.py` - 快速检查脚本

**功能**：
- 快速查看当前观察池状态
- 不重新运行分析，只读取数据库状态
- 彩色输出，重点信息突出显示

**用法**：
```bash
python scripts/quick_check.py
```

**适用场景**：
- 盘中快速查看
- 验证分析结果
- 日常监控

## 🚀 快速开始

### 1. 首次运行

```bash
# 1. 确保在项目根目录
cd /path/to/trading-system

# 2. 激活虚拟环境
source .venv/bin/activate

# 3. 运行每日分析（指定截止日期）
python scripts/daily_zh_blank_analysis.py 2025-06-20

# 4. 查看结果
python scripts/quick_check.py
```

### 2. 设置定时任务

编辑crontab：
```bash
crontab -e
```

添加以下行（每个交易日下午4点运行，使用昨日数据）：
```bash
# Linux/WSL:
0 16 * * 1-5 cd /path/to/trading-system && /path/to/trading-system/.venv/bin/python3 scripts/daily_zh_blank_analysis.py $(date -d "yesterday" +\%Y-\%m-\%d)

# macOS:
0 16 * * 1-5 cd /path/to/trading-system && /path/to/trading-system/.venv/bin/python3 scripts/daily_zh_blank_analysis.py $(date -v-1d +\%Y-\%m-\%d)
```

### 3. 查看历史报告

```bash
# 查看报告目录
ls reports/

# 查看最新报告
cat reports/daily_analysis_$(date +%Y%m%d).md
```

## 📊 输出示例

### 控制台输出
```
🔍 快速检查当前观察池状态

==================================================
📊 当前观察池状态
==================================================
📈 总计: 3 个板块在观察池中

💪 强势池: 0 个板块

🚀 爆发前夕池: 0 个板块

📈 早期吸筹池: 1 个板块
  1. 光伏设备 - 早期吸筹, 更新2025-06-20

❄️ 冷却池: 0 个板块

==================================================
```

### 分析报告示例
```markdown
# 主力攻击方向分析报告
**分析日期**: 2025-06-20
**生成时间**: 2025-06-22 16:00:00

## 观察池状态
**总计**: 1 个板块在观察池中

### 💪 强势池
**数量**: 0 个板块
*暂无板块*

### 🚀 爆发前夕池
**数量**: 0 个板块
*暂无板块*

### 📈 早期吸筹池
**数量**: 1 个板块

| 板块名称 | 评分 | 天数 | 强势天数 | 回撤 |
|---------|------|------|----------|------|
| 光伏设备 | 0 | 0 | 0 | 0.00% |

### ❄️ 冷却池
**数量**: 0 个板块
*暂无板块*
```

## ⚙️ 配置说明

### 分析参数
- **数据窗口**：365天（一年历史数据）
- **分析截止日期**：通过命令行参数指定（必填）
- **板块类型**：仅分析行业板块（不包含概念板块）

### 日志配置
- **控制台日志**：INFO级别，彩色输出
- **文件日志**：DEBUG级别，保留30天
- **日志位置**：`logs/daily_analysis_YYYYMMDD.log`

### 报告配置
- **格式**：Markdown
- **位置**：`reports/daily_analysis_YYYYMMDD.md`
- **内容**：完整的观察池状态和重点关注信息

## 🔧 故障排除

### 常见问题

1. **数据获取失败**
   ```
   解决方案：检查网络连接和数据源可用性
   ```

2. **数据库连接失败**
   ```
   解决方案：检查MySQL服务状态和连接配置
   ```

3. **权限问题**
   ```
   解决方案：确保脚本有读写logs和reports目录的权限
   ```

### 调试模式

运行时添加详细日志：
```bash
PYTHONPATH=. python -c "
from loguru import logger
logger.add('debug.log', level='DEBUG')
exec(open('scripts/daily_zh_blank_analysis.py').read())
"
```

## 📈 使用建议

1. **每日运行时间**：建议在收盘后（下午4点后）运行，确保数据完整
2. **监控频率**：每日运行分析，盘中可用快速检查
3. **重点关注**：优先关注爆发前夕池中的板块
4. **风险控制**：强势池中回撤超过5%的板块需要注意风险

## 🔄 更新日志

- **2025-06-22**: 初始版本，支持完整的四阶段分析
- 支持每日自动分析和报告生成
- 支持快速状态检查
- 完整的日志和错误处理
