#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级股票配置文件生成器
支持批量获取、缓存、增量更新等功能
"""

import os
import json
import re
import sys
import time
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.infra.clients.trading.ak import AkShareClient
from src.domain.analyze.service.analysis import zh_analysis_svc

class StockConfigGenerator:
    """高级股票配置生成器"""
    
    def __init__(self):
        self.ak_client = AkShareClient()
        self.cache_file = project_root / "cache" / "stock_names_cache.json"
        self.cache_expiry_hours = 24  # 缓存24小时
        self.stock_name_cache = self._load_cache()
        
    def _load_cache(self) -> Dict[str, Dict]:
        """加载股票名称缓存"""
        if not self.cache_file.exists():
            return {}
        
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 检查缓存是否过期
            cache_time = datetime.fromisoformat(cache_data.get('cache_time', '2000-01-01'))
            if datetime.now() - cache_time > timedelta(hours=self.cache_expiry_hours):
                print("📅 缓存已过期，将重新获取")
                return {}
            
            print(f"📋 加载缓存: {len(cache_data.get('stocks', {}))} 只股票")
            return cache_data.get('stocks', {})
            
        except Exception as e:
            print(f"⚠️  缓存加载失败: {e}")
            return {}
    
    def _save_cache(self):
        """保存股票名称缓存"""
        try:
            # 确保缓存目录存在
            self.cache_file.parent.mkdir(parents=True, exist_ok=True)
            
            cache_data = {
                'cache_time': datetime.now().isoformat(),
                'stocks': self.stock_name_cache
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 缓存已保存: {self.cache_file}")
            
        except Exception as e:
            print(f"⚠️  缓存保存失败: {e}")
    
    def get_stock_name_with_cache(self, code: str) -> str:
        """获取股票名称（支持缓存）"""
        
        # 检查缓存
        if code in self.stock_name_cache:
            cached_info = self.stock_name_cache[code]
            print(f"📋 缓存命中: {code} - {cached_info['name']}")
            return cached_info['name']
        
        # 从API获取
        try:
            print(f"🔍 API查询: {code}")
            stock_info = self.ak_client.get_zh_stock_info(code)
            stock_name = stock_info.get('股票简称', f'股票{code}')
            
            # 保存到缓存
            self.stock_name_cache[code] = {
                'name': stock_name,
                'updated_at': datetime.now().isoformat(),
                'full_info': stock_info
            }
            
            print(f"✅ API获取成功: {code} - {stock_name}")
            return stock_name
            
        except Exception as e:
            print(f"❌ API获取失败 {code}: {e}")
            
            # 使用备用名称
            fallback_name = f'股票{code}'
            self.stock_name_cache[code] = {
                'name': fallback_name,
                'updated_at': datetime.now().isoformat(),
                'error': str(e)
            }
            return fallback_name
    
    def scan_stock_directories(self, base_path: Path) -> List[str]:
        """扫描股票目录"""
        stock_codes = []
        
        if not base_path.exists():
            print(f"❌ 目录不存在: {base_path}")
            return stock_codes
        
        for item in os.listdir(base_path):
            item_path = base_path / item
            
            # 检查是否为目录且符合股票代码格式（6位数字）
            if item_path.is_dir() and re.match(r'^\d{6}$', item):
                stock_codes.append(item)
        
        return sorted(stock_codes)
    
    def scan_chart_types(self, base_path: Path, stock_code: str) -> set:
        """扫描图表类型"""
        chart_types = set()
        stock_dir = base_path / stock_code
        
        if not stock_dir.exists():
            return chart_types
        
        for file in os.listdir(stock_dir):
            if file.endswith('.html'):
                # 提取图表类型名称
                match = re.match(rf'^{stock_code}_(.+)\.html$', file)
                if match:
                    chart_type = match.group(1)
                    chart_types.add(chart_type)
        
        return chart_types

    def update_stock_analysis(self, stock_codes: List[str], update_analysis: bool = True) -> Dict:
        """更新股票分析数据"""
        update_results = {
            'success': [],
            'failed': [],
            'skipped': []
        }

        if not update_analysis:
            print("⏭️  跳过分析更新")
            return update_results

        print(f"🔄 开始更新 {len(stock_codes)} 只股票的分析数据...")

        for i, code in enumerate(stock_codes, 1):
            try:
                print(f"[{i}/{len(stock_codes)}] 更新分析: {code}")

                # 调用分析函数更新数据
                zh_analysis_svc.analyze_zh_stock_fund_flow(code)

                # 假设成功，因为函数没有返回值，如果有异常会被catch捕获
                update_results['success'].append(code)
                print(f"✅ {code} 分析更新成功")

                # 添加延迟避免过于频繁的请求
                if i % 3 == 0:
                    print("⏸️  暂停1秒...")
                    time.sleep(1)

            except Exception as e:
                update_results['failed'].append(code)
                print(f"❌ {code} 分析更新异常: {e}")

        print("\n📊 分析更新统计:")
        print(f"✅ 成功: {len(update_results['success'])} 只")
        print(f"❌ 失败: {len(update_results['failed'])} 只")
        print(f"⏭️  跳过: {len(update_results['skipped'])} 只")

        if update_results['failed']:
            print(f"❌ 失败的股票: {', '.join(update_results['failed'])}")

        return update_results

    def generate_config(self, force_refresh: bool = False, update_analysis: bool = False) -> Dict:
        """生成配置文件"""
        zh_stock_path = project_root / "assets" / "analysis" / "zh_stock"
        
        print(f"📁 扫描目录: {zh_stock_path}")
        
        # 如果强制刷新，清空缓存
        if force_refresh:
            print("🔄 强制刷新，清空缓存")
            self.stock_name_cache = {}
        
        # 扫描股票代码
        stock_codes = self.scan_stock_directories(zh_stock_path)
        print(f"📊 发现股票: {len(stock_codes)} 只")

        if not stock_codes:
            print("⚠️  未发现任何股票目录")
            return {}


        # 更新股票分析数据（如果需要）
        update_results = self.update_stock_analysis(stock_codes, update_analysis)

        # 生成股票数据
        stocks = []
        all_chart_types = set()

        print("🔍 开始获取股票信息...")
        for i, code in enumerate(stock_codes, 1):
            print(f"[{i}/{len(stock_codes)}] 处理: {code}")

            stock_info = {
                "code": code,
                "name": self.get_stock_name_with_cache(code)
            }
            stocks.append(stock_info)

            # 收集图表类型
            chart_types = self.scan_chart_types(zh_stock_path, code)
            all_chart_types.update(chart_types)

            # 添加小延迟，避免API限制
            if i % 5 == 0:
                time.sleep(0.5)
        
        # 保存缓存
        self._save_cache()
        
        # 图表类型配置
        chart_type_configs = []
        chart_icons = {
            '主力入场信号': '🎯',
            '主力散户线': '📊', 
            '量能堆积': '📈',
            '阶段性底部': '🔍'
        }
        
        chart_colors = {
            '主力入场信号': '#e74c3c',
            '主力散户线': '#3498db',
            '量能堆积': '#2ecc71', 
            '阶段性底部': '#f39c12'
        }
        
        for chart_type in sorted(all_chart_types):
            config = {
                "name": chart_type,
                "icon": chart_icons.get(chart_type, '📊'),
                "color": chart_colors.get(chart_type, '#6c757d')
            }
            chart_type_configs.append(config)
        
        # 生成配置数据
        config_data = {
            "stocks": stocks,
            "chartTypes": chart_type_configs,
            "lastUpdated": datetime.now().isoformat(),
            "totalStocks": len(stocks),
            "totalChartTypes": len(chart_type_configs),
            "generatedBy": "generate_stock_config_advanced.py",
            "cacheStats": {
                "totalCached": len(self.stock_name_cache),
                "cacheFile": str(self.cache_file)
            },
            "analysisUpdateStats": update_results if update_analysis else None
        }
        
        # 保存配置文件
        config_file = zh_stock_path / "stocks.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        print("=" * 60)
        print("✅ 高级配置文件生成完成!")
        print(f"📄 配置文件: {config_file}")
        print(f"📊 股票数量: {len(stocks)}")
        print(f"📈 图表类型: {list(all_chart_types)}")
        print(f"💾 缓存数量: {len(self.stock_name_cache)}")
        print(f"🕒 更新时间: {config_data['lastUpdated']}")

        if update_analysis:
            print(f"🔄 分析更新: 成功 {len(update_results['success'])} / 失败 {len(update_results['failed'])}")

        print("=" * 60)
        
        return config_data

def main():
    """主函数"""
    print("🚀 高级股票配置文件生成器")
    print("=" * 60)
    
    import argparse
    parser = argparse.ArgumentParser(description='生成股票配置文件')
    parser.add_argument('--force-refresh', action='store_true',
                       help='强制刷新缓存，重新获取所有股票信息')
    parser.add_argument('--show-cache', action='store_true',
                       help='显示缓存统计信息')
    parser.add_argument('--update-analysis', action='store_true',
                       help='更新所有股票的分析数据（调用 analyze_zh_stock_fund_flow）')
    
    args = parser.parse_args()
    
    try:
        generator = StockConfigGenerator()
        
        if args.show_cache:
            print(f"📋 缓存文件: {generator.cache_file}")
            print(f"📊 缓存数量: {len(generator.stock_name_cache)}")
            for code, info in list(generator.stock_name_cache.items())[:5]:
                print(f"  • {code}: {info['name']}")
            if len(generator.stock_name_cache) > 5:
                print(f"  ... 还有 {len(generator.stock_name_cache) - 5} 个")
            return
        
        config = generator.generate_config(
            force_refresh=args.force_refresh,
            update_analysis=args.update_analysis
        )
        
        # 显示生成的股票列表
        print("\n📋 生成的股票列表:")
        for stock in config['stocks']:
            print(f"  • {stock['code']} - {stock['name']}")
        
        print(f"\n🎯 总计: {len(config['stocks'])} 只股票")

        # 显示分析更新结果
        if args.update_analysis and config.get('analysisUpdateStats'):
            stats = config['analysisUpdateStats']
            print(f"\n📊 分析更新结果:")
            print(f"✅ 成功更新: {len(stats['success'])} 只股票")
            print(f"❌ 更新失败: {len(stats['failed'])} 只股票")
            if stats['failed']:
                print(f"失败股票: {', '.join(stats['failed'])}")

        print("✅ 配置文件生成成功!")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 生成配置文件失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
