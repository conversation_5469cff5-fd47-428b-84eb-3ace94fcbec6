#!/usr/bin/env python3
"""
每日主力攻击方向分析脚本

用法:
    python scripts/daily_zh_blank_analysis.py <截止日期> [分析类型]

参数:
    截止日期: 分析的截止日期，格式为 YYYY-MM-DD (必填)
    分析类型: 可选参数，支持以下值：
        - industry: 仅分析行业板块 (默认)
        - concept: 仅分析概念板块
        - all: 分析行业板块和概念板块

示例:
    python scripts/daily_zh_blank_analysis.py 2025-06-20                # 仅分析行业板块
    python scripts/daily_zh_blank_analysis.py 2025-06-20 industry      # 仅分析行业板块
    python scripts/daily_zh_blank_analysis.py 2025-06-20 concept       # 仅分析概念板块
    python scripts/daily_zh_blank_analysis.py 2025-06-20 all           # 分析两种板块

功能:
    1. 运行完整的四阶段主力攻击方向分析（支持行业板块和概念板块）
    2. 生成分析报告
    3. 发送结果通知（可选）

建议:
    - 每天收盘后运行一次
    - 可以通过crontab设置定时任务
    - 例如: 0 16 * * 1-5 /path/to/python /path/to/scripts/daily_zh_blank_analysis.py $(date -d "yesterday" +%Y-%m-%d) all
"""

import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.domain.analyze.aggregate.analysis.zh_blank import (
    AnalyzeZhBlankFundFlowAggregate,
)
from loguru import logger


def setup_logging():
    """设置日志配置"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)

    log_file = log_dir / f"daily_analysis_{datetime.now().strftime('%Y%m%d')}.log"

    # 配置loguru
    logger.remove()  # 移除默认handler
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO",
    )
    logger.add(
        log_file,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="1 day",
        retention="30 days",
    )


def parse_arguments():
    """解析命令行参数"""
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print(
            "用法: python scripts/daily_zh_blank_analysis.py <截止日期> [分析类型]",
            file=sys.stderr,
        )
        print(
            "示例: python scripts/daily_zh_blank_analysis.py 2025-06-20",
            file=sys.stderr,
        )
        print(
            "      python scripts/daily_zh_blank_analysis.py 2025-06-20 industry",
            file=sys.stderr,
        )
        print(
            "      python scripts/daily_zh_blank_analysis.py 2025-06-20 concept",
            file=sys.stderr,
        )
        print(
            "      python scripts/daily_zh_blank_analysis.py 2025-06-20 all",
            file=sys.stderr,
        )
        sys.exit(1)

    end_date = sys.argv[1]
    analysis_type = sys.argv[2] if len(sys.argv) == 3 else "industry"

    # 验证分析类型
    valid_types = ["industry", "concept", "all"]
    if analysis_type not in valid_types:
        print(f"❌ 分析类型错误: {analysis_type}", file=sys.stderr)
        print(f"支持的类型: {', '.join(valid_types)}", file=sys.stderr)
        sys.exit(1)

    return end_date, analysis_type


def validate_date(date_str: str) -> str:
    """验证日期格式"""
    try:
        # 验证日期格式
        datetime.strptime(date_str, "%Y-%m-%d")
        return date_str
    except ValueError:
        raise ValueError(f"日期格式错误: {date_str}，请使用 YYYY-MM-DD 格式")


def run_daily_analysis(analysis_date: str, analysis_type: str):
    """运行每日分析"""
    logger.info(
        f"开始每日主力攻击方向分析 - 分析日期: {analysis_date}, 分析类型: {analysis_type}"
    )

    try:
        results = {}

        if analysis_type in ["industry", "all"]:
            # 分析行业板块
            logger.info("开始分析行业板块...")
            industry_analyzer = AnalyzeZhBlankFundFlowAggregate(is_concept=False)
            industry_result = industry_analyzer.run_full_analysis(
                before_days=365, end_date=analysis_date
            )

            if not industry_result.empty:
                logger.info(
                    f"行业板块分析完成，处理了 {len(industry_result)} 个板块状态"
                )
                results["industry"] = {
                    "analyzer": industry_analyzer,
                    "result": industry_result,
                }
            else:
                logger.warning("行业板块分析结果为空")

        if analysis_type in ["concept", "all"]:
            # 分析概念板块
            logger.info("开始分析概念板块...")
            concept_analyzer = AnalyzeZhBlankFundFlowAggregate(is_concept=True)
            concept_result = concept_analyzer.run_full_analysis(
                before_days=365, end_date=analysis_date
            )

            if not concept_result.empty:
                logger.info(
                    f"概念板块分析完成，处理了 {len(concept_result)} 个板块状态"
                )
                results["concept"] = {
                    "analyzer": concept_analyzer,
                    "result": concept_result,
                }
            else:
                logger.warning("概念板块分析结果为空")

        if not results:
            logger.warning("所有分析结果为空，可能是数据获取失败或市场休市")
            return False

        # 生成分析报告
        generate_analysis_report(results, analysis_date, analysis_type)

        logger.info("每日分析完成！")
        return True

    except Exception as e:
        logger.error(f"每日分析失败: {e}")
        import traceback

        logger.error(traceback.format_exc())
        return False


def sort_blanks_by_priority(blanks: list, phase: str) -> list:
    """根据不同阶段的优先级对板块进行排序"""
    if not blanks:
        return blanks

    if phase == "strong_trend":
        # 强势池：按强势天数降序，回撤升序（回撤小的在前）
        return sorted(blanks, key=lambda x: (-x.get("strong_days", 0), x.get("drawdown_pct", 0)))

    elif phase == "pre_burst":
        # 爆发前夕池：按评分降序，天数降序
        return sorted(blanks, key=lambda x: (-x.get("score", 0), -x.get("days_cnt", 0)))

    elif phase == "early_accum":
        # 早期吸筹池：按评分降序，天数降序
        return sorted(blanks, key=lambda x: (-x.get("score", 0), -x.get("days_cnt", 0)))

    elif phase == "consolidation":
        # 整固池：按整固天数升序（刚进入的在前），评分降序
        return sorted(blanks, key=lambda x: (x.get("consolidation_days", 0), -x.get("score", 0)))

    elif phase == "cooldown":
        # 冷却池：按冷却天数升序（快要出来的在前）
        return sorted(blanks, key=lambda x: x.get("cooldown_days", 0))

    else:
        # 默认按评分降序
        return sorted(blanks, key=lambda x: -x.get("score", 0))


def generate_analysis_report(results: dict, analysis_date: str, analysis_type: str):
    """生成分析报告"""
    logger.info("生成分析报告...")

    # 生成报告内容
    report_lines = [
        f"# 主力攻击方向分析报告",
        f"**分析日期**: {analysis_date}",
        f"**分析类型**: {analysis_type}",
        f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
    ]

    # 为每种板块类型生成报告
    type_names = {"industry": "行业板块", "concept": "概念板块"}

    total_all_blanks = 0

    for board_type, type_name in type_names.items():
        if board_type not in results:
            continue

        analyzer = results[board_type]["analyzer"]
        watchlist = analyzer.get_watchlist()
        total_blanks = sum(len(blanks) for blanks in watchlist.values())
        total_all_blanks += total_blanks

        report_lines.extend(
            [
                f"## {type_name}分析结果",
                f"**总计**: {total_blanks} 个板块在观察池中",
                "",
            ]
        )

        # 各池子详情
        pool_names = {
            "strong_trend": "💪 强势池",
            "pre_burst": "🚀 爆发前夕池",
            "early_accum": "📈 早期吸筹池",
            "consolidation": "🔄 整固池",
            "cooldown": "❄️ 冷却池",
        }

        for phase, display_name in pool_names.items():
            # 检查该阶段是否存在于watchlist中
            if phase not in watchlist:
                continue

            blanks = watchlist[phase]
            count = len(blanks)

            report_lines.append(f"### {display_name}")
            report_lines.append(f"**数量**: {count} 个板块")

            if blanks:
                # 对板块进行排序
                sorted_blanks = sort_blanks_by_priority(blanks, phase)

                report_lines.append("")

                # 根据不同阶段使用不同的表头
                if phase == "consolidation":
                    report_lines.append("| 板块名称 | 评分 | 整固天数 | 强势天数 | 回撤 |")
                    report_lines.append("|---------|------|----------|----------|------|")
                else:
                    report_lines.append("| 板块名称 | 评分 | 天数 | 强势天数 | 回撤 |")
                    report_lines.append("|---------|------|------|----------|------|")

                for blank in sorted_blanks[:50]:  # 最多显示50个
                    name = blank["name"]
                    score = blank["score"]
                    days_cnt = blank["days_cnt"]
                    strong_days = blank.get("strong_days", 0)
                    drawdown = blank.get("drawdown_pct", 0)

                    # consolidation池显示整固天数
                    if phase == "consolidation":
                        consolidation_days = blank.get("consolidation_days", 0)
                        report_lines.append(
                            f"| {name} | {score} | {consolidation_days} | {strong_days} | {drawdown:.2%} |"
                        )
                    else:
                        report_lines.append(
                            f"| {name} | {score} | {days_cnt} | {strong_days} | {drawdown:.2%} |"
                        )

                if len(blanks) > 50:
                    report_lines.append(f"| ... | ... | ... | ... | ... |")
                    report_lines.append(f"*（共{len(blanks)}个板块，仅显示前50个）*")
            else:
                report_lines.append("*暂无板块*")

            report_lines.append("")

    # 汇总重点关注
    all_pre_burst = []
    for board_type in results:
        analyzer = results[board_type]["analyzer"]
        watchlist = analyzer.get_watchlist()
        for blank in watchlist["pre_burst"]:
            blank_info = blank.copy()
            blank_info["board_type"] = type_names[board_type]
            all_pre_burst.append(blank_info)

    if all_pre_burst:
        report_lines.extend(
            ["## 🔥 重点关注", "以下板块处于爆发前夕，建议密切关注：", ""]
        )
        for blank in all_pre_burst:
            board_type_label = blank["board_type"]
            report_lines.append(
                f"- **{blank['name']}** ({board_type_label}): 评分{blank['score']}, 已评分{blank['days_cnt']}天"
            )
        report_lines.append("")

    # 保存报告
    report_dir = project_root / "reports"
    report_dir.mkdir(exist_ok=True)

    report_file = (
        report_dir
        / f"daily_analysis_{analysis_date.replace('-', '')}_{analysis_type}.md"
    )

    with open(report_file, "w", encoding="utf-8") as f:
        f.write("\n".join(report_lines))

    logger.info(f"分析报告已保存: {report_file}")

    # 控制台输出摘要
    logger.info("=== 分析摘要 ===")
    logger.info(f"总计: {total_all_blanks} 个板块在观察池中")

    for board_type, type_name in type_names.items():
        if board_type not in results:
            continue
        analyzer = results[board_type]["analyzer"]
        watchlist = analyzer.get_watchlist()
        logger.info(f"\n{type_name}:")
        pool_names = {
            "strong_trend": "💪 强势池",
            "pre_burst": "🚀 爆发前夕池",
            "early_accum": "📈 早期吸筹池",
            "cooldown": "❄️ 冷却池",
        }
        for phase, display_name in pool_names.items():
            count = len(watchlist[phase])
            if count > 0:
                logger.info(f"  {display_name}: {count} 个板块")

    if all_pre_burst:
        logger.info("🔥 重点关注爆发前夕池中的板块！")


def send_notification(success: bool, analysis_date: str, analysis_type: str):
    """发送通知（可选功能）"""
    # 这里可以集成邮件、微信、钉钉等通知方式
    # 暂时只记录日志
    if success:
        logger.info(f"✅ 每日分析成功完成 - {analysis_date} ({analysis_type})")
    else:
        logger.error(f"❌ 每日分析失败 - {analysis_date} ({analysis_type})")


def main():
    """主函数"""
    try:
        # 解析命令行参数
        end_date, analysis_type = parse_arguments()

        # 验证日期格式
        analysis_date = validate_date(end_date)

        # 设置日志
        setup_logging()

        logger.info("=" * 60)
        logger.info("每日主力攻击方向分析脚本启动")
        logger.info(f"分析截止日期: {analysis_date}")
        logger.info(f"分析类型: {analysis_type}")
        logger.info("=" * 60)

        # 运行分析
        success = run_daily_analysis(analysis_date, analysis_type)

        # 发送通知
        send_notification(success, analysis_date, analysis_type)

        logger.info("=" * 60)
        logger.info("每日分析脚本结束")
        logger.info("=" * 60)

        return 0 if success else 1

    except ValueError as e:
        print(f"❌ 参数错误: {e}", file=sys.stderr)
        print(
            "用法: python scripts/daily_zh_blank_analysis.py <截止日期> [分析类型]",
            file=sys.stderr,
        )
        print(
            "示例: python scripts/daily_zh_blank_analysis.py 2025-06-20",
            file=sys.stderr,
        )
        print(
            "      python scripts/daily_zh_blank_analysis.py 2025-06-20 all",
            file=sys.stderr,
        )
        return 1
    except Exception as e:
        print(f"❌ 程序执行失败: {e}", file=sys.stderr)
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
