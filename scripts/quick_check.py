#!/usr/bin/env python3
"""
快速检查脚本 - 查看当前观察池状态

用法:
    python scripts/quick_check.py
    
功能:
    - 快速查看当前各个观察池的状态
    - 不重新运行分析，只读取数据库中的状态
    - 适合盘中快速查看
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.domain.analyze.aggregate.analysis.zh_blank import AnalyzeZhBlankFundFlowAggregate
from loguru import logger

def quick_check():
    """快速检查当前状态"""
    logger.info("🔍 快速检查当前观察池状态")
    
    try:
        # 创建分析器（不运行分析，只读取状态）
        analyzer = AnalyzeZhBlankFundFlowAggregate()
        
        # 获取观察池状态
        watchlist = analyzer.get_watchlist()
        
        # 统计信息
        total_blanks = sum(len(blanks) for blanks in watchlist.values())
        
        print("\n" + "=" * 50)
        print("📊 当前观察池状态")
        print("=" * 50)
        
        if total_blanks == 0:
            print("❌ 当前没有板块在观察池中")
            return
        
        print(f"📈 总计: {total_blanks} 个板块在观察池中\n")
        
        # 各池子详情
        pool_info = {
            'strong_trend': {'name': '💪 强势池', 'color': '\033[92m'},  # 绿色
            'pre_burst': {'name': '🚀 爆发前夕池', 'color': '\033[91m'},  # 红色
            'early_accum': {'name': '📈 早期吸筹池', 'color': '\033[93m'},  # 黄色
            'cooldown': {'name': '❄️ 冷却池', 'color': '\033[94m'}  # 蓝色
        }
        
        for phase, info in pool_info.items():
            blanks = watchlist[phase]
            count = len(blanks)
            
            if count > 0:
                print(f"{info['color']}{info['name']}: {count} 个板块\033[0m")
                
                for i, blank in enumerate(blanks, 1):
                    name = blank['name']
                    score = blank['score']
                    days_cnt = blank['days_cnt']
                    strong_days = blank.get('strong_days', 0)
                    drawdown = blank.get('drawdown_pct', 0)
                    last_date = blank.get('last_date', 'N/A')
                    
                    if phase == 'strong_trend':
                        print(f"  {i}. {name} - 强势{strong_days}天, 回撤{drawdown:.2%}, 更新{last_date}")
                    elif phase == 'pre_burst':
                        print(f"  {i}. {name} - 评分{score}, 已评分{days_cnt}天, 更新{last_date}")
                    elif phase == 'early_accum':
                        print(f"  {i}. {name} - 早期吸筹, 更新{last_date}")
                    else:  # cooldown
                        print(f"  {i}. {name} - 冷却中, 更新{last_date}")
                
                print()
        
        # 重点提醒
        if watchlist['pre_burst']:
            print("\n🔥 重点关注：")
            print("以下板块处于爆发前夕，建议密切关注！")
            for blank in watchlist['pre_burst']:
                print(f"   🎯 {blank['name']} (评分: {blank['score']}/5)")
        
        if watchlist['strong_trend']:
            print("\n💪 强势板块：")
            for blank in watchlist['strong_trend']:
                days = blank.get('strong_days', 0)
                drawdown = blank.get('drawdown_pct', 0)
                if drawdown < -0.05:  # 回撤超过5%
                    print(f"   ⚠️  {blank['name']} (强势{days}天, 回撤{drawdown:.2%}) - 注意回撤风险")
                else:
                    print(f"   ✅ {blank['name']} (强势{days}天, 回撤{drawdown:.2%})")
        
        print("\n" + "=" * 50)
        
    except Exception as e:
        logger.error(f"快速检查失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

def main():
    """主函数"""
    # 简化日志配置
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}",
        level="INFO"
    )
    
    quick_check()

if __name__ == "__main__":
    main()
