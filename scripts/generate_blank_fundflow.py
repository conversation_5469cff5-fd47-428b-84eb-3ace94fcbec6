#!/usr/bin/env python3
"""
生成板块资金流图表脚本

功能:
    1. 获取60天的所有行业板块资金流数据
    2. 生成HTML页面展示折线资金流图
    3. 支持多选板块功能
    4. 通过zh_stock_web.sh启动的web界面访问

用法:
    python scripts/generate_blank_fundflow.py [天数]

参数:
    天数: 可选参数，默认60天

示例:
    python scripts/generate_blank_fundflow.py        # 生成60天数据
    python scripts/generate_blank_fundflow.py 90     # 生成90天数据
"""

import sys
import json
from datetime import datetime, timedelta, date
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Line
from pyecharts.globals import ThemeType
from loguru import logger

from src.domain.store.aggregate.zh.blank import ZhStockBlankAggregate
from src.domain.store.repo.dao import ZhStockBlankFund


class BlankFundFlowGenerator:
    """板块资金流图表生成器"""
    
    def __init__(self, days: int = 300):
        self.days = days
        self.blank_agg = ZhStockBlankAggregate()
        self.output_dir = project_root / "assets" / "analysis" / "zh_stock"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def get_fund_flow_data(self) -> pd.DataFrame:
        """获取板块资金流数据"""
        logger.info(f"开始获取{self.days}天的行业板块资金流数据...")

        # 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=self.days)

        logger.info(f"数据查询范围: {start_date} 到 {end_date}")

        # 获取行业板块资金流数据 (is_concept=False)
        fund_data: List[ZhStockBlankFund] = self.blank_agg.list_stock_blank_fun_from_db(
            start_date=start_date,
            end_date=end_date,
            is_concept=False
        )

        if not fund_data:
            logger.warning(f"未获取到板块资金流数据 (查询范围: {start_date} 到 {end_date})")
            logger.warning("可能原因: 1) 数据还未收集到 2) 数据库中没有该时间段的数据")
            return pd.DataFrame()

        # 转换为DataFrame
        data_list = []
        for item in fund_data:
            data_list.append({
                'name': item.name,
                'date': item.date,
                'major_net_inflow': float(item.major_net_inflow or 0),
                'super_net_inflow': float(item.super_net_inflow or 0),
                'big_net_inflow': float(item.big_net_inflow or 0),
                'medium_net_inflow': float(item.medium_net_inflow or 0),
                'small_net_inflow': float(item.small_net_inflow or 0),
                'major_rate': float(item.major_rate or 0),
            })

        df = pd.DataFrame(data_list)

        # 分析数据覆盖情况
        date_range = df['date'].nunique()
        blank_count = df['name'].nunique()
        earliest_date = df['date'].min()
        latest_date = df['date'].max()

        logger.info(f"获取到{len(df)}条资金流数据，涵盖{blank_count}个板块")
        logger.info(f"数据时间范围: {earliest_date} 到 {latest_date} (共{date_range}个交易日)")

        if date_range < self.days * 0.7:  # 如果实际天数少于预期的70%
            logger.warning(f"实际数据天数({date_range})少于预期天数({self.days})，可能部分数据还未收集")

        return df
    
    def prepare_chart_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """准备图表数据"""
        if df.empty:
            return {}
        
        # 按日期排序
        df = df.sort_values(['name', 'date'])
        
        # 获取所有板块名称
        blank_names = sorted(df['name'].unique())
        
        # 获取所有日期
        dates = sorted(df['date'].unique())
        date_strings = [d.strftime('%Y-%m-%d') for d in dates]
        
        # 准备每个板块的数据
        blank_data = {}
        for blank_name in blank_names:
            blank_df = df[df['name'] == blank_name].sort_values('date')
            
            # 主力净流入数据
            major_values = []
            super_values = []
            big_values = []
            
            for d in dates:
                row = blank_df[blank_df['date'] == d]
                if not row.empty:
                    major_values.append(round(row.iloc[0]['major_net_inflow'] / 10000, 2))  # 转换为万元
                    super_values.append(round(row.iloc[0]['super_net_inflow'] / 10000, 2))
                    big_values.append(round(row.iloc[0]['big_net_inflow'] / 10000, 2))
                else:
                    major_values.append(0)
                    super_values.append(0)
                    big_values.append(0)
            
            blank_data[blank_name] = {
                'major': major_values,
                'super': super_values,
                'big': big_values
            }
        
        return {
            'dates': date_strings,
            'blank_names': blank_names,
            'blank_data': blank_data
        }
    
    def generate_chart(self, chart_data: Dict[str, Any], selected_blanks: List[str] = None) -> str:
        """生成pyecharts图表"""
        if not chart_data:
            return ""

        dates = chart_data['dates']
        blank_names = chart_data['blank_names']
        blank_data = chart_data['blank_data']

        # 如果没有指定选中的板块，默认显示前10个
        if not selected_blanks:
            selected_blanks = blank_names[:10]

        # 创建折线图
        line = (
            Line(init_opts=opts.InitOpts(
                theme=ThemeType.LIGHT,
                width="100%",
                height="600px"
            ))
            .add_xaxis(dates)
        )

        # 只添加选中的板块
        for i, blank_name in enumerate(selected_blanks):
            if blank_name in blank_data:
                major_values = blank_data[blank_name]['major']

                # 设置线条样式
                line_color = self._get_line_color(i)

                line.add_yaxis(
                    series_name=blank_name,
                    y_axis=major_values,
                    linestyle_opts=opts.LineStyleOpts(width=2, color=line_color),
                    label_opts=opts.LabelOpts(is_show=False),
                    markpoint_opts=opts.MarkPointOpts(
                        data=[
                            opts.MarkPointItem(type_="max", name="最大值"),
                            opts.MarkPointItem(type_="min", name="最小值"),
                        ]
                    ),
                )
        
        # 设置全局配置
        line.set_global_opts(
            title_opts=opts.TitleOpts(
                title=f"行业板块资金流向图 (近{self.days}天)",
                subtitle="主力净流入 (万元)",
                pos_left="center"
            ),
            tooltip_opts=opts.TooltipOpts(
                trigger="axis",
                axis_pointer_type="cross"
            ),
            legend_opts=opts.LegendOpts(
                type_="scroll",
                pos_left="left",
                pos_top="10%",
                orient="vertical"
            ),
            xaxis_opts=opts.AxisOpts(
                type_="category",
                boundary_gap=False,
                axislabel_opts=opts.LabelOpts(rotate=45)
            ),
            yaxis_opts=opts.AxisOpts(
                type_="value",
                name="资金流入(万元)",
                axislabel_opts=opts.LabelOpts(formatter="{value}万")
            ),
            datazoom_opts=[
                opts.DataZoomOpts(
                    is_show=True,
                    type_="slider",
                    pos_bottom="5%",
                    range_start=0,
                    range_end=100
                ),
                opts.DataZoomOpts(
                    type_="inside"
                )
            ],
            toolbox_opts=opts.ToolboxOpts(
                is_show=True,
                feature={
                    "saveAsImage": {"title": "保存图片"},
                    "dataZoom": {"title": {"zoom": "区域缩放", "back": "区域缩放还原"}},
                    "restore": {"title": "还原"},
                    "magicType": {"title": {"line": "折线图", "bar": "柱状图"}},
                }
            )
        )
        
        return line.render_embed()
    
    def _get_line_color(self, index: int) -> str:
        """获取线条颜色"""
        colors = [
            "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
            "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
            "#F8C471", "#82E0AA", "#F1948A", "#85C1E9", "#D7BDE2",
            "#A3E4D7", "#F9E79F", "#FADBD8", "#D5DBDB", "#AED6F1"
        ]
        return colors[index % len(colors)]
    
    def generate_html_page(self, chart_html: str, chart_data: Dict[str, Any]) -> str:
        """生成完整的HTML页面"""
        blank_names = chart_data.get('blank_names', [])

        # 将数据转换为JavaScript可用的格式
        js_chart_data = json.dumps(chart_data, ensure_ascii=False, default=str)

        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行业板块资金流向分析</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 28px;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
        }}
        .controls {{
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #fafafa;
        }}
        .control-group {{
            margin-bottom: 15px;
        }}
        .control-group label {{
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }}
        .checkbox-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            background: white;
        }}
        .checkbox-item {{
            display: flex;
            align-items: center;
        }}
        .checkbox-item input {{
            margin-right: 8px;
        }}
        .button-group {{
            margin-top: 15px;
        }}
        .btn {{
            padding: 8px 16px;
            margin-right: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }}
        .btn-primary {{
            background-color: #007bff;
            color: white;
        }}
        .btn-primary:hover {{
            background-color: #0056b3;
        }}
        .btn-secondary {{
            background-color: #6c757d;
            color: white;
        }}
        .btn-secondary:hover {{
            background-color: #545b62;
        }}
        .btn-success {{
            background-color: #28a745;
            color: white;
        }}
        .btn-success:hover {{
            background-color: #1e7e34;
        }}
        .chart-container {{
            padding: 20px;
        }}
        .stats {{
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }}
        .stat-item {{
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }}
        .stat-value {{
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }}
        .stat-label {{
            color: #666;
            margin-top: 5px;
        }}
        .navigation {{
            padding: 20px;
            text-align: center;
            border-top: 1px solid #eee;
        }}
        .nav-link {{
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }}
        .nav-link:hover {{
            background: #0056b3;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 行业板块资金流向分析</h1>
            <p>近{self.days}天主力资金流向趋势 | 数据更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>📊 选择要显示的板块:</label>
                <div class="checkbox-grid" id="blankCheckboxes">
                    {self._generate_checkboxes(blank_names)}
                </div>
            </div>
            
            <div class="button-group">
                <button class="btn btn-primary" onclick="updateChart()">🔄 更新图表</button>
                <button class="btn btn-secondary" onclick="selectAll()">✅ 全选</button>
                <button class="btn btn-secondary" onclick="selectNone()">❌ 全不选</button>
                <button class="btn btn-success" onclick="selectTop10()">🔥 选择前10</button>
            </div>
        </div>
        
        <div class="chart-container">
            <div id="main" style="width: 100%; height: 600px;"></div>
        </div>
        
        <div class="stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">{len(blank_names)}</div>
                    <div class="stat-label">行业板块总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{self.days}</div>
                    <div class="stat-label">数据天数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{len(chart_data.get('dates', []))}</div>
                    <div class="stat-label">交易日数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">主力净流入</div>
                    <div class="stat-label">监控指标</div>
                </div>
            </div>
        </div>
        
        <div class="navigation">
            <a href="index.html" class="nav-link">🏠 返回首页</a>
            <a href="blank_analysis.html" class="nav-link">📈 板块分析</a>
            <a href="stock_analysis.html" class="nav-link">📊 个股分析</a>
        </div>
    </div>

    <script>
        // 板块数据
        const blankNames = {json.dumps(blank_names, ensure_ascii=False)};
        const chartData = {js_chart_data};

        // 初始化ECharts
        const myChart = echarts.init(document.getElementById('main'));

        // 颜色配置
        const colors = [
            "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
            "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
            "#F8C471", "#82E0AA", "#F1948A", "#85C1E9", "#D7BDE2",
            "#A3E4D7", "#F9E79F", "#FADBD8", "#D5DBDB", "#AED6F1"
        ];

        function updateChart() {{
            const checkboxes = document.querySelectorAll('#blankCheckboxes input[type="checkbox"]');
            const selectedBlanks = [];

            checkboxes.forEach(checkbox => {{
                if (checkbox.checked) {{
                    selectedBlanks.push(checkbox.value);
                }}
            }});

            if (selectedBlanks.length === 0) {{
                alert('请至少选择一个板块！');
                return;
            }}

            // 动态更新图表
            renderChart(selectedBlanks);
        }}

        function renderChart(selectedBlanks) {{
            const series = [];

            selectedBlanks.forEach((blankName, index) => {{
                if (chartData.blank_data[blankName]) {{
                    series.push({{
                        name: blankName,
                        type: 'line',
                        data: chartData.blank_data[blankName].major,
                        lineStyle: {{
                            width: 2,
                            color: colors[index % colors.length]
                        }},
                        symbol: 'circle',
                        symbolSize: 4,
                        markPoint: {{
                            data: [
                                {{type: 'max', name: '最大值'}},
                                {{type: 'min', name: '最小值'}}
                            ]
                        }}
                    }});
                }}
            }});

            const option = {{
                title: {{
                    text: '行业板块资金流向图 (近{self.days}天)',
                    subtext: '主力净流入 (万元)',
                    left: 'center'
                }},
                tooltip: {{
                    trigger: 'axis',
                    axisPointer: {{
                        type: 'cross'
                    }}
                }},
                legend: {{
                    type: 'scroll',
                    left: 'left',
                    top: '10%',
                    orient: 'vertical'
                }},
                grid: {{
                    left: '15%',
                    right: '10%',
                    bottom: '15%',
                    top: '20%'
                }},
                xAxis: {{
                    type: 'category',
                    data: chartData.dates,
                    boundaryGap: false,
                    axisLabel: {{
                        rotate: 45
                    }}
                }},
                yAxis: {{
                    type: 'value',
                    name: '资金流入(万元)',
                    axisLabel: {{
                        formatter: '{{value}}万'
                    }}
                }},
                series: series,
                dataZoom: [
                    {{
                        type: 'slider',
                        show: true,
                        bottom: '5%',
                        start: 0,
                        end: 100
                    }},
                    {{
                        type: 'inside'
                    }}
                ],
                toolbox: {{
                    show: true,
                    feature: {{
                        saveAsImage: {{title: '保存图片'}},
                        dataZoom: {{title: {{zoom: '区域缩放', back: '区域缩放还原'}}}},
                        restore: {{title: '还原'}},
                        magicType: {{title: {{line: '折线图', bar: '柱状图'}}}}
                    }}
                }}
            }};

            myChart.setOption(option);
        }}
        
        function selectAll() {{
            const checkboxes = document.querySelectorAll('#blankCheckboxes input[type="checkbox"]');
            checkboxes.forEach(checkbox => checkbox.checked = true);
            updateChart();
        }}

        function selectNone() {{
            const checkboxes = document.querySelectorAll('#blankCheckboxes input[type="checkbox"]');
            checkboxes.forEach(checkbox => checkbox.checked = false);
            myChart.clear();
        }}

        function selectTop10() {{
            const checkboxes = document.querySelectorAll('#blankCheckboxes input[type="checkbox"]');
            checkboxes.forEach((checkbox, index) => {{
                checkbox.checked = index < 10;
            }});
            updateChart();
        }}
        
        // 页面加载时的初始化
        window.addEventListener('load', function() {{
            // 默认选中前10个板块
            selectTop10();

            // 处理URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const selected = urlParams.get('selected');
            if (selected) {{
                const selectedBlanks = selected.split(',');
                const checkboxes = document.querySelectorAll('#blankCheckboxes input[type="checkbox"]');
                checkboxes.forEach(checkbox => {{
                    checkbox.checked = selectedBlanks.includes(checkbox.value);
                }});
                updateChart();
            }}
        }});

        // 响应式处理
        window.addEventListener('resize', function() {{
            myChart.resize();
        }});
    </script>
</body>
</html>
"""
        return html_template
    
    def _generate_checkboxes(self, blank_names: List[str]) -> str:
        """生成复选框HTML"""
        checkboxes = []
        for blank_name in blank_names:
            checkboxes.append(f'''
                <div class="checkbox-item">
                    <input type="checkbox" id="blank_{blank_name}" value="{blank_name}">
                    <label for="blank_{blank_name}">{blank_name}</label>
                </div>
            ''')
        return ''.join(checkboxes)
    
    def generate(self) -> bool:
        """生成板块资金流图表页面"""
        try:
            logger.info("开始生成板块资金流图表...")
            
            # 获取数据
            df = self.get_fund_flow_data()
            if df.empty:
                logger.error("无法获取板块资金流数据")
                return False
            
            # 准备图表数据
            chart_data = self.prepare_chart_data(df)
            if not chart_data:
                logger.error("图表数据准备失败")
                return False
            
            # 生成图表
            chart_html = self.generate_chart(chart_data)
            if not chart_html:
                logger.error("图表生成失败")
                return False
            
            # 生成HTML页面
            html_content = self.generate_html_page(chart_html, chart_data)
            
            # 保存文件
            output_file = self.output_dir / "blank_fundflow.html"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"板块资金流图表已生成: {output_file}")
            logger.info(f"访问地址: http://localhost:8080/blank_fundflow.html")
            
            return True
            
        except Exception as e:
            logger.error(f"生成板块资金流图表失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False


def main():
    """主函数"""
    # 解析命令行参数
    days = 300  # 默认改为300天
    if len(sys.argv) > 1:
        try:
            days = int(sys.argv[1])
            if days <= 0:
                raise ValueError("天数必须大于0")
        except ValueError as e:
            logger.error(f"无效的天数参数: {e}")
            sys.exit(1)

    logger.info(f"开始生成{days}天的板块资金流图表...")

    # 创建生成器并执行
    generator = BlankFundFlowGenerator(days=days)
    success = generator.generate()

    if success:
        logger.info("✅ 板块资金流图表生成成功！")
        logger.info("💡 使用以下命令启动Web服务:")
        logger.info("   bash scripts/zh_stock_web.sh")
        logger.info("   然后访问: http://localhost:8080/blank_fundflow.html")
    else:
        logger.error("❌ 板块资金流图表生成失败！")
        logger.info("💡 如果数据不足，可能需要先运行数据收集脚本")
        sys.exit(1)


if __name__ == "__main__":
    main()
