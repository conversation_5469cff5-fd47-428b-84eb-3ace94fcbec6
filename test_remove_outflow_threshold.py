#!/usr/bin/env python3
"""
测试移除OUTFLOW_THRESHOLD_PCT参数后的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.domain.analyze.aggregate.analysis.zh_blank import AnalyzeZhBlankFundFlowAggregate, _Stage3ScoreByWindowAggregate, _Stage4MigrateByScoreAggregate
from loguru import logger

def test_stage3_parameters():
    """测试Stage3参数"""
    logger.info("=" * 50)
    logger.info("测试Stage3参数")
    logger.info("=" * 50)
    
    try:
        stage3 = _Stage3ScoreByWindowAggregate(is_concept=False)
        
        # 检查参数
        logger.info(f"SCORE_WIN: {stage3.SCORE_WIN}")
        logger.info(f"COOLDOWN_N: {stage3.COOLDOWN_N}")
        logger.info(f"BIG_OUTFLOW: {stage3.BIG_OUTFLOW}")
        
        # 检查是否还有OUTFLOW_THRESHOLD_PCT
        if hasattr(stage3, 'OUTFLOW_THRESHOLD_PCT'):
            logger.error("❌ Stage3仍然有OUTFLOW_THRESHOLD_PCT参数")
            return False
        else:
            logger.info("✅ Stage3已成功移除OUTFLOW_THRESHOLD_PCT参数")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试Stage3参数失败: {e}")
        return False

def test_stage4_parameters():
    """测试Stage4参数"""
    logger.info("\n" + "=" * 50)
    logger.info("测试Stage4参数")
    logger.info("=" * 50)
    
    try:
        stage4 = _Stage4MigrateByScoreAggregate(is_concept=False)
        
        # 检查参数
        logger.info(f"MAX_DRAWDOWN: {stage4.MAX_DRAWDOWN}")
        logger.info(f"MAX_OUT_STREAK: {stage4.MAX_OUT_STREAK}")
        logger.info(f"MAX_STRONG_SECTORS: {stage4.MAX_STRONG_SECTORS}")
        logger.info(f"TIME_DECAY_THRESHOLD: {stage4.TIME_DECAY_THRESHOLD}")
        
        # 检查是否还有OUTFLOW_THRESHOLD_PCT
        if hasattr(stage4, 'OUTFLOW_THRESHOLD_PCT'):
            logger.error("❌ Stage4仍然有OUTFLOW_THRESHOLD_PCT参数")
            return False
        else:
            logger.info("✅ Stage4已成功移除OUTFLOW_THRESHOLD_PCT参数")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试Stage4参数失败: {e}")
        return False

def test_analyzer_initialization():
    """测试分析器初始化"""
    logger.info("\n" + "=" * 50)
    logger.info("测试分析器初始化")
    logger.info("=" * 50)
    
    try:
        # 测试行业板块分析器
        industry_analyzer = AnalyzeZhBlankFundFlowAggregate(is_concept=False)
        logger.info("✅ 行业板块分析器初始化成功")
        
        # 测试概念板块分析器
        concept_analyzer = AnalyzeZhBlankFundFlowAggregate(is_concept=True)
        logger.info("✅ 概念板块分析器初始化成功")
        
        # 测试获取watchlist
        watchlist = industry_analyzer.get_watchlist()
        logger.info(f"✅ 获取watchlist成功，包含{len(watchlist)}个阶段")
        
        for phase, sectors in watchlist.items():
            logger.info(f"  {phase}: {len(sectors)}个板块")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试分析器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_analysis():
    """测试简单分析运行"""
    logger.info("\n" + "=" * 50)
    logger.info("测试简单分析运行")
    logger.info("=" * 50)
    
    try:
        analyzer = AnalyzeZhBlankFundFlowAggregate(is_concept=False)
        
        # 运行简单分析
        result_df = analyzer.run_full_analysis(
            before_days=5,
            end_date="2025-07-21",
            skip_pattern_filter=True
        )
        
        logger.info(f"✅ 分析运行成功，处理了{len(result_df)}个板块")
        
        if not result_df.empty:
            phase_counts = result_df["phase"].value_counts()
            logger.info(f"📊 各阶段分布: {phase_counts.to_dict()}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 分析运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("🧹 测试移除OUTFLOW_THRESHOLD_PCT参数后的功能")
    
    # 测试Stage3参数
    test1 = test_stage3_parameters()
    
    # 测试Stage4参数
    test2 = test_stage4_parameters()
    
    # 测试分析器初始化
    test3 = test_analyzer_initialization()
    
    # 测试简单分析
    test4 = test_simple_analysis()
    
    if test1 and test2 and test3 and test4:
        logger.info("\n🎉 所有测试通过！")
        logger.info("✅ OUTFLOW_THRESHOLD_PCT参数已成功移除")
        logger.info("✅ 所有功能正常工作")
        logger.info("")
        logger.info("📝 修改总结:")
        logger.info("  - Stage3: 移除OUTFLOW_THRESHOLD_PCT，恢复原来的dynamic_threshold逻辑")
        logger.info("  - Stage4: 移除OUTFLOW_THRESHOLD_PCT参数定义")
        logger.info("  - 强势池数量限制保持为10个")
        logger.info("  - 其他参数保持不变")
    else:
        logger.error("❌ 部分测试失败")
        sys.exit(1)
