#%%
import requests
import pandas as pd
#%%
# 获取人气榜数据
url = "https://emappdata.eastmoney.com/stockrank/getAllCurrentList"
payload = {
    "appId": "appId01",
    "globalId": "786e4c21-70dc-435a-93bb-38",
    "marketType": "",
    "pageNo": 1,
    "pageSize": 100,
}
r = requests.post(url, json=payload)
data_json = r.json()
temp_rank_df = pd.DataFrame(data_json["data"])

temp_rank_df["mark"] = [
    "0" + "." + item[2:] if "SZ" in item else "1" + "." + item[2:]
    for item in temp_rank_df["sc"]
]

# temp_rank_df

# 获取人气榜 top 100 数据
params = {
    "ut": "f057cbcbce2a86e2866ab8877db1d059",
    "fltt": "2",
    "invt": "2",
    "fields": "f14,f3,f12,f2",
    "secids": ",".join(temp_rank_df["mark"]) + ",?v=08926209912590994",
}
url = "https://push2.eastmoney.com/api/qt/ulist.np/get"
r = requests.get(url, params=params)
data_json = r.json()
top_data = data_json["data"]["diff"]
#%%
# 查询股票板块
import akshare as ak

info = ak.stock_individual_info_em(symbol="002987")
industry = info.loc[info["item"] == "行业", "value"].iat[0]
print(industry)
info
#%%
import re, json, time, functools, concurrent.futures

# -----------------------------
# 2) 组装 DataFrame & 字段改名
# -----------------------------
df = (pd.DataFrame(top_data)
        .rename(columns={
            "f12": "code",
            "f14": "name",
            "f2":  "close",
            "f3":  "pt_change"
        })
        [["code", "name", "close", "pt_change"]]
      )

# -----------------------------
# 3) 查行业 —— 把速度提上来
#    ↳ industry 做一次本地缓存，防反爬
# -----------------------------
@functools.lru_cache(maxsize=1024)
def fetch_industry(code: str) -> str:
    """拉取单只股票的行业 (东财 F10)，没查到返回空串"""
    try:
        info = ak.stock_individual_info_em(symbol=code)
        return info.loc[info["item"] == "行业", "value"].iat[0]
    except Exception:
        return ""

# 用线程池并发查；几十只股票 2~3 秒就能完
with concurrent.futures.ThreadPoolExecutor(max_workers=8) as pool:
    industries = list(pool.map(fetch_industry, df["code"]))

df["industry"] = industries

# -----------------------------
# 4) OK，检查结果
# -----------------------------

#%%
# 人气榜
df.to_csv("/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/top_data.csv", index=False, encoding="utf-8")

#%%
# 飙升榜

#%%
# 飙升榜
url = "https://emappdata.eastmoney.com/stockrank/getAllHisRcList"
payload = {
    "appId": "appId01",
    "globalId": "786e4c21-70dc-435a-93bb-38",
    "marketType": "",
    "pageNo": 1,
    "pageSize": 100,
}
r = requests.post(url, json=payload)
data_json = r.json()
temp_rank_df = pd.DataFrame(data_json["data"])

temp_rank_df["mark"] = [
    "0" + "." + item[2:] if "SZ" in item else "1" + "." + item[2:]
    for item in temp_rank_df["sc"]
]
",".join(temp_rank_df["mark"]) + "?v=08926209912590994"
params = {
    "ut": "f057cbcbce2a86e2866ab8877db1d059",
    "fltt": "2",
    "invt": "2",
    "fields": "f14,f3,f12,f2",
    "secids": ",".join(temp_rank_df["mark"]) + ",?v=08926209912590994",
}
url = "https://push2.eastmoney.com/api/qt/ulist.np/get"
r = requests.get(url, params=params)
data_json = r.json()
up_data = data_json["data"]["diff"]
# up_data
#%%
import re, json, time, functools, concurrent.futures

# -----------------------------
# 2) 组装 DataFrame & 字段改名
# -----------------------------
df = (pd.DataFrame(up_data)
        .rename(columns={
            "f12": "code",
            "f14": "name",
            "f2":  "close",
            "f3":  "pt_change"
        })
        [["code", "name", "close", "pt_change"]]
      )

# -----------------------------
# 3) 查行业 —— 把速度提上来
#    ↳ industry 做一次本地缓存，防反爬
# -----------------------------
@functools.lru_cache(maxsize=1024)
def fetch_industry(code: str) -> str:
    """拉取单只股票的行业 (东财 F10)，没查到返回空串"""
    try:
        info = ak.stock_individual_info_em(symbol=code)
        return info.loc[info["item"] == "行业", "value"].iat[0]
    except Exception:
        return ""

# 用线程池并发查；几十只股票 2~3 秒就能完
with concurrent.futures.ThreadPoolExecutor(max_workers=8) as pool:
    industries = list(pool.map(fetch_industry, df["code"]))

df["industry"] = industries
df
#%%
df.to_csv("/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/up_data.csv", index=False, encoding="utf-8")

#%%

#%% md
## Stage 0 - 数据准备方案（查阅版）

| 目标 | 把原始榜单 + 板块信息整理成 3 张标准化表，供 Stage 1-4 直接读取 |
| -- | ---------------------------------------- |

### 一、所需原始文件

| 文件                                  | 必含字段                                     | 说明             |
| ----------------------------------- | ---------------------------------------- | -------------- |
| **`top_data.csv`**   (人气榜)          | `code, name, close, pt_change, industry` | 每日热度榜 100 只股票  |
| **`up_data.csv`**    (飙升榜)          | 同上                                       | 每日飙升榜 100 只股票  |
| **`all_industries_daily_data.csv`** | `板块名称, 板块代码, …`                          | 含行业名称与 BK 板块代码 |

> *仅单日榜单时，脚本内统一填 `date = "2025-07-04"`；如果后续多日榜单，自带 `date` 列即可。*

### 二、处理步骤

1. **读入榜单**

   * `pd.read_csv(top_path / up_path)`
   * 填充 `date` 列（或用文件自带）
   * `code` → 字符串 & 补零到 6 位
2. **读入行业日表**

   * 取 `sector_name (板块名称)`、`sector_code (板块代码)`
   * 去重，`sector_code` 去掉 “BK” 前缀并补零
3. **构造 `map_df`（股票⇄板块映射）**

   * 对每个 `sector_code` 调用
     `ak.stock_board_industry_cons_em(symbol=sector_code)`
   * 收集成份股，生成字段：`code, sector, sector_code`
4. **生成标准输出表**

   | 表名            | 字段                                           | 用途             |
   | ------------- | -------------------------------------------- | -------------- |
   | **`hot_df`**  | `date, code, name, sector, close, pt_change` | 供 Stage 1 统计 P |
   | **`boom_df`** | 同上                                           | 供 Stage 1 统计 S |
   | **`map_df`**  | `code, sector, sector_code`                  | 统计分母 & 后续资金分析  |
5. **落盘**

   * `stage0_hot_df.csv`
   * `stage0_boom_df.csv`
   * `stage0_map_df.csv`

### 三、关键注意事项

| 点                | 说明                                                        |
| ---------------- | --------------------------------------------------------- |
| **股票代码格式**       | 全部 `SH600000 / SZ000001` → 补零 6 位                         |
| **行业名称 vs 板块名称** | `industry` 字段直接作 `sector`；若要细分，可在 `map_df` 加 `sector_lv2` |
| **Akshare 抓取速度** | 首次全量较慢，可把抓取结果缓存为 Parquet，下次直接读                            |
| **后续多日扩展**       | 只需把新榜单文件追加、填新 `date`，脚本支持批量写入                             |

### 四、脚本入口参数

```python
TOP_PATH = "top_data.csv"
UP_PATH  = "up_data.csv"
IND_PATH = "all_industries_daily_data.csv"
TODAY    = "2025-07-04"      # ▶ 改成当日日期或从文件读取
```

### 五、脚本输出示例

```
✅ Stage 0 Finished:
  • hot_df  -> stage0_hot_df.csv  (100, 6)
  • boom_df -> stage0_boom_df.csv (100, 6)
  • map_df  -> stage0_map_df.csv  (≈6 k, 3)
```

> **完成后**，Stage 1 直接读取 `stage0_hot_df.csv / stage0_boom_df.csv / stage0_map_df.csv`，即可计算 P、S、D 和 Score\_raw。
> 若后续需要资金验证（Stage 3），只要在 `quota_df` 中加入 `main_net_flow, vol, close` 等列即可，无须改动 Stage 0。

#%%
pre_path = "/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze"
#%%
# =====================  Stage 0  — 数据准备  =====================
# 文件路径
TOP_PATH   = f"{pre_path}/top_data.csv"                  # 人气榜
UP_PATH    = f"{pre_path}/up_data.csv"                   # 飙升榜
IND_PATH   = f"{pre_path}/all_industries_daily_data.csv" # 行业日线，含板块代码
TODAY      = "2025-07-04"                    # 当天榜单日期（yyyy-mm-dd）

HOT_DF_PATH = f"{pre_path}/stage0_hot_df.csv"
BOOM_DF_PATH = f"{pre_path}/stage0_boom_df.csv"
MAP_DF_PATH = f"{pre_path}stage0_map_df.csv"

import pandas as pd
from datetime import datetime
import akshare as ak

# ---------- 1. 读取榜单 ----------
top_df = pd.read_csv(TOP_PATH)
up_df  = pd.read_csv(UP_PATH)

# 填充 date 列
for df in (top_df, up_df):
    df["date"] = pd.to_datetime(TODAY)

# 股票代码补零到 6 位（若已规范，可跳过）
top_df["code"] = top_df["code"].astype(str).str.zfill(6)
up_df["code"]  = up_df["code"].astype(str).str.zfill(6)

# ---------- 2. 读取行业日表（板块代码） ----------
ind_df = pd.read_csv(IND_PATH)
ind_df.rename(columns={"板块名称":"sector_name",
                       "板块代码":"sector_code"}, inplace=True)

# 后续 Stage 3 可能要用到日行情，这里先留着；此处仅取 sector_code/name
sector_dict = ind_df[["sector_name","sector_code"]].drop_duplicates()

# ---------- 3. 构造 股票⇄板块映射 map_df ----------
# 3. 构造 map_df
records = []
for sname, scode_bk in sector_dict.itertuples(index=False):
    # scode_bk 形如 "BK0475"
    try:
        cons = ak.stock_board_industry_cons_em(symbol=scode_bk)
    except Exception as e:
        print(f"[Warning] {sname}({scode_bk}) 拉取失败：{e}")
        continue
    for stk in cons["代码"].astype(str).str.zfill(6):
        records.append({
            "code":        stk,
            "sector":      sname,     # 板块名称
            "sector_code": scode_bk   # 保留 BK 前缀
        })

map_df = pd.DataFrame(records)

# ---------- 4. 生成 hot_df & boom_df （标准化输出） ----------
keep_cols = ["date","code","name","industry","close","pt_change"]
hot_df  = top_df[keep_cols].copy()
boom_df = up_df[keep_cols].copy()

# industry → sector 统一命名
hot_df.rename (columns={"industry":"sector"}, inplace=True)
boom_df.rename(columns={"industry":"sector"}, inplace=True)

# ---------- 5. 输出到本地 / 数据库 ----------
hot_df.to_csv (HOT_DF_PATH,  index=False)
boom_df.to_csv(BOOM_DF_PATH, index=False)
map_df.to_csv (MAP_DF_PATH,  index=False)

print("✅ Stage 0 Finished:")
print(f"  • hot_df  -> stage0_hot_df.csv  ({hot_df.shape})")
print(f"  • boom_df -> stage0_boom_df.csv ({boom_df.shape})")
print(f"  • map_df  -> stage0_map_df.csv  ({map_df.shape})")

#%%
hot_df
#%%
boom_df
#%%
map_df
#%% md
## Stage 1 – 榜单热度计分方案（查阅版）

> **目标**：把 Stage 0 产出的三张表 → 计算每日每个板块的 **P / S / D** 及 **Score\_raw**，供 Stage 2–4 使用。

---

### 一、输入文件

| 文件                       | 关键列                         | 说明           |
| ------------------------ | --------------------------- | ------------ |
| **`stage0_hot_df.csv`**  | `date, code, sector`        | 人气榜（Top）上榜个股 |
| **`stage0_boom_df.csv`** | 同上                          | 飙升榜（Up）上榜个股  |
| **`stage0_map_df.csv`**  | `code, sector, sector_code` | 股票⇄板块映射与成份股池 |

---

### 二、核心指标定义

| 指标                 | 计算                                | 说明           |
| ------------------ | --------------------------------- | ------------ |
| **P** (Popularity) | 板块当日人气榜上榜股票数 `N_hot`              | 热度、活跃度       |
| **S** (Surging)    | 板块当日飙升榜上榜股票数 `N_boom`             | 爆发力，权重更高     |
| **D** (Duplicate)  | 同一股票既在人气又在飙升榜的数量                  | “又热又强”       |
| **Wb** (板块基准权重)    | `1 / 成份股总数`                       | 抵消大板块天然优势    |
| **Score\_raw**     | \[ (P × 1 + S × 2 + D × 1) × Wb ] | Stage 1 最终输出 |

> *注：成份股总数从 `map_df.groupby("sector")["code"].nunique()` 得到。*

---

### 三、算法步骤

1. **加载数据**

   ```python
   hot   = pd.read_csv("stage0_hot_df.csv",  parse_dates=["date"])
   boom  = pd.read_csv("stage0_boom_df.csv", parse_dates=["date"])
   mp_df = pd.read_csv("stage0_map_df.csv")
   ```
2. **准备分母（板块成份股数）**

   ```python
   denom_tbl = mp_df.groupby("sector")["code"].nunique().rename("n_constituents")
   ```
3. **统计 P / S / D**

   ```python
   # 人气榜数量
   p_tbl = (hot.groupby(["date", "sector"])["code"]
              .nunique().rename("P"))

   # 飙升榜数量
   s_tbl = (boom.groupby(["date", "sector"])["code"]
              .nunique().rename("S"))

   # 重复个股
   both = pd.merge(hot[["date","code","sector"]],
                   boom[["date","code","sector"]],
                   on=["date","code","sector"])
   d_tbl = (both.groupby(["date","sector"])["code"]
               .nunique().rename("D"))
   ```
4. **合并并计算 Wb 与 Score\_raw**

   ```python
   score_tbl = (
       pd.concat([p_tbl, s_tbl, d_tbl], axis=1)
         .fillna(0)
         .reset_index()
         .merge(denom_tbl.reset_index(), on="sector", how="left")
   )

   score_tbl["Wb"] = 1 / score_tbl["n_constituents"]
   score_tbl["Score_raw"] = (
       (score_tbl["P"] + 2*score_tbl["S"] + score_tbl["D"])
       * score_tbl["Wb"]
   )
   ```
5. **输出**

   ```python
   score_tbl.to_csv("stage1_score_tbl.csv", index=False)
   ```

   输出字段示例：
   `date, sector, P, S, D, n_constituents, Wb, Score_raw`

---

### 四、边界与异常处理

| 情况        | 策略                            |
| --------- | ----------------------------- |
| 成份股数为 0   | 该板块记录忽略或设 `Wb = 0`，避免除零       |
| 上榜个股找不到映射 | 记录到 `unmapped_codes.txt` 便于后补 |
| 同一股票映射多板块 | 计入所有映射板块；**分母**仍按各自板块总成份股     |

---

### 五、可选扩展

| 功能             | 怎么做                        | 用途             |
| -------------- | -------------------------- | -------------- |
| **P% / S% 占比** | `P / n_constituents` 等     | 直接输出占比列，便于直观比较 |
| 行业/概念双层板块      | 在 `map_df` 增员 `sector_lv2` | 细分主题追踪         |
| 多日滚动合并         | 可在 Stage 1 直接追加新计算行        | 减少 I/O 开销      |

---

### 六、示例输出（单日片段）

| date       | sector | P | S | D | n\_constituents | Score\_raw |
| ---------- | ------ | - | - | - | --------------- | ---------- |
| 2025-07-04 | 银行     | 3 | 1 | 1 | 23              | 0.26       |
| 2025-07-04 | 半导体    | 6 | 3 | 2 | 58              | 0.40       |
| …          | …      | … | … | … | …               | …          |

---

#### 说明

* **P / S / D** 只关心“数量”，权重体现在公式里。
* **Score\_raw** 越高表示该板块当日上榜股票更密集，且飙升股权重 2 倍。
* 下一阶段（Stage 2）会基于 `Score_raw` 滚动 3 日 UpStreak 判定动量。

把这份方案与 Stage 0 一起保存，后续驱动 Notebook 或生产任务都能对照执行。

| 字段名                 | 数据类型             | 含义         | 如何计算 / 取值                                      | 典型范围与示例                                    | 在后续流程中的作用                              |                            |                           |
| ------------------- | ---------------- | ---------- | ---------------------------------------------- | ------------------------------------------ | -------------------------------------- | -------------------------- | ------------------------- |
| **date**            | `datetime64[ns]` | 交易日        | 由榜单文件或脚本常量填充，如 `2025-07-04`                    | 单一日期或随历史扩展                                 | 作为滚动窗口和时间序列索引（Stage 2 的动量判断等）          |                            |                           |
| **sector**          | `str`            | 板块名称（中文）   | 取自 `map_df` 中的 `sector` 列，例如 “半导体设备”、“银行”      | 与券商/申万行业或概念名称一致                            | 主键之一，标识被评估的主题                          |                            |                           |
| **P**               | `int64`          | **人气榜计数**  | 当日该板块上榜的股票只数<br> (\displaystyle P\_{d,k}=      | {i\in H\_d\mid \mathrm{Sec}(i)=k}          | )                                      | 0 – 数十；小板块常见 0-5，大板块可达 10+ | Score\_raw 的加分项之一；衡量交易活跃度 |
| **S**               | `int64`          | **飙升榜计数**  | 当日该板块在飙升榜的股票只数<br> (\displaystyle S\_{d,k}=    | {i\in U\_d\mid \mathrm{Sec}(i)=k}          | )                                      | 0 – 两位数；权重 2 倍             | 反映短期爆发力，Score\_raw 中权重最高  |
| **D**               | `int64`          | **重复计数**   | 当日既在人气榜又在飙升榜的股票只数<br> (\displaystyle D\_{d,k}= | {i\in H\_d\cap U\_d\mid \mathrm{Sec}(i)=k} | )                                      | 0 – P、S 的交集大小              | 捕捉“又热又强”的核心票              |
| **n\_constituents** | `int64`          | **成份股总数**  | 该板块全部股票数量<br> (\displaystyle N\_k=             | C\_k                                       | )                                      | 从个位到数百；例：银行 25、半导体 300     | 用作分母修正板块规模                |
| **Wb**              | `float64`        | **板块基准权重** | $\displaystyle W_{b,k}=1/N_k$                  | 0.001 – 0.1；成份股越少权重越大                      | 把绝对计数转为“密度”，消除大板块天然优势                  |                            |                           |
| **Score\_raw**      | `float64`        | **原始热度得分** | $\displaystyle (P+2S+D)\times W_{b,k}$         | 0 – 1 左右；极端热点 > 1                          | Stage 2 用来判断 3 日 UpStreak；后续排序与筛选的核心度量 |                            |                           |


#%%
# =====================  Stage 1 – 榜单热度计分  =====================
# 依赖：pandas >= 1.3
import pandas as pd
from pathlib import Path

# ---------- 0. 参数 ----------
HOT_PATH = f"{pre_path}/stage0_hot_df.csv"
BOOM_PATH = f"{pre_path}/stage0_boom_df.csv"
MAP_PATH = f"{pre_path}stage0_map_df.csv"

OUT_PATH  = Path(f"{pre_path}/stage1_score_tbl.csv")

# ---------- 1. 读入数据 ----------
hot_df  = pd.read_csv(HOT_PATH,  parse_dates=["date"])
boom_df = pd.read_csv(BOOM_PATH, parse_dates=["date"])
map_df  = pd.read_csv(MAP_PATH)

# ---------- 2. 分母：板块成份股总数 ----------
# 注：同一股票可映射多板块；按 sector 去重后统计
denom_tbl = (
    map_df.groupby("sector")["code"]
          .nunique()
          .rename("n_constituents")
)

# ---------- 3. 统计  P / S ----------
p_tbl = (
    hot_df.groupby(["date", "sector"])["code"]
          .nunique()
          .rename("P")
)

s_tbl = (
    boom_df.groupby(["date", "sector"])["code"]
           .nunique()
           .rename("S")
)

# ---------- 4. 统计  D （重复个股）----------
both = pd.merge(
            hot_df[["date", "code", "sector"]],
            boom_df[["date", "code", "sector"]],
            on=["date", "code", "sector"]
        )
d_tbl = (
    both.groupby(["date", "sector"])["code"]
         .nunique()
         .rename("D")
)

# ---------- 5. 合并并计算 Score_raw ----------
score_tbl = (
    pd.concat([p_tbl, s_tbl, d_tbl], axis=1)
      .fillna(0)
      .reset_index()
      .merge(denom_tbl.reset_index(), on="sector", how="left")
)

# 计算板块基准权重 Wb = 1 / N_k
score_tbl["Wb"] = 1.0 / score_tbl["n_constituents"]

# 计算 Score_raw = (P + 2S + D) * Wb
score_tbl["Score_raw"] = (
    (score_tbl["P"] + 2 * score_tbl["S"] + score_tbl["D"])
    * score_tbl["Wb"]
)

# ---------- 6. 输出 ----------
score_tbl.sort_values(["date", "Score_raw"], ascending=[True, False], inplace=True)
score_tbl.to_csv(OUT_PATH, index=False)
print(f"✅ Stage 1 Finished – 已写出 {OUT_PATH} ({score_tbl.shape[0]} rows)")

# 如果在 Notebook，希望立即查看前几行：
try:
    from ace_tools import display_dataframe_to_user  # ChatGPT 专用
    display_dataframe_to_user("Score Table (head)", score_tbl.head())
except Exception:
    display(score_tbl)



#%% md
## Stage 2 – 热度动量检测（阅读版）

> **目的**：在 Stage 1 得到的 `Score_raw` 时间序列上打 “动量” 标志，告诉后续流程：
> “这个板块的热度已**连续上升**（Momentum = 1）还是仍属平淡/回落（Momentum = 0）”。

---

### 1. 输入

| 文件                         | 关键列                                     |
| -------------------------- | --------------------------------------- |
| **`stage1_score_tbl.csv`** | `date, sector, Score_raw`（外加 P/S/D 等字段） |

---

### 2. 核心逻辑

| 步骤                  | 操作                                                   | 默认参数                               | 说明                         |
| ------------------- | ---------------------------------------------------- | ---------------------------------- | -------------------------- |
| **a. 排序**           | 按 `sector`、`date` **升序** 排序                          | —                                  | 保证时间窗口正确滑动                 |
| **b. UpStreak 计算**  | 对每个板块独立检查 **最近 N=3 个交易日**的 `Score_raw` 是否 **严格递增**   | `N = 3`                            | 连续递增则记 `UpStreak = 1`，否则 0 |
| **c. （可选）Slope 计算** | 对最近 N=3 日做线性回归，取斜率                                   | `slope_threshold = 0`（正值即通过）       | 用于捕捉“递增但偶有持平”或“近似线性上升”的情况  |
| **d. 动量判定**         | `Momentum = 1` 若 **UpStreak = 1** *或* **Slope ≥ 阈值** | `use_slope = False`（默认只看 UpStreak） | 可打开 `use_slope` 以增强平滑性     |

---

### 3. 输出表字段

| 字段名            | 类型        | 含义                     | 典型范围 / 示例              |
| -------------- | --------- | ---------------------- | ---------------------- |
| **date**       | datetime  | 交易日                    | `2025-07-08`           |
| **sector**     | str       | 板块名称                   | “半导体设备”                |
| **Score\_raw** | float     | 当日原始热度得分（直接沿用 Stage 1） | 0 – 1+                 |
| **UpStreak**   | int (0/1) | 最近 N 日是否**严格递增**       | 0 或 1                  |
| **Slope\_N**   | float     | 最近 N 日线性回归斜率（若启用）      | -0.2 – +1.0            |
| **Momentum**   | int (0/1) | 综合动量标志                 | 0 = 无连续上升<br>1 = 热度正加速 |

> *在默认配置 (`use_slope = False`) 下，`Slope_N` 可省略或置 NaN，仅作调参时输出。*

---

### 4. 可调参数一览

| 参数                | 默认值       | 作用                           | 调整建议                     |
| ----------------- | --------- | ---------------------------- | ------------------------ |
| `N`               | **3**     | 滚动窗口长度（天数）                   | 若想捕捉更持久趋势可设 5；捕捉更短节奏可设 2 |
| `use_slope`       | **False** | 是否启用斜率判定                     | 对波动大的板块可设 True           |
| `slope_threshold` | 0         | 斜率阈值，仅当 `use_slope=True` 时生效 | 典型范围 0 – 0.2（越大越苛刻）      |

---

### 5. 典型记录示意

| date       | sector | Score\_raw | UpStreak | Momentum |
| ---------- | ------ | ---------- | -------- | -------- |
| 2025-07-08 | 银行     | 0.32       | 1        | 1        |
| 2025-07-08 | 半导体    | 0.28       | 0        | 0        |
| 2025-07-08 | 机器人    | 0.15       | 1        | 1        |

---

### 6. 用途概览

1. **Stage 3 资金验证** 只对 `Momentum = 1` 的板块继续深入，节约计算。
2. **盘中监控** 若实时刷新 `Score_raw`（分钟级或小时级），Stage 2 可快速提示“热度连续抬升”板块。
3. **可视化** 在热度趋势图上用不同颜色/标记突出 Momentum=1 的区段，便于研报展示。

---

### 7. 必要时的调参流程

1. 回测不同 `N` 与 `slope_threshold` 对后续 **Confirmed 命中率** 的影响。
2. 选择在捕捉足够早期信号与避免噪声之间的最佳折中。

只要遵循以上字段与逻辑，Stage 2 就能与 Stage 1/3 无缝衔接，同时保留足够的灵活性做日后优化。

#%% md
| 字段                    | 作用                                                         |
| --------------------- | ---------------------------------------------------------- |
| **UpStreak**          | 最近 N 日 `Score_raw` **严格递增** (1/0)                          |
| **DownStreak**        | 最近 N 日 **严格递减** (1/0)                                      |
| **Momentum\_Attack**  | 进攻动量标志（默认 = UpStreak；若启用斜率，则 “UpStreak 或 Slope ≥阈值”）       |
| **Momentum\_Retreat** | 退潮动量标志（默认 = DownStreak；若启用斜率，则 “DownStreak 或 Slope ≤ −阈值”） |
| *(Slope\_N)*          | 最近 N 日回归斜率（仅 `USE_SLOPE=True` 时存在）                         |

#%%
# ======================  Stage 2  —  Attack & Retreat Momentum  ======================
# 依赖：pandas >= 1.3   （若用斜率功能需 sklearn）
import pandas as pd
import numpy as np
from pathlib import Path

# ---------- 0. 参数 ----------
IN_PATH       = f"{pre_path}/stage1_score_tbl.csv"     # Stage 1 结果
OUT_PATH      = f"{pre_path}/stage2_momentum_tbl.csv"   # 输出文件
WINDOW_N      = 3      # 连续递增 / 递减 窗口长度（天）
USE_SLOPE     = False  # 是否启用斜率判定（提高容错）
SLOPE_THRESH  = 0.0    # 斜率阈值（仅当 USE_SLOPE=True 生效）

# ---------- 1. 读取并排序 ----------
df = pd.read_csv(IN_PATH, parse_dates=["date"])
df.sort_values(["sector", "date"], inplace=True)   # 保证时间顺序

# ---------- 2. 计算 UpStreak & DownStreak ----------
def rolling_strict(series: pd.Series, n: int, mode: str) -> pd.Series:
    """
    对每个窗口检查严格递增或递减。
    mode = 'up'  → x0 < x1 < … < x{n-1}
    mode = 'down'→ x0 > x1 > … > x{n-1}
    返回 0/1 序列，长度与 series 相同（前 n-1 位为 NaN→0）。
    """
    if mode not in ("up", "down"):
        raise ValueError("mode must be 'up' or 'down'")
    compare = (lambda x, y: x < y) if mode == "up" else (lambda x, y: x > y)

    def check_window(arr):
        return int(all(compare(arr[i], arr[i + 1]) for i in range(n - 1)))

    return (series
            .rolling(window=n, min_periods=n)
            .apply(check_window, raw=True)
            .fillna(0)
            .astype(int))

grouped = df.groupby("sector")["Score_raw"]
df["UpStreak"]   = grouped.apply(lambda s: rolling_strict(s, WINDOW_N, "up")).reset_index(level=0, drop=True)
df["DownStreak"] = grouped.apply(lambda s: rolling_strict(s, WINDOW_N, "down")).reset_index(level=0, drop=True)

# ---------- 3. （可选）斜率判定 ----------
if USE_SLOPE:
    from sklearn.linear_model import LinearRegression

    def rolling_slope(series: pd.Series, n: int) -> pd.Series:
        def slope_of_window(arr):
            x = np.arange(n).reshape(-1, 1)
            model = LinearRegression().fit(x, arr.reshape(-1, 1))
            return model.coef_[0, 0]

        return (series.rolling(window=n, min_periods=n)
                      .apply(lambda x: slope_of_window(x.values), raw=False))

    df[f"Slope_{WINDOW_N}"] = grouped.apply(lambda s: rolling_slope(s, WINDOW_N)).reset_index(level=0, drop=True).fillna(0)
else:
    df[f"Slope_{WINDOW_N}"] = np.nan  # 占位，便于后续联表

# ---------- 4. 最终动量标志 ----------
if USE_SLOPE:
    df["Momentum_Attack"]  = ((df["UpStreak"]   == 1) | (df[f"Slope_{WINDOW_N}"] >=  SLOPE_THRESH)).astype(int)
    df["Momentum_Retreat"] = ((df["DownStreak"] == 1) | (df[f"Slope_{WINDOW_N}"] <= -SLOPE_THRESH)).astype(int)
else:
    df["Momentum_Attack"]  = df["UpStreak"]
    df["Momentum_Retreat"] = df["DownStreak"]

# ---------- 5. 输出 ----------
cols = ["date", "sector", "Score_raw",
        "UpStreak", "DownStreak",
        "Momentum_Attack", "Momentum_Retreat"]
if USE_SLOPE:
    cols.insert(3, f"Slope_{WINDOW_N}")

df[cols].to_csv(OUT_PATH, index=False)
print(f"✅  Stage 2 完成：{OUT_PATH}  ({df.shape[0]} 行)")

# ---------- 6. （示例）打印最新一天的攻/退列表 ----------
latest = df["date"].max()
print(f"\n>>> {latest.date()}  Attack 板块：")
print(df.query("date == @latest and Momentum_Attack == 1")
         .sort_values("Score_raw", ascending=False)
         [["sector", "Score_raw"]]
         .head(10))

print(f"\n>>> {latest.date()}  Retreat 板块：")
print(df.query("date == @latest and Momentum_Retreat == 1")
         .sort_values("Score_raw", ascending=False)
         [["sector", "Score_raw"]]
         .head(10))
df
#%% md
## Stage 3 – 资金 & 行为验证（阅读版）

> **目标**：对 Stage 2 标记出的 **Momentum\_Attack = 1** 或 **Momentum\_Retreat = 1** 的板块，
> 用真实资金与量-价行为做 **二次确认**，输出 “Confirmed Attack / Confirmed Retreat / Watch (未通过)” 标签。

---

### 1 . 输入

| 来源                            | 关键列                                                            | 说明             |
| ----------------------------- | -------------------------------------------------------------- | -------------- |
| **`stage2_momentum_tbl.csv`** | `date, sector, Momentum_Attack, Momentum_Retreat` + Score\_raw | 热度动量结果         |
| **`quota_df`** *(日频行情)*       | `date, code, main_net_flow, vol, close …`                      | 主力净流入、成交量、收盘价等 |
| **`map_df`**                  | `code, sector`                                                 | 股票⇄板块映射        |

> *若你尚未提供 `quota_df`，Stage 3 可以先跳过，输出空验证列；等数据就绪再补跑。*

---

### 2 . 汇总板块级资金指标

对每个 `(date, sector)` 计算：

| 指标                        | 公式                                 | 含义 / 默认阈值                         |
| ------------------------- | ---------------------------------- | --------------------------------- |
| **Flow\_d**               | Σ `main_net_flow`                  | 当日主力净流入（万元）                       |
| **ΔFlow\_d**              | Flow\_d − Flow\_{d-1}              | 日环比增量                             |
| **FlowPosStreak**         | 连续正流入天数                            | ≥ 2 日算连续                          |
| **FlowNegStreak**         | 连续净流出天数                            | ≥ 2 日算连续                          |
| **FlowInDays%**           | 近 N(10/20) 日中净流入天数占比               | **≥ 50 %** (进攻) / **≤ 50 %** (退潮) |
| **Vol\_Breakout**         | `vol_d > MA20(vol)+1σ` 且当日涨幅 > 3 % | 放量突破；退潮镜像为缩量：`vol_d < MA20 − 1σ`  |
| **Flow-Price Divergence** | Flow>0 且价格横盘/微跌 < 1 %              | 退潮镜像：Flow<0 且价横盘/微涨               |

---

### 3 . 验证组合规则

| 动量类型        | 必须满足任一组合才算 **通过**                                                                                                                                                                      |
| ----------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Attack**  | A. `FlowPosStreak ≥ 2` **且** `ΔFlow_d > 0`  <br>**或** B. `Vol_Breakout = True` **且** Flow\_d > 0 <br>**或** C. `Flow-Price Divergence = True` <br>**或** D. `FlowInDays% ≥ 50 %`         |
| **Retreat** | A. `FlowNegStreak ≥ 2` **且** `ΔFlow_d < 0`  <br>**或** B. **缩量** (`vol_d < MA20 −1σ`) **且** Flow\_d < 0 <br>**或** C. 退潮版 Divergence (Flow<0 & 价横盘/微涨) <br>**或** D. `FlowInDays% ≤ 50 %` |

---

### 4 . 输出字段

| 字段名                              | 类型        | 含义                                                 |
| -------------------------------- | --------- | -------------------------------------------------- |
| **date**                         | datetime  | 交易日                                                |
| **sector**                       | str       | 板块名称                                               |
| **Momentum\_Attack / Retreat**   | 0/1       | 来自 Stage 2                                         |
| **Attack\_pass / Retreat\_pass** | 0/1       | 是否满足上述组合                                           |
| **label**                        | str       | `Confirmed Attack` / `Confirmed Retreat` / `Watch` |
| *(可选)* 资金指标列                     | float/int | Flow\_d、FlowInDays% 等，便于调参 & 可视化                   |

---

### 5 . 参数表（可调）

| 参数         | 默认              | 说明                               |
| ---------- | --------------- | -------------------------------- |
| 窗口 N       | 10 (短) / 20 (中) | 计算 FlowInDays%                   |
| 连续正/负流入天数阈 | 2               | `FlowPosStreak`, `FlowNegStreak` |
| 放量 σ       | 1               | `MA20 + 1σ` 以上视为放量               |
| 涨幅同步阈      | 3 %             | 放量突破时的价格确认                       |
| 横盘/微动阈     | ±1 %            | Flow-Price Divergence            |

---

### 6 . 输出示例

| date       | sector | Momentum\_Attack | Attack\_pass | Momentum\_Retreat | Retreat\_pass | label                 |
| ---------- | ------ | ---------------- | ------------ | ----------------- | ------------- | --------------------- |
| 2025-07-08 | 银行     | 1                | 1            | 0                 | 0             | **Confirmed Attack**  |
| 2025-07-10 | 半导体    | 0                | 0            | 1                 | 1             | **Confirmed Retreat** |
| 2025-07-08 | 机器人    | 1                | 0            | 0                 | 0             | Watch                 |

---

### 7 . 用途

1. **Stage 4** 融合 `Score_raw` 段位 → 最终策略输出。
2. **告警系统**：实时推送“主力撤退”警示，避免追高被套。
3. **研报**：双向列表展示板块生命周期，从 Attack → Retreat 的切换。

---

### 8 . 接口回顾

* 只要提供 `quota_df`（含 Flow、vol、close），本阶段即可完整运行；
* 若暂无资金数据，可先输出空验证列，Stage 4 将把所有 Momentum 板块暂列 **Watch**，等数据补齐后重跑即可。

#%%
# ======================  Stage 3 – 资金 & 行为验证  ======================
import pandas as pd
import numpy as np
from pathlib import Path
from sklearn.linear_model import LinearRegression   # 仅用于斜率计算
import akshare as ak

# ---------- 0. 路径 & 全局参数 ----------
pre_path            = "/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze"           # ← 你的工作目录
PATH_MOMENTUM       = f"{pre_path}/stage2_momentum_tbl.csv"
PATH_FUND           = f"{pre_path}/blank_fund.csv"
PATH_OUT            = f"{pre_path}/stage3_validation_tbl.csv"

FLOW_STREAK_DAYS    = 2      # 连续净流入 / 净流出 天数
WINDOW_IN_DAYS      = 20     # Volume/Flow 滚动窗口
VOLUME_SIGMA        = 1.0    # 放量/缩量 σ
FLAT_CHG_PCT        = 0.01   # 横盘 ±1 %
FLOW_RATIO_THRESH   = 0.50   # 近 N 日净流入(出)占比阈
USE_VOLUME_PRICE    = True   # 是否启用成交量 / 价格验证

# ---------- 1. 读入基础数据 ----------
mom_df  = pd.read_csv(PATH_MOMENTUM, parse_dates=["date"])
fund_df = (pd.read_csv(PATH_FUND, parse_dates=["date"])
             .rename(columns={"name": "sector"})         # sector = 中文名称
             .assign(main_net_flow=lambda x: x["major_net_inflow"]))

# 1.1 板块名称 ↔ BK代码 映射
ind_meta = ak.stock_board_industry_name_em()
ind_meta["板块代码"] = ind_meta["板块代码"].str.zfill(6).radd("BK")
name2code = dict(zip(ind_meta["板块名称"], ind_meta["板块代码"]))

fund_df["sector_code"] = fund_df["sector"].map(name2code)

# ---------- 2. 抓板块指数 (close / vol) ----------
if USE_VOLUME_PRICE:
    needed_sector = fund_df["sector"].dropna().unique().tolist()
    date_min, date_max = mom_df["date"].min(), mom_df["date"].max()
    date_min -= pd.Timedelta(days=5)
    date_max += pd.Timedelta(days=5)

    idx_records = []
    for sec_name in needed_sector:
        bk_code = name2code.get(sec_name)
        if bk_code is None:
            continue
        try:
            raw = ak.stock_board_industry_hist_em(
                symbol=sec_name,
                start_date=date_min.strftime("%Y%m%d"),
                end_date=date_max.strftime("%Y%m%d")
            )
            raw = (raw.rename(columns={"日期": "date", "收盘": "close", "成交量": "vol"})
                       .assign(date=lambda x: pd.to_datetime(x["date"]),
                               sector_code=bk_code))
            idx_records.append(raw[["date", "sector_code", "close", "vol"]])
        except Exception as e:
            print(f"[Warn] 板块指数 {sec_name} 抓取失败：{e}")

    if idx_records:
        idx_df = pd.concat(idx_records, ignore_index=True)
    else:
        print("⚠️  未抓到指数行情，降级为资金验证简版 (USE_VOLUME_PRICE=False) ")
        USE_VOLUME_PRICE = False
else:
    idx_df = pd.DataFrame()

# ---------- 3. 资金指标聚合 ----------
flow_daily = (fund_df.groupby(["date", "sector"])
                     .agg(Flow_d=("main_net_flow", "sum"),
                          sector_code=("sector_code", "first"))
                     .reset_index())

# 合并动量表 & sector_code
df = (mom_df.merge(flow_daily, on=["date", "sector"], how="left")
            .sort_values(["sector", "date"]))
df["sector_code"] = df["sector"].map(name2code)          # 补缺

# Fill NaNs
df.loc[:, "Flow_d"] = df["Flow_d"].fillna(0)


# -------- 3.1 ΔFlow & 连续性 & 近 N 日占比 --------
grp = df.groupby("sector")
df["ΔFlow_d"] = grp["Flow_d"].diff().fillna(0)

def streak(arr, cond):
    cnt = 0
    res = []
    for x in arr:
        cnt = cnt + 1 if cond(x) else 0
        res.append(cnt)
    return res

df["FlowPosStreak"] = grp["Flow_d"].transform(lambda s: streak(s.values, lambda t: t > 0))
df["FlowNegStreak"] = grp["Flow_d"].transform(lambda s: streak(s.values, lambda t: t < 0))

df["FlowInDaysPct"] = (grp["Flow_d"]
                       .apply(lambda s: s.rolling(WINDOW_IN_DAYS)
                                         .apply(lambda x: np.mean(x > 0)))
                       .reset_index(level=0, drop=True)
                       .fillna(0))

# ---------- 4. Volume / Price 指标 ----------
if USE_VOLUME_PRICE:
    df = (df.merge(idx_df, on=["date", "sector_code"], how="left")
            .sort_values(["sector", "date"]))

    grp2 = df.groupby("sector")
    df["vol_MA"]  = grp2["vol"].transform(lambda s: s.rolling(WINDOW_IN_DAYS).mean())
    df["vol_STD"] = grp2["vol"].transform(lambda s: s.rolling(WINDOW_IN_DAYS).std())

    df["Vol_Breakout"] = ((df["vol"] > df["vol_MA"] + VOLUME_SIGMA * df["vol_STD"])
                          & df["vol_MA"].notna()).astype(int)
    df["Vol_Shrink"]   = ((df["vol"] < df["vol_MA"] - VOLUME_SIGMA * df["vol_STD"])
                          & df["vol_MA"].notna()).astype(int)

    df["pct_chg"] = grp2["close"].pct_change().fillna(0)

    df["Divergence_Attack"]  = ((df["Flow_d"] > 0) & (df["pct_chg"].abs() < FLAT_CHG_PCT)).astype(int)
    df["Divergence_Retreat"] = ((df["Flow_d"] < 0) & (df["pct_chg"].abs() < FLAT_CHG_PCT)).astype(int)
else:
    df["Vol_Breakout"] = df["Vol_Shrink"] = 0
    df["Divergence_Attack"] = df["Divergence_Retreat"] = 0

# ---------- 5. 组合规则 ----------
def attack_pass(r):
    return (
        ((r["FlowPosStreak"] >= FLOW_STREAK_DAYS) and (r["ΔFlow_d"] > 0)) or
        (r["Vol_Breakout"] and r["Flow_d"] > 0) or
        r["Divergence_Attack"] or
        (r["FlowInDaysPct"] > FLOW_RATIO_THRESH)
    )

def retreat_pass(r):
    return (
        ((r["FlowNegStreak"] >= FLOW_STREAK_DAYS) and (r["ΔFlow_d"] < 0)) or
        (r["Vol_Shrink"] and r["Flow_d"] < 0) or
        r["Divergence_Retreat"] or
        (r["FlowInDaysPct"] < (1 - FLOW_RATIO_THRESH))   # ← 改成 **<** 而不是 ≤
    )


df["Attack_pass"]  = df.apply(lambda r: int(attack_pass(r)), axis=1)
df["Retreat_pass"] = df.apply(
    lambda r: 0 if r["Attack_pass"] else int(retreat_pass(r)), axis=1)

# ---------- 6. 最终标签 ----------
def final_label(r):
    if (r["Momentum_Attack"] == 1) and (r["Attack_pass"] == 1):
        return "Confirmed Attack"
    if (r["Momentum_Retreat"] == 1) and (r["Retreat_pass"] == 1):
        return "Confirmed Retreat"
    return "Watch"

df["label"] = df.apply(final_label, axis=1)

# ---------- 7. 输出 ----------
save_cols = ["date", "sector", "Score_raw",
             "Momentum_Attack", "Momentum_Retreat",
             "Attack_pass", "Retreat_pass", "label"]

df.to_csv(PATH_OUT, columns=save_cols, index=False)
print(f"✅  Stage 3 完成：已生成 {PATH_OUT}  ({df.shape[0]} 行)")

# （可选）展示最新交易日结果
latest = df["date"].max()
print(f"\n>>> {latest.date()} — 当日标签预览")
display(df.query("date == @latest")[save_cols]
        .sort_values(["label", "Score_raw"], ascending=[False, False])
        .head(15))

#%%

#%% md
## Stage 4 – 决策融合 & 输出方案（阅读版）

> **目标**：把 Stage 1–3 的所有结果统一到一张“总表”，
>   • 给每个板块贴最终 **决策标签**（Attack / Retreat / Watch / Neutral）
>   • 输出友好格式（CSV + 日报）供下游告警或可视化使用。

---

### 1. 主要输入

| 来源                              | 关键字段                                | 说明                |
| ------------------------------- | ----------------------------------- | ----------------- |
| **`stage1_score_tbl.csv`**      | `date, sector, Score_raw`           | 当日热度原始得分          |
| **`stage2_momentum_tbl.csv`**   | `Momentum_Attack, Momentum_Retreat` | 3 日动量标志           |
| **`stage3_validation_tbl.csv`** | `Attack_pass, Retreat_pass, label`  | 资金&行为验证结果（含第一版标签） |

---

### 2. 融合逻辑

1. **左连接** 三张表

   ```text
   key = ['date', 'sector']
   master_df = Stage1 ← Stage2 ← Stage3
   ```

2. **打分段**

   * 计算当日所有板块 `Score_raw` 的 **80 th 百分位** (`P80`)；
   * 若 `Score_raw ≥ P80` 记 **段位 = "High"**，否则 `"Low"`。
   * （可选）再设 `P50` 细分 `"Middle"` 段位。

3. **最终标签规则**

| 段位           | Momentum\_Attack | Attack\_pass | Momentum\_Retreat | Retreat\_pass | label                   |
| ------------ | ---------------- | ------------ | ----------------- | ------------- | ----------------------- |
| High         | 1                | 1            | –                 | –             | **Confirmed Attack**    |
| Low / Middle | –                | –            | 1                 | 1             | **Confirmed Retreat**   |
| 任意           | 1                | 0            | 0                 | 0             | **Watch** *(攻势动量但未过验证)* |
| 任意           | 0                | 0            | 0                 | 0             | **Neutral**             |

> * **互斥原则**：先判断 Attack；若已 Confirmed，则不再评估 Retreat。
> * 若动量两边都是 0，则直接 `Neutral`（多数板块会落在此）。

4. **可选信心度 (`confidence`)**

   * `(段位分值) × (资金组合条数通过数)` 归一化到 0–1；
   * 供 UI 着色或告警强度排序。

---

### 3. 输出字段

| 字段                           | 类型       | 说明                                                     |
| ---------------------------- | -------- | ------------------------------------------------------ |
| date                         | datetime | 交易日                                                    |
| sector                       | str      | 板块中文名称                                                 |
| Score\_raw                   | float    | 当日热度得分                                                 |
| 段位 (rank)                    | str      | High / Middle / Low                                    |
| Momentum\_Attack / Retreat   | 0/1      | 来自 Stage 2                                             |
| Attack\_pass / Retreat\_pass | 0/1      | Stage 3 组合结果                                           |
| label                        | str      | Confirmed Attack / Confirmed Retreat / Watch / Neutral |
| confidence\*                 | float    | (可选) 0–1                                               |

---

### 4. 关键可调参数

| 参数            | 默认               | 作用                |
| ------------- | ---------------- | ----------------- |
| 百分位阈值         | `P80`            | 高段位门槛；想保守可用 P85   |
| label 优先级     | Attack > Retreat | 若想同时展示“双标签”，可改为两列 |
| confidence 权重 | 热度 0.6 / 资金 0.4  | 可通过回测调整           |

---

### 5. 典型输出 (示例)

| date       | sector | Score\_raw | rank | label                | confidence |
| ---------- | ------ | ---------- | ---- | -------------------- | ---------- |
| 2025-07-08 | 银行     | 0.62       | High | **Confirmed Attack** | 0.88       |
| 2025-07-08 | 半导体    | 0.29       | Low  | Confirmed Retreat    | 0.74       |
| 2025-07-08 | 机器人    | 0.33       | Low  | Watch                | 0.45       |
| 2025-07-08 | 纺织服饰   | 0.07       | Low  | Neutral              | 0.10       |

---

### 6. 下游使用

| 输出                                  | 用途                                          |
| ----------------------------------- | ------------------------------------------- |
| **CSV (`stage4_decision_tbl.csv`)** | 为策略/回测脚本读取                                  |
| **日报图表**                            | 排名前 10 Attack / Retreat + 热度曲线              |
| **实时告警**                            | 当某板块 `label` 由 `Watch→Confirmed Attack` 即推送 |

---

### 7. 常见扩展

* **多层板块**：同时输出一级行业、二级概念；各自跑 Stage 1–4。
* **阈值自适应**：每月回测后自动上调/下调 `P80`、资金阈，写回配置。
* **回放视图**：可在前端把 Attack→Retreat 的完整生命周期用甘特图展示。

只要遵循上述字段与规则，Stage 4 就能把前 3 阶段的计算结果封装成一张清晰的“决策面板”，供人工研判或自动策略直接调用。

#%%
# ======================  Stage 4 – 决策融合 & 输出  ======================
import pandas as pd
import numpy as np
from pathlib import Path

# ---------- 0. 路径 ----------
pre_path           = "/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze"                 # 若脚本与 CSV 在同一目录
PATH_STAGE1        = f"{pre_path}/stage1_score_tbl.csv"
PATH_STAGE2        = f"{pre_path}/stage2_momentum_tbl.csv"
PATH_STAGE3        = f"{pre_path}/stage3_validation_tbl.csv"
PATH_OUT           = f"{pre_path}/stage4_decision_tbl.csv"

# ---------- 1. 读入三张表 ----------
s1 = pd.read_csv(PATH_STAGE1, parse_dates=["date"])[["date", "sector", "Score_raw"]]
s2 = pd.read_csv(PATH_STAGE2, parse_dates=["date"])[["date", "sector",
                                                     "Momentum_Attack", "Momentum_Retreat"]]
s3 = pd.read_csv(PATH_STAGE3, parse_dates=["date"])[["date", "sector",
                                                     "Attack_pass", "Retreat_pass"]]

# ---------- 2. 融合 ----------
df = (s1.merge(s2, on=["date", "sector"], how="left")
         .merge(s3, on=["date", "sector"], how="left"))

# ---------- 3. 段位 (Rank) ----------
def add_rank(group, pctl=0.80):
    thresh = group["Score_raw"].quantile(pctl)
    group["rank"] = np.where(group["Score_raw"] >= thresh, "High", "Low")
    return group

df = df.groupby("date", group_keys=False).apply(add_rank)

# ---------- 4. 最终标签 ----------
def decide_label(r):
    if (r["Momentum_Attack"] == 1) and (r["Attack_pass"] == 1):
        return "Confirmed Attack"
    if (r["Momentum_Retreat"] == 1) and (r["Retreat_pass"] == 1):
        return "Confirmed Retreat"
    if (r["Momentum_Attack"] == 1) or (r["Momentum_Retreat"] == 1):
        return "Watch"           # 有动量但未通过资金验证
    return "Neutral"

df["label"] = df.apply(decide_label, axis=1)

# ---------- 5. 可选信心度 (简单示例) ----------
weight_heat  = 0.6      # Score_raw 归一化贡献
weight_funds = 0.4      # 资金验证贡献

# 将 Score_raw 在同日归一化到 0–1
df["heat_norm"] = (df.groupby("date")["Score_raw"]
                     .transform(lambda x: (x - x.min()) / (x.max() - x.min() + 1e-9)))

df["funds_score"] = ((df["Attack_pass"] + df["Retreat_pass"]) / 1).clip(0, 1)  # 0/1

df["confidence"] = (weight_heat * df["heat_norm"] +
                    weight_funds * df["funds_score"]).round(3)

# ---------- 6. 输出 ----------
save_cols = ["date", "sector", "Score_raw", "rank",
             "Momentum_Attack", "Momentum_Retreat",
             "Attack_pass", "Retreat_pass",
             "label", "confidence"]

df.to_csv(PATH_OUT, columns=save_cols, index=False)
print(f"✅ Stage 4 完成：{PATH_OUT}  ({df.shape[0]} 行)")

# （可选）查看最新一天结果
latest = df["date"].max()
print(f"\n>>> {latest.date()} – 标签预览")
display(df.query("date == @latest")[save_cols]
        .sort_values(["label", "confidence"], ascending=[False, False])
        .head(15))

#%%
