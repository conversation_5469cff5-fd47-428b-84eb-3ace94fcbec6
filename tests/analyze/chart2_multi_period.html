<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="4c8e50c9b5b3416f90593e23862d7dd3" class="chart-container" style="width:100%; height:1000px; "></div>
    <script>
        var chart_4c8e50c9b5b3416f90593e23862d7dd3 = echarts.init(
            document.getElementById('4c8e50c9b5b3416f90593e23862d7dd3'), 'white', {renderer: 'canvas'});
        var option_4c8e50c9b5b3416f90593e23862d7dd3 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "5\u65e5",
            "legendHoverLink": true,
            "data": [
                -3.53,
                -64.12,
                -11.29,
                -81.13,
                -14.35,
                -11.83,
                7.33,
                23.08,
                -41.02,
                -0.21,
                2.8,
                1.11,
                -9.5,
                -29.8,
                3.78,
                -26.83,
                -4.81,
                -8.6,
                -6.36,
                -6.67,
                -0.97,
                -116.47,
                -12.49,
                -23.35,
                -14.44,
                -4.09,
                -10.4,
                -20.04,
                0.68,
                -12.91,
                -5.2,
                -5.06,
                -1.57,
                0.05,
                -32.85,
                -9.68,
                -25.15,
                -4.94,
                -1.8,
                -3.16,
                -1.86,
                -67.46,
                1.02,
                7.46,
                -1.14,
                -3.32,
                -14.29,
                -12.75,
                4.47,
                -4.55,
                4.59,
                3.08,
                -34.31,
                -14.0,
                -8.58,
                -41.87,
                -6.92,
                -34.6,
                -1.98,
                -17.09,
                -2.72,
                -4.18,
                -1.65,
                -20.56,
                -3.7,
                -6.59,
                -26.43,
                -3.16,
                -2.45,
                -35.79,
                -25.56,
                -4.06,
                -8.75,
                -81.38,
                -13.96,
                -76.83,
                -54.34,
                -5.73,
                -3.18,
                -8.27,
                3.84,
                1.98,
                10.88,
                -3.19,
                -11.45,
                -8.15
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#ff6b6b"
            }
        },
        {
            "type": "bar",
            "name": "10\u65e5",
            "legendHoverLink": true,
            "data": [
                -4.69,
                -88.86,
                -18.39,
                -83.31,
                -19.53,
                -17.92,
                9.45,
                13.94,
                -63.42,
                -1.22,
                0.67,
                -15.52,
                -12.34,
                -59.54,
                -31.58,
                -43.13,
                -5.64,
                -15.51,
                -9.29,
                -13.25,
                -0.04,
                -120.13,
                -26.78,
                -39.21,
                -12.53,
                -10.63,
                -15.55,
                -32.96,
                -0.19,
                -17.88,
                -8.31,
                -8.7,
                -3.3,
                1.66,
                -41.09,
                -8.91,
                -22.76,
                -6.66,
                -2.57,
                -32.3,
                -2.39,
                -111.4,
                -2.74,
                -12.52,
                -3.19,
                -6.93,
                -14.37,
                -18.6,
                3.31,
                -4.31,
                -5.71,
                -6.7,
                -17.85,
                -22.89,
                -15.91,
                -100.21,
                -14.88,
                -43.14,
                -8.91,
                -19.68,
                -5.66,
                -6.17,
                -5.55,
                -22.37,
                -2.94,
                -16.3,
                -27.6,
                -3.81,
                -4.62,
                -49.69,
                1.0,
                -9.38,
                -10.09,
                -115.49,
                -15.29,
                -60.65,
                -82.54,
                -12.26,
                -22.03,
                -18.41,
                4.34,
                -1.82,
                -14.24,
                -9.52,
                -14.72,
                -7.0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4ecdc4"
            }
        },
        {
            "type": "bar",
            "name": "15\u65e5",
            "legendHoverLink": true,
            "data": [
                -6.52,
                -111.16,
                -13.58,
                -84.59,
                -18.76,
                -17.43,
                22.94,
                24.05,
                -67.03,
                -0.68,
                6.93,
                -17.07,
                -7.69,
                -61.76,
                -19.81,
                -47.7,
                -8.83,
                -16.2,
                -8.03,
                -11.36,
                2.76,
                -120.19,
                -29.41,
                -39.29,
                -12.27,
                -13.19,
                -16.06,
                -26.25,
                -0.01,
                -8.78,
                -7.43,
                -9.47,
                -2.63,
                0.19,
                -64.61,
                -10.88,
                -23.81,
                -4.38,
                0.33,
                -32.76,
                -3.25,
                -107.36,
                -5.41,
                -18.02,
                -1.05,
                -11.7,
                -12.98,
                -19.02,
                5.57,
                -5.67,
                3.11,
                -7.94,
                -31.63,
                -20.28,
                -14.85,
                -73.1,
                -17.74,
                -46.43,
                -20.92,
                -26.17,
                -5.49,
                -5.71,
                -0.11,
                -46.6,
                -0.91,
                -14.21,
                -31.94,
                -4.01,
                -5.23,
                -49.38,
                31.43,
                -18.62,
                -8.42,
                -121.84,
                -18.13,
                -105.59,
                -91.02,
                -15.53,
                -10.08,
                -35.22,
                7.18,
                -1.78,
                12.02,
                -8.12,
                -15.96,
                -9.91
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#45b7d1"
            }
        },
        {
            "type": "bar",
            "name": "20\u65e5",
            "legendHoverLink": true,
            "data": [
                -13.68,
                -154.27,
                -30.05,
                -140.73,
                -28.46,
                -20.21,
                19.1,
                15.43,
                -77.35,
                -1.87,
                -11.46,
                -41.62,
                -15.48,
                -99.91,
                -100.74,
                -67.4,
                -17.11,
                -24.1,
                -17.53,
                -35.41,
                -2.48,
                -116.26,
                -44.86,
                -53.92,
                -21.93,
                -30.65,
                -15.22,
                -97.31,
                -3.32,
                -20.05,
                -12.69,
                -20.33,
                -4.8,
                -2.91,
                -91.92,
                -14.01,
                -50.56,
                -9.76,
                -5.44,
                -50.21,
                -4.79,
                -151.41,
                5.47,
                -29.68,
                -5.71,
                -10.0,
                -22.93,
                -32.39,
                6.41,
                -17.75,
                -25.96,
                -36.11,
                -45.75,
                -24.52,
                -14.26,
                -75.18,
                -26.34,
                -78.65,
                -31.3,
                -38.79,
                -6.23,
                -14.9,
                -0.52,
                -76.51,
                -3.58,
                -26.13,
                -37.6,
                -9.08,
                -7.93,
                -74.84,
                -3.8,
                -39.75,
                -16.92,
                -192.86,
                -28.57,
                -121.31,
                -120.15,
                -21.77,
                -19.17,
                -54.55,
                4.52,
                -3.33,
                21.27,
                -6.32,
                -17.65,
                -26.53
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#96ceb4"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "5\u65e5",
                "10\u65e5",
                "15\u65e5",
                "20\u65e5"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "orient": "horizontal",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "shadow"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "name": "\u884c\u4e1a",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u4e13\u4e1a\u670d\u52a1",
                "\u4e13\u7528\u8bbe\u5907",
                "\u4e2d\u836f",
                "\u4e92\u8054\u7f51\u670d\u52a1",
                "\u4ea4\u8fd0\u8bbe\u5907",
                "\u4eea\u5668\u4eea\u8868",
                "\u4fdd\u9669",
                "\u5149\u4f0f\u8bbe\u5907",
                "\u5149\u5b66\u5149\u7535\u5b50",
                "\u516c\u7528\u4e8b\u4e1a",
                "\u519c\u7267\u9972\u6e14",
                "\u519c\u836f\u517d\u836f",
                "\u5305\u88c5\u6750\u6599",
                "\u5316\u5b66\u5236\u54c1",
                "\u5316\u5b66\u5236\u836f",
                "\u5316\u5b66\u539f\u6599",
                "\u5316\u7ea4\u884c\u4e1a",
                "\u5316\u80a5\u884c\u4e1a",
                "\u533b\u7597\u5668\u68b0",
                "\u533b\u7597\u670d\u52a1",
                "\u533b\u836f\u5546\u4e1a",
                "\u534a\u5bfc\u4f53",
                "\u5546\u4e1a\u767e\u8d27",
                "\u5851\u6599\u5236\u54c1",
                "\u591a\u5143\u91d1\u878d",
                "\u5bb6\u7528\u8f7b\u5de5",
                "\u5bb6\u7535\u884c\u4e1a",
                "\u5c0f\u91d1\u5c5e",
                "\u5de5\u7a0b\u54a8\u8be2\u670d\u52a1",
                "\u5de5\u7a0b\u5efa\u8bbe",
                "\u5de5\u7a0b\u673a\u68b0",
                "\u623f\u5730\u4ea7\u5f00\u53d1",
                "\u623f\u5730\u4ea7\u670d\u52a1",
                "\u6559\u80b2",
                "\u6587\u5316\u4f20\u5a92",
                "\u65c5\u6e38\u9152\u5e97",
                "\u6709\u8272\u91d1\u5c5e",
                "\u6a61\u80f6\u5236\u54c1",
                "\u6c34\u6ce5\u5efa\u6750",
                "\u6c7d\u8f66\u6574\u8f66",
                "\u6c7d\u8f66\u670d\u52a1",
                "\u6c7d\u8f66\u96f6\u90e8\u4ef6",
                "\u6d88\u8d39\u7535\u5b50",
                "\u6e38\u620f",
                "\u7164\u70ad\u884c\u4e1a",
                "\u71c3\u6c14",
                "\u7269\u6d41\u884c\u4e1a",
                "\u73af\u4fdd\u884c\u4e1a",
                "\u73bb\u7483\u73bb\u7ea4",
                "\u73e0\u5b9d\u9996\u9970",
                "\u751f\u7269\u5236\u54c1",
                "\u7535\u529b\u884c\u4e1a",
                "\u7535\u5b50\u5143\u4ef6",
                "\u7535\u5b50\u5316\u5b66\u54c1",
                "\u7535\u673a",
                "\u7535\u6c60",
                "\u7535\u6e90\u8bbe\u5907",
                "\u7535\u7f51\u8bbe\u5907",
                "\u77f3\u6cb9\u884c\u4e1a",
                "\u7eba\u7ec7\u670d\u88c5",
                "\u7efc\u5408\u884c\u4e1a",
                "\u7f8e\u5bb9\u62a4\u7406",
                "\u80fd\u6e90\u91d1\u5c5e",
                "\u822a\u5929\u822a\u7a7a",
                "\u822a\u7a7a\u673a\u573a",
                "\u822a\u8fd0\u6e2f\u53e3",
                "\u8239\u8236\u5236\u9020",
                "\u88c5\u4fee\u5efa\u6750",
                "\u88c5\u4fee\u88c5\u9970",
                "\u8ba1\u7b97\u673a\u8bbe\u5907",
                "\u8bc1\u5238",
                "\u8d35\u91d1\u5c5e",
                "\u8d38\u6613\u884c\u4e1a",
                "\u8f6f\u4ef6\u5f00\u53d1",
                "\u901a\u4fe1\u670d\u52a1",
                "\u901a\u4fe1\u8bbe\u5907",
                "\u901a\u7528\u8bbe\u5907",
                "\u9020\u7eb8\u5370\u5237",
                "\u917f\u9152\u884c\u4e1a",
                "\u91c7\u6398\u884c\u4e1a",
                "\u94a2\u94c1\u884c\u4e1a",
                "\u94c1\u8def\u516c\u8def",
                "\u94f6\u884c",
                "\u975e\u91d1\u5c5e\u6750\u6599",
                "\u98ce\u7535\u8bbe\u5907",
                "\u98df\u54c1\u996e\u6599"
            ]
        }
    ],
    "yAxis": [
        {
            "name": "\u4e3b\u529b\u8d44\u91d1\u6d41\u5165 (\u4ebf\u5143)",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u8fd15\u65e5\u300110\u65e5\u300115\u65e5\u300120\u65e5\u4e3b\u529b\u8d44\u91d1\u6d41\u5165\u5bf9\u6bd4 (\u6240\u6709\u884c\u4e1a)",
            "target": "blank",
            "subtext": "\u5355\u4f4d\uff1a\u4ebf\u5143",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_4c8e50c9b5b3416f90593e23862d7dd3.setOption(option_4c8e50c9b5b3416f90593e23862d7dd3);
            window.addEventListener('resize', function(){
                chart_4c8e50c9b5b3416f90593e23862d7dd3.resize();
            })
    </script>
</body>
</html>
