<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="b619b6df5f1440909573a7373a9a4b27" class="chart-container" style="width:1200px; height:2000px; "></div>
    <script>
        var chart_b619b6df5f1440909573a7373a9a4b27 = echarts.init(
            document.getElementById('b619b6df5f1440909573a7373a9a4b27'), 'white', {renderer: 'canvas'});
        var option_b619b6df5f1440909573a7373a9a4b27 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8d44\u91d1\u6d41\u5165",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0.19,
                0.33,
                2.76,
                3.11,
                5.57,
                6.93,
                7.18,
                12.02,
                22.94,
                24.05,
                31.43
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#ff4444"
            }
        },
        {
            "type": "bar",
            "name": "\u8d44\u91d1\u6d41\u51fa",
            "legendHoverLink": true,
            "data": [
                -121.84,
                -120.19,
                -111.16,
                -107.36,
                -105.59,
                -91.02,
                -84.59,
                -73.1,
                -67.03,
                -64.61,
                -61.76,
                -49.38,
                -47.7,
                -46.6,
                -46.43,
                -39.29,
                -35.22,
                -32.76,
                -31.94,
                -31.63,
                -29.41,
                -26.25,
                -26.17,
                -23.81,
                -20.92,
                -20.28,
                -19.81,
                -19.02,
                -18.76,
                -18.62,
                -18.13,
                -18.02,
                -17.74,
                -17.43,
                -17.07,
                -16.2,
                -16.06,
                -15.96,
                -15.53,
                -14.85,
                -14.21,
                -13.58,
                -13.19,
                -12.98,
                -12.27,
                -11.7,
                -11.36,
                -10.88,
                -10.08,
                -9.91,
                -9.47,
                -8.83,
                -8.78,
                -8.42,
                -8.12,
                -8.03,
                -7.94,
                -7.69,
                -7.43,
                -6.52,
                -5.71,
                -5.67,
                -5.49,
                -5.41,
                -5.23,
                -4.38,
                -4.01,
                -3.25,
                -2.63,
                -1.78,
                -1.05,
                -0.91,
                -0.68,
                -0.11,
                -0.01,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#00aa44"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8d44\u91d1\u6d41\u5165",
                "\u8d44\u91d1\u6d41\u51fa"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "shadow"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "name": "\u8d44\u91d1\u6d41\u5165 (\u4ebf\u5143)",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "yAxis": [
        {
            "name": "\u884c\u4e1a",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u8f6f\u4ef6\u5f00\u53d1",
                "\u534a\u5bfc\u4f53",
                "\u4e13\u7528\u8bbe\u5907",
                "\u6c7d\u8f66\u96f6\u90e8\u4ef6",
                "\u901a\u4fe1\u8bbe\u5907",
                "\u901a\u7528\u8bbe\u5907",
                "\u4e92\u8054\u7f51\u670d\u52a1",
                "\u7535\u6c60",
                "\u5149\u5b66\u5149\u7535\u5b50",
                "\u6587\u5316\u4f20\u5a92",
                "\u5316\u5b66\u5236\u54c1",
                "\u8ba1\u7b97\u673a\u8bbe\u5907",
                "\u5316\u5b66\u539f\u6599",
                "\u822a\u5929\u822a\u7a7a",
                "\u7535\u7f51\u8bbe\u5907",
                "\u5851\u6599\u5236\u54c1",
                "\u91c7\u6398\u884c\u4e1a",
                "\u6c7d\u8f66\u6574\u8f66",
                "\u8239\u8236\u5236\u9020",
                "\u7535\u5b50\u5143\u4ef6",
                "\u5546\u4e1a\u767e\u8d27",
                "\u5c0f\u91d1\u5c5e",
                "\u7eba\u7ec7\u670d\u88c5",
                "\u6709\u8272\u91d1\u5c5e",
                "\u77f3\u6cb9\u884c\u4e1a",
                "\u7535\u5b50\u5316\u5b66\u54c1",
                "\u5316\u5b66\u5236\u836f",
                "\u73af\u4fdd\u884c\u4e1a",
                "\u4ea4\u8fd0\u8bbe\u5907",
                "\u8d35\u91d1\u5c5e",
                "\u901a\u4fe1\u670d\u52a1",
                "\u6e38\u620f",
                "\u7535\u6e90\u8bbe\u5907",
                "\u4eea\u5668\u4eea\u8868",
                "\u519c\u836f\u517d\u836f",
                "\u5316\u80a5\u884c\u4e1a",
                "\u5bb6\u7535\u884c\u4e1a",
                "\u98ce\u7535\u8bbe\u5907",
                "\u9020\u7eb8\u5370\u5237",
                "\u7535\u673a",
                "\u822a\u8fd0\u6e2f\u53e3",
                "\u4e2d\u836f",
                "\u5bb6\u7528\u8f7b\u5de5",
                "\u7269\u6d41\u884c\u4e1a",
                "\u591a\u5143\u91d1\u878d",
                "\u71c3\u6c14",
                "\u533b\u7597\u670d\u52a1",
                "\u65c5\u6e38\u9152\u5e97",
                "\u917f\u9152\u884c\u4e1a",
                "\u98df\u54c1\u996e\u6599",
                "\u623f\u5730\u4ea7\u5f00\u53d1",
                "\u5316\u7ea4\u884c\u4e1a",
                "\u5de5\u7a0b\u5efa\u8bbe",
                "\u8d38\u6613\u884c\u4e1a",
                "\u975e\u91d1\u5c5e\u6750\u6599",
                "\u533b\u7597\u5668\u68b0",
                "\u7535\u529b\u884c\u4e1a",
                "\u5305\u88c5\u6750\u6599",
                "\u5de5\u7a0b\u673a\u68b0",
                "\u4e13\u4e1a\u670d\u52a1",
                "\u7f8e\u5bb9\u62a4\u7406",
                "\u73e0\u5b9d\u9996\u9970",
                "\u7efc\u5408\u884c\u4e1a",
                "\u6d88\u8d39\u7535\u5b50",
                "\u88c5\u4fee\u88c5\u9970",
                "\u6a61\u80f6\u5236\u54c1",
                "\u88c5\u4fee\u5efa\u6750",
                "\u6c7d\u8f66\u670d\u52a1",
                "\u623f\u5730\u4ea7\u670d\u52a1",
                "\u94c1\u8def\u516c\u8def",
                "\u7164\u70ad\u884c\u4e1a",
                "\u822a\u7a7a\u673a\u573a",
                "\u516c\u7528\u4e8b\u4e1a",
                "\u80fd\u6e90\u91d1\u5c5e",
                "\u5de5\u7a0b\u54a8\u8be2\u670d\u52a1",
                "\u6559\u80b2",
                "\u6c34\u6ce5\u5efa\u6750",
                "\u533b\u836f\u5546\u4e1a",
                "\u751f\u7269\u5236\u54c1",
                "\u73bb\u7483\u73bb\u7ea4",
                "\u519c\u7267\u9972\u6e14",
                "\u94a2\u94c1\u884c\u4e1a",
                "\u94f6\u884c",
                "\u4fdd\u9669",
                "\u5149\u4f0f\u8bbe\u5907",
                "\u8bc1\u5238"
            ]
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u8fd115\u65e5\u884c\u4e1a\u4e3b\u529b\u8d44\u91d1\u6d41\u5165 (\u4ebf\u5143)",
            "target": "blank",
            "subtext": "\u7ea2\u8272=\u6d41\u5165\uff0c\u7eff\u8272=\u6d41\u51fa",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_b619b6df5f1440909573a7373a9a4b27.setOption(option_b619b6df5f1440909573a7373a9a4b27);
    </script>
</body>
</html>
