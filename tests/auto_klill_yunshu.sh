#!/bin/zsh

# 无限循环，每10秒执行一次
while true; do
  # 查找所有包含 "yunshu" 的进程 PID
  pids=$(ps aux | grep -i yunshu | grep -v grep | awk '{print $2}')

  if [[ -z "$pids" ]]; then
    echo "$(date) 没有找到 yunshu 进程。"
  else
    # 逐个终止进程
    for pid in $pids; do
      echo "$(date) 正在终止进程: $pid"
      sudo kill -9 $pid
      sleep 0.5
      if [[ $? -eq 0 ]]; then
        echo "$(date) 已终止 PID: $pid"
      else
        echo "$(date) 终止失败 PID: $pid (可能需要更高权限或进程受保护)"
      fi
    done
  fi

  echo "$(date) 操作完成，等待10秒后重新检查..."
  sleep 10
done
