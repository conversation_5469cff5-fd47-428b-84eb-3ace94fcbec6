import  pandas as pd


def compute_volume_ratio(df_today: pd.DataFrame, history_list: pd.DataFrame, time_col='trade_time', vol_col='vol'):
    """
    计算量比：当前每根K线的成交量 / 历史同一时间段平均成交量

    参数：
    - df_today: DataFrame，当日5分钟数据
    - history_list: list of DataFrame，历史N天5分钟数据
    - time_col: 时间列名（格式 'YYYY-MM-DD HH:MM'）
    - vol_col: 成交量列名

    返回：
    - df_today 增加 volume_ratio 列
    """
    import pandas as pd

    # 构建：key = 'HH:MM'，value = 所有历史日的这个时间的成交量列表
    avg_volume_dict = {}
    for df_hist in history_list:
        for _, row in df_hist.iterrows():
            time_str = row[time_col][-5:]
            avg_volume_dict.setdefault(time_str, []).append(row[vol_col])

    # 计算历史平均量
    for k in avg_volume_dict:
        avg_volume_dict[k] = sum(avg_volume_dict[k]) / len(avg_volume_dict[k])

    # 映射到今天的数据
    def get_volume_ratio(row):
        time_str = row[time_col][-5:]
        avg = avg_volume_dict.get(time_str)
        if avg and avg > 0:
            return row[vol_col] / avg
        return None

    df_today = df_today.copy()
    df_today['volume_ratio'] = df_today.apply(get_volume_ratio, axis=1)
    return df_today