#!/bin/bash

# 配置参数
PROCESS_NAME="yunshumanager"  # 监控的进程名（不区分大小写）
THRESHOLD=10.0                # CPU 告警阈值（百分比）
LOG_FILE="/tmp/${PROCESS_NAME}_cpu_alert.log"  # 日志路径

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# 浮点数比较函数
compare_float() {
    awk -v n1="$1" -v n2="$2" 'BEGIN {if (n1 > n2) exit 0; exit 1}'
}

# 获取所有匹配进程的信息
process_info=$(ps aux | grep -i "$PROCESS_NAME" | grep -v grep)

if [[ -z "$process_info" ]]; then
    log_message "警告: 未找到进程 '$PROCESS_NAME'"
    exit 1
fi

# 处理多个进程，计算总CPU使用率
total_cpu=0
process_count=0
while IFS= read -r line; do
    if [[ -n "$line" ]]; then
        cpu=$(echo "$line" | awk '{print $3}')
        pid=$(echo "$line" | awk '{print $2}')

        # 验证CPU使用率是否为有效数字
        if [[ "$cpu" =~ ^[0-9]+\.?[0-9]*$ ]]; then
            total_cpu=$(awk -v total="$total_cpu" -v current="$cpu" 'BEGIN {print total + current}')
            process_count=$((process_count + 1))
            log_message "进程 PID:$pid CPU使用率: ${cpu}%"
        fi
    fi
done <<< "$process_info"

if [[ $process_count -eq 0 ]]; then
    log_message "错误: 未获取到有效的CPU使用率数据"
    exit 1
fi

log_message "进程 '$PROCESS_NAME' 总CPU使用率: ${total_cpu}% (进程数: $process_count)"

# 使用浮点数比较检查是否超过阈值
if compare_float "$total_cpu" "$THRESHOLD"; then
    alert_message="检测到 CPU 使用率超过阈值: ${total_cpu}% > ${THRESHOLD}%"
    log_message "告警: $alert_message"

    # 发送桌面通知 (macOS)
    if command -v osascript >/dev/null 2>&1; then
        osascript -e "display notification \"$alert_message\" with title \"进程告警\" subtitle \"$PROCESS_NAME (${process_count}个进程)\""
    fi

    # 可选: 发送邮件通知 (需要配置邮件服务)
    # echo "$alert_message" | mail -s "CPU告警: $PROCESS_NAME" <EMAIL>

    exit 2  # 告警退出码
else
    log_message "正常: CPU使用率 ${total_cpu}% 未超过阈值 ${THRESHOLD}%"
fi

