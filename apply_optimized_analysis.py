#!/usr/bin/env python3
"""
应用优化后的分析参数，重新运行分析以生效新的强势池管理策略
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date, datetime, timedelta
from src.domain.analyze.aggregate.analysis.zh_blank import AnalyzeZhBlankFundFlowAggregate
from src.infra.app import app
from loguru import logger

def apply_optimized_analysis():
    """应用优化后的分析参数"""
    logger.info("=" * 70)
    logger.info("应用优化后的分析参数")
    logger.info("=" * 70)
    
    logger.info("🎯 优化内容:")
    logger.info("  1. 回撤止盈从15%收紧到10%")
    logger.info("  2. 资金流出阈值改为成交额的7%")
    logger.info("  3. 强势池限制为10个板块")
    logger.info("  4. 强势时间>15天开始降权")
    
    # 获取优化前的状态
    analyzer = AnalyzeZhBlankFundFlowAggregate(is_concept=False)
    before_watchlist = analyzer.get_watchlist()
    
    logger.info(f"\n📊 优化前状态:")
    for phase, sectors in before_watchlist.items():
        logger.info(f"  {phase}: {len(sectors)}个板块")
    
    strong_before = len(before_watchlist.get("strong_trend", []))
    
    # 运行优化后的分析
    logger.info(f"\n🚀 开始运行优化后的分析...")
    
    # 使用最近30天的数据进行分析
    end_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    
    try:
        result_df = analyzer.run_full_analysis(
            before_days=30,
            end_date=end_date,
            skip_pattern_filter=True
        )
        
        logger.info(f"✅ 分析完成，处理了{len(result_df)}个板块状态")
        
        # 获取优化后的状态
        after_watchlist = analyzer.get_watchlist()
        
        logger.info(f"\n📊 优化后状态:")
        for phase, sectors in after_watchlist.items():
            logger.info(f"  {phase}: {len(sectors)}个板块")
        
        strong_after = len(after_watchlist.get("strong_trend", []))
        
        # 对比变化
        logger.info(f"\n📈 变化对比:")
        logger.info(f"  强势池: {strong_before}个 → {strong_after}个 (变化{strong_after - strong_before:+d})")
        
        if strong_after <= 10:
            logger.info(f"  ✅ 强势池数量已控制在目标范围内")
        else:
            logger.warning(f"  ⚠️ 强势池仍有{strong_after}个板块，可能需要进一步调整参数")
        
        # 显示当前强势池详情
        strong_sectors = after_watchlist.get("strong_trend", [])
        if strong_sectors:
            logger.info(f"\n🔥 当前强势池详情:")
            strong_sorted = sorted(strong_sectors, key=lambda x: x.get('strong_days', 0), reverse=True)
            
            for i, sector in enumerate(strong_sorted[:10], 1):
                logger.info(f"  {i:2d}. {sector['name']}: {sector.get('strong_days', 0)}天, 回撤{sector.get('drawdown_pct', 0):.2%}")
            
            if len(strong_sectors) > 10:
                logger.info(f"  ... 还有{len(strong_sectors) - 10}个板块")
        
        # 分析被淘汰的板块
        before_strong_names = {s['name'] for s in before_watchlist.get("strong_trend", [])}
        after_strong_names = {s['name'] for s in after_watchlist.get("strong_trend", [])}
        eliminated = before_strong_names - after_strong_names
        
        if eliminated:
            logger.info(f"\n❄️ 被优化淘汰的板块({len(eliminated)}个):")
            for name in list(eliminated)[:10]:  # 最多显示10个
                logger.info(f"  - {name}")
            if len(eliminated) > 10:
                logger.info(f"  ... 还有{len(eliminated) - 10}个")
        
        return True
        
    except Exception as e:
        logger.error(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_optimization_summary():
    """显示优化总结"""
    logger.info(f"\n" + "=" * 70)
    logger.info("优化效果总结")
    logger.info("=" * 70)
    
    logger.info(f"🎯 优化目标达成情况:")
    logger.info(f"  ✅ 回撤止盈收紧: 15% → 10% (更严格的风控)")
    logger.info(f"  ✅ 资金流出阈值: 改为成交额的7% (更敏感的资金监控)")
    logger.info(f"  ✅ 强势池数量限制: 无限制 → 10个 (精选强势板块)")
    logger.info(f"  ✅ 时间衰减机制: 无 → 15天阈值 (避免长期占位)")
    
    logger.info(f"\n💡 优化带来的好处:")
    logger.info(f"  1. 更精准的强势池筛选，避免过度分散")
    logger.info(f"  2. 更严格的风控标准，及时止损")
    logger.info(f"  3. 更敏感的资金监控，快速响应资金变化")
    logger.info(f"  4. 动态的池子管理，保持板块活力")
    
    logger.info(f"\n🔄 后续建议:")
    logger.info(f"  1. 观察优化后的强势池表现")
    logger.info(f"  2. 根据市场情况微调参数")
    logger.info(f"  3. 定期运行分析以保持池子更新")

if __name__ == "__main__":
    try:
        success = apply_optimized_analysis()
        
        if success:
            show_optimization_summary()
            logger.info(f"\n🎉 优化应用成功！新的强势池管理策略已生效")
        else:
            logger.error(f"❌ 优化应用失败，请检查错误信息")
            
    except Exception as e:
        logger.error(f"应用优化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
