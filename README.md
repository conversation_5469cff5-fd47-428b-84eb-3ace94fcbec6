# 观富交易系统 (GuanFu Trading System)

一个基于 Python 的智能股票分析与监控系统，专注于A股市场的技术分析、资金流向分析和实时监控。

## 🚀 核心功能

### 📊 数据分析
- **技术分析**: K线形态识别、趋势分析、支撑阻力位计算
- **资金流向分析**: 主力资金进出监控、板块资金流向分析
- **板块分析**: 行业板块和概念板块的综合分析
- **量价分析**: 成交量异动监控、换手率分析

### 🔍 实时监控
- **价格监控**: 股价异动实时提醒
- **成交量监控**: 1分钟、5分钟成交量异动检测
- **资金监控**: 大单资金流入流出监控
- **智能预警**: 多维度综合预警系统

### 📈 可视化界面
- **Web界面**: 基于FastAPI的现代化Web界面
- **图表展示**: 使用pyecharts的交互式图表
- **实时数据**: WebSocket实时数据推送
- **移动端适配**: 响应式设计支持移动设备

### 🤖 自动化分析
- **定时任务**: 每日自动分析和报告生成
- **批量处理**: 支持批量股票分析
- **报告生成**: 自动生成Markdown格式分析报告

## 🏗️ 项目结构

```
trading-system/
├── src/                          # 源代码目录
│   ├── api/                      # API接口层
│   │   └── v1/                   # API v1版本
│   ├── application/              # 应用服务层
│   │   └── monitor/              # 监控应用服务
│   ├── domain/                   # 领域层
│   │   ├── analyze/              # 分析领域
│   │   │   ├── repo/             # 分析数据仓库
│   │   │   └── service/          # 分析服务
│   │   ├── communicate/          # 通信领域
│   │   ├── kline/                # K线数据领域
│   │   ├── monitor/              # 监控领域
│   │   ├── store/                # 数据存储领域
│   │   └── trading/              # 交易领域
│   ├── infra/                    # 基础设施层
│   │   ├── app/                  # 应用配置
│   │   ├── clients/              # 外部客户端
│   │   │   └── mysql/            # MySQL客户端
│   │   ├── config/               # 配置管理
│   │   ├── events/               # 事件系统
│   │   ├── log/                  # 日志系统
│   │   ├── scheduler/            # 调度器
│   │   └── web/                  # Web服务
│   ├── scheduler/                # 任务调度
│   └── schema/                   # 数据模式定义
├── config/                       # 配置文件
├── scripts/                      # 脚本目录
│   ├── daily_zh_blank_analysis.py # 每日板块分析脚本
│   └── generate_stock_config_advanced.py # 股票配置生成
├── reports/                      # 分析报告目录
├── logs/                         # 日志文件目录
├── cache/                        # 缓存目录
├── assets/                       # 静态资源
│   ├── analysis/                 # 分析相关资源
│   └── training/                 # 训练数据
├── tests/                        # 测试目录
├── docs/                         # 文档目录
├── alembic/                      # 数据库迁移
├── main.py                       # 应用入口
├── pyproject.toml               # 项目配置
└── README.md                    # 项目说明
```

## 🗄️ 数据库表结构

### 核心数据表

#### K线数据表
- **guanfu_k_line_30_min**: 30分钟K线数据
- **guanfu_k_line_60_min**: 60分钟K线数据
- **guanfu_k_line_daily**: 日K线数据

```sql
-- K线数据表结构
CREATE TABLE guanfu_k_line_daily (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(12) NOT NULL COMMENT '股票代码',
    date DATE COMMENT '日期',
    open DECIMAL(15,2) COMMENT '开盘价',
    close DECIMAL(15,2) COMMENT '收盘价',
    high DECIMAL(15,2) COMMENT '最高价',
    low DECIMAL(15,2) COMMENT '最低价',
    volume DECIMAL(15,2) COMMENT '成交量',
    amount DECIMAL(15,2) COMMENT '成交额',
    tag VARCHAR(16) COMMENT '标签',
    INDEX idx_code_datetime (code, date)
);
```

#### 股票基础信息表
- **guanfu_zh_stock**: A股股票基础信息
- **guanfu_hk_stock**: 港股股票信息
- **guanfu_us_stock**: 美股股票信息

```sql
-- A股股票信息表
CREATE TABLE guanfu_zh_stock (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(12) UNIQUE NOT NULL COMMENT '股票代码',
    name VARCHAR(64) NOT NULL COMMENT '股票名称',
    newest_price DECIMAL(15,2) COMMENT '最新价格',
    price_change DECIMAL(15,2) COMMENT '涨跌额',
    price_change_rate DECIMAL(15,2) COMMENT '涨跌幅',
    turnover DECIMAL(15,2) COMMENT '换手率',
    volume DECIMAL(15,2) COMMENT '成交量',
    amount DECIMAL(15,2) COMMENT '成交额',
    pe_rate DECIMAL(15,2) COMMENT '市盈率',
    market_cap DECIMAL(15,2) COMMENT '总市值',
    free_market_cap DECIMAL(15,2) COMMENT '流通市值',
    market VARCHAR(12) COMMENT '所属市场',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 板块数据表
- **guanfu_zh_stock_blank**: 板块基础信息
- **guanfu_zh_stock_blank_fund**: 板块资金流向数据

```sql
-- 板块信息表
CREATE TABLE guanfu_zh_stock_blank (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(12) UNIQUE NOT NULL COMMENT '板块代码',
    name VARCHAR(64) COMMENT '板块名称',
    newest_price DECIMAL(15,2) COMMENT '最新价格',
    price_change DECIMAL(15,2) COMMENT '涨跌额',
    price_change_rate DECIMAL(15,2) COMMENT '涨跌幅',
    turnover DECIMAL(15,2) COMMENT '换手率',
    gainers INT COMMENT '上涨数',
    losers INT COMMENT '下跌数',
    is_concept BOOLEAN DEFAULT FALSE COMMENT '是否概念板块',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 分析数据表
- **guanfu_analyze_zh_underlying_asset**: 标的资产分析数据

```sql
-- 分析数据表
CREATE TABLE guanfu_analyze_zh_underlying_asset (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(12) UNIQUE NOT NULL COMMENT '股票代码',
    stage VARCHAR(32) COMMENT '当前阶段',
    score INT DEFAULT 0 COMMENT '5日累计得分',
    days_cnt INT DEFAULT 0 COMMENT '评分周期天数',
    cooldown_days INT DEFAULT 0 COMMENT '冷却倒计时',
    high_since_strong FLOAT DEFAULT 0.0 COMMENT '强势期以来最高点',
    drawdown_pct FLOAT DEFAULT 0.0 COMMENT '回撤百分比',
    strong_days INT DEFAULT 0 COMMENT '强势天数',
    outflow_streak INT DEFAULT 0 COMMENT '资金流出连续天数',
    consolidation_days INT DEFAULT 0 COMMENT '整固观察天数',
    last_date DATE COMMENT '最近交易日',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 监控配置表
- **guanfu_monitor_object**: 监控对象配置

```sql
-- 监控对象表
CREATE TABLE guanfu_monitor_object (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(12) NOT NULL COMMENT '股票代码',
    name VARCHAR(64) NOT NULL COMMENT '股票名称',
    market_tag VARCHAR(16) NOT NULL COMMENT '市场标签',
    monitor_type VARCHAR(32) NOT NULL COMMENT '监控类型',
    monitor_arg VARCHAR(2048) NOT NULL COMMENT '监控参数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_market_tag (market_tag),
    INDEX idx_monitor_type (monitor_type)
);
```

## 🛠️ 技术栈

### 后端技术
- **Python 3.12+**: 主要开发语言
- **FastAPI**: 现代化Web框架，支持异步和自动API文档
- **SQLAlchemy**: ORM框架，支持多种数据库
- **Alembic**: 数据库迁移工具
- **APScheduler**: 任务调度框架
- **Pydantic**: 数据验证和设置管理

### 数据处理
- **AkShare**: 金融数据获取库
- **TuShare**: 金融数据接口
- **Pandas**: 数据分析和处理
- **NumPy**: 数值计算
- **Scikit-learn**: 机器学习算法

### 可视化
- **PyEcharts**: 交互式图表库
- **Matplotlib**: 基础绘图库
- **mplfinance**: 金融图表专用库

### 数据库
- **MySQL**: 主数据库，优化配置适配2核2GB服务器
- **连接池**: 优化的数据库连接池配置

### 通信与监控
- **Lark API**: 飞书机器人集成
- **WebSocket**: 实时数据推送
- **Loguru**: 高性能日志系统

## 📦 安装与部署

### 环境要求
- Python 3.12+
- MySQL 5.7+ / 8.0+
- 2GB+ 内存
- 2核+ CPU

### 快速开始

1. **克隆项目**
```bash
<NAME_EMAIL>:shadowYD/trading-system.git
cd trading-system
```

2. **安装依赖**
```bash
# 使用Poetry管理依赖
poetry install

# 或使用pip
pip install -r requirements.txt
```

3. **配置数据库**
```bash
# 复制配置文件
cp config/no_prod.toml config/prod.toml

# 编辑配置文件，设置数据库连接信息
vim config/prod.toml
```

4. **初始化数据库**
```bash
# 创建数据库迁移
alembic revision --autogenerate -m "Initial migration"

# 执行数据库迁移
alembic upgrade head
```

5. **启动应用**
```bash
# 开发环境
python main.py

# 生产环境
uvicorn main:app --host 0.0.0.0 --port 9999
```

### Docker部署

```bash
# 构建镜像
docker build -t trading-system .

# 运行容器
docker run -d \
  --name trading-system \
  -p 9999:9999 \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/logs:/app/logs \
  trading-system
```

## 🚀 使用指南

### Web界面访问
- **主界面**: http://localhost:9999
- **API文档**: http://localhost:9999/docs
- **监控面板**: http://localhost:9999/monitor

### 核心API接口

#### 股票分析
```bash
# 分析单只股票资金流向
POST /api/v1/analyze/zh/stock/analysis/fund-flow
{
  "code": "000001"
}

# 获取股票基础信息
GET /api/v1/stock/zh/info?code=000001
```

#### 板块分析
```bash
# 获取板块列表
GET /api/v1/blank/zh/list

# 分析板块资金流向
POST /api/v1/analyze/zh/blank/fund-flow
{
  "codes": ["BK0001", "BK0002"]
}
```

#### 监控管理
```bash
# 添加监控对象
POST /api/v1/monitor/add
{
  "code": "000001",
  "monitor_type": "volume",
  "threshold": 2.0
}

# 获取监控列表
GET /api/v1/monitor/list
```

### 定时任务

系统内置多个定时任务：

1. **每日板块分析**: 每日收盘后自动分析所有板块
2. **实时监控**: 交易时间内实时监控配置的股票
3. **数据更新**: 定时更新股票基础信息和K线数据

### 脚本工具

```bash
# 每日板块分析
python scripts/daily_zh_blank_analysis.py --end-date 2024-01-15

# 生成股票配置
python scripts/generate_stock_config_advanced.py

# 快速检查
python scripts/quick_check.py
```

## 📊 核心算法

### 交易信号算法
- **洗盘检测**: 识别主力洗盘行为，结合价量分析
- **吸筹信号**: 检测主力资金建仓信号
- **出货信号**: 识别主力出货行为
- **趋势判断**: 基于MA5/MA15的趋势强度判断

### 技术指标
- **ATR**: 平均真实波幅，衡量波动性
- **布林带**: 价格通道分析
- **成交量标准**: 基于历史数据的成交量异动检测
- **资金流向**: 大单资金进出分析

### 板块分析算法
- **四阶段分析**: 吸筹期、拉升期、出货期、整固期
- **资金动能**: 基于资金流向的动能变化分析
- **形态识别**: K线形态的自动识别和分类

## 🔧 配置说明

### 数据库配置
```toml
[mysql]
host = "localhost"
port = 3306
user = "root"
password = "your_password"
db = "trading_system"
charset = "utf8mb4"

# 连接池配置（适配2核2GB服务器）
pool_size = 3
max_overflow = 7
pool_recycle = 3600
pool_timeout = 30
pool_pre_ping = true
conn_timeout = 10
debug = false
```

### 应用配置
```toml
[app]
name = "guanfu-trader"
host = "0.0.0.0"
http_port = 9999
log_level = "INFO"
thread_pool_size = 50

[scheduler]
timezone = "Asia/Shanghai"
job_defaults = { coalesce = false, max_instances = 3 }
```

### 监控配置
```toml
[monitor]
# 成交量监控阈值
volume_threshold = 2.0
# 价格变动阈值
price_threshold = 0.05
# 监控间隔（秒）
check_interval = 60
```

## 📈 性能优化

### 数据库优化
- **索引优化**: 针对查询频繁的字段建立复合索引
- **连接池**: 根据服务器配置优化连接池参数
- **批量操作**: 使用批量插入和更新减少I/O
- **数据分区**: 按时间分区存储历史数据

### 应用优化
- **异步处理**: 使用FastAPI的异步特性
- **缓存机制**: 缓存频繁查询的数据
- **内存管理**: 优化大数据集的内存使用
- **并发控制**: 合理控制并发任务数量

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
pytest tests/

# 运行特定测试
pytest tests/test_analysis.py

# 生成覆盖率报告
pytest --cov=src tests/
```

### 测试覆盖
- 单元测试：核心算法和业务逻辑
- 集成测试：API接口和数据库操作
- 性能测试：高并发场景测试

## 📝 开发指南

### 代码规范
- 使用Black进行代码格式化
- 遵循PEP 8编码规范
- 使用类型注解提高代码可读性
- 编写详细的文档字符串

### 提交规范
```bash
# 功能开发
git commit -m "feat: 添加新的股票分析功能"

# 问题修复
git commit -m "fix: 修复数据库连接池配置问题"

# 文档更新
git commit -m "docs: 更新API文档"
```

### 分支管理
- `main`: 主分支，稳定版本
- `develop`: 开发分支，最新功能
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [AkShare](https://github.com/akfamily/akshare) - 金融数据接口
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化Web框架
- [PyEcharts](https://pyecharts.org/) - 数据可视化
- [SQLAlchemy](https://www.sqlalchemy.org/) - Python ORM

## 📞 联系方式

- 作者: Shadow-L
- 邮箱: <EMAIL>
- 项目地址: [https://gitee.com/shadowYD/trading-system](https://gitee.com/shadowYD/trading-system)

---

⭐ 如果这个项目对你有帮助，请给它一个星标！