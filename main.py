from src.api import API_V1
from src.domain import communicate
from src.domain.monitor.service.manager import smart_monitor_manager_svc
from src.domain.monitor.service.price import price_monitor_svc
from src.domain.monitor.service.volume import volume_monitor_svc
from src.infra.app import app
from src.infra.events import event_bus
from src.scheduler import keep_scheduler

_ = keep_scheduler
_ = API_V1

if __name__ == "__main__":
    # ====== on start ======
    # monitor price
    # 测试的时候用
    # app.add_on_start(price_monitor_svc.start_all_monitor)
    # app.add_on_start(volume_monitor_svc.start_all_monitor)

    app.add_on_start(event_bus.start)
    app.add_on_start(app.scheduler.start)

    app.add_on_start(smart_monitor_manager_svc.start)
    app.add_on_start(communicate.receiver_svc.start)

    # ====== on stop ======

    app.add_on_stop(event_bus.stop)
    app.add_on_stop(app.scheduler.stop)
    app.add_on_stop(smart_monitor_manager_svc.stop)
    app.add_on_stop(communicate.receiver_svc.stop)
    app.launch()
