#%%
import pandas as pd
code = "603899"
daily_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_daily_data.csv"
fund_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_individual_fund_flow.csv"
daily_data = pd.read_csv(daily_path)
# 读取资金流数据
funds_data = pd.read_csv(fund_path)
#%%
import pandas as pd
import numpy as np
from pyecharts.charts import Kline, Scatter
from pyecharts import options as opts

# === CONFIG ===
L = 10                 # 价格 & 资金滚动窗口
BOT_Q = 0.2            # 底部价格区位阈值
FLOW_Z_TH = 0.3        # 资金偏离阈值 (σ)
EPS = 1e-6

# === 1. 读取数据 ===
# daily_path = '9950f00b-2b1d-4988-b4d2-293ffb946b72.csv'
# fund_path  = 'b205a957-bd45-432c-9503-5c8f424f0da3.csv'

daily_raw = pd.read_csv(daily_path)
fund_raw  = pd.read_csv(fund_path)

# 重命名为英文列（假设 daily_raw 带有 OHLC 字段）
daily = daily_raw.rename(columns={
    'trade_date': 'trade_date',
    'open': 'open',
    'high': 'high',
    'low': 'low',
    'close': 'close'
})
funds = fund_raw.rename(columns={
    '日期': 'trade_date',
    '小单净流入-净额': 'small_net_amt',
    '大单净流入-净额': 'large_net_amt',
    '超大单净流入-净额': 'elg_net_amt'
})

daily['trade_date'] = pd.to_datetime(daily['trade_date'], format='%Y%m%d')
funds['trade_date'] = pd.to_datetime(funds['trade_date'], format='%Y-%m-%d')

# 只保留需要列
daily = daily[['trade_date', 'open', 'high', 'low', 'close']]
funds = funds[['trade_date', 'small_net_amt', 'large_net_amt', 'elg_net_amt']]

# 合并数据
df = pd.merge(
    daily,
    funds,
    on='trade_date',
    how='inner'
).set_index('trade_date').sort_index()

# === 2. 指标计算 ===
# 2.1 价格区位 price_rank，带振幅保护
df['roll_max'] = df['close'].rolling(L).max()
df['roll_min'] = df['close'].rolling(L).min()
rolling_mean_close = df['close'].rolling(L).mean()
rng = df['roll_max'] - df['roll_min']
valid_range = rng > rolling_mean_close * 0.02  # 过去 20 日振幅至少占均价 2%
df['price_rank'] = np.where(
    valid_range,
    (df['close'] - df['roll_min']) / (rng + EPS),
    np.nan
)

# 2.2 资金倾向 flow_balance → flow_z
df['large_net_amt_comb'] = df['large_net_amt'] + df['elg_net_amt']
df['flow_balance'] = (df['large_net_amt_comb'] - df['small_net_amt']).rolling(L).sum()

# 估算成交活跃度：使用绝对净额之和做归一化
df['gross_flow'] = (
    df['large_net_amt_comb'].abs() + df['small_net_amt'].abs()
).rolling(L).sum()
df['flow_ratio'] = df['flow_balance'] / (df['gross_flow'] + EPS)

# 资金 Z-score
mu = df['flow_ratio'].rolling(L).mean()
std = df['flow_ratio'].rolling(L).std() + EPS
df['flow_z'] = (df['flow_ratio'] - mu) / std

# === 3. 生成 K 线数据和底部信号 ===
# K 线日期列表
kline_dates = df.index.strftime('%Y-%m-%d').tolist()
# K 线 OHLC 数值列表 [open, close, low, high]
kline_values = df[['open', 'close', 'low', 'high']].round(2).values.tolist()

# 底部信号标记
df['bottom_signal'] = 0
df.loc[
    (df['price_rank'] < BOT_Q) &
    (df['flow_z'] > FLOW_Z_TH),
    'bottom_signal'
] = 1
bottom_df = df[df['bottom_signal'] == 1]

# 底部信号的日期和价格位置（用 low*0.98 放在蜡烛下方）
bottom_dates = bottom_df.index.strftime('%Y-%m-%d').tolist()
bottom_prices = (bottom_df['low'] * 0.98).round(2).tolist()

# === 4. 绘制 pyecharts K 线图并叠加底部散点 ===
kline = (
    Kline(init_opts=opts.InitOpts(width="100%", height="400px"))
    .add_xaxis(kline_dates)
    .add_yaxis("K Line", kline_values)
    .set_global_opts(
        title_opts=opts.TitleOpts(title=f"{code} K 线图与底部信号"),
        xaxis_opts=opts.AxisOpts(type_="category"),
        yaxis_opts=opts.AxisOpts(is_scale=True),
        datazoom_opts=[opts.DataZoomOpts(type_="inside"), opts.DataZoomOpts()],
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross")
    )
)

scatter = (
    Scatter()
    .add_xaxis(bottom_dates)
    .add_yaxis(
        "Bottom Signal",
        bottom_prices,
        symbol="circle",
        symbol_size=10,
        itemstyle_opts=opts.ItemStyleOpts(color="green")
    )
)

kline.overlap(scatter)

# 渲染并保存为 HTML
html_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_kline_with_bottom_signals.html"
kline.render(html_path)

#%%

#%%

#%%
