from datetime import datetime

from loguru import logger

from src.domain import communicate
from src.domain.store.service.zh import (
    zh_stock_service,
    zh_index_service,
    zh_blank_service,
)
from src.infra.app import app
from src.infra.clients.trading.utils import (
    is_zh_trading_date,
    get_zh_today_trading_time_range,
)
from src.infra.scheduler import app_scheduler


# 同步 zh_stock 行情
def sync_zh_stock_quote():
    if is_zh_trading_date(today=datetime.now()):
        communicate.sender_svc.notice(
            sub_title="同步股票行情", content="开始同步股票行情"
        )
        logger.info("Today is trading day, sync stock quotes...")
        try:
            zh_stock_service.sync_stock_quotes()
            communicate.sender_svc.notice_success(
                sub_title="同步股票行情", content="同步股票行情成功"
            )
        except Exception as e:
            logger.error(f"Sync stock quotes failed: {e}")
            communicate.sender_svc.notice_fail(
                sub_title="同步股票行情", content=f"同步股票行情失败, {e}"
            )
        return
    logger.info("Today is not trading day, skip sync stock quotes...")


# 同步 zh_stock kline 数据
def sync_zh_stock_kline():
    today = datetime.now()
    if is_zh_trading_date(today=today):
        communicate.sender_svc.notice(
            sub_title="同步股票K线", content="开始同步股票K线"
        )
        logger.info("Today is trading day, sync stock kline...")
        try:
            start_date, end_date = get_zh_today_trading_time_range()
            zh_stock_service.sync_all_stock_kline(
                start_date=start_date,
                end_date=end_date,
            )
            communicate.sender_svc.notice_success(
                sub_title="同步股票K线", content="同步股票K线成功"
            )
        except Exception as e:
            logger.exception(f"Sync stock kline failed: {e}")
            communicate.sender_svc.notice_fail(
                sub_title="同步股票K线", content=f"同步股票K线失败, {e}"
            )
        return
    logger.info("Today is not trading day, skip sync stock kline...")


# 同步指数行情
def sync_zh_index_quote():
    if is_zh_trading_date(today=datetime.now()):
        communicate.sender_svc.notice(
            sub_title="同步指数行情", content="开始同步指数行情"
        )
        logger.info("Today is trading day, sync index quotes...")
        try:
            zh_index_service.sync_index_quotes()
            communicate.sender_svc.notice_success(
                sub_title="同步指数行情", content="同步指数行情成功"
            )
        except Exception as e:
            logger.exception(f"Sync index quotes failed: {e}")
            communicate.sender_svc.notice_fail(
                sub_title="同步指数行情", content=f"同步指数行情失败, {e}"
            )
        return
    logger.info("Today is not trading day, skip sync index quotes...")


# 同步指数K线
def sync_zh_index_kline():
    today = datetime.now()
    if is_zh_trading_date(today=today):
        communicate.sender_svc.notice(
            sub_title="同步指数K线", content="开始同步指数K线"
        )
        logger.info("Today is trading day, sync index kline...")
        try:
            start_date, end_date = get_zh_today_trading_time_range()
            zh_index_service.sync_index_kline(
                start_date=start_date,
                end_date=end_date,
            )
            communicate.sender_svc.notice_success(
                sub_title="同步指数K线", content="同步指数K线成功"
            )
        except Exception as e:
            logger.exception(f"Sync index kline failed: {e}")
            communicate.sender_svc.notice_fail(
                sub_title="同步指数K线", content=f"同步指数K线失败, {e}"
            )
        return
    logger.info("Today is not trading day, skip sync index kline...")


# 更新板块数据
def update_stock_blank():
    try:
        if is_zh_trading_date(today=datetime.now()):
            communicate.sender_svc.notice(
                sub_title="更新板块数据", content="开始更新板块数据"
            )
            logger.info("Update stock blank...")
            zh_blank_service.update_stock_blank()
            communicate.sender_svc.notice_success(
                sub_title="更新板块数据", content="更新板块数据成功"
            )
    except Exception as e:
        logger.exception(f"Update stock blank failed: {e}")
        communicate.sender_svc.notice_fail(
            sub_title="更新板块数据", content=f"更新板块数据失败, {e}"
        )


def collect_stock_blank_fund():
    try:
        if is_zh_trading_date(today=datetime.now()):
            communicate.sender_svc.notice(
                sub_title="收集板块资金", content="开始收集板块资金"
            )
            logger.info("Collect stock blank fund...")
            zh_blank_service.collect_stock_blank_fund()
            communicate.sender_svc.notice_success(
                sub_title="收集板块资金", content="收集板块资金成功"
            )
    except Exception as e:
        logger.exception(f"Collect stock blank fund failed: {e}")
        communicate.sender_svc.notice_fail(
            sub_title="收集板块资金", content=f"收集板块资金失败, {e}"
        )


# 同步个股资金流数据
def collect_stock_fund_flow():
    try:
        if is_zh_trading_date(today=datetime.now()):
            communicate.sender_svc.notice(
                sub_title="同步个股资金流", content="开始同步个股资金流"
            )
            logger.info("Collect stock fund flow...")
            zh_stock_service.sync_all_stock_fund_flow(before_days=0)
            communicate.sender_svc.notice_success(
                sub_title="同步个股资金流", content="同步个股资金流成功"
            )
            return
    except Exception as e:
        logger.exception(f"Collect stock fund flow failed: {e}")
        communicate.sender_svc.notice_fail(
            sub_title="同步个股资金流", content=f"同步个股资金流失败, {e}"
        )


# 同步个股异动资金数据
def collect_stock_uma():
    try:
        if is_zh_trading_date(today=datetime.now()):
            communicate.sender_svc.notice(
                sub_title="同步个股异动资金", content="开始同步个股异动资金"
            )
            logger.info("Collect stock uma...")
            zh_stock_service.only_sync_stock_uma_which_in_db(before_days=0)
            communicate.sender_svc.notice_success(
                sub_title="同步个股异动资金", content="同步个股异动资金成功"
            )
    except Exception as e:
        logger.exception(f"Collect stock uma failed: {e}")
        communicate.sender_svc.notice_fail(
            sub_title="同步个股异动资金", content=f"同步个股异动资金失败, {e}"
        )


# 16:00
app.scheduler.add_job(
    sync_zh_index_quote,
    app_scheduler.CRON,
    hour="16",
    minute="0",
    second="0",
)
# 16:10
app.scheduler.add_job(
    sync_zh_index_kline,
    app_scheduler.CRON,
    hour="16",
    minute="10",
    second="0",
    id=app_scheduler.job_id("sync_zh_index_kline"),
)

# 16:20
app.scheduler.add_job(
    update_stock_blank,
    app_scheduler.CRON,
    hour="16",
    minute="20",
    second="0",
    id=app_scheduler.job_id("update_stock_blank"),
)

# 16:30
app.scheduler.add_job(
    collect_stock_blank_fund,
    app_scheduler.CRON,
    hour="16",
    minute="30",
    second="0",
    id=app_scheduler.job_id("collect_stock_blank_fund"),
)

# 16:40
app.scheduler.add_job(
    sync_zh_stock_quote,
    app_scheduler.CRON,
    hour="16",
    minute="40",
    second="0",
    id=app_scheduler.job_id("sync_zh_stock_quote"),
)

# 16:45
app.scheduler.add_job(
    collect_stock_fund_flow,
    app_scheduler.CRON,
    hour="16",
    minute="45",
    second="0",
    id=app_scheduler.job_id("collect_stock_fund_flow"),
)
app.scheduler.add_job(
    collect_stock_uma,
    app_scheduler.CRON,
    hour="16",
    minute="45",
    second="0",
    id=app_scheduler.job_id("collect_stock_uma"),
)

# 17:10
app.scheduler.add_job(
    sync_zh_stock_kline,
    app_scheduler.CRON,
    hour="17",
    minute="10",
    second="0",
    id=app_scheduler.job_id("sync_zh_stock_kline"),
)
