from __future__ import annotations

import time
from datetime import date
from typing import Optional, List

import numpy as np
import pandas as pd
from loguru import logger

from src.domain.analyze.aggregate.analysis.utils import list_zh_stock_blank_fund
from src.domain.analyze.repo.dao import ZhBlankAnalyzeFundFlowState, PhaseEnum
from src.infra.app import app
from src.infra.clients.trading.ak import ak_cli


class AnalyzeZhBlankFundFlowAggregate:
    """主力攻击方向分析聚合器 - 完整的四阶段分析流程（支持行业板块和概念板块）"""

    def __init__(self, is_concept: bool = False):
        # 支持行业板块和概念板块分析
        self.is_concept = is_concept
        self._stage0 = _Stage0PreProcessAggregate()
        self._stage1 = _Stage1ClassifyZoneAggregate()
        self._stage2 = _Stage2FilterByPatternAggregate()
        self._stage3 = _Stage3ScoreByWindowAggregate(is_concept)
        self._stage4 = _Stage4MigrateByScoreAggregate(is_concept)

    def run_full_analysis(
        self,
        before_days: int = 365,
        end_date: str = "2025-06-20",
        skip_pattern_filter: bool = True,
    ) -> pd.DataFrame:
        """运行完整的四阶段分析流程（支持行业板块和概念板块）"""
        board_type = "概念板块" if self.is_concept else "行业板块"
        logger.info(
            f"开始主力攻击方向分析 - {board_type}，截止到{end_date}，使用{before_days}天数据"
        )

        # Stage 0: 数据预处理
        df0 = self._stage0.preprocess_data(before_days, self.is_concept, end_date)

        if df0.empty:
            logger.error("Stage 0 数据预处理失败，返回空状态")
            return self._get_empty_state_df()

        logger.info(f"Stage 0 完成 - 处理了 {len(df0)} 条记录")

        # Stage 1: 资金四分区分类
        df1 = self._stage1.classify_zones(df0)

        if df1.empty:
            logger.error("Stage 1 分区分类失败，返回空状态")
            return self._get_empty_state_df()

        # 统计最新交易日的分区情况并打印各区板块详情
        latest_date = df1["date"].max()
        latest_data = df1[df1["date"] == latest_date]
        latest_zones = latest_data["zone"].value_counts().to_dict()

        # 打印各区的板块详情
        logger.info(f"Stage 1 完成 - 最新交易日分区统计: {latest_zones}")
        for zone in ["A", "B", "C", "D"]:
            zone_sectors = latest_data[latest_data["zone"] == zone]["name"].tolist()
            if zone_sectors:
                logger.info(
                    f"  {zone}区板块({len(zone_sectors)}个): {', '.join(zone_sectors)}"
                )
            else:
                logger.info(f"  {zone}区板块: 无")

        # Stage 2: 形态过滤
        df2 = self._stage2.filter_by_pattern(
            df1, skip_pattern_filter=skip_pattern_filter
        )

        # Stage 3: 5日评分与状态机迁移
        state_df = self._stage3.update_scores_and_migrate(df2)
        phase_counts = state_df["phase"].value_counts().to_dict()
        logger.info(f"Stage 3 完成 - 状态统计: {phase_counts}")

        # 详细打印各阶段的板块
        for phase in ["pre_burst", "early_accum", "strong_trend", "cooldown"]:
            phase_sectors = state_df[state_df["phase"] == phase].index.tolist()
            if phase_sectors:
                logger.info(
                    f"  {phase}池({len(phase_sectors)}个): {', '.join(phase_sectors)}"
                )
            else:
                logger.info(f"  {phase}池: 无")

        # Stage 4: 强势池跟踪与退场管理
        final_state = self._stage4.update_strong_pool(df2, state_df)
        summary = self._stage4.get_strong_pool_summary(final_state)
        logger.info(
            f"Stage 4 完成 - 强势池: {summary['count']}个, 平均天数: {summary.get('avg_days', 0):.1f}"
        )

        return final_state

    def _get_empty_state_df(self) -> pd.DataFrame:
        """返回空的状态DataFrame"""
        return pd.DataFrame(
            columns=[
                "name",
                "phase",
                "score",
                "days_cnt",
                "cooldown_days",
                "high_since_strong",
                "drawdown_pct",
                "strong_days",
                "outflow_streak",
                "last_date",
            ]
        ).set_index("name")

    def get_watchlist(self) -> dict:
        """获取各个池子的板块列表"""
        with app.orm_session() as session:
            states = (
                session.query(ZhBlankAnalyzeFundFlowState)
                .filter_by(is_concept=self.is_concept)
                .all()
            )

        result = {
            "strong_trend": [],
            "pre_burst": [],
            "early_accum": [],
            "cooldown": [],
        }

        for state in states:
            if state.phase.value in result:
                result[state.phase.value].append(
                    {
                        "name": state.name,
                        "score": state.score,
                        "days_cnt": state.days_cnt,
                        "strong_days": state.strong_days,
                        "drawdown_pct": state.drawdown_pct,
                        "last_date": state.last_date,
                    }
                )

        return result


class _Stage0PreProcessAggregate:
    """Stage 0: 数据预处理 - 计算各种技术指标和资金指标"""

    def __init__(self, is_concept: bool = False):
        self.is_concept = is_concept

    def preprocess_data(
        self, before_days: int, is_concept: bool, end_date: str = "2025-06-20"
    ) -> pd.DataFrame:
        """
        数据预处理主函数

        Returns:
            包含以下字段的DataFrame:
            - name, date: 主键
            - F1, F5, F10, FlowSlope: 资金指标
            - 52W_pct_rank, drawdown_1y, Slope_40d: 位置/形态指标
            - ATR_pct, BB_width, vol_ratio_5: 波动/量能指标
        """
        # 获取板块资金流数据
        df_fund = list_zh_stock_blank_fund(before_days, is_concept, end_date)

        if df_fund.empty:
            logger.warning("未获取到板块资金流数据")
            return pd.DataFrame()

        # 获取板块价格数据
        df_price = self._get_blank_price_data(
            df_fund["name"].unique(), before_days, end_date, is_concept
        )

        if df_price.empty:
            logger.error("无法获取板块价格数据，分析终止")
            return pd.DataFrame()

        # 合并数据
        df = self._merge_price_and_fund_data(df_price, df_fund)

        if df.empty:
            logger.error("数据合并后为空，分析终止")
            return pd.DataFrame()

        # 计算技术指标
        df = self._calculate_indicators(df)

        # 计算Stage 3需要的额外字段
        df = self._calculate_stage3_indicators(df)

        return df

    def _get_blank_price_data(
        self,
        blank_names: List[str],
        before_days: int,
        end_date: str = "2025-06-20",
        is_concept: bool = None,
    ) -> pd.DataFrame:
        """获取板块价格数据 - 使用ak_cli获取真实数据，支持行业板块和概念板块"""
        from datetime import datetime, timedelta

        # 如果没有传入is_concept参数，使用实例的设置
        if is_concept is None:
            is_concept = self.is_concept

        # 使用固定的结束日期
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        start_dt = end_dt - timedelta(days=before_days)
        start_date = start_dt.strftime("%Y-%m-%d")
        end_date_str = end_date

        all_data = []
        success_count = 0

        board_type = "概念板块" if is_concept else "行业板块"
        logger.info(
            f"开始获取 {len(blank_names)} 个{board_type}的价格数据，时间范围: {start_date} 到 {end_date_str}"
        )

        for i, name in enumerate(blank_names):
            try:
                # 根据板块类型选择合适的API
                time.sleep(0.2)  # 避免请求过于频繁
                if is_concept:
                    df = ak_cli.list_zh_blank_concept_daily(
                        blank_name=name,
                        start_date=start_dt,
                        end_date=end_dt,
                        period="daily",
                        adjust="",
                    )
                else:
                    df = ak_cli.list_zh_blank_daily(
                        blank_name=name,
                        start_date=start_dt,
                        end_date=end_dt,
                        period="日k",
                        adjust="",
                    )

                if df.empty:
                    logger.warning(f"板块 {name} 无数据")
                    continue

                # 标准化列名和数据格式
                df = df.rename(
                    columns={
                        "日期": "date",
                        "开盘": "open",
                        "收盘": "close",
                        "最高": "high",
                        "最低": "low",
                        "成交量": "volume",
                        "成交额": "amount",
                        "涨跌幅": "pct_chg",
                    }
                )

                # 添加板块名称
                df["name"] = name

                # 确保日期格式正确
                df["date"] = pd.to_datetime(df["date"]).dt.date

                # 选择需要的列
                required_cols = [
                    "name",
                    "date",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                ]
                available_cols = [col for col in required_cols if col in df.columns]
                df = df[available_cols]

                all_data.append(df)
                success_count += 1

                # 进度提示
                if (i + 1) % 10 == 0:
                    logger.info(f"已处理 {i + 1}/{len(blank_names)} 个板块")

            except Exception as e:
                logger.warning(f"获取板块 {name} 数据失败: {e}")
                continue

        if not all_data:
            logger.error("未能获取任何板块价格数据，返回空DataFrame")
            return pd.DataFrame()

        # 合并所有数据
        result_df = pd.concat(all_data, ignore_index=True)
        logger.info(
            f"成功获取 {success_count}/{len(blank_names)} 个板块的价格数据，共 {len(result_df)} 条记录"
        )

        return result_df

    def _calculate_stage3_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算Stage 3评分需要的额外指标"""
        if df.empty:
            return df

        df = df.copy()

        # 按板块分组计算
        def _calc_indicators(group):
            group = group.sort_values("date").copy()

            # MA20
            group["MA20"] = group["close"].rolling(20, min_periods=10).mean()

            # 重命名volume为vol以匹配原始Notebook
            if "volume" in group.columns and "vol" not in group.columns:
                group["vol"] = group["volume"]

            # 成交量相关
            group["vol_mean_5"] = group["vol"].rolling(5, min_periods=3).mean()
            group["vol_ratio_5"] = group["vol"] / group["vol_mean_5"]

            # 涨跌幅（如果没有的话）
            if "pct_chg" not in group.columns:
                group["pct_chg"] = group["close"].pct_change() * 100  # 转换为百分比

            # 是否跌破MA20
            group["close_below_ma20"] = group["close"] < group["MA20"]

            return group

        df = df.groupby("name", group_keys=False).apply(_calc_indicators)

        return df

    def _merge_price_and_fund_data(
        self, df_price: pd.DataFrame, df_fund: pd.DataFrame
    ) -> pd.DataFrame:
        """合并价格和资金数据"""
        # 确保日期格式一致
        df_price["date"] = pd.to_datetime(df_price["date"])
        df_fund["date"] = pd.to_datetime(df_fund["date"])

        # 左连接，保留所有价格数据
        df = df_price.merge(df_fund, on=["name", "date"], how="left")

        # 填充缺失的资金数据
        fund_columns = [
            "major_net_inflow",
            "super_net_inflow",
            "big_net_inflow",
            "medium_net_inflow",
            "small_net_inflow",
        ]
        for col in fund_columns:
            if col in df.columns:
                df[col] = df[col].fillna(0)

        return df.sort_values(["name", "date"])

    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算各种技术指标（与原始Notebook完全一致）"""
        df = df.copy()

        # 重命名volume为vol以匹配原始Notebook
        if "volume" in df.columns and "vol" not in df.columns:
            df["vol"] = df["volume"]

        # 按板块分组计算
        grouped = df.groupby("name", group_keys=False)

        # 1. 资金指标（与原始Notebook完全一致）
        df["F1"] = df["major_net_inflow"]
        # 使用min_periods=window确保与原始_rolling_sum函数一致
        df["F5"] = grouped["F1"].apply(lambda x: x.rolling(5, min_periods=5).sum())
        df["F10"] = grouped["F1"].apply(lambda x: x.rolling(10, min_periods=10).sum())
        df["FlowSlope"] = (df["F5"] - df["F10"]) / (df["F10"].abs() + 1e-9)

        # 2. 位置/形态指标
        df["high_52w"] = grouped["close"].apply(
            lambda x: x.rolling(252, min_periods=1).max()
        )
        df["52W_pct_rank"] = df["close"] / df["high_52w"]
        df["drawdown_1y"] = (df["close"] - df["high_52w"]) / df["high_52w"]
        df["Slope_40d"] = grouped["close"].apply(
            lambda x: x.rolling(40, min_periods=40).apply(
                self._calculate_slope, raw=True
            )
        )

        # 3. 波动/量能指标
        df["TR"] = df["high"] - df["low"]
        df["ATR_20"] = grouped["TR"].apply(
            lambda x: x.rolling(20, min_periods=20).mean()
        )
        df["ATR_pct"] = df["ATR_20"] / df["close"]
        df["BB_width"] = (
            2
            * grouped["close"].apply(lambda x: x.rolling(20, min_periods=20).std())
            / df["close"]
        )
        df["vol_mean_5"] = grouped["vol"].apply(
            lambda x: x.rolling(5, min_periods=5).mean()
        )
        df["vol_ratio_5"] = df["vol"] / df["vol_mean_5"]

        # 4. 移动平均线
        df["MA20"] = grouped["close"].apply(
            lambda x: x.rolling(20, min_periods=20).mean()
        )

        # 5. 涨跌幅计算
        df["pre_close"] = grouped["close"].shift(1)
        df["pct_chg"] = ((df["close"] - df["pre_close"]) / df["pre_close"] * 100).round(
            2
        )

        return df

    def _calculate_slope(self, arr: np.ndarray) -> float:
        """计算对数价格斜率"""
        if len(arr) < 2 or np.any(np.isnan(arr)) or np.any(arr <= 0):
            return np.nan

        y = np.log(arr)
        x = np.arange(len(y))

        try:
            return np.cov(x, y, bias=True)[0, 1] / np.var(x)
        except:
            return np.nan


class _Stage1ClassifyZoneAggregate:
    """Stage 1: 资金四分区分类 - 根据资金动能状态分为A/B/C/D四个区段"""

    def classify_zones(self, df: pd.DataFrame, slope_cut: float = 0.25) -> pd.DataFrame:
        """
        资金四分区分类

        Args:
            df: Stage 0 输出的数据
            slope_cut: FlowSlope阈值，用于区分B/C区

        Returns:
            添加了zone列的DataFrame，zone ∈ {A, B, C, D, None}
        """
        if df.empty:
            return df

        df = df.copy()
        df["zone"] = None

        # 按日期分组，计算每日的F10阈值
        for date_val in df["date"].unique():
            day_mask = df["date"] == date_val
            day_data = df[day_mask]

            # 计算当日F10阈值（中位数）
            f10_threshold = day_data["F10"].abs().median(skipna=True)

            # 对当日数据进行分区
            for idx in day_data.index:
                row = df.loc[idx]
                zone = self._classify_single_record(row, f10_threshold, slope_cut)
                df.loc[idx, "zone"] = zone

        return df

    def _classify_single_record(
        self, row: pd.Series, f10_threshold: float, slope_cut: float
    ) -> Optional[str]:
        """对单条记录进行分区分类"""
        F1, F5, F10, slope = row["F1"], row["F5"], row["F10"], row["FlowSlope"]

        # 检查必要字段是否有效
        if pd.isna(F10) or pd.isna(F5) or pd.isna(slope):
            return None

        # 分区逻辑
        if (F10 < 0) and (F5 < 0) and abs(slope) < 0.05:
            # A区：撤离 - 持续流出且无明显变化
            return "A"
        elif (F10 < 0) and (F5 < 0) and (slope > 0):
            # B区：吸筹 - 流出在收敛，开始回流
            return "B"
        elif (
            (F10 < 0)
            and (abs(F10) < f10_threshold)
            and (slope >= slope_cut)
            and (F1 > 0)
        ):
            # C区：临界 - 贴近零轴且首次转正
            return "C"
        elif (F5 >= 0) and (F10 >= 0):
            # D区：上拐 - 资金全面转正
            return "D"
        else:
            return None

    def get_latest_snapshot(
        self, df: pd.DataFrame, top_n: Optional[int] = None, sort_by: str = "FlowSlope"
    ) -> pd.DataFrame:
        """获取最新交易日的快照数据"""
        if df.empty:
            return df

        last_date = df["date"].max()
        snap = df[df["date"] == last_date].copy()

        # 排序
        if sort_by == "F1":
            snap = snap.sort_values(sort_by, key=lambda s: s.abs(), ascending=False)
        else:
            snap = snap.sort_values(sort_by, ascending=False, na_position="last")

        if top_n:
            snap = snap.head(top_n)

        # 返回关键列
        cols = ["name", "F1", "F5", "F10", "FlowSlope", "zone"]
        available_cols = [col for col in cols if col in snap.columns]
        return snap[available_cols].reset_index(drop=True)


class _Stage2FilterByPatternAggregate:
    """Stage 2: 形态过滤 - 对B区和C区板块进行形态过滤并分配phase"""

    def filter_by_pattern(
        self, df: pd.DataFrame, skip_pattern_filter: bool = False
    ) -> pd.DataFrame:
        """
        形态过滤并分配phase

        逻辑:
        - skip_pattern_filter=True: 跳过形态过滤，直接分配phase
        - skip_pattern_filter=False: 使用形态过滤
          - C区 + shape_ok → phase='pre_burst'
          - B区 + shape_ok → phase='early_accum'
          - 其他 → phase=None
        """
        if df.empty:
            return df

        df = df.copy()

        if skip_pattern_filter:
            # 跳过形态过滤，直接根据资金分区分配phase
            logger.info("跳过形态过滤，直接根据资金分区分配phase")
            df["shape_ok"] = True  # 所有板块都通过形态检查
        else:
            # 计算形态条件（简化版本）
            df["shape_ok"] = self._calculate_shape_ok(df)

        # 分配phase
        df["phase"] = None

        # C区 + shape_ok → pre_burst
        c_mask = (df["zone"] == "C") & df["shape_ok"]
        df.loc[c_mask, "phase"] = "pre_burst"

        # B区 + shape_ok → early_accum
        b_mask = (df["zone"] == "B") & df["shape_ok"]
        df.loc[b_mask, "phase"] = "early_accum"

        # 只统计最新交易日的数据
        latest_date = df["date"].max()
        latest_df = df[df["date"] == latest_date]

        latest_c_mask = (latest_df["zone"] == "C") & latest_df["shape_ok"]
        latest_b_mask = (latest_df["zone"] == "B") & latest_df["shape_ok"]

        pre_burst_count = latest_c_mask.sum()
        early_accum_count = latest_b_mask.sum()

        logger.info(
            f"Stage 2 完成 - pre_burst: {pre_burst_count}, early_accum: {early_accum_count}"
        )

        # 详细列出各阶段的板块
        if pre_burst_count > 0:
            pre_burst_names = latest_df[latest_c_mask]["name"].tolist()
            logger.info(f"  pre_burst板块: {pre_burst_names}")
        if early_accum_count > 0:
            early_accum_names = latest_df[latest_b_mask]["name"].tolist()
            logger.info(f"  early_accum板块: {early_accum_names}")

        return df

    def _calculate_shape_ok(self, df: pd.DataFrame) -> pd.Series:
        """
        计算形态是否OK - 实现真正的形态过滤逻辑

        条件（来自原始Jupyter Notebook）:
        1. 低位: 52W_pct_rank ≤ 0.75 AND drawdown_1y ≤ -0.20
        2. 横盘: abs(Slope_40d) ≤ 0.0015
        3. 压缩: ATR_pct ≤ 0.04 OR BB_width ≤ rolling_quantile(0.25)
        """
        if df.empty:
            return pd.Series(dtype=bool)

        # 检查必要字段
        required_cols = [
            "52W_pct_rank",
            "drawdown_1y",
            "Slope_40d",
            "ATR_pct",
            "BB_width",
        ]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logger.warning(
                f"形态过滤缺少字段: {missing_cols}，将所有板块标记为shape_ok=False"
            )
            return pd.Series(False, index=df.index)

        # 参数设置（极度放宽条件以适应当前市场环境）
        low_rank_cut = 0.98  # 52周位置阈值 (极度放宽到0.98，几乎允许所有位置)
        drawdown_cut = 0.05  # 回撤阈值 (改为正值，允许小幅上涨)
        slope_cut = 0.02  # 横盘斜率阈值 (大幅放宽到0.02，允许明显趋势)
        atr_cut = 0.25  # ATR阈值 (大幅放宽到0.25，允许25%的波动率)
        bb_rank_cut = 0.90  # BB宽度分位数 (极度放宽到0.90，几乎所有板块通过)
        rank_window = 252  # 滚动窗口（一年）

        # 计算BB动态阈值（按板块分组）
        def _roll_q(s: pd.Series):
            w = rank_window if len(s) >= rank_window else max(len(s), 100)
            return s.rolling(w, min_periods=max(20, w // 4)).quantile(bb_rank_cut)

        df_copy = df.copy()
        df_copy["BB_thresh"] = df_copy.groupby("name", group_keys=False)[
            "BB_width"
        ].transform(_roll_q)

        # 三个条件
        cond_low = (df_copy["52W_pct_rank"] <= low_rank_cut) & (
            df_copy["drawdown_1y"] <= drawdown_cut
        )
        cond_flat = df_copy["Slope_40d"].abs() <= slope_cut
        cond_tight = (df_copy["ATR_pct"] <= atr_cut) | (
            df_copy["BB_width"] <= df_copy["BB_thresh"]
        )

        # 形态OK = 低位 AND 横盘 AND 压缩
        shape_ok = cond_low & cond_flat & cond_tight

        # 统计信息
        total_count = len(df)
        shape_ok_count = shape_ok.sum()
        logger.info(f"形态过滤结果: {shape_ok_count}/{total_count} 个板块通过形态检查")

        return shape_ok

    def _is_low_position(self, row: pd.Series) -> bool:
        """判断是否处于低位"""
        return (
            not pd.isna(row.get("52W_pct_rank", np.nan)) and row["52W_pct_rank"] < 0.7
        )

    def _is_sideways(self, row: pd.Series) -> bool:
        """判断是否横盘"""
        slope = row.get("Slope_40d", np.nan)
        return not pd.isna(slope) and abs(slope) < 0.001

    def _is_compressed(self, row: pd.Series) -> bool:
        """判断是否压缩"""
        atr_pct = row.get("ATR_pct", np.nan)
        bb_width = row.get("BB_width", np.nan)

        atr_ok = not pd.isna(atr_pct) and atr_pct < 0.03
        bb_ok = not pd.isna(bb_width) and bb_width < 0.05

        return atr_ok and bb_ok


class _Stage3ScoreByWindowAggregate:
    """Stage 3: 5日评分与状态机迁移"""

    # 评分参数
    SCORE_WIN = 5  # 评分窗口：连续5个交易日
    COOLDOWN_N = 3  # 失败后冷却天数
    BIG_OUTFLOW = 15e9  # 15亿元：单日净流出超过此值直接失败

    def __init__(self, is_concept: bool = False):
        self._full_data = None  # 用于存储完整数据以供前一日数据查询
        self._is_concept = is_concept  # 存储板块类型

    def _calculate_outflow_threshold(self, blank_name: str, today_date: date) -> float:
        """根据板块规模动态计算大额流出阈值"""
        # 默认阈值
        default_threshold = self.BIG_OUTFLOW

        try:
            # 使用方法2: 基于当日板块成交额计算
            from datetime import datetime, timedelta

            # 获取当日成交额数据
            start_dt = datetime.combine(today_date, datetime.min.time())
            end_dt = start_dt + timedelta(days=1)

            # 根据板块类型选择合适的API
            if self._is_concept:
                df = ak_cli.list_zh_blank_concept_daily(
                    blank_name=blank_name,
                    start_date=start_dt,
                    end_date=end_dt,
                    period="daily",
                    adjust="",
                )
            else:
                df = ak_cli.list_zh_blank_daily(
                    blank_name=blank_name,
                    start_date=start_dt,
                    end_date=end_dt,
                    period="日k",
                    adjust="",
                )

            if not df.empty and "成交额" in df.columns:
                # 获取最新的成交额
                latest_amount = df.iloc[-1]["成交额"]
                if not pd.isna(latest_amount) and latest_amount > 0:
                    # 使用成交额的10%作为阈值，至少3亿
                    dynamic_threshold = max(0.1 * latest_amount, 3e9)
                    logger.debug(
                        f"{blank_name} 动态阈值计算: 成交额={latest_amount:,.0f}, 阈值={dynamic_threshold:,.0f}"
                    )
                    return dynamic_threshold

            logger.debug(
                f"{blank_name} 无法获取成交额数据，使用默认阈值{default_threshold:,.0f}"
            )
            return default_threshold

        except Exception as e:
            logger.warning(
                f"计算{blank_name}的资金流出阈值失败: {e}，使用默认值{default_threshold:,.0f}"
            )
            return default_threshold

    def update_scores_and_migrate(self, df2: pd.DataFrame) -> pd.DataFrame:
        """更新评分并进行状态机迁移"""
        if df2.empty:
            return self._get_empty_state_df()

        # 保存完整数据供前一日数据查询使用
        self._full_data = df2

        # 获取当前状态
        current_states = self._load_current_states()

        # 获取最新交易日数据
        latest_date = df2["date"].max()
        today_data = df2[df2["date"] == latest_date].set_index("name")

        # 调试信息：检查df2中的phase分布
        latest_df2 = df2[df2["date"] == latest_date]
        phase_counts_df2 = latest_df2["phase"].value_counts()
        logger.info(f"df2最新交易日phase分布: {phase_counts_df2.to_dict()}")

        # 检查today_data中的phase分布
        phase_counts_today = today_data["phase"].value_counts()
        logger.info(f"today_data中phase分布: {phase_counts_today.to_dict()}")

        # 更新状态
        updated_states = self._update_states_with_today_data(
            current_states, today_data, latest_date
        )

        # 保存状态到数据库
        self._save_states_to_db(updated_states, self._is_concept)

        # 清理临时数据
        self._full_data = None

        return updated_states

    def _load_current_states(self) -> pd.DataFrame:
        """从数据库加载当前状态"""
        with app.orm_session() as session:
            states = (
                session.query(ZhBlankAnalyzeFundFlowState)
                .filter_by(is_concept=self._is_concept)
                .all()
            )

        if not states:
            return self._get_empty_state_df()

        data = []
        for state in states:
            data.append(
                {
                    "name": state.name,
                    "phase": state.phase.value,
                    "score": state.score,
                    "days_cnt": state.days_cnt,
                    "cooldown_days": state.cooldown_days,
                    "high_since_strong": state.high_since_strong,
                    "drawdown_pct": state.drawdown_pct,
                    "strong_days": state.strong_days,
                    "outflow_streak": state.outflow_streak,
                    "last_date": state.last_date,
                }
            )

        return pd.DataFrame(data).set_index("name")

    def _get_empty_state_df(self) -> pd.DataFrame:
        """返回空的状态DataFrame"""
        return pd.DataFrame(
            columns=[
                "name",
                "phase",
                "score",
                "days_cnt",
                "cooldown_days",
                "high_since_strong",
                "drawdown_pct",
                "strong_days",
                "outflow_streak",
                "last_date",
            ]
        ).set_index("name")

    def _update_states_with_today_data(
        self, states: pd.DataFrame, today_data: pd.DataFrame, today_date: date
    ) -> pd.DataFrame:
        """使用今日数据更新状态"""
        # 处理冷却期倒计时
        states = self._update_cooldown(states)

        # 处理今日有phase的板块（从Stage 2输出）
        phase_data = today_data[today_data["phase"].notna()]
        logger.info(f"Stage 2输出的有phase板块: {len(phase_data)}个")

        for phase_name in ["pre_burst", "early_accum"]:
            phase_count = len(phase_data[phase_data["phase"] == phase_name])
            if phase_count > 0:
                phase_names = phase_data[
                    phase_data["phase"] == phase_name
                ].index.tolist()
                logger.info(f"  {phase_name}: {phase_count}个 - {phase_names}")

        for name, row in phase_data.iterrows():
            phase = row["phase"]

            if name not in states.index:
                # 新板块
                states.loc[name] = self._create_new_state(name, phase, today_date)
                logger.info(f"新增{phase}板块: {name}")
                if phase == "early_accum":
                    logger.info(
                        f"新增early_accum板块详情: {name}, score={states.loc[name, 'score']}, days_cnt={states.loc[name, 'days_cnt']}"
                    )
            elif states.loc[name, "phase"] in ["none", "cooldown"]:
                # 从其他状态转入新phase
                old_phase = states.loc[name, "phase"]
                states.loc[name, "phase"] = phase
                states.loc[name, "score"] = 0
                states.loc[name, "days_cnt"] = 0
                states.loc[name, "last_date"] = today_date
                logger.info(f"{name} 从{old_phase}转入{phase}")
            elif states.loc[name, "phase"] == "early_accum" and phase == "pre_burst":
                # early_accum升级到pre_burst（B区→C区）
                states.loc[name, "phase"] = "pre_burst"
                states.loc[name, "score"] = 0
                states.loc[name, "days_cnt"] = 0
                states.loc[name, "last_date"] = today_date
                logger.info(f"{name} 从early_accum升级到pre_burst")
            elif states.loc[name, "phase"] == "early_accum" and phase == "early_accum":
                # early_accum板块继续保持，稍后会在专门的处理逻辑中更新
                logger.info(f"{name} 继续保持early_accum状态")
                pass

        # 处理pre_burst阶段的评分
        pre_burst_blanks = states[states["phase"] == "pre_burst"].index
        for name in pre_burst_blanks:
            if name in today_data.index:
                # 获取前一日数据用于评分计算
                prev_row = self._get_previous_day_data(name, today_date, today_data)
                states.loc[name] = self._update_pre_burst_score(
                    states.loc[name], today_data.loc[name], today_date, name, prev_row
                )

        # early_accum板块需要验证是否仍满足条件（增加容错机制）
        # 注意：这里要在处理完新板块后再获取early_accum列表
        early_accum_blanks = states[states["phase"] == "early_accum"].index
        logger.info(
            f"处理early_accum板块: {len(early_accum_blanks)}个 - {list(early_accum_blanks)}"
        )
        for name in early_accum_blanks:
            if name in today_data.index:
                today_row = today_data.loc[name]
                prev_row = self._get_previous_day_data(name, today_date, today_data)

                # 计算early_accum阶段的评分
                daily_score = self._calculate_early_accum_score(today_row, prev_row)

                # 更新累计评分和天数
                states.loc[name, "score"] += daily_score
                states.loc[name, "days_cnt"] += 1
                states.loc[name, "last_date"] = today_date

                # 检查状态迁移条件
                if (
                    today_row.get("zone") == "C"
                    and today_row.get("phase") == "pre_burst"
                ):
                    # 升级到pre_burst
                    states.loc[name, "phase"] = "pre_burst"
                    states.loc[name, "score"] = 0
                    states.loc[name, "days_cnt"] = 0
                    logger.info(f"{name} 从early_accum升级到pre_burst")
                elif (
                    states.loc[name, "score"] >= 3 and states.loc[name, "days_cnt"] >= 3
                ):
                    # 评分达到升级阈值，升级到pre_burst
                    states.loc[name, "phase"] = "pre_burst"
                    states.loc[name, "score"] = 0
                    states.loc[name, "days_cnt"] = 0
                    logger.info(f"{name} 通过评分从early_accum升级到pre_burst")
                elif (
                    states.loc[name, "score"] <= -5
                    or states.loc[name, "days_cnt"] >= 15
                ):
                    # 评分过低或观察期过长，移除状态
                    states.loc[name, "phase"] = "none"
                    states.loc[name, "score"] = 0
                    states.loc[name, "days_cnt"] = 0
                    logger.info(f"{name} early_accum评分过低或观察期过长，移除状态")
                # 其他情况继续观察，不轻易移除
            else:
                # 今日没有数据的early_accum板块，保持状态但不更新评分
                logger.warning(f"{name} early_accum板块今日无数据，保持现有状态")

        return states

    def _get_previous_day_data(
        self, name: str, today_date: date, today_data: pd.DataFrame
    ) -> Optional[pd.Series]:
        """
        获取指定板块的前一日数据

        需要的字段：
        - F1: 主力净流入，用于计算"F1比昨日增加"
        - pct_chg: 涨跌幅，用于计算"连续两日阴线"
        """
        try:
            from datetime import timedelta

            # 计算前一个交易日
            prev_date = today_date - timedelta(days=1)

            # 方法1: 从当前分析的完整数据中查找前一日数据
            # 这需要在Stage 3中传入完整的df2数据
            if hasattr(self, "_full_data") and self._full_data is not None:
                prev_data = self._full_data[
                    (self._full_data["name"] == name)
                    & (self._full_data["date"] == prev_date)
                ]
                if not prev_data.empty:
                    return prev_data.iloc[0]

            # 方法2: 重新获取前一日的资金和价格数据（较重的操作）
            return self._fetch_single_day_data(name, prev_date, self._is_concept)

        except Exception as e:
            logger.warning(f"获取{name}前一日数据失败: {e}")
            return None

    def _fetch_single_day_data(
        self, name: str, target_date: date, is_concept: bool = False
    ) -> Optional[pd.Series]:
        """获取指定板块指定日期的数据"""
        try:
            from datetime import datetime, timedelta

            # 获取目标日期前后几天的数据，确保能找到交易日
            start_date = target_date - timedelta(days=5)
            end_date = target_date + timedelta(days=1)

            # 获取资金数据
            df_fund = list_zh_stock_blank_fund(
                before_days=7,
                is_concept=is_concept,
                end_date=target_date.strftime("%Y-%m-%d"),
            )

            if df_fund.empty:
                return None

            # 筛选指定板块和日期的数据
            target_fund = df_fund[
                (df_fund["name"] == name)
                & (pd.to_datetime(df_fund["date"]).dt.date == target_date)
            ]

            if target_fund.empty:
                return None

            # 获取价格数据（简化版，只获取涨跌幅）
            try:
                start_dt = datetime.combine(start_date, datetime.min.time())
                end_dt = datetime.combine(end_date, datetime.min.time())
                # 根据板块类型选择合适的API
                time.sleep(0.2)  # 避免请求过于频繁
                if is_concept:
                    df_price = ak_cli.list_zh_blank_concept_daily(
                        blank_name=name,
                        start_date=start_dt,
                        end_date=end_dt,
                        period="daily",
                        adjust="",
                    )
                else:
                    df_price = ak_cli.list_zh_blank_daily(
                        blank_name=name,
                        start_date=start_dt,
                        end_date=end_dt,
                        period="日k",
                        adjust="",
                    )

                if not df_price.empty:
                    # 标准化列名
                    df_price = df_price.rename(
                        columns={"日期": "date", "涨跌幅": "pct_chg"}
                    )
                    df_price["date"] = pd.to_datetime(df_price["date"]).dt.date

                    # 筛选目标日期
                    target_price = df_price[df_price["date"] == target_date]

                    if not target_price.empty:
                        # 合并资金和价格数据
                        result = target_fund.iloc[0].copy()
                        result["F1"] = result["major_net_inflow"]
                        result["pct_chg"] = target_price.iloc[0]["pct_chg"]
                        return result

            except Exception as e:
                logger.debug(f"获取{name}价格数据失败，仅使用资金数据: {e}")

            # 如果价格数据获取失败，至少返回资金数据
            result = target_fund.iloc[0].copy()
            result["F1"] = result["major_net_inflow"]
            result["pct_chg"] = 0  # 默认值
            return result

        except Exception as e:
            logger.debug(f"获取{name}在{target_date}的数据失败: {e}")
            return None

    def _update_cooldown(self, states: pd.DataFrame) -> pd.DataFrame:
        """更新冷却期倒计时"""
        cooldown_mask = states["phase"] == "cooldown"
        states.loc[cooldown_mask, "cooldown_days"] -= 1

        # 冷却期结束，重置为none
        finished_cooldown = (states["phase"] == "cooldown") & (
            states["cooldown_days"] <= 0
        )
        states.loc[finished_cooldown, "phase"] = "none"
        states.loc[finished_cooldown, "cooldown_days"] = 0

        return states

    def _create_new_state(self, name: str, phase: str, date_val: date) -> pd.Series:
        """创建新的状态记录"""
        return pd.Series(
            {
                "phase": phase,
                "score": 0,
                "days_cnt": 0,
                "cooldown_days": 0,
                "high_since_strong": 0.0,
                "drawdown_pct": 0.0,
                "strong_days": 0,
                "outflow_streak": 0,
                "last_date": date_val,
            },
            name=name,
        )

    def _update_pre_burst_score(
        self,
        state: pd.Series,
        today_row: pd.Series,
        today_date: date,
        name: str,
        prev_row: pd.Series = None,
    ) -> pd.Series:
        """更新pre_burst阶段的评分"""
        # 计算当日得分
        daily_score = self._calculate_daily_score(today_row, prev_row)

        # 更新累计评分
        state["score"] += daily_score
        state["days_cnt"] += 1
        state["last_date"] = today_date

        # 计算动态流出阈值
        dynamic_threshold = self._calculate_outflow_threshold(name, today_date)

        # 检查是否需要状态迁移
        if state["score"] >= 5 and state["days_cnt"] >= self.SCORE_WIN:
            # 升级到strong_trend
            state["phase"] = "strong_trend"
            state["high_since_strong"] = today_row.get("close", 0)
            state["strong_days"] = 0
            state["score"] = 0
            state["days_cnt"] = 0
        elif state["score"] <= -3 or today_row.get("F1", 0) < -dynamic_threshold:
            # 失败，进入冷却期
            state["phase"] = "cooldown"
            state["cooldown_days"] = self.COOLDOWN_N
            state["score"] = 0
            state["days_cnt"] = 0
        elif state["days_cnt"] >= self.SCORE_WIN:
            # 满5日但未触发阈值，重新开始
            state["score"] = daily_score
            state["days_cnt"] = 1

        return state

    def _calculate_daily_score(
        self, today_row: pd.Series, prev_row: pd.Series = None
    ) -> int:
        """
        计算单日评分（与原始Notebook完全一致）

        评分规则:
        +2: F1 > 0 (主力净流入)
        +1: F1比昨日增加 (流入加速)
        +2: 涨幅>1%且量比5≥1.2 (量价共振)
        -3: 连续两日阴线且连续净流出
        -2: 跌破MA20
        """
        score = 0

        # 正分规则
        if today_row.get("F1", 0) > 0:
            score += 2

        if prev_row is not None and today_row.get("F1", 0) > prev_row.get("F1", 0):
            score += 1

        pct_chg = today_row.get("pct_chg", 0)
        vol_ratio_5 = today_row.get("vol_ratio_5", 1)
        if pct_chg > 0.01 and vol_ratio_5 >= 1.2:  # 注意：原始是0.01不是1.0
            score += 2

        # 负分规则
        if prev_row is not None:
            today_down = today_row.get("pct_chg", 0) < 0
            prev_down = prev_row.get("pct_chg", 0) < 0
            today_outflow = today_row.get("F1", 0) < 0
            prev_outflow = prev_row.get("F1", 0) < 0

            if today_down and prev_down and today_outflow and prev_outflow:
                score -= 3

        if today_row.get("close_below_ma20", False):
            score -= 2

        return score

    def _calculate_early_accum_score(
        self, today_row: pd.Series, prev_row: pd.Series = None
    ) -> int:
        """
        计算early_accum阶段的评分（相对宽松的评分机制）

        评分规则:
        +1: F1 > 0 (主力净流入)
        +1: F1比昨日增加 (流入加速)
        +1: 涨幅>0.5%且量比5≥1.1 (温和量价共振)
        +1: 仍在B区 (保持在资金回流区)
        -1: F1 < 0 (资金流出)
        -2: 连续两日阴线且连续净流出
        -1: 跌破MA20
        -2: 跌出B区到D区 (资金明显撤离)
        """
        score = 0

        # 正分规则（相对宽松）
        if today_row.get("F1", 0) > 0:
            score += 1

        if prev_row is not None and today_row.get("F1", 0) > prev_row.get("F1", 0):
            score += 1

        pct_chg = today_row.get("pct_chg", 0)
        vol_ratio_5 = today_row.get("vol_ratio_5", 1)
        if pct_chg > 0.005 and vol_ratio_5 >= 1.1:  # 更宽松的量价条件
            score += 1

        if today_row.get("zone") == "B":  # 仍在B区
            score += 1

        # 负分规则
        if today_row.get("F1", 0) < 0:
            score -= 1

        if prev_row is not None:
            today_down = today_row.get("pct_chg", 0) < 0
            prev_down = prev_row.get("pct_chg", 0) < 0
            today_outflow = today_row.get("F1", 0) < 0
            prev_outflow = prev_row.get("F1", 0) < 0

            if today_down and prev_down and today_outflow and prev_outflow:
                score -= 2

        if today_row.get("close_below_ma20", False):
            score -= 1

        if today_row.get("zone") == "D":  # 跌到D区，资金明显撤离
            score -= 2

        return score

    def _save_states_to_db(self, states: pd.DataFrame, is_concept: bool):
        """保存状态到数据库"""
        with app.orm_session() as session:
            # 只清空对应类型的现有数据
            session.query(ZhBlankAnalyzeFundFlowState).filter_by(
                is_concept=is_concept
            ).delete()

            # 插入新数据
            for name, row in states.iterrows():
                state = ZhBlankAnalyzeFundFlowState(
                    name=name,
                    is_concept=is_concept,
                    phase=PhaseEnum(row["phase"]),
                    score=int(row["score"]),
                    days_cnt=int(row["days_cnt"]),
                    cooldown_days=int(row["cooldown_days"]),
                    high_since_strong=float(row["high_since_strong"]),
                    drawdown_pct=float(row["drawdown_pct"]),
                    strong_days=int(row["strong_days"]),
                    outflow_streak=int(row["outflow_streak"]),
                    last_date=row["last_date"],
                )
                session.add(state)

            session.commit()


class _Stage4MigrateByScoreAggregate:
    """Stage 4: 强势池跟踪与退场状态机"""

    # 退场参数
    MAX_DRAWDOWN = 0.10  # 10%回撤止盈（收紧）
    MAX_OUT_STREAK = 3  # 连续34日资金净流出触发退场
    COOLDOWN_N2 = 3  # 退场后冷却天数
    VOLUME_WEAK_MA20 = True  # 开启量价弱破位规则

    # 强势池管理参数
    MAX_STRONG_SECTORS = 40  # 最多保留40个强势板块
    TIME_DECAY_THRESHOLD = 20  # 强势时间>15天开始降权

    def __init__(self, is_concept: bool = False):
        self._is_concept = is_concept

    def update_strong_pool(
        self, df2: pd.DataFrame, state_df: pd.DataFrame
    ) -> pd.DataFrame:
        """检查强势池，更新高点&回撤，满足退场条件则冷却"""
        if df2.empty or state_df.empty:
            return state_df

        # 获取最新交易日数据
        latest_date = df2["date"].max()
        today_data = df2[df2["date"] == latest_date].set_index("name")

        # 更新强势池中的板块
        strong_trend_blanks = state_df[state_df["phase"] == "strong_trend"].index

        for name in strong_trend_blanks:
            if name in today_data.index:
                state_df.loc[name] = self._update_strong_trend_state(
                    state_df.loc[name], today_data.loc[name], latest_date
                )
            else:
                # 停牌或无数据：强势天数+1
                state_df.loc[name, "strong_days"] += 1

        # 应用强势池管理策略
        state_df = self._apply_strong_pool_management(state_df)

        # 保存更新后的状态
        self._save_updated_states(state_df)

        return state_df

    def _apply_strong_pool_management(self, state_df: pd.DataFrame) -> pd.DataFrame:
        """应用强势池管理策略：限制数量和时间衰减"""
        strong_sectors = state_df[state_df["phase"] == "strong_trend"].copy()

        if len(strong_sectors) <= self.MAX_STRONG_SECTORS:
            return state_df  # 数量未超限，无需处理

        logger.info(
            f"强势池超限：{len(strong_sectors)}个 > {self.MAX_STRONG_SECTORS}个，开始筛选"
        )

        # 计算综合评分（考虑时间衰减）
        strong_sectors["management_score"] = self._calculate_management_score(
            strong_sectors
        )

        # 按评分排序，保留前N个
        top_sectors = strong_sectors.nlargest(
            self.MAX_STRONG_SECTORS, "management_score"
        )
        eliminated_sectors = strong_sectors.drop(top_sectors.index)

        # 将被淘汰的板块移到cooldown
        for name in eliminated_sectors.index:
            state_df.loc[name, "phase"] = "cooldown"
            state_df.loc[name, "cooldown_days"] = self.COOLDOWN_N2
            state_df.loc[name, "score"] = 0
            state_df.loc[name, "days_cnt"] = 0
            logger.info(f"{name} 被强势池管理淘汰，进入cooldown")

        logger.info(
            f"强势池管理完成，保留{len(top_sectors)}个板块，淘汰{len(eliminated_sectors)}个板块"
        )

        return state_df

    def _calculate_management_score(self, strong_sectors: pd.DataFrame) -> pd.Series:
        """计算强势池管理评分"""
        scores = pd.Series(0.0, index=strong_sectors.index)

        # 基础分：回撤越小越好（回撤为负值，所以用负号）
        scores += -strong_sectors["drawdown_pct"] * 100  # 回撤-5%得5分

        # 时间衰减：强势时间>15天开始扣分
        time_penalty = (strong_sectors["strong_days"] - self.TIME_DECAY_THRESHOLD).clip(
            lower=0
        )
        scores -= time_penalty * 0.5  # 每超过1天扣0.5分

        # 强势天数奖励（适度）：前15天每天+0.2分
        time_bonus = (
            strong_sectors["strong_days"].clip(upper=self.TIME_DECAY_THRESHOLD) * 0.2
        )
        scores += time_bonus

        return scores

    def _update_strong_trend_state(
        self, state: pd.Series, today_row: pd.Series, today_date: date
    ) -> pd.Series:
        """更新强势趋势状态"""
        close = today_row.get("close", 0)
        f1 = today_row.get("F1", 0)
        flow_slope = today_row.get("FlowSlope", 0)
        vol = today_row.get("volume", 0)
        vol_mean_5 = today_row.get("vol_mean_5", vol)
        ma20 = today_row.get("MA20", close)

        # 更新最高点
        high_prev = (
            state["high_since_strong"] if state["high_since_strong"] > 0 else close
        )
        high_new = max(high_prev, close)

        # 计算回撤
        drawdown = (close - high_new) / high_new if high_new > 0 else 0

        # 更新基本字段
        state["high_since_strong"] = high_new
        state["drawdown_pct"] = drawdown
        state["strong_days"] += 1
        state["last_date"] = today_date

        # 更新资金流出连续计数
        if f1 < 0:
            state["outflow_streak"] += 1
        else:
            state["outflow_streak"] = 0

        # 检查退场条件
        exit_flag = False

        # 1. 回撤止盈
        if drawdown <= -self.MAX_DRAWDOWN:
            exit_flag = True
            logger.info(f"{state.name} 触发回撤止盈: {drawdown:.2%}")

        # 2. 资金转弱
        elif state["outflow_streak"] >= self.MAX_OUT_STREAK and flow_slope < 0:
            exit_flag = True
            logger.info(
                f"{state.name} 触发资金转弱: 连续{state['outflow_streak']}日流出"
            )

        # 3. 弱量破位
        elif self.VOLUME_WEAK_MA20 and close < ma20 and vol < vol_mean_5:
            exit_flag = True
            logger.info(f"{state.name} 触发弱量破位")

        # 执行退场
        if exit_flag:
            state["phase"] = "cooldown"
            state["score"] = 0
            state["days_cnt"] = 0
            state["cooldown_days"] = self.COOLDOWN_N2
            state["outflow_streak"] = 0

        return state

    def _save_updated_states(self, state_df: pd.DataFrame):
        """保存更新后的状态到数据库"""
        with app.orm_session() as session:
            for name, row in state_df.iterrows():
                # 查找现有记录（根据name和is_concept）
                existing = (
                    session.query(ZhBlankAnalyzeFundFlowState)
                    .filter_by(name=name, is_concept=self._is_concept)
                    .first()
                )

                if existing:
                    # 更新现有记录
                    existing.phase = PhaseEnum(row["phase"])
                    existing.score = int(row["score"])
                    existing.days_cnt = int(row["days_cnt"])
                    existing.cooldown_days = int(row["cooldown_days"])
                    existing.high_since_strong = float(row["high_since_strong"])
                    existing.drawdown_pct = float(row["drawdown_pct"])
                    existing.strong_days = int(row["strong_days"])
                    existing.outflow_streak = int(row["outflow_streak"])
                    existing.last_date = row["last_date"]
                else:
                    # 创建新记录
                    new_state = ZhBlankAnalyzeFundFlowState(
                        name=name,
                        is_concept=self._is_concept,
                        phase=PhaseEnum(row["phase"]),
                        score=int(row["score"]),
                        days_cnt=int(row["days_cnt"]),
                        cooldown_days=int(row["cooldown_days"]),
                        high_since_strong=float(row["high_since_strong"]),
                        drawdown_pct=float(row["drawdown_pct"]),
                        strong_days=int(row["strong_days"]),
                        outflow_streak=int(row["outflow_streak"]),
                        last_date=row["last_date"],
                    )
                    session.add(new_state)

            session.commit()

    def get_strong_pool_summary(self, state_df: pd.DataFrame) -> dict:
        """获取强势池摘要信息"""
        strong_blanks = state_df[state_df["phase"] == "strong_trend"]

        if strong_blanks.empty:
            return {"count": 0, "avg_days": 0, "avg_drawdown": 0}

        return {
            "count": len(strong_blanks),
            "avg_days": strong_blanks["strong_days"].mean(),
            "avg_drawdown": strong_blanks["drawdown_pct"].mean(),
            "max_drawdown": strong_blanks["drawdown_pct"].min(),
            "top_performers": strong_blanks.nlargest(5, "strong_days")[
                ["strong_days", "drawdown_pct"]
            ].to_dict("index"),
        }
    
    
class ZhBlankFundFlowAggregate:
    pass
