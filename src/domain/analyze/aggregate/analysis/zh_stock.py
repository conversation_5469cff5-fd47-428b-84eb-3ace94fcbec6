import os

import numpy as np
import pandas as pd
from loguru import logger
from pyecharts import options as opts
from pyecharts.charts import <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Grid

from src.domain.analyze.aggregate.analysis.utils import (
    AnalyzeDataSpec,
    list_zh_stock_daily_data,
    list_zh_stock_fund_flow,
    create_analysis_graph_dir,
    list_zh_stock_uma,
)
from src.infra.clients.trading.ak import ak_cli


class AnalyzeZhStockFundFlowAggregate:

    def __init__(self, code: str):
        self.code = code

    def start_zh_smart_money_vs_noise_trader_flow(self):
        logger.info(
            f"Start analyze zh stock smart money vs noise trader flow, code: {self.code}"
        )
        aggregate = _ZhSmartMoneyVsNoiseTraderFlowAggregate(self.code).analyze()
        aggregate.make_graph()

    def start_zh_major_player_entry_signal(self):
        logger.info(
            f"Start analyze zh stock major player entry signal, code: {self.code}"
        )
        aggregate = _ZhMajorPlayerEntrySignalAggregate(self.code).analyze()
        aggregate.make_graph()

    def start_zh_volume_inertia_cluster(self):
        logger.info(f"Start analyze zh stock volume inertia cluster, code: {self.code}")
        aggregate = _ZhVolumeInertiaClusterAggregate(self.code).analyze()
        aggregate.make_graph()

    def start_zh_phase_bottom(self):
        logger.info(f"Start analyze zh stock phase bottom, code: {self.code}")
        aggregate = _ZhPhaseBottomAggregate(self.code).analyze()
        aggregate.make_graph()


class AnalyzeZhSectorFundFlowAggregate:

    def start_zh_sector_fund_flow(self):
        pass


# 主力散户线分析
class _ZhSmartMoneyVsNoiseTraderFlowAggregate(AnalyzeDataSpec):

    def __init__(self, code: str):
        self.code = code
        self.kline_data = None
        self.kline_dates = None
        self.all_flow_dates = None
        self.div_main_str = None
        self.div_heat_str = None
        self.div_retail_str = None
        self.div_main_vals = None
        self.div_heat_vals = None
        self.div_retail_vals = None
        self.main_vals = None
        self.heat_vals = None
        self.retail_vals = None

    def analyze(self) -> "_ZhSmartMoneyVsNoiseTraderFlowAggregate":
        daily_data = list_zh_stock_daily_data(self.code)
        funds_data = list_zh_stock_fund_flow(self.code)

        # 确保date列是datetime类型
        daily_data["date"] = pd.to_datetime(daily_data["date"])
        funds_data["date"] = pd.to_datetime(funds_data["date"])

        daily_data = daily_data.set_index("date").sort_index()
        funds_data = funds_data.set_index("date").sort_index()

        # 定义滚动窗口大小
        window = 5

        # 计算三条净占比：主力（大单+超大单）、游资（中单）、散户（小单）
        funds_data["main_net_pct"] = funds_data["super_rate"] + funds_data["big_rate"]
        funds_data["heat_net_pct"] = funds_data["medium_rate"]
        funds_data["retail_net_pct"] = funds_data["small_rate"]

        # 计算滚动均值，用于背离判断
        funds_data["main_trend"] = funds_data["main_net_pct"].rolling(window).mean()
        funds_data["retail_trend"] = funds_data["retail_net_pct"].rolling(window).mean()
        funds_data["heat_trend"] = funds_data["heat_net_pct"].rolling(window).mean()

        # 找出各种背离日期
        mask_main = (funds_data["main_trend"] > 0) & (funds_data["retail_trend"] < 0)
        mask_heat = (funds_data["heat_trend"] > 0) & (funds_data["retail_trend"] < 0)
        mask_retail = (funds_data["retail_trend"] > 0) & (funds_data["main_trend"] < 0)

        div_main_dates = funds_data[mask_main].index.tolist()
        div_heat_dates = funds_data[mask_heat].index.tolist()
        div_retail_dates = funds_data[mask_retail].index.tolist()

        # 转成字符串
        self.all_flow_dates = [d.strftime("%Y-%m-%d") for d in funds_data.index]
        self.div_main_str = [d.strftime("%Y-%m-%d") for d in div_main_dates]
        self.div_heat_str = [d.strftime("%Y-%m-%d") for d in div_heat_dates]
        self.div_retail_str = [d.strftime("%Y-%m-%d") for d in div_retail_dates]

        # 对应背离点的净占比数值
        self.div_main_vals = (
            funds_data.loc[div_main_dates, "main_net_pct"].round(2).tolist()
        )
        self.div_heat_vals = (
            funds_data.loc[div_heat_dates, "heat_net_pct"].round(2).tolist()
        )
        self.div_retail_vals = (
            funds_data.loc[div_retail_dates, "retail_net_pct"].round(2).tolist()
        )

        # 三条折线的完整数据
        self.main_vals = funds_data["main_net_pct"].round(2).tolist()
        self.heat_vals = funds_data["heat_net_pct"].round(2).tolist()
        self.retail_vals = funds_data["retail_net_pct"].round(2).tolist()

        # 准备K线数据
        self.kline_dates = daily_data.index.strftime("%Y-%m-%d").tolist()
        self.kline_data = [
            [
                float(daily_data["open"].iloc[i]),
                float(daily_data["close"].iloc[i]),
                float(daily_data["low"].iloc[i]),
                float(daily_data["high"].iloc[i]),
            ]
            for i in range(len(daily_data))
        ]
        return self

    def make_graph(self):
        kline = (
            Kline(init_opts=opts.InitOpts(width="100%", height="400px"))
            .add_xaxis(self.kline_dates)
            .add_yaxis(
                series_name="K 线",
                y_axis=self.kline_data,
                itemstyle_opts=opts.ItemStyleOpts(color="#ec0000", color0="#00da3c"),
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(title=f"{self.code} 主力散户线"),
                tooltip_opts=opts.TooltipOpts(
                    trigger="axis",
                    axis_pointer_type="cross",
                    is_show=True,
                    trigger_on="mousemove",
                ),
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    is_scale=True,
                    boundary_gap=False,
                    axisline_opts=opts.AxisLineOpts(is_on_zero=False),
                    splitline_opts=opts.SplitLineOpts(is_show=False),
                    axislabel_opts=opts.LabelOpts(is_show=False),
                    # 关键：设置轴指针联动
                    axispointer_opts=opts.AxisPointerOpts(
                        is_show=True, link=[{"xAxisIndex": "all"}]
                    ),
                ),
                yaxis_opts=opts.AxisOpts(
                    is_scale=True, splitline_opts=opts.SplitLineOpts(is_show=True)
                ),
                datazoom_opts=[
                    opts.DataZoomOpts(
                        type_="inside", xaxis_index=[0, 1], range_start=0, range_end=100
                    ),
                    opts.DataZoomOpts(
                        is_show=True, xaxis_index=[0, 1], range_start=0, range_end=100
                    ),
                ],
                # 关键：设置画刷联动
                brush_opts=opts.BrushOpts(x_axis_index="all"),
            )
        )

        # === 4. 绘制资金流折线图，并叠加背离散点 ===
        flow_line = (
            Line(init_opts=opts.InitOpts(width="100%", height="300px"))
            .add_xaxis(self.all_flow_dates)
            .add_yaxis(
                series_name="主力净占比",
                y_axis=self.main_vals,
                is_smooth=True,
                linestyle_opts=opts.LineStyleOpts(color="#1f77b4", width=2),
                label_opts=opts.LabelOpts(is_show=False),
            )
            .add_yaxis(
                series_name="游资净占比",
                y_axis=self.heat_vals,
                is_smooth=True,
                linestyle_opts=opts.LineStyleOpts(color="#9467bd", width=2),
                label_opts=opts.LabelOpts(is_show=False),
            )
            .add_yaxis(
                series_name="散户净占比",
                y_axis=self.retail_vals,
                is_smooth=True,
                linestyle_opts=opts.LineStyleOpts(color="#ff7f0e", width=2),
                label_opts=opts.LabelOpts(is_show=False),
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(title=""),
                tooltip_opts=opts.TooltipOpts(
                    trigger="axis",
                    axis_pointer_type="cross",
                    is_show=True,
                    trigger_on="mousemove",
                ),
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    boundary_gap=False,
                    axislabel_opts=opts.LabelOpts(rotate=-45),
                    # 关键：设置轴指针联动
                    axispointer_opts=opts.AxisPointerOpts(
                        is_show=True, link=[{"xAxisIndex": "all"}]
                    ),
                ),
                yaxis_opts=opts.AxisOpts(name="净占比 (%)", is_scale=True),
                datazoom_opts=[
                    opts.DataZoomOpts(
                        type_="slider", xaxis_index=[0, 1], range_start=0, range_end=100
                    )
                ],
                legend_opts=opts.LegendOpts(pos_top="5%"),
                # 关键：设置画刷联动
                brush_opts=opts.BrushOpts(x_axis_index="all"),
            )
        )

        # 创建背离点散点图
        scatter_main = (
            Scatter()
            .add_xaxis(self.div_main_str)
            .add_yaxis(
                series_name="主力背离",
                y_axis=self.div_main_vals,
                symbol="triangle",
                symbol_size=15,
                itemstyle_opts=opts.ItemStyleOpts(color="red"),
            )
        )

        scatter_heat = (
            Scatter()
            .add_xaxis(self.div_heat_str)
            .add_yaxis(
                series_name="游资背离",
                y_axis=self.div_heat_vals,
                symbol="diamond",
                symbol_size=15,
                itemstyle_opts=opts.ItemStyleOpts(color="gold"),
            )
        )

        scatter_retail = (
            Scatter()
            .add_xaxis(self.div_retail_str)
            .add_yaxis(
                series_name="散户背离",
                y_axis=self.div_retail_vals,
                symbol="circle",
                symbol_size=15,
                itemstyle_opts=opts.ItemStyleOpts(color="black"),
            )
        )

        # 将散点图叠加到折线图上
        flow_line = (
            flow_line.overlap(scatter_main)
            .overlap(scatter_heat)
            .overlap(scatter_retail)
        )

        # === 5. 用 Grid 将两张图合并并实现联动 ===
        grid = (
            Grid(init_opts=opts.InitOpts(width="100%", height="750px"))
            .add(
                kline,
                grid_opts=opts.GridOpts(
                    pos_left="5%", pos_right="5%", pos_top="5%", height="45%"
                ),
            )
            .add(
                flow_line,
                grid_opts=opts.GridOpts(
                    pos_left="5%", pos_right="5%", pos_top="55%", height="40%"
                ),
            )
        )

        # 添加联动的JavaScript代码
        grid.add_js_funcs(
            """
            // 等待页面加载完成
            setTimeout(function() {
                // 获取所有图表实例
                var charts = [];
                var chartDoms = document.querySelectorAll('[_echarts_instance_]');
                chartDoms.forEach(function(dom) {
                    var chart = echarts.getInstanceByDom(dom);
                    if (chart) {
                        charts.push(chart);
                    }
                });

                console.log('找到图表数量:', charts.length);

                // 设置缩放联动
                if (charts.length >= 2) {
                    echarts.connect(charts);
                    console.log('图表缩放联动已设置');
                }
            }, 100);
        """
        )

        # create dir
        dir_path = create_analysis_graph_dir(self.code)

        # 导出HTML文件
        html_path = os.path.join(dir_path, f"{self.code}_主力散户线.html")
        grid.render(html_path)


# 主力入场信号
class _ZhMajorPlayerEntrySignalAggregate(AnalyzeDataSpec):

    def __init__(self, code: str):
        self.code = code
        self.df = None

    def analyze(self) -> "_ZhMajorPlayerEntrySignalAggregate":
        """
        纯 K 线 + 主力信号
        新增 **洗盘** 识别，现含：吸筹 / 加仓 / 洗盘 / 出货 / 隐形出货
        """
        # ------------------------------------------------------------------
        # 0. 可调参数
        # ------------------------------------------------------------------
        win_high = 60    # 高位滚动窗口 (日)
        high_pct = 0.9   # 距高点 X 倍判高位
        runup_win = 10   # 拉升窗口
        runup_pct = 15   # 拉升阈值 (%)

        # ------------------------------------------------------------------
        # 1. 读取数据
        # ------------------------------------------------------------------
        daily = list_zh_stock_daily_data(self.code)
        fund = list_zh_stock_fund_flow(self.code)
        anom = list_zh_stock_uma(self.code)

        # 确保date列是datetime类型
        daily["date"] = pd.to_datetime(daily["date"])
        fund["date"] = pd.to_datetime(fund["date"])
        anom["date"] = pd.to_datetime(anom["date"])

        # ------------------------------------------------------------------
        # 2. 预处理
        # ------------------------------------------------------------------
        daily = daily.sort_values("date").reset_index(drop=True)

        # 确保数值列是float类型，避免Decimal类型导致的运算错误
        numeric_columns = ["open", "close", "high", "low", "volume", "amount"]
        for col in numeric_columns:
            if col in daily.columns:
                daily[col] = pd.to_numeric(daily[col], errors="coerce")

        # —— 基本派生 ——
        daily["pct_chg"] = daily["close"].pct_change().mul(100)
        daily["vol_ma5"] = daily["volume"].rolling(5).mean()
        daily["range_pct"] = (daily["high"] - daily["low"]) / daily["close"] * 100
        daily["body_pct"] = (daily["close"] - daily["open"]).abs() / (
            daily["high"] - daily["low"] + 1e-6
        ) * 100

        # —— 合并 ——
        cols_fund = ["code", "date", "major_net_inflow", "major_rate"]
        cols_anom = ["code", "date", "up_vol", "down_vol"]

        df = (
            daily.merge(fund[cols_fund], on=["code", "date"], how="left")
                .merge(anom[cols_anom], on=["code", "date"], how="left")
        )

        # 确保合并后的数值列也是float类型
        numeric_cols_after_merge = ["major_net_inflow", "major_rate", "up_vol", "down_vol"]
        for col in numeric_cols_after_merge:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors="coerce")

        # 填充缺失值
        df = df.fillna({"major_net_inflow": 0, "major_rate": 0, "up_vol": 0, "down_vol": 0})

        # ------------------------------------------------------------------
        # 3. 高位 & 拉升过滤因子
        # ------------------------------------------------------------------
        rolling_high = df.high.rolling(win_high).max()
        df["near_high"] = df.close >= rolling_high * high_pct

        runup = df.pct_chg.rolling(runup_win).sum()
        df["run_up"] = runup > runup_pct

        # 连续主力流出天数 (隐形出货因子)
        flow_grp = (df.major_net_inflow >= 0).cumsum()
        df["连续流出天数"] = (df.major_net_inflow < 0).groupby(flow_grp).cumcount() + 1

        # ------------------------------------------------------------------
        # 4. 主力信号规则
        # ------------------------------------------------------------------
        ## 4.1 吸筹
        df["吸筹"] = (
            (df.major_net_inflow > 0)
            & (df.major_rate > 3)
            & (
                (df.pct_chg.abs() <= 1.5)
                | ((df.low < df.close) & (df.range_pct > 3) & (df.body_pct < 40))
            )
        )

        ## 4.2 加仓
        df["加仓"] = (
            (df.major_net_inflow > 0)
            & (df.major_rate > 3)
            & (df.pct_chg >= 3)
            & (df.volume > df.vol_ma5 * 1.3)
        )

        ## 4.3 洗盘（NEW）
        # 价跌 (≤ -2%) + 放量 ≥1.2× + 主力净流入或小幅流出 (& 占比>-1)
        # 同时出现长下影或实体位于当日振幅下部 (<40%)
        long_lower_shadow = (
            ((df.close < df.open) | (df.close == df.open))
            & (df.range_pct > 2)
            & ((df.close - df.low) / (df.high - df.low + 1e-6) < 0.4)
        )

        df["洗盘"] = (
            (df.pct_chg <= -2)
            & (df.volume > df.vol_ma5 * 1.2)
            & (df.major_rate > -1)   # 主力未显著流出
            & (df.major_net_inflow > df.volume * -0.02)  # 允许微流出
            & long_lower_shadow
            & df["run_up"]           # 需先有一段拉升
            & ~df["near_high"]       # 避免与高位派发混淆
        )

        ## 4.4 出货（显性派发，含高位过滤）
        cond_fund_out = (df.major_net_inflow < 0) & (df.major_rate < -3)

        long_upper = (
            (df.close > df.open)
            & (df.body_pct < 50)
            & (df.range_pct > 3)
            & ((df.high - df.close) / df.close > 0.03)
        )

        price_vol_div = (
            (df.pct_chg > 2)
            & (df.volume < df.vol_ma5 * 0.8)
        )

        df["出货"] = (
            cond_fund_out
            & (long_upper | price_vol_div)
            & (df.down_vol > df.up_vol)
            & df["near_high"]
            & df["run_up"]
        )

        ## 4.5 隐形出货
        cond1 = (df["连续流出天数"] >= 3) & (df.pct_chg.rolling(3).sum() < 1)
        cond2 = (df.pct_chg <= -3) & (df.volume > df.vol_ma5 * 1.5) & (df.major_net_inflow < 0)
        cond3 = (
            (df.range_pct < 2)
            & (df.volume > df.vol_ma5 * 2)
            & (df.major_net_inflow < 0)
        )

        df["隐形出货"] = (cond1 | cond2 | cond3) & df["near_high"]

        # 存储处理后的数据
        self.df = df

        return self

    def make_graph(self):
        # K线图
        def make_scatter_series(flag, name, symbol, color):
            """仅返回满足 flag 为 True 的 (x, y) 并指定颜色"""
            sel = self.df.loc[flag]
            return (
                Scatter()
                .add_xaxis(sel.date.dt.strftime("%Y-%m-%d").tolist())
                .add_yaxis(
                    name,
                    sel.low.tolist(),
                    symbol=symbol,
                    symbol_size=18,
                    itemstyle_opts=opts.ItemStyleOpts(color=color),
                    label_opts=opts.LabelOpts(is_show=False),
                )
            )

        # ------------------------------------------------------------------
        # 4. 构造 K 线主图 & 散点叠加
        # ------------------------------------------------------------------
        x_axis = self.df.date.dt.strftime("%Y-%m-%d").tolist()
        k_data = self.df[["open", "close", "low", "high"]].round(2).values.tolist()

        kline = (
            Kline()
            .add_xaxis(x_axis)
            .add_yaxis(
                "K线",
                k_data,
                itemstyle_opts=opts.ItemStyleOpts(
                    color="#ec0000",  # 阳线
                    color0="#00da3c",  # 阴线
                    border_color="#880000",
                    border_color0="#008F28",
                ),
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(title=f"{self.code} 主力信号 (含洗盘)"),
                tooltip_opts=opts.TooltipOpts(
                    trigger="axis", axis_pointer_type="cross"
                ),
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    is_scale=True,
                    axislabel_opts=opts.LabelOpts(rotate=45, interval=10),
                ),
                yaxis_opts=opts.AxisOpts(
                    is_scale=True, splitarea_opts=opts.SplitAreaOpts(is_show=True)
                ),
                datazoom_opts=[
                    opts.DataZoomOpts(type_="inside"),
                    opts.DataZoomOpts(type_="slider"),
                ],
                legend_opts=opts.LegendOpts(pos_top="2%"),
            )
        )

        panel = (
            kline.overlap(
                make_scatter_series(self.df["吸筹"], "吸筹", "diamond", "#F4B400")
            )  # 金黄
            .overlap(
                make_scatter_series(self.df["加仓"], "加仓", "triangle", "#4285F4")
            )  # 亮蓝
            .overlap(
                make_scatter_series(self.df["洗盘"], "洗盘", "rect", "#16A085")
            )  # 青色方块
            .overlap(
                make_scatter_series(self.df["出货"], "出货", "circle", "#DB4437")
            )  # 鲜红
            .overlap(
                make_scatter_series(self.df["隐形出货"], "隐形出货", "pin", "#8E44AD")
            )  # 紫色图钉
        )

        grid = Grid(init_opts=opts.InitOpts(width="100%", height="700px"))
        grid.add(
            panel,
            grid_opts=opts.GridOpts(
                pos_left="5%", pos_right="5%", pos_top="5%", height="90%"
            ),
        )
        # create dir
        dir_path = create_analysis_graph_dir(self.code)

        # 导出HTML文件
        html_path = os.path.join(dir_path, f"{self.code}_主力入场信号.html")
        grid.render(html_path)


# 量能堆积
class _ZhVolumeInertiaClusterAggregate(AnalyzeDataSpec):

    def __init__(self, code: str):
        self.code = code
        self.kline_data = None
        self.small_body = None
        self.long_green = None
        self.long_upper = None
        self.dates = None
        self.df = None

    def _get_float_shares(self) -> float:
        float_shares = ak_cli.get_zh_stock_info(self.code)["流通股"]
        logger.info(f"{self.code} float_shares: {float_shares}")
        return float(float_shares)

    def analyze(self) -> "_ZhVolumeInertiaClusterAggregate":
        turn_z = 1.0  # 换手放大： > MA20 + 1σ
        pct_break = 3.0  # 长阳定义（%）
        long_body = 0.0

        df_kline = list_zh_stock_daily_data(self.code)
        df_funds = list_zh_stock_fund_flow(self.code)

        df_kline["date"] = pd.to_datetime(df_kline["date"], format="%Y%m%d")
        df_funds["date"] = pd.to_datetime(df_funds["date"], format="%Y%m%d")
        daily_data = df_kline.set_index("date").sort_index()
        funds_data = df_funds.set_index("date").sort_index()

        self.df = daily_data.join(funds_data[["major_net_inflow"]], how="left").fillna(
            {"major_net_inflow": 0}
        )
        # 确保volume是float类型
        self.df["volume"] = self.df["volume"].astype(float)

        self.df["turn"] = round(self.df["volume"] / self._get_float_shares(), 4)
        self.df["turn_ma20"] = self.df["turn"].rolling(20).mean()
        self.df["turn_std20"] = self.df["turn"].rolling(20).std()

        cond_big = self.df["turn"] > (
            self.df["turn_ma20"] + turn_z * self.df["turn_std20"]
        )
        cond_up = (self.df["turn"] > self.df["turn"].shift(1)) & (
            self.df["turn"].shift(1) > self.df["turn"].shift(2)
        )
        self.df["TurnSpike"] = cond_big & cond_up

        # 3. 价格辅助形态
        self.df["body"] = (self.df["close"] - self.df["open"]).abs()
        self.df["range"] = self.df["high"] - self.df["low"]
        self.df["upper_wick"] = self.df["high"] - self.df[["close", "open"]].max(axis=1)

        # 小实体
        ratio = self.df["body"] / self.df["range"].replace(0, np.nan)
        self.small_body = ratio < 0.3

        # 4. 三类信号
        ## 4.1 堆量吸筹
        self.df["吸筹"] = (
            self.df["TurnSpike"]
            & (self.df["pct_chg"].abs() <= 1.2)
            & (self.df["major_net_inflow"] > 0)
            & self.small_body
        )

        ## 4.2 堆量突破
        # 避免除零错误，使用replace方法将0替换为NaN
        body_ratio = self.df["body"] / self.df["range"].replace(0, np.nan)
        body_ratio_condition = body_ratio > long_body
        self.long_green = (
            (self.df["pct_chg"] > pct_break)
            & body_ratio_condition.fillna(False)
            & (self.df["close"] > self.df["open"])
        )  # 长阳实体
        self.df["突破"] = self.df["TurnSpike"] & self.long_green

        ## 4.3 堆量派发
        # 避免除零错误，使用replace方法将0替换为NaN
        upper_wick_ratio = self.df["upper_wick"] / self.df["range"].replace(0, np.nan)
        self.long_upper = (upper_wick_ratio > 0.4).fillna(False)
        self.df["派发"] = self.df["TurnSpike"] & (
            (self.df["major_net_inflow"] < 0) | self.long_upper
        )

        # 5. 可视化标记
        ## 5.1 K 线
        self.dates = self.df.index.strftime("%Y-%m-%d").tolist()
        self.kline_data = (
            self.df[["open", "close", "low", "high"]].round(2).values.tolist()
        )
        return self

    def make_graph(self):
        kline = (
            Kline(init_opts=opts.InitOpts(width="100%", height="480px"))
            .add_xaxis(self.dates)
            .add_yaxis(
                "K线",
                self.kline_data,
                itemstyle_opts=opts.ItemStyleOpts(color="#ec0000", color0="#00da3c"),
            )
            .set_global_opts(
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    boundary_gap=False,
                    axislabel_opts=opts.LabelOpts(rotate=-45),
                ),
                datazoom_opts=[opts.DataZoomOpts(type_="inside"), opts.DataZoomOpts()],
                tooltip_opts=opts.TooltipOpts(
                    trigger="axis", axis_pointer_type="cross"
                ),
                title_opts=opts.TitleOpts(title=f"{self.code} 量能-换手堆积三信号"),
            )
        )

        ## 5.2 信号散点
        def scatter_series(mask, symbol, color, name):
            idx = self.df.index[mask]
            if idx.empty:
                return None
            return (
                Scatter()
                .add_xaxis(idx.strftime("%Y-%m-%d").tolist())
                .add_yaxis(
                    name,
                    y_axis=self.df.loc[idx, "high"].tolist(),  # 把点放 K 线顶端
                    symbol=symbol,
                    symbol_size=16,
                    itemstyle_opts=opts.ItemStyleOpts(color=color),
                    label_opts=opts.LabelOpts(is_show=False),
                )
            )

        for m, sym, col, n in [
            (self.df["吸筹"], "triangle", "#2ca02c", "堆量吸筹"),
            (self.df["突破"], "pin", "#1f77b4", "堆量突破"),
            (self.df["派发"], "diamond", "#d62728", "堆量派发"),
        ]:
            sc = scatter_series(m, sym, col, n)
            if sc:
                kline.overlap(sc)

        # create dir
        dir_path = create_analysis_graph_dir(self.code)

        # 导出HTML文件
        html_path = os.path.join(dir_path, f"{self.code}_量能堆积.html")
        logger.info(f"rendering {html_path}")
        kline.render(html_path)


# 阶段性底部
class _ZhPhaseBottomAggregate(AnalyzeDataSpec):

    def __init__(self, code: str):
        self.code = code
        self.kline_dates = None
        self.kline_data = None
        self.bottom_dates = None
        self.bottom_prices = None
        self.df = None

    def analyze(self) -> "_ZhPhaseBottomAggregate":
        # === CONFIG ===
        l = 10  # 价格 & 资金滚动窗口
        bot_q = 0.2  # 底部价格区位阈值
        flow_z_th = 0.3  # 资金偏离阈值 (σ)
        eps = 1e-6

        df_kline = list_zh_stock_daily_data(self.code)
        df_funds = list_zh_stock_fund_flow(self.code)

        # 确保date列是datetime类型
        df_kline["date"] = pd.to_datetime(df_kline["date"])
        df_funds["date"] = pd.to_datetime(df_funds["date"])

        daily_data = df_kline.set_index("date").sort_index()
        funds_data = df_funds.set_index("date").sort_index()

        # 合并数据
        self.df = daily_data.join(
            funds_data[["small_net_inflow", "big_net_inflow", "super_net_inflow"]],
            how="inner",
        )

        # 确保数值列是float类型，避免Decimal类型导致的运算错误
        numeric_columns = [
            "open",
            "close",
            "high",
            "low",
            "volume",
            "amount",
            "pct_chg",
        ]
        for col in numeric_columns:
            if col in self.df.columns:
                self.df[col] = pd.to_numeric(self.df[col], errors="coerce")

        # === 2. 指标计算 ===
        # 2.1 价格区位 price_rank，带振幅保护
        self.df["roll_max"] = self.df["close"].rolling(l).max()
        self.df["roll_min"] = self.df["close"].rolling(l).min()
        rolling_mean_close = self.df["close"].rolling(l).mean()
        rng = self.df["roll_max"] - self.df["roll_min"]
        valid_range = rng > rolling_mean_close * 0.02  # 过去 20 日振幅至少占均价 2%
        self.df["price_rank"] = np.where(
            valid_range, (self.df["close"] - self.df["roll_min"]) / (rng + eps), np.nan
        )

        # 2.2 资金倾向 flow_balance → flow_z
        self.df["large_net_amt_comb"] = (
            self.df["big_net_inflow"] + self.df["super_net_inflow"]
        )
        self.df["flow_balance"] = (
            (self.df["large_net_amt_comb"] - self.df["small_net_inflow"])
            .rolling(l)
            .sum()
        )

        # 估算成交活跃度：使用绝对净额之和做归一化
        self.df["gross_flow"] = (
            (self.df["large_net_amt_comb"].abs() + self.df["small_net_inflow"].abs())
            .rolling(l)
            .sum()
        )
        self.df["flow_ratio"] = self.df["flow_balance"] / (self.df["gross_flow"] + eps)

        # 资金 Z-score
        mu = self.df["flow_ratio"].rolling(l).mean()
        std = self.df["flow_ratio"].rolling(l).std() + eps
        self.df["flow_z"] = (self.df["flow_ratio"] - mu) / std

        # === 3. 生成 K 线数据和底部信号 ===
        # K 线日期列表
        self.kline_dates = self.df.index.strftime("%Y-%m-%d").tolist()
        # K 线 OHLC 数值列表 [open, close, low, high]
        self.kline_data = (
            self.df[["open", "close", "low", "high"]].round(2).values.tolist()
        )

        # 底部信号标记
        self.df["bottom_signal"] = 0
        self.df.loc[
            (self.df["price_rank"] < bot_q) & (self.df["flow_z"] > flow_z_th),
            "bottom_signal",
        ] = 1
        bottom_df = self.df[self.df["bottom_signal"] == 1]

        # 底部信号的日期和价格位置（用 low*0.98 放在蜡烛下方）
        self.bottom_dates = bottom_df.index.strftime("%Y-%m-%d").tolist()
        self.bottom_prices = (bottom_df["low"] * 0.98).round(2).tolist()

        return self

    def make_graph(self):
        # K线图
        kline = (
            Kline(init_opts=opts.InitOpts(width="100%", height="500px"))
            .add_xaxis(self.kline_dates)
            .add_yaxis(
                "K 线",
                self.kline_data,
                itemstyle_opts=opts.ItemStyleOpts(
                    color="#ef232a",  # 阳线颜色
                    color0="#14b143",  # 阴线颜色
                    border_color="#ef232a",
                    border_color0="#14b143",
                ),
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(title=f"{self.code} 阶段性底部信号"),
                tooltip_opts=opts.TooltipOpts(
                    trigger="axis", axis_pointer_type="cross"
                ),
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    boundary_gap=False,
                    axislabel_opts=opts.LabelOpts(rotate=-45),
                ),
                yaxis_opts=opts.AxisOpts(
                    is_scale=True,
                    splitarea_opts=opts.SplitAreaOpts(
                        is_show=True, areastyle_opts=opts.AreaStyleOpts(opacity=1)
                    ),
                ),
                datazoom_opts=[
                    opts.DataZoomOpts(type_="inside", range_start=0, range_end=100),
                    opts.DataZoomOpts(is_show=True, range_start=0, range_end=100),
                ],
            )
        )

        # 底部信号散点图
        if self.bottom_dates and self.bottom_prices:
            scatter_bottom = (
                Scatter()
                .add_xaxis(self.bottom_dates)
                .add_yaxis(
                    "阶段性底部",
                    self.bottom_prices,
                    symbol="triangle",
                    symbol_size=20,
                    itemstyle_opts=opts.ItemStyleOpts(
                        color="#FF6B35",
                        border_color="#CC5529",
                        border_width=2,
                        opacity=0.9,
                    ),
                )
            )
            kline = kline.overlap(scatter_bottom)

        # create dir
        dir_path = create_analysis_graph_dir(self.code)

        # 导出HTML文件
        html_path = os.path.join(dir_path, f"{self.code}_阶段性底部.html")
        kline.render(html_path)


# 板块资金流
class _ZhSectorFundFlowAggregate:
    pass
