import os
from abc import ABC, abstractmethod
from typing import List

import pandas as pd

from src.domain.schema import MarketTag
from src.domain.store.repo.dao import (
    KLineDaily,
    ZhStockFund,
    ZhStockBlankFund,
    ZhStockUMA,
)
from src.domain.store.service.zh import zh_stock_service, zh_blank_service
from src.infra.clients.trading.utils import get_datetime_range
from src.infra.utils import get_work_dir


class AnalyzeDataSpec(ABC):

    @abstractmethod
    def analyze(self):
        raise NotImplementedError

    @abstractmethod
    def make_graph(self):
        raise NotImplementedError


def list_zh_stock_daily_data(code: str, before_days: int = 185) -> pd.DataFrame:
    start_date, end_date = get_datetime_range(before_days)

    data_object_list: List[KLineDaily] = zh_stock_service.get_stock_daily_kline(
        code, start_date, end_date
    )
    # 转成 dataframe
    data_dict_list = []
    pre_close = 0
    for data_object in data_object_list:
        if pre_close == 0:
            pre_close = data_object.open
        data_dict_list.append(
            {
                "code": data_object.code,
                "date": data_object.date,
                "open": data_object.open,
                "close": data_object.close,
                "high": data_object.high,
                "low": data_object.low,
                "volume": data_object.volume,
                "amount": data_object.amount,
                "pre_close": pre_close,
            }
        )
        pre_close = data_object.close

    df = pd.DataFrame(data_dict_list)

    # 增加pct_chg字段
    df["pct_chg"] = round((df["close"] - df["pre_close"]) / df["pre_close"], 2) * 100
    return df


def list_zh_stock_fund_flow(code: str, before_days: int = 185) -> pd.DataFrame:
    start_date, end_date = get_datetime_range(before_days)

    data_object_list: List[ZhStockFund] = zh_stock_service.list_stock_fund_flow(
        code, start_date, end_date
    )
    data_dict_list = []
    for data_object in data_object_list:
        data_dict_list.append(
            {
                "date": data_object.date,
                "code": data_object.code,
                "major_net_inflow": data_object.major_net_inflow,
                "super_net_inflow": data_object.super_net_inflow,
                "big_net_inflow": data_object.big_net_inflow,
                "medium_net_inflow": data_object.medium_net_inflow,
                "small_net_inflow": data_object.small_net_inflow,
                "major_rate": data_object.major_rate,
                "super_rate": data_object.super_rate,
                "big_rate": data_object.big_rate,
                "medium_rate": data_object.medium_rate,
                "small_rate": data_object.small_rate,
            }
        )
    df = pd.DataFrame(data_dict_list)
    return df


def list_zh_stock_blank_fund(
    before_days: int = 185, is_concept: bool = False, end_date: str = None
) -> pd.DataFrame:
    if end_date:
        from datetime import datetime, timedelta
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        start_dt = end_dt - timedelta(days=before_days)
        start_date = start_dt.strftime("%Y-%m-%d")
        end_date = end_date
    else:
        start_date, end_date = get_datetime_range(before_days)

    data_object_list: List[ZhStockBlankFund] = zh_blank_service.list_stock_blank_fund(
        is_concept, start_date, end_date
    )
    data_dict_list = []
    for data_object in data_object_list:
        data_dict_list.append(
            {
                "name": data_object.name,
                "date": data_object.date,
                "major_net_inflow": data_object.major_net_inflow,
                "super_net_inflow": data_object.super_net_inflow,
                "big_net_inflow": data_object.big_net_inflow,
                "medium_net_inflow": data_object.medium_net_inflow,
                "small_net_inflow": data_object.small_net_inflow,
                "major_rate": data_object.major_rate,
                "super_rate": data_object.super_rate,
                "big_rate": data_object.big_rate,
                "medium_rate": data_object.medium_rate,
                "small_rate": data_object.small_rate,
            }
        )
    df = pd.DataFrame(data_dict_list)
    return df


def list_zh_stock_uma(code: str, before_days: int = 185) -> pd.DataFrame:
    start_date, end_date = get_datetime_range(before_days)
    data_object_list: List[ZhStockUMA] = zh_stock_service.list_stock_uma(
        code, start_date, end_date
    )

    if not data_object_list:
        zh_stock_service.sync_stock_uma(code, before_days)
        data_object_list = zh_stock_service.list_stock_uma(code, start_date, end_date)

    data_dict_list = []
    for data_object in data_object_list:
        data_dict_list.append(
            {
                "code": data_object.code,
                "date": data_object.date,
                "up_type_code": data_object.up_type_code,
                "down_type_code": data_object.down_type_code,
                "up_vol": data_object.up_vol,
                "down_vol": data_object.down_vol,
            }
        )
    df = pd.DataFrame(data_dict_list)
    return df


def create_analysis_graph_dir(
    code: str, market_tag: str = MarketTag.zh_stock.value
) -> str:
    dir_path = f"{get_work_dir()}/assets/analysis/{market_tag}/{code}"
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)
    return dir_path
