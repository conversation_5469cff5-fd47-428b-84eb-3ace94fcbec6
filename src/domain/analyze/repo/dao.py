import enum

from sqlalchemy import Column, VARCHAR, Enum, Integer, Float, Date, CheckConstraint, Boolean

from src.infra.clients.mysql.orm import IdModelMixin, DateTimeModelMixin, TablePrefix


class PhaseEnum(str, enum.Enum):
    strong_trend = "strong_trend"  # 强趋势阶段
    pre_burst = "pre_burst"  # 爆发前阶段
    early_accum = "early_accum"  # 早期积累阶段
    consolidation = "consolidation"  # 整固阶段（新增）
    cooldown = "cooldown"  # 冷却阶段
    none = "none"  # 无阶段


class ZoneEnum(str, enum.Enum):
    A = "A"  # 撤离区 - 资金持续流出
    B = "B"  # 吸筹区 - 流出收敛，回流试探
    C = "C"  # 临界区 - 零轴临界，爆发前夕
    D = "D"  # 上拐区 - 资金全面转正


class ZhBlankAnalyzeFundFlowState(IdModelMixin, DateTimeModelMixin):
    __tablename__ = f"{TablePrefix}_zh_blank_analyze_fund_flow_state"

    name = Column(VARCHAR(64), nullable=False, comment="blank name")
    is_concept = Column(Boolean, nullable=False, default=False, comment="是否概念板块")
    # ---- 阶段 & 评分 ----
    phase = Column(
        Enum(PhaseEnum), default=PhaseEnum.none, nullable=False, comment="当前阶段"
    )
    score = Column(Integer, default=0, comment="5日累计得分")
    days_cnt = Column(Integer, default=0, comment="评分周期天数(0-5)")
    cooldown_days = Column(Integer, default=0, comment="冷却倒计时")

    # ---- 强势期追踪 ----
    high_since_strong = Column(Float, default=0.0, comment="强势期以来最高点")
    drawdown_pct = Column(Float, default=0.0, comment="回撤百分比(-0.08表示-8%)")
    strong_days = Column(Integer, default=0, comment="强势天数")
    outflow_streak = Column(Integer, default=0, comment="资金流出连续天数")

    # ---- 整固期追踪（新增）----
    consolidation_days = Column(Integer, default=0, comment="整固观察天数")
    consolidation_high = Column(Float, default=0.0, comment="整固期间最高点")
    consolidation_low = Column(Float, default=0.0, comment="整固期间最低点")

    # ---- 日期字段 ----
    last_date = Column(Date, nullable=True, comment="最近交易日")

    # ---- 约束 ----
    __table_args__ = (
        CheckConstraint("drawdown_pct <= 0", name="ck_drawdown_le_zero"),
        # 复合唯一约束：name + is_concept
        # UniqueConstraint("name", "is_concept", name="uq_name_is_concept"),
    )

    # ---- 字符串显示 ----
    def __repr__(self):
        return (
            f"<ZhBlankAnalyzeFundFlowState {self.name} {self.phase} "
            f"score={self.score} DD={self.drawdown_pct:0.2%}>"
        )
