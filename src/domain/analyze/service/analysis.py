from src.domain.analyze.aggregate.analysis.zh_stock import AnalyzeZhStockFundFlowAggregate


class ZhAnalysisService:

    def analyze_zh_stock_fund_flow(self, code: str):
        aggregate = AnalyzeZhStockFundFlowAggregate(code)
        aggregate.start_zh_volume_inertia_cluster()
        aggregate.start_zh_major_player_entry_signal()
        aggregate.start_zh_smart_money_vs_noise_trader_flow()
        aggregate.start_zh_phase_bottom()


zh_analysis_svc = ZhAnalysisService()
