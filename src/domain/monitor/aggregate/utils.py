import threading
from datetime import datetime, date
from typing import Dict, Optional
from loguru import logger


class DailyRateLimiter:
    """
    每日发送次数限制器

    用于记录和限制每天的告警发送次数，第二天自动重置。
    线程安全，支持多个发送装置同时查询和更新。
    """

    def __init__(self):
        # 存储每个告警类型的发送记录 {alert_type: {"date": date, "count": int, "limit": int}}
        self._records: Dict[str, Dict] = {}
        # 线程锁，确保线程安全
        self._lock = threading.Lock()
        # 默认每日发送限制
        self._default_daily_limit = 100

    def set_daily_limit(self, alert_type: str, limit: int = 1) -> None:
        """
        设置指定告警类型的每日发送限制

        Args:
            alert_type: 告警类型标识
            limit: 每日发送限制次数
        """
        with self._lock:
            if alert_type not in self._records:
                self._records[alert_type] = {
                    "date": date.today(),
                    "count": 0,
                    "limit": limit
                }
            else:
                self._records[alert_type]["limit"] = limit

    def can_send(self, alert_key: str) -> bool:
        """
        查询指定告警类型是否可以发送

        Args:
            alert_key: 告警类型标识

        Returns:
            bool: True表示可以发送，False表示已达到限制
        """
        with self._lock:
            self._check_and_reset_if_new_day(alert_key)

            if alert_key not in self._records:
                # 如果没有记录，使用默认限制并允许发送
                self._records[alert_key] = {
                    "date": date.today(),
                    "count": 0,
                    "limit": self._default_daily_limit
                }
                return True

            record = self._records[alert_key]
            return record["count"] < record["limit"]

    def record_send(self, alert_type: str) -> bool:
        """
        记录一次发送，并返回是否成功记录

        Args:
            alert_type: 告警类型标识

        Returns:
            bool: True表示成功记录，False表示已达到限制无法发送
        """
        with self._lock:
            # 检查并重置新的一天
            self._check_and_reset_if_new_day(alert_type)

            if alert_type not in self._records:
                # 如果没有记录，使用默认限制
                self._records[alert_type] = {
                    "date": date.today(),
                    "count": 0,
                    "limit": self._default_daily_limit
                }

            record = self._records[alert_type]

            # 检查是否可以发送
            if record["count"] >= record["limit"]:
                logger.warning(f"告警类型 {alert_type} 已达到每日发送限制")
                return False

            # 增加发送计数
            record["count"] += 1

            logger.debug(f"记录告警发送: {alert_type}, "
                        f"今日已发送: {record['count']}, "
                        f"限制: {record['limit']}")
            return True

    def get_send_status(self, alert_type: str) -> Dict[str, any]:
        """
        获取指定告警类型的发送状态

        Args:
            alert_type: 告警类型标识

        Returns:
            dict: 包含发送状态信息的字典
        """
        with self._lock:
            self._check_and_reset_if_new_day(alert_type)

            if alert_type not in self._records:
                return {
                    "alert_type": alert_type,
                    "date": date.today().isoformat(),
                    "sent_count": 0,
                    "daily_limit": self._default_daily_limit,
                    "can_send": True,
                    "remaining": self._default_daily_limit
                }

            record = self._records[alert_type]
            remaining = max(0, record["limit"] - record["count"])

            return {
                "alert_type": alert_type,
                "date": record["date"].isoformat(),
                "sent_count": record["count"],
                "daily_limit": record["limit"],
                "can_send": record["count"] < record["limit"],
                "remaining": remaining
            }

    def get_all_status(self) -> Dict[str, Dict]:
        """
        获取所有告警类型的发送状态

        Returns:
            dict: 所有告警类型的状态信息
        """
        with self._lock:
            result = {}
            for alert_type in list(self._records.keys()):
                # 检查并重置新的一天
                self._check_and_reset_if_new_day(alert_type)

                record = self._records[alert_type]
                remaining = max(0, record["limit"] - record["count"])

                result[alert_type] = {
                    "alert_type": alert_type,
                    "date": record["date"].isoformat(),
                    "sent_count": record["count"],
                    "daily_limit": record["limit"],
                    "can_send": record["count"] < record["limit"],
                    "remaining": remaining
                }
            return result

    def reset_daily_count(self, alert_type: Optional[str] = None) -> None:
        """
        重置每日计数

        Args:
            alert_type: 指定告警类型，如果为None则重置所有类型
        """
        with self._lock:
            if alert_type is None:
                # 重置所有类型
                for record in self._records.values():
                    record["date"] = date.today()
                    record["count"] = 0
                logger.info("已重置所有告警类型的每日发送计数")
            else:
                # 重置指定类型
                if alert_type in self._records:
                    self._records[alert_type]["date"] = date.today()
                    self._records[alert_type]["count"] = 0
                    logger.info(f"已重置告警类型 {alert_type} 的每日发送计数")

    def _check_and_reset_if_new_day(self, alert_type: str) -> None:
        """
        检查是否是新的一天，如果是则重置计数

        Args:
            alert_type: 告警类型标识
        """
        if alert_type in self._records:
            record = self._records[alert_type]
            today = date.today()
            if record["date"] != today:
                # 新的一天，重置计数
                record["date"] = today
                record["count"] = 0
                logger.info(f"新的一天，已重置告警类型 {alert_type} 的发送计数")


# 全局单例实例
daily_rate_limiter = DailyRateLimiter()