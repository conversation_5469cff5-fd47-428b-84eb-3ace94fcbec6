import json
from typing import List, Optional

from loguru import logger
from pydantic import BaseModel

from src.domain.monitor.repo import dao
from src.domain.monitor.schema import MonitorType
from src.domain.schema import MarketTag
from src.infra.app import app


class BaseMonitorAggregate:

    def create_monitor_object(
        self,
        code: str,
        name: str,
        market_tag: MarketTag,
        monitor_type: MonitorType,
        args: BaseModel = None,
    ) -> int:
        with app.orm_session() as session:
            monitor_arg = args.model_dump_json() if args else "{}"
            object = dao.MonitorObject(
                code=code,
                name=name,
                market_tag=market_tag.value,
                monitor_type=monitor_type.value,
                monitor_arg=monitor_arg,
            )
            session.add(object)
            session.commit()
            return object.id

    def update_monitor_object(self, id: int, args: BaseModel):
        with app.orm_session() as session:
            object = (
                session.query(dao.MonitorObject)
                .filter(dao.MonitorObject.id == id)
                .first()
            )
            if not object:
                logger.warning(f"监控对象 {id} 不存在")
                return
            object.monitor_arg = args.model_dump_json()
            session.commit()

    def list_monitor_objects(
        self, monitor_type: MonitorType, market_tag: MarketTag = None
    ) -> List[dao.MonitorObject]:

        with app.orm_session() as session:
            if market_tag:
                return (
                    session.query(dao.MonitorObject)
                    .filter(dao.MonitorObject.monitor_type == monitor_type.value)
                    .filter(dao.MonitorObject.market_tag == market_tag.value)
                    .all()
                )
            return (
                session.query(dao.MonitorObject)
                .filter(dao.MonitorObject.monitor_type == monitor_type.value)
                .all()
            )

    def get_monitor_object_by_id(self, id: int) -> Optional[dao.MonitorObject]:
        with app.orm_session() as session:
            return (
                session.query(dao.MonitorObject)
                .filter(dao.MonitorObject.id == id)
                .first()
            )

    def get_monitor_object_by_info(
        self,
        code: str,
        monitor_type: MonitorType,
        market_tag: MarketTag,
        monitor_arg: BaseModel = None,
    ) -> Optional[dao.MonitorObject]:
        with app.orm_session() as session:
            objects = (
                session.query(dao.MonitorObject)
                .filter(dao.MonitorObject.code == code)
                .filter(dao.MonitorObject.monitor_type == monitor_type.value)
                .filter(dao.MonitorObject.market_tag == market_tag.value)
                .all()
            )

        for obj in objects:
            if monitor_arg is None:
                return obj
            if json.loads(obj.monitor_arg) == monitor_arg.model_dump():
                return obj
        return None

    def delete_monitor_object(self, id: int):
        with app.orm_session() as session:
            session.query(dao.MonitorObject).filter(dao.MonitorObject.id == id).delete()
            session.commit()
