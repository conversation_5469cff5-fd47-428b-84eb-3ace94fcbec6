import threading
import time
from typing import Optional, List

import pandas as pd
from loguru import logger

from src.domain import communicate
from src.domain.monitor.aggregate.base import BaseMonitorAggregate
from src.domain.monitor.aggregate.message import message_sender_agg
from src.domain.monitor.aggregate.utils import daily_rate_limiter
from src.domain.monitor.schema import MonitorType
from src.domain.monitor.spec import MonitorObjectSpec
from src.domain.schema import MarketTag
from src.infra.app import app
from src.infra.clients.trading.ak import ak_cli
from src.infra.clients.trading.highlevel import high_level_trading_client
from src.infra.clients.trading.utils import (
    get_datetime_range,
    get_zh_today_trading_time_range,
)


class VolumeMonitorAggregate(BaseMonitorAggregate):

    def __init__(self):
        self.five_monitor_objects: List[ZhStockFiveMinVolumeMonitor] = []
        self.one_monitor_objects: List[ZhStockOneMinVolumeMonitor] = []
        self._lock = threading.Lock()

    def start_all_monitor(self):
        if self.five_monitor_objects:
            logger.warning("已经有监控在运行，跳过启动")
            return

        objects = self.list_monitor_objects(MonitorType.volume)
        for obj in objects:
            if obj.market_tag != MarketTag.zh_stock.value:
                logger.warning(f"成交量监控只支持A股股票，不支持 {obj.market_tag}")
                continue
            monitor = ZhStockFiveMinVolumeMonitor(obj.code, obj.name)
            app.submit_concurrent_task(monitor.start_monitor)
            self.five_monitor_objects.append(monitor)
            # 暂时暂停
            # one_min_monitor = ZhStockOneMinVolumeMonitor(obj.code, obj.name)
            # app.submit_concurrent_task(one_min_monitor.start_monitor)
            # self.one_monitor_objects.append(one_min_monitor)

    def stop_all_monitor(self):
        for monitor in self.five_monitor_objects:
            monitor.stop_monitor()
        self.five_monitor_objects = []

    def start_monitor(self, id: int):
        with self._lock:
            obj = self.get_monitor_object_by_id(id)
            if obj is None:
                raise ValueError(f"监控对象 {id} 不存在")

            if obj.monitor_type != MonitorType.volume.value:
                raise ValueError(f"监控对象 {id} 不是成交量监控")

            if obj.market_tag != MarketTag.zh_stock.value:
                raise ValueError(f"成交量监控只支持A股股票，不支持 {obj.market_tag}")

            monitor = ZhStockFiveMinVolumeMonitor(obj.code, obj.name)
            self.five_monitor_objects.append(monitor)
            app.submit_concurrent_task(monitor.start_monitor)
            #
            # one_min_monitor = ZhStockOneMinVolumeMonitor(obj.code, obj.name)
            # self.one_monitor_objects.append(one_min_monitor)
            # app.submit_concurrent_task(one_min_monitor.start_monitor)

    def stop_monitor(self, id: int):
        with self._lock:
            obj = self.get_monitor_object_by_id(id)
            if obj is None:
                raise ValueError(f"监控对象 {id} 不存在")

            if obj.monitor_type != MonitorType.volume.value:
                raise ValueError(f"监控对象 {id} 不是成交量监控")

            for monitor in self.five_monitor_objects:
                if monitor.code == obj.code:
                    monitor.stop_monitor()
                    self.five_monitor_objects.remove(monitor)

    def create_object(
        self, code: str, name: str, market_tag: MarketTag
    ) -> Optional[int]:
        if market_tag != MarketTag.zh_stock:
            raise ValueError(f"成交量监控只支持A股股票，不支持 {market_tag}")

        object = self.get_monitor_object_by_info(
            code,
            MonitorType.volume,
            market_tag,
        )
        if object:
            logger.warning(f"量能监控对象 {code},{name} 已经存在，跳过创建")
            return

        object_id = super().create_monitor_object(
            code, name, market_tag, MonitorType.volume
        )
        self.start_monitor(object_id)
        return object_id

    def delete_object(self, id: int):
        self.stop_monitor(id)
        super().delete_monitor_object(id)


class ZhStockFiveMinVolumeMonitor(MonitorObjectSpec):
    NORMAL_TAG = "正常波动"

    def __init__(self, code: str, name: str):
        self.code = code
        self.name = name
        self._stop_monitor = False
        self._alert_type = self.make_alert_type(code)
        self._daily_limit = 1
        self._monitor_interval = 300  # 5分钟 = 300秒
        self._history_5_min_data = self._list_history_5_min_data()
        self._ema_vol: Optional[float] = None
        self._std_vol: Optional[float] = None
        # 计算5分钟放量标准：取前15大成交量，去掉前2个极值，求平均
        self._five_min_heavy_std_vol: Optional[float] = self._compute_top10_volume_avg(
            self._history_5_min_data, vol_col="成交量", top_n=20, drop_top_k=3
        )
        logger.info(
            f"{self.name}({self.code}) 5分钟放量标准: {self._five_min_heavy_std_vol}"
        )
        self._init_ema_std()

    @property
    def alert_type(self) -> str:
        return self._alert_type

    @classmethod
    def make_alert_type(cls, code: str) -> str:
        return f"zh_volume_monitor_{code}"

    def _make_alert_key(self, row: pd.Series) -> str:
        return f"{self._alert_type}_{row['时间']}"

    def _compute_top10_volume_avg(
        self, df, vol_col="vol", top_n=10, drop_top_k=1
    ) -> float:
        df_sorted = df.sort_values(by=vol_col, ascending=False)
        top_df = df_sorted.head(top_n + drop_top_k)
        clean_df = top_df.iloc[drop_top_k:]  # 剔除前k个极端值
        avg_vol = clean_df[vol_col].mean()
        return round(avg_vol * 0.8, 2)

    def _get_current_stock_price(self) -> Optional[float]:
        data = high_level_trading_client.get_zh_stock_current_min_data(self.code)
        if data is None:
            return None
        return data.close

    def _list_history_5_min_data(
        self,
    ) -> pd.DataFrame:
        start_date, end_date = get_datetime_range(before_days=35)
        df = ak_cli.list_zh_stock_data_with_minute(
            self.code, start_date, end_date, period="5"
        )
        return df

    def _list_today_5_min_data(self) -> pd.DataFrame:
        start_date, end_date = get_zh_today_trading_time_range()
        df = ak_cli.list_zh_stock_data_with_minute(
            self.code, start_date, end_date, period="5"
        )
        return df

    def _init_ema_std(self):
        # 计算 EMA 和 STD（基于当前历史）
        # 用于平滑成交量曲线，反映成交量的“正常趋势水平”。
        self._ema_vol = round(
            self._history_5_min_data["成交量"].ewm(span=1000).mean().iloc[-1], 2
        )
        logger.info(f"{self.name}({self.code}) ema_vol: {self._ema_vol}")
        # 用于判断当前成交量是否显著偏离平均（放量 / 缩量），参考统计波动范围。
        self._std_vol = round(
            self._history_5_min_data["成交量"].rolling(window=1000).std().iloc[-1], 2
        )
        logger.info(f"{self.name}({self.code}) std_vol: {self._std_vol}")

    def _compute_volume_ratio(
        self,
        df_today: pd.DataFrame,
        history_df: pd.DataFrame,  # 改为单个DataFrame
        time_col="时间",
        vol_col="成交量",
    ):
        """
        计算今日每条5分钟K线的"量比"，即相对历史同一时间段的成交量放大倍数。

        参数说明：
        - df_today: 今天的5分钟数据，包含时间和成交量
        - history_df: 历史5分钟数据的DataFrame
        - time_col: 时间列
        - vol_col: 成交量列

        返回值：
        - 增加一列 'volume_ratio'，为量比
        """

        # 构造一个dict：key=时间点，value=过去N天这个时间点的平均成交量
        avg_volume_dict = {}
        # 直接处理单个DataFrame
        for idx, row in history_df.iterrows():
            time_str = row[time_col][-5:]  # 只取 hh:mm 部分
            avg_volume_dict.setdefault(time_str, []).append(row[vol_col])

        # 计算平均值
        for k in avg_volume_dict:
            avg_volume_dict[k] = sum(avg_volume_dict[k]) / len(avg_volume_dict[k])

        # 计算今天每条的量比
        def get_volume_ratio(row):
            time_str = row[time_col][-5:]
            avg = avg_volume_dict.get(time_str)
            if avg and avg > 0:
                return row[vol_col] / avg
            return None

        df_today = df_today.copy()
        df_today["volume_ratio"] = df_today.apply(get_volume_ratio, axis=1)
        return df_today

    def _classify_volume_price(self, row: pd.Series, ema: float, std: float, daily_change_rate: Optional[float] = None) -> str:
        """
        判断当前K线的量价形态类别

        参数：
        - row: 包含 open、close、vol、volume_ratio 等字段
        - ema: 当前成交量的EMA值
        - std: 当前成交量的STD值
        - daily_change_rate: 当天涨跌幅（百分比，如-2.16表示-2.16%）

        返回：
        - 类型标签字符串
        """
        open_ = row["开盘"]
        close = row["收盘"]
        vol = row["成交量"]
        vr = row.get("volume_ratio", None)

        # 优先使用当天涨跌幅，如果没有则使用5分钟K线内部涨跌幅
        if daily_change_rate is not None:
            change_rate = daily_change_rate / 100  # 转换为小数形式
        else:
            # 计算5分钟K线内部涨速（作为备用）
            if open_ > 0:
                change_rate = (close - open_) / open_
            else:
                return "<font color='purple'> 异常数据 </font>"

        label = self.NORMAL_TAG

        if vol > ema + 2 * std:
            # 设置每日发送限制
            if change_rate > 0.00618:
                label = "<font color='red'>**放量上涨 (趋势确认)**</font>"
            elif change_rate > 0:
                label = "<font color='orange'>**放量滞涨 (主力可能在出货，摸到了关键止盈点)**</font>"
            elif abs(change_rate) < 0.002:
                label = "<font color='blue'>**放量震荡 (可能在洗盘或换手，寻找支撑位)**</font>"
            else:
                label = "<font color='green'>**放量下跌 (关注是否跌破支撑位)**</font>"
        elif vol < ema - 2 * std:
            if change_rate > 0:
                label = "<font color='orange'>**缩量上涨 (可能高度控盘或锁仓，需要关注后续量能)**</font>"

        # 添加量比标签
        if vr is not None and vr > 2:
            label = f"<font color='red'>**[同时段量比倍数放大({round(vr, 2)})]**</font> + {label}"

        return label

    def _process_kline_data(self, daily_change_percent: Optional[float] = None) -> pd.DataFrame:
        """
        主函数：处理今日数据，计算指标并输出标签

        参数：
        - daily_change_percent: 当天涨跌幅（百分比）

        返回：
        - df_today：添加了 volume_ratio, EMA, STD, classify 列
        """
        df = self._list_today_5_min_data()
        if df.empty:
            return df

        # 计算量比
        df = self._compute_volume_ratio(df, self._history_5_min_data)

        # 逐行分类判断
        def classify_row(row):
            return self._classify_volume_price(row, self._ema_vol, self._std_vol, daily_change_percent)

        df["分类"] = df.apply(classify_row, axis=1)
        return df

    def _monitor(self):
        current_price = self._get_current_stock_price()
        if current_price is None:
            logger.warning(f"获取股票 {self.code} 的当前价格失败")
            return
        logger.debug(f"{self.name}({self.code}) 当前价格: {current_price}")

        # 获取当天涨跌幅
        daily_change_percent = high_level_trading_client.get_zh_stock_current_change(
            self.code
        )
        if daily_change_percent is None:
            logger.warning(f"获取股票 {self.code} 的当前涨跌幅失败")
            return

        df = self._process_kline_data(daily_change_percent)
        if df.empty:
            logger.warning(f"获取股票 {self.code} 的当前5分钟数据为空")
            return
        # 最后一行，如果不是正常波动，则发送告警
        last_row = df.iloc[-1]
        logger.debug(f"{self.name}({self.code}) 当前5分钟数据:\n{last_row}")
        if last_row["分类"] != self.NORMAL_TAG:
            daily_rate_limiter.set_daily_limit(self._make_alert_key(last_row), limit=1)
            # 计算当前成交量相对于5分钟放量标准的倍数
            five_min_times = round(last_row["成交量"] / self._five_min_heavy_std_vol, 2)

            if daily_change_percent >= 0:
                change_percent_str = f"<font color='red'>+{daily_change_percent}%</font>"
            else:
                change_percent_str = f"<font color='green'>{daily_change_percent}%</font>"
            current_price = f"{last_row['收盘']:.2f} ({change_percent_str})"
            # 使用5分钟量能告警消息，注意参数名称匹配
            message_sender_agg.five_min_volume_alert_message(
                code=self.code,
                name=self.name,
                current_price=current_price,
                current_volume=last_row["成交量"],
                five_min_std_vol=self._five_min_heavy_std_vol,
                five_min_times=five_min_times,
                alert_content=last_row["分类"],
                alert_key=self._make_alert_key(last_row),
            )

    def start_monitor(self):
        logger.info(
            f"开始监控股票 {self.code} 的量价关系, 告警类型: {self._alert_type} "
        )
        while True:
            try:
                self._monitor()
                if self._stop_monitor:
                    break
            except Exception as e:
                logger.exception(f"监控股票 {self.code} 的量价关系时出错: {e}")
                communicate.sender_svc.log_alert(
                    f"{self.start_monitor.__name__}",
                    f"监控股票 {self.code} 的量价关系时出错: {e}",
                )
            finally:
                time.sleep(self._monitor_interval)

    def stop_monitor(self):
        logger.info(
            f"停止监控股票 {self.code} 的量价关系, 告警类型: {self._alert_type} "
        )
        self._stop_monitor = True


class ZhStockOneMinVolumeMonitor(MonitorObjectSpec):
    NORMAL_TAG = "正常波动"

    def __init__(self, code: str, name: str):
        self.code = code
        self.name = name
        self._stop_monitor = False
        self._alert_type = self.make_alert_type(code)
        self._daily_limit = 1
        self._monitor_interval = 60  # 1分钟 = 60秒
        self._history_5_min_data = self._list_history_5_min_data()
        # 计算1分钟放量标准：取前15根5分钟K线的平均值，去掉前3根，然后除以5
        self._one_min_std_vol: Optional[float] = self._compute_1min_volume_standard()
        logger.info(f"{self.name}({self.code}) 1分钟放量标准: {self._one_min_std_vol}")

    @property
    def alert_type(self) -> str:
        return self._alert_type

    @classmethod
    def make_alert_type(cls, code: str) -> str:
        return f"zh_one_min_volume_monitor_{code}"

    def _make_alert_key(self, row: pd.Series) -> str:
        return f"{self._alert_type}_{row['时间']}_{row['收盘']}"

    def _list_history_5_min_data(self) -> pd.DataFrame:
        """获取历史5分钟数据用于计算1分钟放量标准"""
        start_date, end_date = get_datetime_range(before_days=35)
        df = ak_cli.list_zh_stock_data_with_minute(
            self.code, start_date, end_date, period="5"
        )
        return df

    def _list_today_1_min_data(self) -> pd.DataFrame:
        """获取今日1分钟数据"""
        start_date, end_date = get_zh_today_trading_time_range()
        df = ak_cli.list_zh_stock_data_with_minute(
            self.code, start_date, end_date, period="1"
        )
        return df

    def _compute_1min_volume_standard(self) -> Optional[float]:
        """计算1分钟放量标准：成交量最大的15根5分钟K线的平均值，去掉前3根，然后除以5"""
        if self._history_5_min_data.empty or len(self._history_5_min_data) < 15:
            logger.warning(
                f"{self.name}({self.code}) 历史5分钟数据不足15条，无法计算放量标准"
            )
            return None

        # 确保成交量列是数值类型
        self._history_5_min_data["成交量"] = pd.to_numeric(
            self._history_5_min_data["成交量"], errors="coerce"
        )

        # 按成交量排序，取成交量最大的15条5分钟数据
        top_15_by_volume = self._history_5_min_data.nlargest(15, "成交量")
        # 去掉前3根（成交量最大的3根），计算平均值，然后除以5
        valid_12 = top_15_by_volume.iloc[3:]  # 去掉前3根最大的
        avg_5min_vol = valid_12["成交量"].mean()

        # 检查计算结果是否有效
        if pd.isna(avg_5min_vol) or avg_5min_vol <= 0:
            logger.warning(
                f"{self.name}({self.code}) 计算5分钟平均成交量失败: {avg_5min_vol}"
            )
            return None

        # 除以5得到1分钟平均成交量标准
        avg_1min_vol = avg_5min_vol / 5
        return round(avg_1min_vol, 2)

    def _get_current_stock_price(self) -> Optional[float]:
        data = high_level_trading_client.get_zh_stock_current_min_data(self.code)
        if data is None:
            return None
        return data.close

    def _get_volume_price_alert_content(
        self, change_percent: float, volume_times: float, abs_change: float
    ) -> Optional[str]:
        """
        根据涨跌幅度和成交量倍数生成告警内容

        Args:
            change_percent: 涨跌幅百分比
            volume_times: 成交量倍数
            abs_change: 涨跌幅绝对值

        Returns:
            告警内容字符串，如果不满足告警条件则返回None
        """
        # 量价配合告警规则
        if abs_change >= 2.618:
            # 涨跌幅度 >= 2%
            direction = "拉升" if change_percent > 0 else "下跌"
            color = "red" if change_percent > 0 else "green"
            return f"<font color='{color}'>**快速{direction}，关注放量倍数({volume_times}倍)，确认趋势**</font>"
        elif abs_change >= 1.618:
            # 涨跌幅度 >= 1.618%
            direction = "拉升" if change_percent > 0 else "下跌"
            position_text = "冲破" if change_percent > 0 else "跌破"
            color = "red" if change_percent > 0 else "green"
            return f"<font color='{color}'>**放量{direction}({volume_times}倍)，关注是否{position_text}关键位置**</font>"
        elif abs_change >= 0.618:
            # 涨跌幅度 >= 0.618%
            direction = "上涨" if change_percent > 0 else "下跌"
            color = "red" if change_percent > 0 else "green"
            return f"<font color='{color}'>**异动放量{direction}({volume_times}倍)，需要关注**</font>"
        else:
            # 涨跌幅度不足0.618%，不告警
            return None

    def _monitor(self):
        """主监控逻辑"""
        current_price = self._get_current_stock_price()
        if current_price is None:
            logger.warning(f"获取股票 {self.code} 的当前价格失败")
            return
        logger.debug(f"{self.name}({self.code}) 当前价格: {current_price}")

        df = self._list_today_1_min_data()
        if df.empty:
            logger.debug(f"获取股票 {self.code} 的当前1分钟数据为空")
            return

        # 确保成交量列是数值类型
        df["成交量"] = pd.to_numeric(df["成交量"], errors="coerce")

        # 获取最新的1分钟数据
        last_row = df.iloc[-1]
        current_volume = last_row["成交量"]

        # 检查当前成交量是否有效
        if pd.isna(current_volume) or current_volume <= 0:
            logger.debug(f"{self.name}({self.code}) 当前成交量无效: {current_volume}")
            return

        # 使用基于5分钟数据计算的1分钟放量标准
        if self._one_min_std_vol is None:
            logger.debug(f"{self.name}({self.code}) 无法获取1分钟放量标准")
            return
        avg_vol = self._one_min_std_vol

        # 判断是否超过成交量标准
        if current_volume > avg_vol:
            # 计算成交量倍数
            volume_times = round(current_volume / avg_vol, 2)

            # 获取今天涨跌幅
            change_percent = high_level_trading_client.get_zh_stock_current_change(
                self.code
            )
            if change_percent is None:
                logger.warning(f"获取股票 {self.code} 的当前涨跌幅失败")
                return

            # 当前分钟的涨跌幅
            current_min_data = high_level_trading_client.get_zh_stock_current_min_data(
                self.code
            )
            if current_min_data is None:
                logger.warning(f"获取股票 {self.code} 的当前涨跌幅失败")
                return

            # 量价配合判断：只有在特定涨跌幅度下才告警
            abs_change = abs(current_min_data.change_rate)
            alert_content = self._get_volume_price_alert_content(
                current_min_data.change_rate, volume_times, abs_change
            )

            # 如果没有匹配的量价配合条件，不发送告警
            if alert_content is None:
                logger.debug(
                    f"{self.name}({self.code}) 放量但涨跌幅度不足，不发送告警。涨跌幅: {change_percent}%, 成交量倍数: {volume_times}"
                )
                return

            daily_rate_limiter.set_daily_limit(self._make_alert_key(last_row), limit=1)

            # 格式化价格显示
            if change_percent >= 0:
                change_percent_str = f"<font color='red'>+{change_percent}%</font>"
            else:
                change_percent_str = f"<font color='green'>{change_percent}%</font>"
            current_price_str = f"{last_row['收盘']:.2f} ({change_percent_str})"

            # 发送1分钟量能告警消息
            message_sender_agg.one_min_volume_alert_message(
                code=self.code,
                name=self.name,
                current_price=current_price_str,
                current_volume=int(current_volume),
                one_min_std_vol=int(avg_vol),
                one_min_times=volume_times,
                alert_content=alert_content,
                alert_key=self._make_alert_key(last_row),
            )

    def start_monitor(self):
        logger.info(
            f"开始监控股票 {self.code} 的1分钟量能, 告警类型: {self._alert_type}"
        )
        while True:
            try:
                self._monitor()
                if self._stop_monitor:
                    break
            except Exception as e:
                logger.exception(f"监控股票 {self.code} 的1分钟量能时出错: {e}")
                communicate.sender_svc.log_alert(
                    f"{self.start_monitor.__name__}",
                    f"监控股票 {self.code} 的1分钟量能时出错: {e}",
                )
            finally:
                time.sleep(self._monitor_interval)

    def stop_monitor(self):
        logger.info(
            f"停止监控股票 {self.code} 的1分钟量能, 告警类型: {self._alert_type}"
        )
        self._stop_monitor = True
