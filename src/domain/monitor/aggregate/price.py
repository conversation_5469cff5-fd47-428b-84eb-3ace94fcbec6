import threading
import time
from datetime import datetime
from typing import List, Optional

import pandas as pd
from loguru import logger

from src.domain import communicate
from src.domain.monitor.aggregate.base import BaseMonitorAggregate
from src.domain.monitor.aggregate.message import message_sender_agg
from src.domain.monitor.aggregate.utils import daily_rate_limiter
from src.domain.monitor.repo import do
from src.domain.monitor.schema import MonitorType
from src.domain.monitor.spec import MonitorObjectSpec
from src.domain.schema import MarketTag
from src.infra.app import app
from src.infra.clients.trading.ak import ak_cli
from src.infra.clients.trading.highlevel import high_level_trading_client
from src.infra.clients.trading.utils import get_zh_today_trading_time_range


class PriceMonitorAggregate(BaseMonitorAggregate):

    def __init__(self):
        self.monitor_objects: List[MonitorObjectSpec] = []
        self._lock = threading.Lock()

    def start_all_monitor(self):
        if self.monitor_objects:
            logger.warning("已经有监控在运行，跳过启动")
            return
        objects = self.list_monitor_objects(MonitorType.price)
        for obj in objects:
            if obj.market_tag != MarketTag.zh_stock.value:
                logger.warning(f"价格监控只支持A股股票，不支持 {obj.market_tag}")
                continue
            # 价格基础监控
            args = do.MonitorPriceArgs.model_validate_json(obj.monitor_arg)
            monitor = ZhStockPriceMonitor(obj.code, obj.name, args)
            app.submit_concurrent_task(monitor.start_monitor)
            self.monitor_objects.append(monitor)

            # 异常价格监控
            monitor = ZhAbnormalPriceMoveMonitor(obj.code, obj.name)
            app.submit_concurrent_task(monitor.start_monitor)
            self.monitor_objects.append(monitor)

    def stop_all_monitor(self):
        for monitor in self.monitor_objects:
            monitor.stop_monitor()

        self.monitor_objects = []

    def start_monitor(self, id: int):
        with self._lock:
            obj = self.get_monitor_object_by_id(id)
            if obj is None:
                raise ValueError(f"监控对象 {id} 不存在")

            if obj.monitor_type != MonitorType.price.value:
                raise ValueError(f"监控对象 {id} 不是价格监控")

            if obj.market_tag != MarketTag.zh_stock:
                raise ValueError(f"价格监控只支持A股股票，不支持 {obj.market_tag}")

            args = do.MonitorPriceArgs.model_validate_json(obj.monitor_arg)
            alert_type = ZhStockPriceMonitor.make_alert_type(obj.code, args)
            for monitor in self.monitor_objects:
                if monitor.alert_type == alert_type:
                    logger.warning(f"监控对象 {monitor.alert_type} 已经存在，跳过启动")
                    return
            # 价格基础监控
            monitor = ZhStockPriceMonitor(obj.code, obj.name, args)
            self.monitor_objects.append(monitor)
            app.submit_concurrent_task(monitor.start_monitor)

            # 异常价格监控
            monitor = ZhAbnormalPriceMoveMonitor(obj.code, obj.name)
            self.monitor_objects.append(monitor)
            app.submit_concurrent_task(monitor.start_monitor)

    def stop_monitor(self, id: int):
        with self._lock:
            obj = self.get_monitor_object_by_id(id)
            if obj is None:
                raise ValueError(f"监控对象 {id} 不存在")

            if obj.monitor_type != MonitorType.price.value:
                raise ValueError(f"监控对象 {id} 不是价格监控")

            args = do.MonitorPriceArgs.model_validate_json(obj.monitor_arg)
            alert_type = ZhStockPriceMonitor.make_alert_type(obj.code, args)
            for monitor in self.monitor_objects:
                if monitor.alert_type == alert_type:
                    monitor.stop_monitor()
                    self.monitor_objects.remove(monitor)
                    break

    def create_object(
        self, code: str, name: str, market_tag: MarketTag, args: do.MonitorPriceArgs
    ) -> Optional[int]:
        if market_tag != MarketTag.zh_stock:
            raise ValueError(f"价格监控只支持A股股票，不支持 {market_tag}")

        object = self.get_monitor_object_by_info(
            code, MonitorType.price, market_tag, monitor_arg=args
        )
        if object:
            logger.warning(f"价格监控对象 {code},{name}. {args} 已经存在，跳过创建")
            return

        object_id = super().create_monitor_object(
            code, name, market_tag, MonitorType.price, args
        )
        self.start_monitor(object_id)
        return object_id

    def update_object(self, id: int, args: do.MonitorPriceArgs):
        super().update_monitor_object(id, args)
        self.stop_monitor(id)
        self.start_monitor(id)

    def delete_object(self, id: int):
        self.stop_monitor(id)
        super().delete_monitor_object(id)


class ZhStockPriceMonitor(MonitorObjectSpec):

    def __init__(self, code: str, name: str, args: do.MonitorPriceArgs):
        self._monitor_interval = 7
        self._daily_limit = 1
        self.message_sender = message_sender_agg
        self.code = code
        self.name = name
        self.args: Optional[do.MonitorPriceArgs] = args
        self._stop_monitor = False
        self._alert_type = self.make_alert_type(code, args)

    @property
    def alert_type(self) -> str:
        return self._alert_type

    @classmethod
    def make_alert_type(cls, code: str, args: do.MonitorPriceArgs) -> str:
        return f"zh_price_monitor_{code}_{args.action}_{args.alert_price}"

    def _get_current_stock_price(self) -> Optional[float]:
        data = high_level_trading_client.get_zh_stock_current_min_data(self.code)
        if data is None:
            return None
        return data.close

    def _monitor(self):
        current_price = self._get_current_stock_price()
        if current_price is None:
            logger.warning(f"获取股票 {self.code} 的当前价格失败")
            return
        logger.debug(
            f"{self.name}({self.code}) 当前价格: {current_price}, 监控价格: {self.args.alert_price}"
        )
        # 上涨 还是 下跌
        if current_price >= self.args.alert_price:
            action = "upward"
        else:
            action = "downward"

        # 判断是否触发监控
        if action == self.args.action and current_price >= self.args.alert_price:
            change_percent = high_level_trading_client.get_zh_stock_current_change(
                self.code
            )
            current_price_text = (
                f"{current_price:.2f} (<font color='red'>+{change_percent}%</font>)"
            )
            alert_price_text = (
                f"{self.args.alert_price:.2f} (<text_tag color='red'>突破</text_tag>)"
            )

            self.message_sender.price_alert_message(
                self.code,
                self.name,
                current_price_text,
                alert_price_text,
                self.args.alert_content.description,
                self._alert_type,
            )

        if action == self.args.action and current_price < self.args.alert_price:
            change_percent = high_level_trading_client.get_zh_stock_current_change(
                self.code
            )
            current_price_text = (
                f"{current_price:.2f} (<font color='green'>{change_percent}%</font>)"
            )
            alert_price_text = (
                f"{self.args.alert_price:.2f} (<text_tag color='green'>跌破</text_tag>)"
            )

            self.message_sender.price_alert_message(
                self.code,
                self.name,
                current_price_text,
                alert_price_text,
                self.args.alert_content.description,
                self._alert_type,
            )

    def start_monitor(self):
        # 设置每日发送限制
        daily_rate_limiter.set_daily_limit(self._alert_type, self._daily_limit)
        logger.info(f"开始监控股票 {self.code} 的价格, 告警类型: {self._alert_type} ")
        while True:
            try:
                self._monitor()
                if self._stop_monitor:
                    break
            except Exception as e:
                logger.exception(f"监控股票 {self.code} 的价格时出错: {e}")
                communicate.sender_svc.log_alert(
                    f"{self.start_monitor.__name__}",
                    f"监控股票 {self.code} 的价格时出错: {e}",
                )
            finally:
                time.sleep(self._monitor_interval)

    def stop_monitor(self):
        logger.info(f"停止监控股票 {self.code} 的价格, 告警类型: {self._alert_type} ")
        self._stop_monitor = True


class ZhAbnormalPriceMoveMonitor(MonitorObjectSpec):
    WINDOW_SIZE = 3
    ALPHA = 0.01  # 基础阈值 1%
    PHI = 1.618  # 黄金分割比
    THRESHOLD = [
        ALPHA,
        ALPHA * PHI,
        round(ALPHA * PHI**2, 3),
        round(ALPHA * PHI**3, 3),
        round(ALPHA * PHI**4, 3),
    ]
    LABELS = ["轻度(1)", "中度(1.6)", "重度(2.6)", "剧烈(4.2)", "极端(6.8)"]

    def __init__(self, code: str, name: str):
        self.code = code
        self.name = name
        self._stop_monitor = False
        self._alert_type = self.make_alert_type(code)
        self._daily_limit = 2
        self._monitor_interval = 38

    @property
    def alert_type(self) -> str:
        return self._alert_type

    @classmethod
    def make_alert_type(cls, code: str) -> str:
        return f"abnormal_price_move_monitor_{code}"

    def _make_alert_key(self, row: pd.Series) -> str:
        return f"{self._alert_type}_{row['时间']}"

    def _list_today_1min_data(self) -> pd.DataFrame:
        start_time, end_time = get_zh_today_trading_time_range()
        return ak_cli.list_zh_stock_data_with_minute(
            code=self.code,
            start_datetime=start_time,
            end_datetime=end_time,
        )

    def _monitor(self):
        df = self._list_today_1min_data()
        if df.empty or len(df) <= self.WINDOW_SIZE:
            return
        # pct
        pct = (df["收盘"].iloc[-1] - df["收盘"].iloc[-1 - self.WINDOW_SIZE]) / df[
            "收盘"
        ].iloc[-1 - self.WINDOW_SIZE]
        logger.debug(
            f"{self.name}({self.code}) 当前价格: {df['收盘'].iloc[-1]}, 涨跌幅: {pct * 100:.2f}%"
        )
        # row
        last_row = df.iloc[-1]
        # 判断级别
        abs_pct = abs(pct)
        for level, thresh in reversed(list(enumerate(self.THRESHOLD))):  # 先判重度
            if abs_pct >= thresh:
                daily_rate_limiter.set_daily_limit(
                    self._make_alert_key(last_row), self._daily_limit
                )

                change_percent = high_level_trading_client.get_zh_stock_current_change(
                    self.code
                )
                if change_percent is None:
                    logger.warning(f"获取股票 {self.code} 的当前涨跌幅失败")
                    return
                if change_percent >= 0:
                    change_percent = f"<font color='red'>+{change_percent}%</font>"
                else:
                    change_percent = f"<font color='green'>{change_percent}%</font>"

                current_price = f"{last_row['收盘']:.2f} ({change_percent})"

                direction = "↑" if pct > 0 else "↓"

                if pct > 0:
                    alert_content = f"<font color='red'>3分钟异动，{self.LABELS[level]}，涨幅 {round(pct * 100, 2)}% ({direction}) </font>"

                else:
                    alert_content = f"<font color='green'>3分钟异动，{self.LABELS[level]}，跌幅 {round(abs(pct) * 100, 2)}% ({direction}) </font>"

                message_sender_agg.price_abnormal_move_alert_message(
                    code=self.code,
                    name=self.name,
                    current_price=current_price,
                    datetime=datetime.now().strftime("%H:%M:%S"),
                    alert_content=alert_content,
                    alert_key=self._make_alert_key(last_row),
                )
                break

    def start_monitor(self):
        # 设置每日发送限制

        logger.info(
            f"开始监控股票 {self.code} 的异常价格波动, 告警类型: {self._alert_type} "
        )
        while True:
            try:
                self._monitor()
                if self._stop_monitor:
                    break
            except Exception as e:
                logger.exception(f"监控股票 {self.code} 的异常价格波动时出错: {e}")
                communicate.sender_svc.log_alert(
                    f"{self.start_monitor.__name__}",
                    f"监控股票 {self.code} 的异常价格波动时出错: {e}",
                )
            finally:
                time.sleep(self._monitor_interval)

    def stop_monitor(self):
        logger.info(
            f"停止监控股票 {self.code} 的异常价格波动, 告警类型: {self._alert_type} "
        )
        self._stop_monitor = True
