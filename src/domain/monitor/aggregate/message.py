from datetime import datetime

from loguru import logger

from src.domain import communicate
from src.domain.monitor.aggregate.utils import daily_rate_limiter
from src.infra.app import app
from src.infra.clients.lark.client import LarkClientType


class MessageSenderAggregate:

    def __init__(self):
        self._current_date = datetime.now().date()
        self.sender = communicate.sender_svc
        self.sender_daily_limiter = daily_rate_limiter

    def _reset_daily_count(self):
        if self._current_date != datetime.now().date():
            self._current_date = datetime.now().date()
            self.sender_daily_limiter.reset_daily_count()

    def price_alert_message(
            self,
            code: str,
            name: str,
            current_price: str,
            monitor_price: str,
            alert_content: str,
            alert_key: str,
    ):
        self._reset_daily_count()
        if self.sender_daily_limiter.can_send(alert_key):
            send_params = {
                "code": code,
                "name": name,
                "current_price": f"{current_price}",
                "monitor_price": monitor_price,
                "content": alert_content,
            }
            logger.info(
                f"发送价格监控消息: {code} {name} {current_price} {monitor_price}"
            )
            self.sender.send_card(
                app.config.lark.trading.monitor_price_tmpl_id,
                send_params,
                LarkClientType.trading,
            )
            self.sender_daily_limiter.record_send(alert_key)

    def five_min_volume_alert_message(self,
                                      code: str,
                                      name: str,
                                      current_price: str,
                                      current_volume: int,
                                      five_min_std_vol: int,
                                      five_min_times: float,
                                      alert_content: str,
                                      alert_key: str,
                                      ):
        self._reset_daily_count()
        if self.sender_daily_limiter.can_send(alert_key):
            send_params = {
                "code": code,
                "name": name,
                "current_price": f"{current_price}",
                "current_vol": f"{current_volume}",
                "five_min_std_vol": f"{five_min_std_vol}",
                "five_min_times": f"{five_min_times}",
                "content": alert_content,
            }
            logger.info(
                f"发送5分钟量价监控消息: {code} {name} {current_price} {current_volume}"
            )
            self.sender.send_card(
                app.config.lark.trading.monitor_5_min_volume_tmpl_id,
                send_params,
                LarkClientType.trading,
            )
            self.sender_daily_limiter.record_send(alert_key)

    def one_min_volume_alert_message(self,
                                     code: str,
                                     name: str,
                                     current_price: str,
                                     current_volume: int,
                                     one_min_std_vol: int,
                                     one_min_times: float,
                                     alert_content: str,
                                     alert_key: str,
                                     ):
        self._reset_daily_count()
        if self.sender_daily_limiter.can_send(alert_key):
            send_params = {
                "code": code,
                "name": name,
                "current_price": f"{current_price}",
                "current_vol": f"{current_volume}",
                "one_min_std_vol": f"{one_min_std_vol}",
                "one_min_times": f"{one_min_times}",
                "content": alert_content,
            }
            logger.info(
                f"发送1分钟量价监控消息: {code} {name} {current_price} {current_volume}"
            )
            self.sender.send_card(
                app.config.lark.trading.monitor_1_min_volume_tmpl_id,
                send_params,
                LarkClientType.trading,
            )
            self.sender_daily_limiter.record_send(alert_key)

    def price_abnormal_move_alert_message(self,
                                      code: str,
                                      name: str,
                                      current_price: str,
                                      datetime: str,
                                      alert_content: str,
                                      alert_key: str,
                                      ):
        self._reset_daily_count()
        if self.sender_daily_limiter.can_send(alert_key):
            send_params = {
                "code": code,
                "name": name,
                "current_price": current_price,
                "datetime": datetime,
                "content": alert_content,
            }
            logger.info(
                f"发送价格异常监控消息: {code} {name} {current_price} {alert_content}"
            )
            self.sender.send_card(
                app.config.lark.trading.monitor_price_abnormal_tmpl_id,
                send_params,
                LarkClientType.trading,
            )
            self.sender_daily_limiter.record_send(alert_key)


message_sender_agg = MessageSenderAggregate()
