from abc import ABC, abstractmethod



class MonitorObjectSpec(ABC):
    
    @property
    @abstractmethod
    def alert_type(self) -> str:
        raise NotImplementedError

    @abstractmethod
    def make_alert_type(self, *args, **kwargs) -> str:
        raise NotImplementedError

    @abstractmethod
    def start_monitor(self):
        raise NotImplementedError

    @abstractmethod
    def stop_monitor(self):
        raise NotImplementedError
