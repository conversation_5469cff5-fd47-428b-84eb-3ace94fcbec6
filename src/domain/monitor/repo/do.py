from enum import Enum
from typing import Literal, Union, Dict, List, Any

from pydantic import Field, BaseModel


class PriceAlertContent(str, Enum):
    
    stop_loss = "stop_loss", "<font color='green'> **止损线** </font>"
    stage_take_profit = "stage_take_profit", "<font color='red'> **止盈线 (左侧离场)** </font>"
    moving_stop_loss = (
        "moving_stop_loss",
        "<font color='green'> **移动止损线 (右侧离场) **</font>",
    )
    key_breakthrough = (
        "key_breakthrough",
        "<font color='red'> **关键突破位 (确认是否放量突破)** </font>",
    )
    key_support = "key_support", "<font color='blue'> **关键支撑位 (确认形态是否被改变)** </font>"
    entry_price = "entry_price", "<font color='blue'> **到达入场价** </font>"
    
    
    def __new__(cls, value, description):
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj.description = description
        return obj
    
    def __str__(self):
        return f"{self._value_}"


class MonitorArgs(BaseModel):
    monitor_type: str = Field(..., description="监控类型")
    monitor_arg: Dict[str, Any] = Field(..., description="监控参数")


class MonitorObject(BaseModel):
    id: int = Field(..., description="监控对象ID")
    code: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    market_tag: str = Field(..., description="股票市场标签")
    args: List[MonitorArgs] = Field(..., description="监控参数")



class MonitorPriceArgs(BaseModel):
    alert_price: float = Field(..., description="价格")
    action: Union[Literal["upward", "downward"], str] = Field(
        default="upward", description="监控方向，upward: 上涨，downward: 下跌"
    )
    alert_content: PriceAlertContent = Field(..., description="告警内容")
    
    
    
    