from sqlalchemy import Column, VARCHAR

from src.infra.clients.mysql.orm import IdModelMixin, DateTimeModelMixin, TablePrefix


class MonitorObject(IdModelMixin, DateTimeModelMixin):
    
    __tablename__ = f"{TablePrefix}_monitor_object"


    code = Column(VARCHAR(12), index=True, nullable=False, comment="股票代码")
    name = Column(VARCHAR(64), nullable=False, comment="股票名称")
    market_tag = Column(
        VARCHAR(16),
        index=True,
        nullable=False,
        comment="标签, zh_stock, zh_index, zh_etf, hk_stock, us_stock ...",
    )
    monitor_type = Column(
        VARCHAR(32),
        index=True,
        nullable=False,
        comment="监控类型, price, volume, abnormal_fund ...",
    )
    monitor_arg = Column(VARCHAR(2048), nullable=False, comment="监控参数")
    
    
