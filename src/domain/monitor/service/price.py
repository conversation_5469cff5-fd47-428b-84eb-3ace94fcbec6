from typing import List

from src.domain.monitor.aggregate.price import PriceMonitorAggregate
from src.domain.monitor.repo import do, dao
from src.domain.monitor.schema import MonitorType
from src.domain.schema import MarketTag


class PriceMonitorService:
    
    
    def __init__(self):
        self._price_monitor_agg = PriceMonitorAggregate()
    
    def start_all_monitor(self):
        self._price_monitor_agg.start_all_monitor()
    
    def stop_all_monitor(self):
        self._price_monitor_agg.stop_all_monitor()
    
    def start_monitor(self, id: int):
        self._price_monitor_agg.start_monitor(id)
    
    def stop_monitor(self, id: int):
        self._price_monitor_agg.stop_monitor(id)
    
    def create_monitor(self, code: str, name: str, market_tag: MarketTag, args: do.MonitorPriceArgs):
        return self._price_monitor_agg.create_object(code, name, market_tag, args=args)
    
    def update_monitor(self, id: int, *, args: do.MonitorPriceArgs):
        self._price_monitor_agg.update_object(id, args=args)
    
    def delete_monitor(self, id: int):
        self._price_monitor_agg.delete_object(id)
        
        
    def list_monitor(self, market_tag: MarketTag) ->  List[dao.MonitorObject]:
        return self._price_monitor_agg.list_monitor_objects(MonitorType.price, market_tag)
    
    
        
        
price_monitor_svc = PriceMonitorService()
