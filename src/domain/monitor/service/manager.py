import threading
import time
from datetime import datetime, time as dt_time
from typing import Optional

from loguru import logger

from src.domain.monitor.service.price import price_monitor_svc
from src.domain.monitor.service.volume import volume_monitor_svc
from src.infra.clients.trading.utils import (
    is_zh_trading_date,
    is_zh_trading_time_range,
    waiting_for_zh_trading_open,
    waiting_for_zh_trading_close,
)


class SmartMonitorManagerService:
    """智能监控管理器
    
    功能：
    1. 在程序启动时智能判断当前状态并采取相应行动
    2. 自动在开市时启动监控，关市时停止监控
    3. 支持在任何时间启动程序都能正确工作
    """
    
    def __init__(self):
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_flag = threading.Event()
        self._lock = threading.Lock()
        self._is_running = False
        
    def start(self):
        """启动智能监控管理器"""
        with self._lock:
            if self._is_running:
                logger.warning("Smart price monitor manager is already running")
                return
                
            logger.info("Starting smart price monitor manager...")
            self._stop_flag.clear()
            self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()
            self._is_running = True
            logger.info("Smart monitor manager started")
    
    def stop(self):
        """停止智能监控管理器"""
        with self._lock:
            if not self._is_running:
                logger.warning("Smart monitor manager is not running")
                return
                
            logger.info("Stopping smart monitor manager...")
            self._stop_flag.set()
            
            # 停止所有价格监控
            try:
                price_monitor_svc.stop_all_monitor()
                logger.info("All price monitors stopped")
            except Exception as e:
                logger.error(f"Error stopping price monitors: {e}")
            
            if self._monitor_thread and self._monitor_thread.is_alive():
                self._monitor_thread.join(timeout=5)
                
            self._is_running = False
            logger.info("Smart monitor manager stopped")
    
    def _monitor_loop(self):
        """监控主循环"""
        logger.info("Smart monitor loop started")
        
        while not self._stop_flag.is_set():
            try:
                current_time = datetime.now()
                
                # 检查是否是交易日
                if not is_zh_trading_date(today=current_time):
                    logger.info("Today is not a trading day, waiting...")
                    self._sleep_until_next_check()
                    continue
                
                # 是交易日，检查当前是否在交易时间
                if is_zh_trading_time_range(current_time):
                    # 在交易时间内，启动监控并等待关市
                    self._handle_trading_session()
                else:
                    # 不在交易时间内，等待开市
                    self._wait_for_market_open()
                    
            except Exception as e:
                logger.error(f"Error in monitor loop: {e}")
                # 发生错误时等待一段时间再重试
                self._stop_flag.wait(60)
        
        logger.info("Smart monitor loop ended")
    
    def _handle_trading_session(self):
        """处理交易时段"""
        logger.info("Currently in trading hours, starting price monitors...")
        
        try:
            # 启动所有价格监控
            price_monitor_svc.start_all_monitor()
            logger.info("All price monitors started successfully")
            # 启动volume 监控
            volume_monitor_svc.start_all_monitor()
            logger.info("All volume monitors started successfully")
            
            # 等待关市
            logger.info("Waiting for market close...")
            waiting_for_zh_trading_close()
            
            # 关市后停止监控
            logger.info("Market closed, stopping price monitors...")
            price_monitor_svc.stop_all_monitor()
            logger.info("Market closed, stopping volume monitors...")
            volume_monitor_svc.stop_all_monitor()
            logger.info("All price monitors stopped")
            
        except Exception as e:
            logger.error(f"Error during trading session: {e}")
            # 确保在出错时也停止监控
            try:
                price_monitor_svc.stop_all_monitor()
                volume_monitor_svc.stop_all_monitor()
            except Exception as stop_error:
                logger.error(f"Error stopping monitors after error: {stop_error}")
    
    def _wait_for_market_open(self):
        """等待开市"""
        current_time = datetime.now()
        current_dt_time = current_time.time()
        
        # 判断是在开市前还是收市后
        morning_start = dt_time(9, 25, 0)
        afternoon_end = dt_time(15, 0, 0)
        
        if current_dt_time < morning_start:
            logger.info("Before market open, waiting for trading to start...")
        elif current_dt_time > afternoon_end:
            logger.info("After market close, waiting for next trading day...")
        else:
            # 在午休时间
            logger.info("During lunch break, waiting for afternoon session...")
        
        # 等待开市
        waiting_for_zh_trading_open()
    
    def _sleep_until_next_check(self):
        """睡眠到下次检查时间"""
        # 非交易日时，每小时检查一次
        self._stop_flag.wait(3600)
    
    @property
    def is_running(self) -> bool:
        """检查管理器是否正在运行"""
        return self._is_running


# 全局实例
smart_monitor_manager_svc = SmartMonitorManagerService()
