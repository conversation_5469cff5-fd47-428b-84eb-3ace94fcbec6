from typing import Optional

from src.domain.monitor.aggregate.volume import VolumeMonitorAggregate
from src.domain.monitor.schema import MonitorType
from src.domain.schema import MarketTag


class VolumeMonitorService:
    
    
    def __init__(self):
        self._volume_monitor_agg = VolumeMonitorAggregate()
    
    def start_all_monitor(self):
        self._volume_monitor_agg.start_all_monitor()
    
    def stop_all_monitor(self):
        self._volume_monitor_agg.stop_all_monitor()
    
    def start_monitor(self, id: int):
        self._volume_monitor_agg.start_monitor(id)
    
    def stop_monitor(self, id: int):
        self._volume_monitor_agg.stop_monitor(id)
    
    def create_monitor(self, code: str, name: str, market_tag: MarketTag) -> Optional[int]:
        return self._volume_monitor_agg.create_object(code, name, market_tag)
    
    def delete_monitor(self, id: int):
        self._volume_monitor_agg.delete_object(id)
    
    def list_monitor(self, market_tag: MarketTag):
        return self._volume_monitor_agg.list_monitor_objects(MonitorType.volume, market_tag)
    

volume_monitor_svc = VolumeMonitorService()
