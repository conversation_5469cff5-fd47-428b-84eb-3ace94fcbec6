from sqlalchemy import Column, VARCHAR, DECIMAL, DATETIME, DATE, INT, BOOLEAN, Index

from src.infra.clients.mysql.orm import (
    IdModelMixin,
    BaseModel,
    DateTimeModelMixin,
    TablePrefix,
)

__all__ = [
    "KLine30Min",
    "KLine60Min",
    "KLineDaily",
    "ZhStock",
    "ZhStockBlank",
    "ZhStockBlankFund",
    "HkStock",
    "UsStock",
    "AnalyzeZhUnderlyingAsset",
]


class KLine(IdModelMixin):
    __abstract__ = True

    code = Column(VARCHAR(12), index=True, nullable=False, comment="股票代码")
    open = Column(DECIMAL(15, 2), nullable=True, comment="开盘价")
    close = Column(DECIMAL(15, 2), nullable=True, comment="收盘价")
    high = Column(DECIMAL(15, 2), nullable=True, comment="最高价")
    low = Column(DECIMAL(15, 2), nullable=True, comment="最低价")
    volume = Column(DECIMAL(15, 2), nullable=True, comment="成交量")
    amount = Column(DECIMAL(15, 2), nullable=True, comment="成交额")
    tag = Column(
        VARCHAR(16),
        index=True,
        nullable=True,
        comment="标签, zh_stock, zh_index, zh_etf, hk_stock, us_stock ...",
    )


class KLineDatetime(BaseModel):
    __abstract__ = True
    datetime = Column(DATETIME, nullable=True, index=True, comment="时间")


class KLineDate(BaseModel):
    __abstract__ = True
    date = Column(DATE, nullable=True, index=True, comment="日期")


class KLine30Min(KLine, KLineDatetime):
    __tablename__ = f"{TablePrefix}_k_line_30_min"

    __table_args__ = (
        Index("idx_code_datetime", "code", "datetime"),  # ✅ 联合索引
        # 可继续添加其他索引或约束
    )


class KLine60Min(KLine, KLineDatetime):
    __tablename__ = f"{TablePrefix}_k_line_60_min"

    __table_args__ = (
        Index("idx_code_datetime", "code", "datetime"),  # ✅ 联合索引
        # 可继续添加其他索引或约束
    )


class KLineDaily(KLine, KLineDate):
    __tablename__ = f"{TablePrefix}_k_line_daily"

    __table_args__ = (
        Index("idx_code_datetime", "code", "date"),  # ✅ 联合索引
        # 可继续添加其他索引或约束
    )


class ZhIndex(IdModelMixin, DateTimeModelMixin):
    __tablename__ = f"{TablePrefix}_zh_index"

    code = Column(
        VARCHAR(12), index=True, nullable=False, comment="index code", unique=True
    )
    name = Column(VARCHAR(64), nullable=True, comment="index name")
    newest_price = Column(DECIMAL(15, 2), nullable=True, comment="newest price")
    price_change = Column(DECIMAL(15, 2), nullable=True, comment="涨跌额")
    price_change_rate = Column(DECIMAL(15, 2), nullable=True, comment="涨跌幅")
    volume = Column(DECIMAL(15, 2), nullable=True, comment="成交量")
    amount = Column(DECIMAL(15, 2), nullable=True, comment="成交额")


class ZhStock(IdModelMixin, DateTimeModelMixin):
    __tablename__ = f"{TablePrefix}_zh_stock"
    code = Column(
        VARCHAR(12), index=True, nullable=False, comment="stock code", unique=True
    )
    name = Column(VARCHAR(64), nullable=False, comment="stock name")
    newest_price = Column(DECIMAL(15, 2), nullable=True, comment="newest price")
    price_change = Column(DECIMAL(15, 2), nullable=True, comment="涨跌额")
    price_change_rate = Column(DECIMAL(15, 2), nullable=True, comment="涨跌幅")
    turnover = Column(DECIMAL(15, 2), nullable=True, comment="换手率")
    volume = Column(DECIMAL(15, 2), nullable=True, comment="成交量")
    amount = Column(DECIMAL(15, 2), nullable=True, comment="成交额")
    pe_rate = Column(DECIMAL(15, 2), nullable=True, comment="市盈率")
    market_cap = Column(DECIMAL(15, 2), nullable=True, comment="总市值")
    free_market_cap = Column(DECIMAL(15, 2), nullable=True, comment="流通市值")
    market = Column(
        VARCHAR(12), nullable=True, comment="所属市场， sh,sz,bj", index=True
    )
    is_delisting = Column(
        BOOLEAN, nullable=False, default=False, comment="是否退市", index=True
    )
    is_st = Column(BOOLEAN, nullable=False, default=False, index=True, comment="是否ST")


class ZhStockFund(IdModelMixin, DateTimeModelMixin):
    __tablename__ = f"{TablePrefix}_zh_stock_fund"

    code = Column(VARCHAR(12), index=True, nullable=False, comment="stock code")
    date = Column(DATE, nullable=False, comment="日期", index=True)
    major_net_inflow = Column(DECIMAL(15, 2), nullable=True, comment="主力净流入")
    super_net_inflow = Column(DECIMAL(15, 2), nullable=True, comment="超大单净流入")
    big_net_inflow = Column(DECIMAL(15, 2), nullable=True, comment="大单净流入")
    medium_net_inflow = Column(DECIMAL(15, 2), nullable=True, comment="中单净流入")
    small_net_inflow = Column(DECIMAL(15, 2), nullable=True, comment="小单净流入")
    major_rate = Column(DECIMAL(15, 2), nullable=True, comment="主力净流入占比")
    super_rate = Column(DECIMAL(15, 2), nullable=True, comment="超大单净流入占比")
    big_rate = Column(DECIMAL(15, 2), nullable=True, comment="大单净流入占比")
    medium_rate = Column(DECIMAL(15, 2), nullable=True, comment="中单净流入占比")
    small_rate = Column(DECIMAL(15, 2), nullable=True, comment="小单净流入占比")


class ZhStockMarginTrading(IdModelMixin, DateTimeModelMixin):
    __tablename__ = f"{TablePrefix}_zh_stock_margin_trading"

    code = Column(VARCHAR(12), index=True, nullable=False, comment="stock code")
    date = Column(DATE, nullable=False, comment="日期", index=True)
    margin_balance = Column(DECIMAL(15, 2), nullable=True, comment="融资余额")
    margin_buy = Column(DECIMAL(15, 2), nullable=True, comment="融资买入额")
    margin_repay = Column(DECIMAL(15, 2), nullable=True, comment="融资偿还额")
    short_balance = Column(DECIMAL(15, 2), nullable=True, comment="融券余额")
    short_sell = Column(DECIMAL(15, 2), nullable=True, comment="融券卖出量")
    short_repay = Column(DECIMAL(15, 2), nullable=True, comment="融券偿还量")


# unusual market activity
class ZhStockUMA(IdModelMixin, DateTimeModelMixin):
    __tablename__ = f"{TablePrefix}_zh_stock_uma"

    code = Column(VARCHAR(12), index=True, nullable=False, comment="stock code")
    date = Column(DATE, nullable=False, comment="日期", index=True)
    up_type_code = Column(VARCHAR(1024), nullable=True, comment="上涨类型")
    down_type_code = Column(VARCHAR(1024), nullable=True, comment="下跌类型")
    # 需要累加后写入
    up_vol = Column(INT, nullable=True, comment="上涨成交量")
    # 需要累加后写入
    down_vol = Column(INT, nullable=True, comment="下跌成交量")


class ZhStockBlank(IdModelMixin, DateTimeModelMixin):
    __tablename__ = f"{TablePrefix}_zh_stock_blank"

    code = Column(
        VARCHAR(12), index=True, nullable=False, comment="stock code", unique=True
    )
    name = Column(VARCHAR(64), nullable=True, comment="stock name")
    newest_price = Column(DECIMAL(15, 2), nullable=True, comment="newest price")
    price_change = Column(DECIMAL(15, 2), nullable=True, comment="涨跌额")
    price_change_rate = Column(DECIMAL(15, 2), nullable=True, comment="涨跌幅")
    turnover = Column(DECIMAL(15, 2), nullable=True, comment="换手率")
    gainers = Column(INT, nullable=True, comment="上涨数")
    losers = Column(INT, nullable=True, comment="下跌数")
    is_concept = Column(BOOLEAN, nullable=False, default=False, comment="是否概念")


class ZhStockBlankFund(IdModelMixin, DateTimeModelMixin):
    __tablename__ = f"{TablePrefix}_zh_stock_blank_fund"

    name = Column(VARCHAR(32), index=True, nullable=False, comment="blank name")
    date = Column(DATE, nullable=False, comment="日期", index=True)
    major_net_inflow = Column(DECIMAL(15, 2), nullable=True, comment="主力净流入")
    super_net_inflow = Column(DECIMAL(15, 2), nullable=True, comment="超大单净流入")
    big_net_inflow = Column(DECIMAL(15, 2), nullable=True, comment="大单净流入")
    medium_net_inflow = Column(DECIMAL(15, 2), nullable=True, comment="中单净流入")
    small_net_inflow = Column(DECIMAL(15, 2), nullable=True, comment="小单净流入")
    major_rate = Column(DECIMAL(15, 2), nullable=True, comment="主力净流入占比")
    super_rate = Column(DECIMAL(15, 2), nullable=True, comment="超大单净流入占比")
    big_rate = Column(DECIMAL(15, 2), nullable=True, comment="大单净流入占比")
    medium_rate = Column(DECIMAL(15, 2), nullable=True, comment="中单净流入占比")
    small_rate = Column(DECIMAL(15, 2), nullable=True, comment="小单净流入占比")


class HkStock(IdModelMixin, DateTimeModelMixin):
    __tablename__ = f"{TablePrefix}_hk_stock"

    code = Column(
        VARCHAR(12), index=True, nullable=False, comment="stock code", unique=True
    )
    name = Column(VARCHAR(64), nullable=False, comment="stock name")
    newest_price = Column(DECIMAL(15, 2), nullable=True, comment="newest price")
    price_change = Column(DECIMAL(15, 2), nullable=True, comment="涨跌额")
    price_change_rate = Column(DECIMAL(15, 2), nullable=True, comment="涨跌幅")
    volume = Column(DECIMAL(15, 2), nullable=True, comment="成交量")
    amount = Column(DECIMAL(15, 2), nullable=True, comment="成交额")
    is_index = Column(BOOLEAN, nullable=False, default=True, comment="是否指数")


class UsStock(IdModelMixin, DateTimeModelMixin):
    __tablename__ = f"{TablePrefix}_us_stock"

    code = Column(
        VARCHAR(12), index=True, nullable=False, comment="stock code", unique=True
    )
    name = Column(VARCHAR(64), nullable=False, comment="stock name")
    newest_price = Column(DECIMAL(15, 2), nullable=True, comment="newest price")
    price_change = Column(DECIMAL(15, 2), nullable=True, comment="涨跌额")
    price_change_rate = Column(DECIMAL(15, 2), nullable=True, comment="涨跌幅")
    volume = Column(DECIMAL(15, 2), nullable=True, comment="成交量")
    amount = Column(DECIMAL(15, 2), nullable=True, comment="成交额")
    is_index = Column(BOOLEAN, nullable=False, default=True, comment="是否指数")


class AnalyzeZhUnderlyingAsset(IdModelMixin, DateTimeModelMixin):
    __tablename__ = f"{TablePrefix}_analyze_zh_underlying_asset"

    code = Column(VARCHAR(12), index=True, nullable=False, comment="asset code")
    name = Column(VARCHAR(64), nullable=False, comment="asset name")
    is_blank = Column(BOOLEAN, nullable=False, default=False, comment="是否板块")
    is_concept = Column(BOOLEAN, nullable=False, default=False, comment="是否概念")
