from datetime import datetime, date
from typing import Literal, List

from loguru import logger

from src.domain.store.aggregate.zh.blank import ZhStockBlankAggregate, ZhStockBlankFundAggregate
from src.domain.store.aggregate.zh.kline import KLineModel
from src.domain.store.aggregate.zh.stock import (
    ZhStockStoreAggregate, ZhStockFundAggregate,
)
from src.domain.store.aggregate.zh.index import ZhIndexStoreAggregate
from src.domain.store.repo import dao
from src.domain.store.repo.dao import K<PERSON>ineDaily, ZhStockBlankFund
from src.infra.app import app


class ZhStockStoreService:

    def __init__(self):
        self._zh_stock_store_agg = ZhStockStoreAggregate()
        self._zh_stock_fund_agg = ZhStockFundAggregate()
    
    
    def get_stock_quotes(self, code: str) -> dao.ZhStock:
        return self._zh_stock_store_agg.get_stock_quotes_from_db(code)

    # 同步行情数据
    def sync_stock_quotes(self):
        logger.info("Syncing stock quotes...")
        self._zh_stock_store_agg.just_update_stock_quotes()
        logger.info("Syncing stock quotes done.")

    # 同步单个
    def sync_one_stock_line(self, code: str, start_date: datetime, end_date: datetime):
        self._zh_stock_store_agg.update_single_stock_kline(code, start_date, end_date)

    # 同步K线数据
    def sync_all_stock_kline(self, start_date: datetime, end_date: datetime):
        self._zh_stock_store_agg.update_stock_all_info(
            start_date, end_date, is_update_quotes=False
        )

    # 获取 30分钟K线
    def get_stock_30min_kline(
        self, code: str, start_date: datetime, end_date: datetime
    ):
        return self._zh_stock_store_agg.kline_30min.list_from_db(
            code, start_date, end_date
        )

    # 获取 60分钟K线
    def get_stock_60min_kline(
        self, code: str, start_date: datetime, end_date: datetime
    ):
        return self._zh_stock_store_agg.kline_60min.list_from_db(
            code, start_date, end_date
        )

    # 获取 日K线
    def get_stock_daily_kline(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> List[KLineDaily]:
        return self._zh_stock_store_agg.kline_daily.list_from_db(
            code, start_date, end_date
        )

    def list_stock_fund_flow(self, code: str, start_date: datetime, end_date: datetime, is_db: bool = True
                             ) -> List[dao.ZhStockFund]:
        if is_db:
            return self._zh_stock_fund_agg.list_stock_fund_flow_from_db(code, start_date, end_date)
        else:
            return self._zh_stock_fund_agg.list_stock_fund_flow_from_api(code, start_date, end_date)

    # 获取个股融资融劵
    def list_stock_margin_trading(self, code: str, start_date: date, end_date: date, is_db: bool = True
                                  ) -> List[dao.ZhStockMarginTrading]:
        if is_db:
            return self._zh_stock_fund_agg.list_stock_margin_trading_from_db(code, start_date, end_date)
        else:
            return self._zh_stock_fund_agg.list_stock_margin_trading_from_api(code, start_date, end_date)

    # 获取个股盘口异动
    def list_stock_uma(self, code: str, start_date: datetime, end_date: datetime
                       ) -> List[dao.ZhStockUMA]:
        return self._zh_stock_fund_agg.list_stock_uma_from_db(code, start_date, end_date)

    # 同步个股资金流
    def sync_stock_fund_flow(self, code: str, before_days: int = 60):
        self._zh_stock_fund_agg.collect_stock_fund_flow(code, before_days)
        
        
    # 同步所有个股资金流
    def sync_all_stock_fund_flow(self, before_days: int = 1):
        self._zh_stock_fund_agg.collect_all_stock_fund_flow(before_days)

    # 同步个股融资融劵
    def sync_stock_margin_trading(self, code: str, start_date: date, end_date: date):
        self._zh_stock_fund_agg.collect_stock_margin_trading(code, start_date, end_date)

    # 同步个股盘口异动
    def sync_stock_uma(self, code: str, before_days: int = 60):
        self._zh_stock_fund_agg.collect_stock_uma(code, before_days)
    
    def sync_all_stock_uma(self, before_days: int = 1):
        self._zh_stock_fund_agg.collect_all_stock_uma(before_days)
        
        
    def only_sync_stock_uma_which_in_db(self, before_days: int = 1):
        self._zh_stock_fund_agg.collect_stock_uma_which_in_db(before_days)


class ZhIndexStoreService:

    def __init__(self):
        self._zh_index_store_agg = ZhIndexStoreAggregate()

    def list_index(
        self,
        choice_type: Literal[
            "沪深重要指数", "上证系列指数", "深证系列指数", "指数成份", "中证系列指数"
        ],
    ):
        self._zh_index_store_agg.list_index_from_api(choice_type)

    def add_index(
        self,
        code: str,
        choice_type: Literal[
            "沪深重要指数", "上证系列指数", "深证系列指数", "指数成份", "中证系列指数"
        ],
    ):
        self._zh_index_store_agg.add_index(code, choice_type)
    
    # 删除指数   
    def delete_index(self, code: str):
        self._zh_index_store_agg.delete_index(code)
    
    # 同步指数行情
    def sync_index_quotes(self):
        self._zh_index_store_agg.just_update_index_quotes()
    
    # 同步指数K线
    def sync_index_kline(self, start_date: datetime, end_date: datetime):
        self._zh_index_store_agg.update_index_all_info(
            start_date, end_date, is_update_quotes=False
        )


class ZhBlankStoreService:
    
    def __init__(self):
        self._zh_blank_store_agg = ZhStockBlankAggregate()
        self._zh_blank_fund_store_agg = ZhStockBlankFundAggregate()
    
    # 列出板块数据
    def list_stock_blank(self, is_concept: bool = False):
        return self._zh_blank_store_agg.list_stock_blank_from_db(is_concept)
    
    # 初始化板块数据
    def init_stock_blank(self, is_concept: bool = False):
        self._zh_blank_store_agg.init_stock_blank(is_concept)
    
    # 更新板块数据
    def update_stock_blank(self, is_concept: bool = False):
        self._zh_blank_store_agg.update_stock_blank(is_concept)
        
        
    # 列出板块资金流
    def list_stock_blank_fund(self, is_concept: bool, start_date: date, end_date: date) ->  List[ZhStockBlankFund]:
        return self._zh_blank_store_agg.list_stock_blank_fun_from_db(start_date, end_date, is_concept)
    
    # 收集板块资金流
    def collect_stock_blank_fund(self):
        self._zh_blank_fund_store_agg.collect_stock_blank_fund()




zh_stock_service = ZhStockStoreService()
zh_index_service = ZhIndexStoreService()
zh_blank_service = ZhBlankStoreService()

