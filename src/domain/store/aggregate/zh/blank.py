from datetime import date, datetime
from typing import List

from loguru import logger
from sqlalchemy import select

from src.domain.store.repo import dao
from src.infra.app import app
from src.infra.clients.mysql.orm import bulk_insert, bulk_update_v2
from src.infra.clients.trading.ak import AkShareClient


class ZhStockBlankAggregate:

    def __init__(self):
        self._ak_share_client = AkShareClient()

    def list_stock_blank_from_api(
        self, is_concept: bool = False
    ) -> List[dao.ZhStockBlank]:
        if is_concept:
            df = self._ak_share_client.list_stock_concept()
        else:
            df = self._ak_share_client.list_stock_industry()
        resp: List[dao.ZhStockBlank] = []
        for _, row in df.iterrows():
            resp.append(
                dao.ZhStockBlank(
                    code=row["板块代码"],
                    name=row["板块名称"],
                    newest_price=row["最新价"],
                    price_change=row["涨跌额"],
                    price_change_rate=row["涨跌幅"],
                    turnover=row["换手率"],
                    gainers=row["上涨家数"],
                    losers=row["下跌家数"],
                    is_concept=is_concept,
                )
            )
        return resp

    def list_stock_blank_from_db(
        self, is_concept: bool = False
    ) -> List[dao.ZhStockBlank]:
        with app.orm_session() as session:
            return (
                session.query(dao.ZhStockBlank)
                .filter(dao.ZhStockBlank.is_concept == is_concept)
                .all()
            )
        
    def list_stock_blank_fun_from_db(self,start_date: date, end_date: date, is_concept: bool = False
    ) -> List[dao.ZhStockBlankFund]:
        with app.orm_session() as session:
            stmt = (
                select(dao.ZhStockBlankFund)
                .join(dao.ZhStockBlank, dao.ZhStockBlank.name == dao.ZhStockBlankFund.name)
                .where(dao.ZhStockBlank.is_concept == is_concept).
                where(dao.ZhStockBlankFund.date >= start_date).
                where(dao.ZhStockBlankFund.date <= end_date)
            )

            return session.execute(stmt).scalars().all()

    def init_stock_blank(self, is_concept: bool = False):
        """
        初始化股票板块数据到数据库。
        :return:
        """
        if len(self.list_stock_blank_from_db(is_concept=is_concept)) != 0:
            logger.info("数据库中已有板块数据，跳过初始化数据")
            return

        api_data = self.list_stock_blank_from_api(is_concept=is_concept)
        if not api_data:
            return

        with app.orm_session() as session:
            bulk_insert(session, dao.ZhStockBlank, api_data)
            session.commit()

    def update_stock_blank(self, is_concept: bool = False):
        """

        :return:
        """
        api_data = self.list_stock_blank_from_api(is_concept=is_concept)
        logger.info(f"获取到 {len(api_data)} 条板块数据, is_concept: {is_concept}")
        if not api_data:
            return

        blank_obj_data = self.list_stock_blank_from_db(is_concept=is_concept)
        if not blank_obj_data:
            return
        logger.info(f"数据库中已有 {len(blank_obj_data)} 条板块数据, is_concept: {is_concept}")
        with app.orm_session() as session:
            for api_obj in api_data:
                for blank_obj in blank_obj_data:
                    if api_obj.code == blank_obj.code:
                        blank_obj.name = api_obj.name
                        blank_obj.newest_price = api_obj.newest_price
                        blank_obj.price_change = api_obj.price_change
                        blank_obj.price_change_rate = api_obj.price_change_rate
                        blank_obj.turnover = api_obj.turnover
                        blank_obj.gainers = api_obj.gainers
                        blank_obj.losers = api_obj.losers
                        blank_obj.updated_at = datetime.now()
                        break
                else:
                    # 不存在的进行新增
                    session.add(api_obj)
            bulk_update_v2(session, dao.ZhStockBlank, blank_obj_data)
            session.commit()


class ZhStockBlankFundAggregate:

    def __init__(self):
        self._ak_share_client = AkShareClient()

    def list_stock_blank_fund_from_api(
        self, is_concept: bool = False
    ) -> List[dao.ZhStockBlankFund]:

        sector_type = "概念资金流" if is_concept else "行业资金流"

        df = self._ak_share_client.list_stock_fund_flow_rank(
            indicator="今日", sector_type=sector_type
        )
        resp: List[dao.ZhStockBlankFund] = []
        for _, row in df.iterrows():
            resp.append(
                dao.ZhStockBlankFund(
                    name=row["名称"],
                    date=datetime.now().date(),
                    major_net_inflow=row["今日主力净流入-净额"],
                    super_net_inflow=row["今日超大单净流入-净额"],
                    big_net_inflow=row["今日大单净流入-净额"],
                    medium_net_inflow=row["今日中单净流入-净额"],
                    small_net_inflow=row["今日小单净流入-净额"],
                    major_rate=row["今日主力净流入-净占比"],
                    super_rate=row["今日超大单净流入-净占比"],
                    big_rate=row["今日大单净流入-净占比"],
                    medium_rate=row["今日中单净流入-净占比"],
                    small_rate=row["今日小单净流入-净占比"],
                )
            )
        return resp

    def list_stock_blank_fund_from_db(
        self, name: str, start_date: date, end_date: date
    ) -> List[dao.ZhStockBlankFund]:
        with app.orm_session() as session:
            return (
                session.query(dao.ZhStockBlankFund)
                .filter(dao.ZhStockBlankFund.name == name)
                .filter(dao.ZhStockBlankFund.date >= start_date)
                .filter(dao.ZhStockBlankFund.date <= end_date)
                .all()
            )
    
    

    
    
    def collect_stock_blank_fund(self):
        concept_api_data = self.list_stock_blank_fund_from_api(is_concept=True)
        logger.info(f"获取到 {len(concept_api_data)} 条概念资金流数据")
        industry_api_data = self.list_stock_blank_fund_from_api(is_concept=False)
        logger.info(f"获取到 {len(industry_api_data)} 条行业资金流数据")
        api_data = concept_api_data + industry_api_data
        if not api_data:
            return

        with app.orm_session() as session:
            bulk_insert(session, dao.ZhStockBlankFund, api_data)
            session.commit()
        logger.info(f"资金流数据更新完成，共处理 {len(api_data)} 条数据")
