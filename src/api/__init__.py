from src.infra.app import app
from src.infra.web.api import sys_router
from src.api.v1 import low_api
from src.api.v1 import high_level

API_V1 = "/api/v1"

app.http_server.register_router(router=sys_router, tags=["system"])

app.http_server.register_router(
    router=low_api.store, tags=["low-api.store"], prefix=API_V1
)
app.http_server.register_router(
    router=low_api.local, tags=["low-api.local"], prefix=API_V1
)

# monitor
app.http_server.register_router(
    router=high_level.monitor.monitor_price_router, tags=["high-level.monitor.price"], prefix=API_V1
)
app.http_server.register_router(
    router=high_level.monitor.monitor_realtime_router, tags=["high-level.monitor.realtime"], prefix=API_V1
)

# analyze
app.http_server.register_router(
    router=high_level.analyze.analyze_router, tags=["high-level.analyze"], prefix=API_V1
)
