from datetime import date, datetime
import json
import os
from pathlib import Path

from src.api.v1.low_api import dto
from src.domain.store.service.zh import (
    zh_stock_service,
    zh_index_service,
    zh_blank_service,
)
from src.infra.app import app
from src.infra.web import const
from src.infra.web.http import http_server

store = http_server.new_router()

URI_PREFIX = "/store"
Zh_STOCK_URI_PREFIX = f"{URI_PREFIX}/zh/stock"
Zh_INDEX_URI_PREFIX = f"{URI_PREFIX}/zh/index"
Zh_BLANK_URI_PREFIX = f"{URI_PREFIX}/zh/blank"


@store.post(f"{Zh_STOCK_URI_PREFIX}/quotes/sync")
def sync_stock_quotes():
    app.submit_concurrent_task(zh_stock_service.sync_stock_quotes)
    return {"message": const.RESP_SUCCEED}


@store.post(f"{Zh_STOCK_URI_PREFIX}/kline/one/sync")
def sync_one_stock_kline(req: dto.SyncOneStockKlineReq):
    app.submit_concurrent_task(
        zh_stock_service.sync_one_stock_line,
        req.code,
        req.start_date,
        req.end_date,
    )
    return {"message": const.RESP_SUCCEED}


@store.post(f"{Zh_STOCK_URI_PREFIX}/kline/all/sync")
def sync_all_stock_kline(req: dto.SyncAllStockKlineReq):
    zh_stock_service.sync_all_stock_kline(
        start_date=req.start_date,
        end_date=req.end_date,
    )
    return {"message": const.RESP_SUCCEED}


@store.post(f"{Zh_STOCK_URI_PREFIX}/fund/flow/sync")
def sync_stock_fund_flow(req: dto.SyncStockFundFlowReq):
    zh_stock_service.sync_stock_fund_flow(req.code, req.before_days)
    return {"message": const.RESP_SUCCEED}

@store.post(f"{Zh_STOCK_URI_PREFIX}/fund/flow/all/sync")
def sync_all_stock_fund_flow(req: dto.SyncAllStockFundFlowReq):
    zh_stock_service.sync_all_stock_fund_flow(req.before_days)
    return {"message": const.RESP_SUCCEED}



@store.post(f"{Zh_STOCK_URI_PREFIX}/margin/trading/sync")
def sync_stock_margin_trading(req: dto.SyncStockMarginTradingReq):
    zh_stock_service.sync_stock_margin_trading(req.code, req.start_date, req.end_date)
    return {"message": const.RESP_SUCCEED}


@store.post(f"{Zh_STOCK_URI_PREFIX}/uma/sync")
def sync_stock_uma(req: dto.SyncStockUMAReq):
    zh_stock_service.sync_stock_uma(req.code, req.before_days)
    return {"message": const.RESP_SUCCEED}

@store.post(f"{Zh_STOCK_URI_PREFIX}/uma/all/sync")
def sync_all_stock_uma(req: dto.SyncAllStockUMAReq):
    zh_stock_service.sync_all_stock_uma(req.before_days)
    return {"message": const.RESP_SUCCEED}


@store.get(f"{Zh_STOCK_URI_PREFIX}/uma/list")
def list_stock_uma(code: str, start_date: date, end_date: date):
    return zh_stock_service.list_stock_uma(code, start_date, end_date)


@store.get(f"{Zh_STOCK_URI_PREFIX}/fund/flow/list")
def list_stock_fund_flow(
    code: str, start_date: datetime, end_date: datetime, is_db: bool = True
):
    return zh_stock_service.list_stock_fund_flow(code, start_date, end_date, is_db)


@store.get(f"{Zh_STOCK_URI_PREFIX}/margin/trading/list")
def list_stock_margin_trading(
    code: str, start_date: date, end_date: date, is_db: bool = True
):
    return zh_stock_service.list_stock_margin_trading(code, start_date, end_date, is_db)





# =================== Index ===================


@store.post(f"{Zh_INDEX_URI_PREFIX}/add")
def add_index(req: dto.AddIndexReq):
    zh_index_service.add_index(req.code, req.choice_type)
    return {"message": const.RESP_SUCCEED}


@store.delete(f"{Zh_INDEX_URI_PREFIX}/delete")
def delete_index(req: dto.DeleteIndexReq):
    zh_index_service.delete_index(req.code)
    return {"message": const.RESP_SUCCEED}


@store.post(f"{Zh_INDEX_URI_PREFIX}/quotes/sync")
def sync_index_quotes():
    zh_index_service.sync_index_quotes()
    return {"message": const.RESP_SUCCEED}


@store.post(f"{Zh_INDEX_URI_PREFIX}/kline/sync")
def sync_index_kline(req: dto.SyncIndexKlineReq):
    zh_index_service.sync_index_kline(
        start_date=req.start_date,
        end_date=req.end_date,
    )
    return {"message": const.RESP_SUCCEED}


# =================== Blank ===================
@store.post(f"{Zh_BLANK_URI_PREFIX}/init")
def init_stock_blank(req: dto.IsConceptReq):
    zh_blank_service.init_stock_blank(req.is_concept)
    return {"message": const.RESP_SUCCEED}


@store.post(f"{Zh_BLANK_URI_PREFIX}/update")
def update_stock_blank(req: dto.IsConceptReq):
    zh_blank_service.update_stock_blank(req.is_concept)
    return {"message": const.RESP_SUCCEED}


@store.post(f"{Zh_BLANK_URI_PREFIX}/fund/collect")
def collect_stock_blank_fund():
    zh_blank_service.collect_stock_blank_fund()
    return {"message": const.RESP_SUCCEED}


@store.get(f"{Zh_BLANK_URI_PREFIX}/fund/list")
def list_stock_blank_fund(is_concept: bool, start_date: date, end_date: date):
    return zh_blank_service.list_stock_blank_fund(is_concept, start_date, end_date)


@store.get(f"{Zh_BLANK_URI_PREFIX}/list")
def list_stock_blank(is_concept: bool = False):
    return zh_blank_service.list_stock_blank(is_concept)



