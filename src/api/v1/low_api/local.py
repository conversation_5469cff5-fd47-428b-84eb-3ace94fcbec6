from datetime import datetime
import json
from pathlib import Path

from src.api.v1.low_api import dto
from src.domain.store.service.zh import zh_stock_service
from src.infra.web.http import http_server

local = http_server.new_router()

URI_PREFIX = "/local"
Zh_STOCK_URI_PREFIX = f"{URI_PREFIX}/zh/stock"


# =================== Stock Info Management ===================

@local.get(f"{Zh_STOCK_URI_PREFIX}/info")
def get_stock_info(code: str):
    """获取股票基本信息"""
    try:
        stock_quotes = zh_stock_service.get_stock_quotes(code)
        if stock_quotes:
            return {
                "code": 0,
                "message": "获取成功",
                "data": {
                    "code": stock_quotes.code,
                    "name": stock_quotes.name,
                    "market": stock_quotes.market,
                    "newest_price": float(stock_quotes.newest_price) if stock_quotes.newest_price else None,
                    "price_change": float(stock_quotes.price_change) if stock_quotes.price_change else None,
                    "price_change_rate": float(stock_quotes.price_change_rate) if stock_quotes.price_change_rate else None,
                    "volume": float(stock_quotes.volume) if stock_quotes.volume else None,
                    "amount": float(stock_quotes.amount) if stock_quotes.amount else None,
                    "market_cap": float(stock_quotes.market_cap) if stock_quotes.market_cap else None,
                    "pe_rate": float(stock_quotes.pe_rate) if stock_quotes.pe_rate else None
                }
            }
        else:
            return {
                "code": 1,
                "message": f"未找到股票 {code} 的信息",
                "data": None
            }
    except Exception as e:
        return {
            "code": 1,
            "message": f"获取股票信息失败: {str(e)}",
            "data": None
        }


# =================== Stock Config Management ===================

@local.post(f"{Zh_STOCK_URI_PREFIX}/config/add")
def add_stock_to_config(req: dto.AddStockToConfigReq):
    """添加新股票到配置文件"""
    try:
        # 获取配置文件路径
        config_file = Path("assets/analysis/zh_stock/stocks.json")

        if not config_file.exists():
            return {"code": 1, "message": "配置文件不存在", "data": None}

        # 读取现有配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        # 检查股票是否已存在
        existing_codes = [stock['code'] for stock in config_data.get('stocks', [])]
        if req.code in existing_codes:
            return {"code": 1, "message": f"股票 {req.code} 已存在", "data": None}

        # 添加新股票
        new_stock = {
            "code": req.code,
            "name": req.name
        }
        config_data['stocks'].append(new_stock)

        # 更新统计信息
        config_data['totalStocks'] = len(config_data['stocks'])
        config_data['lastUpdated'] = datetime.now().isoformat()

        # 保存配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)

        return {
            "code": 0,
            "message": "股票添加成功",
            "data": {
                "stock": new_stock,
                "totalStocks": config_data['totalStocks']
            }
        }

    except Exception as e:
        return {"code": 1, "message": f"添加股票失败: {str(e)}", "data": None}


@local.delete(f"{Zh_STOCK_URI_PREFIX}/config/remove")
def remove_stock_from_config(req: dto.RemoveStockFromConfigReq):
    """从配置文件中删除股票并清除相关数据"""
    try:
        # 获取配置文件路径
        config_file = Path("assets/analysis/zh_stock/stocks.json")

        if not config_file.exists():
            return {"code": 1, "message": "配置文件不存在", "data": None}

        # 读取现有配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        # 查找要删除的股票
        stocks = config_data.get('stocks', [])
        stock_to_remove = None
        new_stocks = []

        for stock in stocks:
            if stock['code'] == req.code:
                stock_to_remove = stock
            else:
                new_stocks.append(stock)

        if not stock_to_remove:
            return {"code": 1, "message": f"股票 {req.code} 不存在", "data": None}

        # 更新配置
        config_data['stocks'] = new_stocks
        config_data['totalStocks'] = len(new_stocks)
        config_data['lastUpdated'] = datetime.now().isoformat()

        # 保存配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)

        # 清除相关数据目录
        cleanup_results = _cleanup_stock_data(req.code)

        return {
            "code": 0,
            "message": "股票删除成功",
            "data": {
                "removedStock": stock_to_remove,
                "totalStocks": config_data['totalStocks'],
                "cleanupResults": cleanup_results
            }
        }

    except Exception as e:
        return {"code": 1, "message": f"删除股票失败: {str(e)}", "data": None}


def _cleanup_stock_data(stock_code: str):
    """清除股票相关的数据文件和目录"""
    cleanup_results = {
        "removedFiles": [],
        "removedDirs": [],
        "errors": []
    }

    try:
        # 清除分析图表目录
        chart_dir = Path(f"assets/analysis/zh_stock/{stock_code}")
        if chart_dir.exists():
            import shutil
            shutil.rmtree(chart_dir)
            cleanup_results["removedDirs"].append(str(chart_dir))

        # 清除缓存文件中的股票名称缓存
        cache_file = Path("cache/stock_names_cache.json")
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                if stock_code in cache_data:
                    del cache_data[stock_code]
                    with open(cache_file, 'w', encoding='utf-8') as f:
                        json.dump(cache_data, f, ensure_ascii=False, indent=2)
                    cleanup_results["removedFiles"].append(f"cache entry for {stock_code}")
            except Exception as e:
                cleanup_results["errors"].append(f"清除缓存失败: {str(e)}")

        # 可以在这里添加更多清理逻辑，比如：
        # - 清除数据库中的相关记录
        # - 清除其他临时文件
        # - 清除监控配置等

    except Exception as e:
        cleanup_results["errors"].append(f"清理数据时发生错误: {str(e)}")

    return cleanup_results