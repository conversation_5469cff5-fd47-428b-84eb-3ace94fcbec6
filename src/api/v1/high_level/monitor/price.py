from src.api.v1.high_level import dto
from src.domain.monitor.schema import MonitorType
from src.domain.monitor.service.price import price_monitor_svc
from src.domain.schema import MarketTag
from src.infra.web import const
from src.infra.web.http import http_server

monitor_price_router = http_server.new_router()

URI_PREFIX = "/monitor/price"
Zh_STOCK_URI_PREFIX = f"{URI_PREFIX}/zh/stock"
Zh_INDEX_URI_PREFIX = f"{URI_PREFIX}/zh/index"
Zh_BLANK_URI_PREFIX = f"{URI_PREFIX}/zh/blank"



@monitor_price_router.get(f"{Zh_STOCK_URI_PREFIX}/list")
def list_stock_price_monitor(market_tag: MarketTag = MarketTag.zh_stock):
    return price_monitor_svc.list_monitor(market_tag)


@monitor_price_router.post(f"{Zh_STOCK_URI_PREFIX}/create")
def create_stock_price_monitor(req: dto.CreateStockPriceMonitorReq):
    price_monitor_svc.create_monitor(
        req.code,
        req.name,
        req.market_tag,
        args=req.args,
    )
    return {"message": const.RESP_SUCCEED}


@monitor_price_router.post(f"{Zh_STOCK_URI_PREFIX}/update")
def update_stock_price_monitor(req: dto.UpdateStockPriceMonitorReq):
    price_monitor_svc.update_monitor(req.id, args=req.args)
    return {"message": const.RESP_SUCCEED}


@monitor_price_router.post(f"{Zh_STOCK_URI_PREFIX}/delete")
def delete_stock_price_monitor(req: dto.DeleteMonitorReq):
    price_monitor_svc.delete_monitor(req.id)
    return {"message": const.RESP_SUCCEED}



