from src.api.v1.high_level import dto
from src.application.monitor.realtime import RealTimeMonitorApplication
from src.domain.schema import MarketTag
from src.infra.web import const
from src.infra.web.http import http_server

monitor_realtime_router = http_server.new_router()


URI_PREFIX = "/monitor/realtime"
Zh_STOCK_URI_PREFIX = f"{URI_PREFIX}/zh/stock"
Zh_INDEX_URI_PREFIX = f"{URI_PREFIX}/zh/index"
Zh_BLANK_URI_PREFIX = f"{URI_PREFIX}/zh/blank"


realtime_monitor_app = RealTimeMonitorApplication()

@monitor_realtime_router.get(f"{Zh_STOCK_URI_PREFIX}/list")
def list_stock_realtime_monitor(market_tag: MarketTag = MarketTag.zh_stock):
    return realtime_monitor_app.list_object(market_tag)


@monitor_realtime_router.post(f"{Zh_STOCK_URI_PREFIX}/create")
def create_stock_realtime_monitor(req: dto.CreateStockRealTimeMonitorReq):
    realtime_monitor_app.create_object(
        req.code,
        req.name,
        req.market_tag,
        req.price_monitor_args,
    )
    return {"message": const.RESP_SUCCEED}


@monitor_realtime_router.post(f"{Zh_STOCK_URI_PREFIX}/update")
def update_stock_realtime_monitor(req: dto.UpdateStockRealTimeMonitorReq):
    realtime_monitor_app.update_object(req.id, req.price_monitor_args)
    return {"message": const.RESP_SUCCEED}


@monitor_realtime_router.post(f"{Zh_STOCK_URI_PREFIX}/delete")
def delete_stock_realtime_monitor(req: dto.DeleteRealTimeMonitorReq):
    realtime_monitor_app.delete_object(req.id)
    return {"message": const.RESP_SUCCEED}
