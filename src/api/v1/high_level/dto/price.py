from pydantic import BaseModel, Field

from src.domain.monitor.repo import do
from src.domain.schema import MarketTag


class CreateStockPriceMonitorReq(BaseModel):
    code: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    market_tag: MarketTag = Field(..., description="股票市场标签")
    args: do.MonitorPriceArgs = Field(..., description="监控参数")
    
    
class UpdateStockPriceMonitorReq(BaseModel):
    id: int = Field(..., description="监控对象ID")
    args: do.MonitorPriceArgs = Field(..., description="监控参数")
    
class DeleteMonitorReq(BaseModel):
    id: int = Field(..., description="监控对象ID")
    


class CreateStockRealTimeMonitorReq(BaseModel):
    code: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    market_tag: MarketTag = Field(..., description="股票市场标签")
    price_monitor_args: do.MonitorPriceArgs = Field(..., description="价格监控参数")


class UpdateStockRealTimeMonitorReq(BaseModel):
    id: int = Field(..., description="监控对象ID")
    price_monitor_args: do.MonitorPriceArgs = Field(..., description="价格监控参数")


class DeleteRealTimeMonitorReq(BaseModel):
    id: int = Field(..., description="监控对象ID")
