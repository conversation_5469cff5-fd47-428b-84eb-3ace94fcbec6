from datetime import date, timedelta
from typing import List, Dict, Any

from src.api.v1.high_level import dto
from src.domain.analyze.service.analysis import zh_analysis_svc
from src.domain.store.aggregate.zh.blank import ZhStockBlankAggregate
from src.infra.web import const
from src.infra.web.http import http_server

analyze_router = http_server.new_router()

URI_PREFIX = "/analyze"
Zh_STOCK_URI_PREFIX = f"{URI_PREFIX}/zh/stock"
Zh_BLANK_URI_PREFIX = f"{URI_PREFIX}/zh/blank"


@analyze_router.post(f"{Zh_STOCK_URI_PREFIX}/analysis/fund-flow")
def analyze_zh_stock_fund_flow(req: dto.AnalyzeStockFundFlowReq):
    zh_analysis_svc.analyze_zh_stock_fund_flow(req.code)
    return {"message": const.RESP_SUCCEED}


@analyze_router.get(f"{Zh_BLANK_URI_PREFIX}/fund-flow")
def get_blank_fund_flow_data(days: int = 300):
    """获取板块资金流数据"""
    blank_agg = ZhStockBlankAggregate()

    # 计算日期范围
    end_date = date.today()
    start_date = end_date - timedelta(days=days)

    # 获取行业板块资金流数据 (is_concept=False)
    fund_data = blank_agg.list_stock_blank_fun_from_db(
        start_date=start_date,
        end_date=end_date,
        is_concept=False
    )

    if not fund_data:
        return {"dates": [], "blank_data": {}, "blank_names": []}

    # 转换为字典格式
    data_dict = {}
    for item in fund_data:
        blank_name = item.name
        if blank_name not in data_dict:
            data_dict[blank_name] = []
        data_dict[blank_name].append({
            "date": item.date.strftime("%Y-%m-%d"),
            "major": item.major_net_inflow / 10000,  # 转换为万元
            "super": item.super_net_inflow / 10000,
            "big": item.big_net_inflow / 10000,
            "medium": item.medium_net_inflow / 10000,
            "small": item.small_net_inflow / 10000,
        })

    # 获取所有日期
    all_dates = set()
    for blank_data in data_dict.values():
        for item in blank_data:
            all_dates.add(item["date"])

    dates = sorted(list(all_dates))

    # 重新组织数据格式，确保每个板块都有完整的日期序列
    blank_data = {}
    for blank_name, items in data_dict.items():
        # 创建日期到数据的映射
        date_map = {item["date"]: item for item in items}

        # 为每个日期填充数据，缺失的用0填充
        major_data = []
        for date_str in dates:
            if date_str in date_map:
                major_data.append(date_map[date_str]["major"])
            else:
                major_data.append(0)

        blank_data[blank_name] = {
            "major": major_data,
            "dates": dates
        }

    blank_names = list(blank_data.keys())

    return {
        "dates": dates,
        "blank_data": blank_data,
        "blank_names": blank_names
    }
