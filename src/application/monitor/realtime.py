import json
from typing import List

from loguru import logger

from src.domain.monitor.repo import do, dao
from src.domain.monitor.schema import MonitorType
from src.domain.monitor.service.price import PriceMonitorService
from src.domain.monitor.service.volume import VolumeMonitorService
from src.domain.schema import MarketTag


class RealTimeMonitorApplication:
    
    
    def __init__(self):
        self._price_monitor_svc = PriceMonitorService()
        self._volume_monitor_svc = VolumeMonitorService()
    
    
    def create_object(self, 
                      code: str,
                      name: str, 
                      market_tag: MarketTag,
                      price_monitor_args: do.MonitorPriceArgs):
        
        price_m_id = None
        volume_m_id = None
        try:
            price_m_id = self._price_monitor_svc.create_monitor(code, name, market_tag, price_monitor_args)
            volume_m_id = self._volume_monitor_svc.create_monitor(code, name, market_tag)
        except Exception as e:
            logger.exception(f"创建监控对象失败: {e}")
            # 回滚
            if price_m_id:
                self._price_monitor_svc.delete_monitor(price_m_id)
            if volume_m_id:
                self._volume_monitor_svc.delete_monitor(volume_m_id)
    
    def update_object(self, id: int, price_monitor_args: do.MonitorPriceArgs):
        self._price_monitor_svc.update_monitor(id, args=price_monitor_args)
    
    def delete_object(self, id: int):
        self._price_monitor_svc.delete_monitor(id)
        self._volume_monitor_svc.delete_monitor(id)
    
    def list_object(self, market_tag: MarketTag) -> List[do.MonitorObject]:
        price_monitor_objects = self._price_monitor_svc.list_monitor(market_tag)
        res: List[do.MonitorObject] = []
        for obj in price_monitor_objects:
            arg = do.MonitorArgs(
                monitor_type=MonitorType.price.value,
                monitor_arg=json.loads(obj.monitor_arg),
            )
            res.append(
                do.MonitorObject(
                    id=obj.id,
                    code=obj.code,
                    name=obj.name,
                    market_tag=obj.market_tag,
                    args=[arg],
                )
            )
        return res
    
