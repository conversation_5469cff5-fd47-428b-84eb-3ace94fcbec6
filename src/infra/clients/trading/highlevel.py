from typing import Optional

from loguru import logger

from src.infra.clients.trading.ak import AkShareClient
from src.infra.clients.trading.do import StockMinuteData
from src.infra.clients.trading.mine import MyClient
from src.infra.clients.trading.utils import get_zh_latest_trading_datetime


class HighLevelTradingClient:

    def __init__(self):
        self.ak_cli = AkShareClient()
        self.my_cli = MyClient()

    def get_zh_stock_today_open(self, code: str) -> Optional[float]:
        today = get_zh_latest_trading_datetime()
        df = self.ak_cli.list_zh_stock_data_with_daily(code, today, today)
        if df.empty:
            return None
        return df.iloc[0]["开盘"]

    def get_zh_stock_current_change(self, code: str) -> Optional[float]:
        today = get_zh_latest_trading_datetime()
        df = self.ak_cli.list_zh_stock_data_with_daily(code, today, today)
        if df.empty:
            return None
        return df.iloc[-1]["涨跌幅"]

    def get_zh_stock_current_min_data(self, code: str) -> Optional[StockMinuteData]:
        df = self.my_cli.get_zh_stock_realtime_quotes(code)
        if df.empty:
            logger.info(f"未获取到股票 {code} 当前分钟数据")
            return None
        # 返回最后一条数据的成
        return StockMinuteData(
            code=code,
            open=df.iloc[-1]["开盘价"],
            close=df.iloc[-1]["收盘价"],
            high=df.iloc[-1]["最高价"],
            low=df.iloc[-1]["最低价"],
            volume=df.iloc[-1]["成交量"],
            amount=df.iloc[-1]["成交额"],
            change_rate=df.iloc[-1]["涨跌幅"],
            pre_close=df.iloc[-1]["昨收价"],
        )


high_level_trading_client = HighLevelTradingClient()
