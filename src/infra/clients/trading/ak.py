from datetime import datetime
from functools import cache
from typing import Dict, Literal, List, Union

import akshare as ak
import pandas as pd

from src.infra.clients.trading.schema import (
    DongCaiMinPeriod,
    DongCaiDailyPeriod,
    DongCaiFQ,
    DateTimeFormat,
)
from src.infra.utils import retry_network


class AkShareClient:
    TIME_INDEX = "时间"
    DATE_INDEX = "日期"

    @cache
    @retry_network
    def get_zh_stock_info(self, code: str) -> Dict[str, str]:
        """
        获取股票信息
        :return: item key: 最新，股票代码，股票简称，总股本，....
        """
        stock_info = ak.stock_individual_info_em(symbol=code)
        stock_info.set_index("item", inplace=True)
        res = {}
        for item in stock_info.index:
            res[item] = stock_info.loc[item, "value"]
        return res

    @retry_network
    def list_zh_stock_data_with_daily(
        self,
        code: str,
        start_date: datetime,
        end_date: datetime,
        period: DongCaiDailyPeriod = "daily",
        adjust: DongCaiFQ = "qfq",
    ) -> pd.DataFrame:
        """
        :return:
        日期，代码，开盘，收盘，最高，最低，成交量，成交额，振幅，涨跌幅，涨跌额，换手率
        2017-03-01  000001   9.49   9.49  ...  0.84  0.11  0.01  0.21
        """
        d_fmt = DateTimeFormat.DATE_FORMAT_1.value
        stock_his = ak.stock_zh_a_hist(
            symbol=code,
            start_date=start_date.strftime(d_fmt),
            end_date=end_date.strftime(d_fmt),
            adjust=adjust,
            period=period,
        )
        # stock_his[self.DATE_INDEX] = pd.to_datetime(stock_his[self.DATE_INDEX])
        # stock_his.set_index(self.DATE_INDEX, inplace=True)
        return stock_his

    @retry_network
    def list_zh_stock_data_with_minute(
        self,
        code: str,
        start_datetime: datetime,
        end_datetime: datetime,
        period: DongCaiMinPeriod = "1",
        adjust: DongCaiFQ = "qfq",
    ) -> pd.DataFrame:
        """

        :return: 时间，开盘，收盘，最高，最低，成交量，成交额，均价
        """
        d_fmt = DateTimeFormat.DATETIME_FORMAT_1.value
        stock_his = ak.stock_zh_a_hist_min_em(
            symbol=code,
            start_date=start_datetime.strftime(d_fmt),
            end_date=end_datetime.strftime(d_fmt),
            period=period,
            adjust=adjust,
        )
        return stock_his

    @cache
    @retry_network
    def get_etf_info(self, code: str) -> Dict[str, str]:
        """
        获取ETF信息
        :return: key: 基金代码，拼音缩写，基金简称
        """
        df = ak.fund_name_em()
        df = df[df["基金代码"] == code]
        res = {}
        for i in range(5):
            idx = i - 1
            res[df.columns[idx]] = df.values[0][idx]

        return res

    @retry_network
    def list_all_etf_info(self) -> pd.DataFrame:
        return ak.fund_name_em()

    @retry_network
    def list_etf_stock_with_minute(
        self,
        code: str,
        start_date: datetime,
        end_date: datetime,
        period: DongCaiMinPeriod = "1",
        adjust: DongCaiFQ = "qfq",
    ) -> pd.DataFrame:
        """
        :return: 时间，开盘，收盘，最高，最低，成交量，成交额，均价
        """
        d_fmt = DateTimeFormat.DATETIME_FORMAT_1.value
        etf_his = ak.fund_etf_hist_min_em(
            symbol=code,
            start_date=start_date.strftime(d_fmt),
            end_date=end_date.strftime(d_fmt),
            period=period,
            adjust=adjust,
        )
        # etf_his[self.TIME_INDEX] = pd.to_datetime(etf_his[self.TIME_INDEX])
        # etf_his.set_index(self.TIME_INDEX, inplace=True)
        return etf_his

    @retry_network
    def list_etf_stock_with_daily(
        self,
        code: str,
        start_date: datetime,
        end_date: datetime,
        period: DongCaiDailyPeriod = "daily",
        adjust: DongCaiFQ = "qfq",
    ) -> pd.DataFrame:
        """
        :return:
        日期，开盘，收盘，最高，最低，成交量，成交额，振幅，涨跌幅，涨跌额，换手率
        014-01-15  0.994  0.986  0.996  ...  0.00  0.00  0.000  1.8
        """
        d_fmt = DateTimeFormat.DATE_FORMAT_2.value
        etf_his = ak.fund_etf_hist_em(
            symbol=code,
            start_date=start_date.strftime(d_fmt),
            end_date=end_date.strftime(d_fmt),
            period=period,
            adjust=adjust,
        )
        # etf_his[self.DATE_INDEX] = pd.to_datetime(etf_his[self.DATE_INDEX])
        # etf_his.set_index(self.DATE_INDEX, inplace=True)
        return etf_his

    @retry_network
    def list_stock_cyw(self, code: str) -> pd.DataFrame:
        """
        获取主力控盘
        :return: 交易日，机构参与度
        """
        df = ak.stock_comment_detail_zlkp_jgcyd_em(symbol=code)
        df["交易日"] = pd.to_datetime(df["交易日"])
        return df

    @retry_network
    def list_stock_user_focus(self, code: str) -> pd.DataFrame:
        """
        获取用户关注度
        :return: 交易日，用户关注指数

        """
        df = ak.stock_comment_detail_scrd_focus_em(symbol=code)
        sorted_df = df.sort_values(by="交易日", ascending=False)
        sorted_df["交易日"] = pd.to_datetime(sorted_df["交易日"])
        return sorted_df

    @retry_network
    def list_stock_industry(self) -> pd.DataFrame:
        """
        获取行业板块
        :return: 排名	板块名称	板块代码	最新价	涨跌额	涨跌幅	总市值	换手率	上涨家数	下跌家数	领涨股票	领涨股票-涨跌幅
        """
        df = ak.stock_board_industry_name_em()
        return df

    @retry_network
    def list_stock_concept(self) -> pd.DataFrame:
        """
        获取概念板块
        :param code:
        :return: 排名	板块名称	板块代码	最新价	涨跌额	涨跌幅	总市值	换手率	上涨家数	下跌家数	领涨股票	领涨股票-涨跌幅
        """
        df = ak.stock_board_concept_name_em()
        return df

    @retry_network
    def get_single_stock_fund_flow(
        self, code: str, market: str, start_date: datetime, end_date: datetime
    ) -> pd.DataFrame:
        """
        获取个股资金流
        :return: 日期 主力净流入-净额	主力净流入-净占比	超大单净流入-净额	超大单净流入-净占比	大单净流入-净额	大单净流入-净占比	中单净流入-净额	中单净流入-净占比	小单净流入-净额	小单净流入-净占比
        """
        df = ak.stock_individual_fund_flow(stock=code, market=market)
        return df[(df["日期"] >= start_date.date()) & (df["日期"] <= end_date.date())]

    @retry_network
    def list_zh_blank_daily(
        self,
        blank_name: str,
        start_date: datetime,
        end_date: datetime,
        period="日k",
        adjust="",
    ) -> pd.DataFrame:
        """
        获取板块日线数据
        :return: 日期	开盘	收盘	最高	最低	成交量	成交额	振幅	涨跌幅	涨跌额	换手率
        """
        d_fmt = DateTimeFormat.DATE_FORMAT_1.value
        stock_board_industry_hist_em_df = ak.stock_board_industry_hist_em(
            symbol=blank_name,
            start_date=start_date.strftime(d_fmt),
            end_date=end_date.strftime(d_fmt),
            period=period,
            adjust=adjust,
        )
        return stock_board_industry_hist_em_df

    @retry_network
    def list_zh_blank_concept_daily(
        self,
        blank_name: str,
        start_date: datetime,
        end_date: datetime,
        period="daily",
        adjust="",
    ) -> pd.DataFrame:
        """
        获取概念板块日线数据
        :return: 日期	开盘	收盘	最高	最低	成交量	成交额	振幅	涨跌幅	涨跌额	换手率
        """
        d_fmt = DateTimeFormat.DATE_FORMAT_1.value
        stock_board_concept_hist_em_df = ak.stock_board_concept_hist_em(
            symbol=blank_name,
            start_date=start_date.strftime(d_fmt),
            end_date=end_date.strftime(d_fmt),
            period=period,
            adjust=adjust,
        )
        return stock_board_concept_hist_em_df

    @retry_network
    def list_stock_fund_flow_rank(
        self,
        indicator: Literal["今日", "5日", "10日"],
        sector_type: Union[Literal["行业资金流", "概念资金流"], str],
    ) -> pd.DataFrame:
        """
        获取板块资金流
        :return: 号	名称	今日涨跌幅	今日主力净流入-净额	今日主力净流入-净占比	今日超大单净流入-净额	今日超大单净流入-净占比	今日大单净流入-净额	今日大单净流入-净占比	今日中单净流入-净额	今日中单净流入-净占比	今日小单净流入-净额	今日小单净流入-净占比	今日主力净流入最大股
        """
        df = ak.stock_sector_fund_flow_rank(
            indicator=indicator, sector_type=sector_type
        )
        return df

    @retry_network
    def list_stock_industry_cons(self, code: str) -> pd.DataFrame:
        """
        获取行业成分股
        :return: 序号	代码	名称	最新价	涨跌幅	涨跌额	成交量	成交额	振幅	最高	最低	今开	昨收	换手率	市盈率-动态	市净率
        """
        df = ak.stock_board_industry_cons_em(symbol=code)
        return df

    @retry_network
    def list_stock_concept_cons(self, code: str) -> pd.DataFrame:
        """
        获取概念板块成分股
        :return: 序号	代码	名称	最新价	涨跌幅	涨跌额	成交量	成交额	振幅	最高	最低	今开	昨收	换手率	市盈率-动态	市净率
        """
        df = ak.stock_board_concept_cons_em(symbol=code)
        return df

    @retry_network
    def list_main_stock_index(
        self,
        symbol: Union[
            Literal[
                "沪深重要指数",
                "上证系列指数",
                "深证系列指数",
                "指数成份",
                "中证系列指数",
            ],
            str,
        ],
    ) -> pd.DataFrame:
        """
        获取重要指数列表
        :return: 序号	代码	名称	最新价	涨跌幅	涨跌额	成交量	成交额	振幅	最高	最低	今开	昨收	量比
        """
        return ak.stock_zh_index_spot_em(symbol=symbol)

    @retry_network
    def list_index_data_with_daily(
        self,
        code: str,
        start_date: datetime,
        end_date: datetime,
        period: DongCaiDailyPeriod = "daily",
    ) -> pd.DataFrame:
        """
        指数历史行情数据
        :return: 日期	开盘	收盘	最高	最低	成交量	成交额	振幅	涨跌幅	涨跌额	换手率
        """
        d_fmt = DateTimeFormat.DATE_FORMAT_2.value
        ak_df = ak.index_zh_a_hist(
            symbol=code,
            period=period,
            start_date=start_date.strftime(d_fmt),
            end_date=end_date.strftime(d_fmt),
        )
        # ak_df[self.DATE_INDEX] = pd.to_datetime(ak_df[self.DATE_INDEX])
        # ak_df.set_index(self.DATE_INDEX, inplace=True)
        return ak_df

    @retry_network
    def list_index_data_with_minute(
        self,
        code: str,
        period: DongCaiMinPeriod = "1",
        start_date: datetime = None,
        end_date: datetime = None,
    ) -> pd.DataFrame:
        """
        指数分时行情
        :return: 时间	开盘	收盘	最高	最低	涨跌幅	涨跌额	成交量	成交额	振幅	换手率
        """
        d_fmt = DateTimeFormat.DATETIME_FORMAT_1.value
        ak_df = ak.index_zh_a_hist_min_em(
            symbol=code,
            period=period,
            start_date=start_date.strftime(d_fmt),
            end_date=end_date.strftime(d_fmt),
        )
        # ak_df[self.TIME_INDEX] = pd.to_datetime(ak_df[self.TIME_INDEX])
        # ak_df.set_index(self.TIME_INDEX, inplace=True)
        return ak_df

    @retry_network
    def list_hk_stock_spot(self) -> pd.DataFrame:
        """
        港股实时行情数据
        :return:   序号	代码	名称	最新价	涨跌额	涨跌幅	今开	最高	最低	昨收	成交量	成交额

        """
        return ak.stock_hk_main_board_spot_em()

    @retry_network
    def list_hk_index_spot(self) -> pd.DataFrame:
        """
        港股指数实时行情数据
        :return: 序号	内部编号	代码	名称	最新价	涨跌额	涨跌幅	今开	最高	最低	昨收	成交量	成交额
        """
        return ak.stock_hk_index_spot_em()

    @retry_network
    def list_hk_index_with_daily(self, code: str) -> pd.DataFrame:
        """
        港股历史行情数据
        :return: 日期	开盘	收盘	最高	最低	成交量	成交额	振幅	涨跌幅	涨跌额	换手率
        """
        ak_df = ak.stock_hk_index_daily_em(symbol=code)
        ak_df[self.DATE_INDEX] = pd.to_datetime(ak_df[self.DATE_INDEX])
        ak_df.set_index(self.DATE_INDEX, inplace=True)
        return ak_df

    @retry_network
    def list_us_index_with_daily(
        self, symbol: Union[Literal[".DJI", ".INX", ".NDX"], str]
    ) -> pd.DataFrame:
        """
        美股指数
        symbol: choice of {".DJI", ".INX", ".NDX"}, INX 是标普500
        :return: date	open	high	low	close	volume	amount
        """
        df = ak.index_us_stock_sina(symbol=symbol)
        df[self.DATE_INDEX] = pd.to_datetime(df[self.DATE_INDEX])
        return df

    @retry_network
    def list_bond_us_rate(self, start_date: datetime) -> pd.DataFrame:
        """
        中美国债收益率
        :param start_date:
        :return: 日期	中国国债收益率2年	中国国债收益率5年	中国国债收益率10年	中国国债收益率30年	中国国债收益率10年-2年	中国GDP年增率	美国国债收益率2年	美国国债收益率5年	美国国债收益率10年	美国国债收益率30年	美国国债收益率10年-2年	美国GDP年增率
        """
        d_fmt = DateTimeFormat.DATE_FORMAT_2.value
        df = ak.bond_zh_us_rate(start_date=start_date.strftime(d_fmt))
        df[self.DATE_INDEX] = pd.to_datetime(df[self.DATE_INDEX])
        return df

    @retry_network
    def list_stock_index_pe_lg(
        self, symbol: Literal["中证500", "沪深300"]
    ) -> pd.DataFrame:
        """
        市盈率
        :param symbol: choice of {"中证500", "沪深300"}
        :return 日期	指数	等权静态市盈率	静态市盈率	静态市盈率中位数	等权滚动市盈率	滚动市盈率	滚动市盈率中位数
        """
        df = ak.stock_index_pe_lg(symbol=symbol)
        df[self.DATE_INDEX] = pd.to_datetime(df[self.DATE_INDEX])
        return df

    @retry_network
    def list_foreign_futures_symbol(self) -> pd.DataFrame:
        """
        外盘期货列表
        :return: symbol	code
        """
        df = ak.futures_hq_subscribe_exchange_symbol()
        return df

    @retry_network
    def list_foreign_futures_quote(self, symbol_list: List[str]) -> pd.DataFrame:
        """
        外盘期货实时行情
        :return: 名称	最新价	人民币报价	涨跌额	涨跌幅	开盘价	最高价	最低价	昨日结算价	持仓量	买价	卖价	行情时间
        """
        df = ak.futures_foreign_commodity_realtime(symbol=",".join(symbol_list))
        df[self.DATE_INDEX] = pd.to_datetime(df[self.DATE_INDEX])
        return df

    @retry_network
    def list_a_stock_delisting(self) -> pd.DataFrame:
        """
        查询退市
        """
        return ak.stock_zh_a_stop_em()

    @retry_network
    def list_a_stock_quote_sh(self) -> pd.DataFrame:
        """
         沪市
         :return: '序号', '代码', '名称', '最新价', '涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低',
        '今开', '昨收', '量比', '换手率', '市盈率-动态', '市净率', '总市值', '流通市值', '涨速', '5分钟涨跌',
        '60日涨跌幅', '年初至今涨跌幅'
        """
        df = ak.stock_sh_a_spot_em()
        return df

    @retry_network
    def list_a_stock_quote_sz(self) -> pd.DataFrame:
        """
         深圳市场
         :return: '序号', '代码', '名称', '最新价', '涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低'
        '今开', '昨收', '量比', '换手率', '市盈率-动态', '市净率', '总市值', '流通市值', '涨速', '5分钟涨跌',
        '60日涨跌幅', '年初至今涨跌幅'
        """
        df = ak.stock_sz_a_spot_em()
        return df

    @retry_network
    def list_etf_quote(self) -> pd.DataFrame:
        """
        etf 市场行情
        :return: '代码', '名称', '最新价', '涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低', '昨收', '量比', '换手率'
        """
        return ak.fund_etf_spot_em()


ak_cli = AkShareClient()
