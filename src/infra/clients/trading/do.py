from datetime import datetime

from pydantic import BaseModel, Field


class StockMinuteData(BaseModel):
    code: str = Field(..., description="股票代码")
    open: float = Field(..., description="开盘价")
    close: float = Field(..., description="收盘价")
    high: float = Field(..., description="最高价")
    low: float = Field(..., description="最低价")
    volume: float = Field(..., description="成交量")
    amount: float = Field(..., description="成交额")
    change_rate: float = Field(..., description="涨跌幅")
    pre_close: float = Field(..., description="昨收价")
