import json
import time
from asyncio import AbstractEventLoop

import uvicorn
from fastapi import FastAPI, APIRouter, Request
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
from starlette import status
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.responses import J<PERSON><PERSON>esponse, StreamingResponse

from config.globalconfig import get_or_create_settings_ins
from src.infra.web import const, exceptions
from src.infra.web.schema import HttpResponse

__all__ = [
    "http_server",
    "HttpResponse",
    "const",
]


class HttpServer(FastAPI):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # infra
        self.config = get_or_create_settings_ins()
        self.openapi_url = self.config.app.http_openapi_url
        self.docs_url = self.config.app.http_docs_url

        # 添加CORS中间件
        self._setup_cors()

    def _setup_cors(self):
        """配置CORS中间件以解决跨域问题"""
        self.add_middleware(
            CORSMiddleware,
            allow_origins=[
                "*",  # 开发环境允许所有来源（生产环境应该限制）
            ],
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
            allow_headers=[
                "Accept",
                "Accept-Language",
                "Content-Language",
                "Content-Type",
                "Authorization",
                "X-Requested-With",
                "X-Process-Time",
            ],
            expose_headers=["X-Process-Time"],  # 暴露自定义响应头
        )
        logger.info("✅ CORS中间件已配置 - 支持跨域请求")

    def new_router(self) -> APIRouter:
        return APIRouter()

    def register_router(self, **kwargs) -> "HttpServer":
        self.include_router(**kwargs)
        return self

    def launch(self):
        uvicorn.run(self,
                    host=self.config.app.host,
                    port=self.config.app.http_port,
                    log_config=const.LOGGING_CONFIG,
                    log_level=str.lower(self.config.app.log_level),
                    )


http_server = HttpServer()


def _ignore_wrap_response(request: Request, response: StreamingResponse) -> bool:
    if response.headers["Content-Type"] != "application/json":
        return True

    if request.url.path == http_server.config.app.http_docs_url:
        return True

    if request.url.path == http_server.config.app.http_openapi_url:
        return True

    return False


@http_server.middleware("stander_response")
async def wrap_response(request: Request, call_next):
    start_time = time.time()
    response: StreamingResponse = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)

    if _ignore_wrap_response(request, response):
        return response

    body_container = []

    async for chunk in response.body_iterator:
        body_container.append(str(chunk, encoding="utf-8"))

    resp_data = json.loads(body_container[0])
    data = HttpResponse()
    if response.status_code >= 400:
        data.code = const.CODE_ERROR
        data.message = resp_data
    else:
        data.code = const.CODE_SUCCEED
        data.data = resp_data

    data_str = data.json().encode("utf-8")
    body_container = [data_str]
    # 设置头部长度
    response.headers["Content-Length"] = str(len(data_str))

    async def new_body_iterator():
        yield body_container[0]

    response.body_iterator = new_body_iterator()
    return response


@http_server.exception_handler(Exception)
async def common_exception_handler(request, exc: Exception):
    resp = HttpResponse()
    resp.code = const.CODE_ERROR
    resp.message = "Unknown error." if str(exc) == "" else str(exc)
    return JSONResponse(content=resp.dict(), status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@http_server.exception_handler(StarletteHTTPException)
async def http_exception_handler(request, exc: StarletteHTTPException):
    resp = HttpResponse()
    resp.code = const.CODE_ERROR
    resp.message = "Unknown error." if str(exc) == "" else str(exc)
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR if exc.status_code == 0 else exc.status_code
    return JSONResponse(content=resp.dict(), status_code=status_code)


@http_server.exception_handler(RequestValidationError)
async def validation_exception_handler(request, err: RequestValidationError):
    resp = HttpResponse()
    resp.code = const.CODE_ERROR
    resp.message = "Unprocessable entity."
    resp.data = err.errors()
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    return JSONResponse(content=resp.dict(), status_code=status_code)


@http_server.exception_handler(exceptions.NotFoundError)
async def validation_exception_handler(request, err: exceptions.NotFoundError):
    logger.exception(err)
    status_code = status.HTTP_404_NOT_FOUND
    return JSONResponse(content=err.errors(), status_code=status_code)
